{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // This interface extends the base input props\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-11 w-full rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-sm font-medium text-secondary-700 transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-400 focus-visible:outline-none focus-visible:border-primary-500 focus-visible:ring-4 focus-visible:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 hover:border-secondary-300\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gbACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning:\n          \"border-transparent bg-slate-100 text-slate-800 hover:bg-slate-200\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {\n  // This interface extends the base label props\n}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = \"Label\"\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  // This interface extends the base textarea props\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { X } from \"lucide-react\"\n\ninterface DialogProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n  enableScrollInteraction?: boolean // خيار لتفعيل التفاعل مع التمرير\n  maxHeight?: string // الحد الأقصى للارتفاع\n}\n\ninterface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Dialog = ({\n  open,\n  onOpenChange,\n  children,\n  enableScrollInteraction = true,\n  maxHeight = \"90vh\"\n}: DialogProps) => {\n  const dialogRef = React.useRef<HTMLDivElement>(null)\n  const containerRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onOpenChange?.(false)\n      }\n    }\n\n    const handleWheel = (e: WheelEvent) => {\n      if (!open || !enableScrollInteraction || !dialogRef.current) return\n\n      // السماح بالتمرير داخل الحوار فقط\n      const dialogElement = dialogRef.current\n      const isScrollable = dialogElement.scrollHeight > dialogElement.clientHeight\n\n      if (isScrollable) {\n        // التحقق من أن الماوس داخل منطقة الحوار\n        const rect = dialogElement.getBoundingClientRect()\n        const isInsideDialog = e.clientX >= rect.left && e.clientX <= rect.right &&\n                              e.clientY >= rect.top && e.clientY <= rect.bottom\n\n        if (isInsideDialog) {\n          // السماح بالتمرير الطبيعي داخل الحوار\n          return\n        }\n      }\n\n      // منع التمرير خارج الحوار\n      e.preventDefault()\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEscape)\n      if (enableScrollInteraction) {\n        document.addEventListener('wheel', handleWheel, { passive: false })\n      }\n      // عدم منع التمرير في الخلفية للسماح بالتمرير داخل الحوار\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.removeEventListener('wheel', handleWheel)\n    }\n  }, [open, onOpenChange, enableScrollInteraction])\n\n  if (!open) return null\n\n  return (\n    <div\n      ref={containerRef}\n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n    >\n      <div\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300\"\n        onClick={() => onOpenChange?.(false)}\n      />\n      <div\n        ref={dialogRef}\n        className={cn(\n          \"relative z-50 w-full overflow-hidden animate-in fade-in-0 zoom-in-95 duration-300\",\n          \"smooth-scroll\", // فئة CSS للتمرير السلس\n          enableScrollInteraction ? \"overflow-y-auto\" : \"overflow-hidden\"\n        )}\n        style={{\n          maxHeight: maxHeight,\n          transform: 'translate3d(0, 0, 0)', // تحسين الأداء\n          willChange: 'transform', // تحسين الأداء\n        }}\n      >\n        {children}\n      </div>\n    </div>\n  )\n}\n\nconst DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200\",\n        \"backdrop-blur-sm bg-white/95\", // تحسين الشفافية\n        \"hover:shadow-3xl transition-shadow duration-300\", // تأثير الظل عند التمرير\n        \"max-h-full overflow-y-auto smooth-scroll\", // تمكين التمرير السلس\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogContent.displayName = \"DialogContent\"\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-bold leading-none tracking-tight text-slate-800\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = \"DialogTitle\"\n\nconst DialogDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-slate-600 font-medium\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = \"DialogDescription\"\n\nconst DialogTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement>\n>(({ className, ...props }, ref) => (\n  <button\n    ref={ref}\n    className={className}\n    {...props}\n  />\n))\nDialogTrigger.displayName = \"DialogTrigger\"\n\nconst DialogClose = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & { onOpenChange?: (open: boolean) => void }\n>(({ className, onOpenChange, ...props }, ref) => (\n  <button\n    ref={ref}\n    type=\"button\"\n    onClick={() => onOpenChange?.(false)}\n    className={cn(\n      \"absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10\",\n      className\n    )}\n    {...props}\n  >\n    <X className=\"h-4 w-4 text-slate-600\" />\n    <span className=\"sr-only\">إغلاق</span>\n  </button>\n))\nDialogClose.displayName = \"DialogClose\"\n\nexport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n  DialogClose,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAkBA,MAAM,SAAS,CAAC,EACd,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,0BAA0B,IAAI,EAC9B,YAAY,MAAM,EACN;;IACZ,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAkB;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAkB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd,MAAM;iDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB,eAAe;oBACjB;gBACF;;YAEA,MAAM;gDAAc,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,UAAU,OAAO,EAAE;oBAE7D,kCAAkC;oBAClC,MAAM,gBAAgB,UAAU,OAAO;oBACvC,MAAM,eAAe,cAAc,YAAY,GAAG,cAAc,YAAY;oBAE5E,IAAI,cAAc;wBAChB,wCAAwC;wBACxC,MAAM,OAAO,cAAc,qBAAqB;wBAChD,MAAM,iBAAiB,EAAE,OAAO,IAAI,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,KAAK,KAAK,IAClD,EAAE,OAAO,IAAI,KAAK,GAAG,IAAI,EAAE,OAAO,IAAI,KAAK,MAAM;wBAEvE,IAAI,gBAAgB;4BAClB,sCAAsC;4BACtC;wBACF;oBACF;oBAEA,0BAA0B;oBAC1B,EAAE,cAAc;gBAClB;;YAEA,IAAI,MAAM;gBACR,SAAS,gBAAgB,CAAC,WAAW;gBACrC,IAAI,yBAAyB;oBAC3B,SAAS,gBAAgB,CAAC,SAAS,aAAa;wBAAE,SAAS;oBAAM;gBACnE;YACA,yDAAyD;YAC3D;YAEA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,mBAAmB,CAAC,SAAS;gBACxC;;QACF;2BAAG;QAAC;QAAM;QAAc;KAAwB;IAEhD,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAEV,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAEhC,6LAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA,iBACA,0BAA0B,oBAAoB;gBAEhD,OAAO;oBACL,WAAW;oBACX,WAAW;oBACX,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAIT;GAlFM;KAAA;AAoFN,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QACnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA,gCACA,mDACA,4CACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+HACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGjC,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE,oBACxC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,SAAS,IAAM,eAAe;QAC9B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wRACA;QAED,GAAG,KAAK;;0BAET,6LAAC,+LAAA,CAAA,IAAC;gBAAC,WAAU;;;;;;0BACb,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;;AAG9B,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-11 w-full items-center justify-between rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-base font-medium text-secondary-700 transition-all duration-200 hover:border-secondary-300 focus:border-primary-500 focus:outline-none focus:ring-4 focus:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-5 w-5 text-secondary-400 transition-transform duration-200 data-[state=open]:rotate-180\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-xl border border-secondary-200 bg-white text-secondary-700 shadow-xl backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-2\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-2 pl-10 pr-3 text-sm font-bold text-secondary-600 bg-secondary-50\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-pointer select-none items-center rounded-lg py-3 pl-10 pr-3 text-sm font-medium text-secondary-700 transition-colors hover:bg-primary-50 focus:bg-primary-100 focus:text-primary-800 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[state=checked]:bg-primary-100 data-[state=checked]:text-primary-800 data-[state=checked]:font-semibold\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-3 flex h-4 w-4 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4 text-primary-600\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sWACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,seACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wEAAwE;QACrF,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gYACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/incomes/income-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { DollarSign, Search, X, User } from 'lucide-react'\n\nconst incomeSchema = z.object({\n  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),\n  date: z.string().min(1, 'التاريخ مطلوب'),\n  source: z.string().min(1, 'مصدر الإيراد مطلوب'),\n  type: z.enum(['SUBSCRIPTION', 'DONATION', 'EVENT', 'OTHER']).default('SUBSCRIPTION'),\n  description: z.string().optional(),\n  notes: z.string().optional(),\n  memberId: z.string().min(1, 'يجب اختيار عضو'),\n})\n\ntype IncomeInput = z.infer<typeof incomeSchema>\n\ninterface IncomeDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess?: () => void\n  income?: {\n    id: string\n    amount: number\n    date: string\n    source: string\n    type: string\n    description?: string\n    notes?: string\n    member?: {\n      id: string\n      name: string\n    }\n  } | null\n}\n\ninterface Member {\n  id: string\n  name: string\n}\n\nexport default function IncomeDialog({\n  open,\n  onOpenChange,\n  onSuccess,\n  income = null,\n}: IncomeDialogProps) {\n  const [loading, setLoading] = useState(false)\n  const [members, setMembers] = useState<Member[]>([])\n  const [filteredMembers, setFilteredMembers] = useState<Member[]>([])\n  const [memberSearch, setMemberSearch] = useState('')\n  const [selectedMember, setSelectedMember] = useState<Member | null>(null)\n  const [showMemberDropdown, setShowMemberDropdown] = useState(false)\n  const memberSearchRef = useRef<HTMLDivElement>(null)\n\n  const {\n    register,\n    handleSubmit,\n    reset,\n    setValue,\n    watch,\n    formState: { errors },\n  } = useForm<IncomeInput>({\n    resolver: zodResolver(incomeSchema),\n    defaultValues: {\n      amount: 0,\n      date: new Date().toISOString().split('T')[0], // التاريخ الحالي\n      source: '',\n      type: 'SUBSCRIPTION',\n      description: '',\n      notes: '',\n      memberId: '',\n    },\n  })\n\n  const selectedType = watch('type')\n\n  // جلب قائمة الأعضاء\n  const fetchMembers = async () => {\n    try {\n      const response = await fetch('/api/members?limit=1000')\n      if (response.ok) {\n        const data = await response.json()\n        setMembers(data.members || [])\n      }\n    } catch (error) {\n      console.error('خطأ في جلب الأعضاء:', error)\n    }\n  }\n\n  useEffect(() => {\n    if (open) {\n      fetchMembers()\n\n      if (income) {\n        // تعديل إيراد موجود\n        reset({\n          amount: income.amount,\n          date: income.date.split('T')[0], // تحويل التاريخ للصيغة المطلوبة\n          source: income.source,\n          type: income.type as any,\n          description: income.description || '',\n          notes: income.notes || '',\n          memberId: income.member?.id || '',\n        })\n\n        if (income.member) {\n          setSelectedMember(income.member)\n          setMemberSearch(income.member.name)\n        } else {\n          setSelectedMember(null)\n          setMemberSearch('')\n        }\n      } else {\n        // إضافة إيراد جديد\n        reset({\n          amount: 0,\n          date: new Date().toISOString().split('T')[0],\n          source: '',\n          type: 'SUBSCRIPTION',\n          description: '',\n          notes: '',\n          memberId: '',\n        })\n        setSelectedMember(null)\n        setMemberSearch('')\n      }\n\n      setShowMemberDropdown(false)\n    }\n  }, [open, income, reset])\n\n  // تصفية الأعضاء عند البحث\n  useEffect(() => {\n    if (memberSearch.trim() === '') {\n      setFilteredMembers(members.slice(0, 10)) // عرض أول 10 أعضاء\n    } else {\n      const filtered = members.filter(member =>\n        member.name.toLowerCase().includes(memberSearch.toLowerCase())\n      ).slice(0, 10) // عرض أول 10 نتائج\n      setFilteredMembers(filtered)\n    }\n  }, [memberSearch, members])\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (memberSearchRef.current && !memberSearchRef.current.contains(event.target as Node)) {\n        setShowMemberDropdown(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  // اختيار عضو\n  const handleSelectMember = (member: Member) => {\n    setSelectedMember(member)\n    setMemberSearch(member.name)\n    setShowMemberDropdown(false)\n    setValue('memberId', member.id)\n  }\n\n  // إزالة اختيار العضو\n  const handleClearMember = () => {\n    setSelectedMember(null)\n    setMemberSearch('')\n    setValue('memberId', '')\n  }\n\n  // فتح قائمة البحث\n  const handleMemberSearchFocus = () => {\n    setShowMemberDropdown(true)\n    if (memberSearch.trim() === '') {\n      setFilteredMembers(members.slice(0, 10))\n    }\n  }\n\n  const onSubmit = async (data: IncomeInput) => {\n    try {\n      setLoading(true)\n\n      // تحويل البيانات\n      const submitData = {\n        ...data,\n        amount: Number(data.amount),\n        date: new Date(data.date),\n        memberId: data.memberId,\n        description: data.description?.trim() || null,\n        notes: data.notes?.trim() || null,\n      }\n\n      const isEditing = !!income\n      const url = isEditing ? `/api/incomes/${income.id}` : '/api/incomes'\n      const method = isEditing ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submitData),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.error || 'حدث خطأ')\n      }\n\n      alert(isEditing ? 'تم تعديل الإيراد بنجاح' : 'تم إضافة الإيراد بنجاح')\n      onOpenChange(false)\n      onSuccess?.()\n    } catch (error: any) {\n      console.error('خطأ في حفظ الإيراد:', error)\n      alert(error.message || 'حدث خطأ في حفظ الإيراد')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // const getIncomeTypeText = (type: string) => {\n  //   switch (type) {\n  //     case 'SUBSCRIPTION': return 'اشتراكات'\n  //     case 'DONATION': return 'تبرعات'\n  //     case 'EVENT': return 'فعاليات'\n  //     case 'OTHER': return 'أخرى'\n  //     default: return type\n  //   }\n  // }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-[50vw] max-h-[95vh] overflow-y-auto bg-white rounded-2xl shadow-2xl border-0\">\n        <DialogHeader className=\"relative p-8 pb-6 border-b border-gray-100 bg-gradient-to-br from-diwan-50 via-blue-50 to-indigo-50 rounded-t-2xl\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-diwan-600/5 via-blue-600/5 to-indigo-600/5 rounded-t-2xl\"></div>\n          <div className=\"relative flex items-center gap-4\">\n            <div className=\"p-4 bg-white rounded-2xl shadow-lg border border-diwan-200/50 backdrop-blur-sm\">\n              <DollarSign className=\"w-7 h-7 text-diwan-600\" />\n            </div>\n            <div className=\"flex-1\">\n              <DialogTitle className=\"text-2xl font-bold text-gray-900 mb-2\">\n                {income ? 'تعديل الإيراد' : 'إضافة إيراد جديد'}\n              </DialogTitle>\n              <p className=\"text-gray-600 text-base leading-relaxed\">\n                {income\n                  ? 'قم بتعديل بيانات الإيراد بعناية لضمان دقة السجلات المالية'\n                  : 'قم بإدخال بيانات الإيراد الجديد بعناية لضمان دقة السجلات المالية'\n                }\n              </p>\n            </div>\n          </div>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"p-8 space-y-8 bg-gray-50/30\">\n          {/* القسم الأول: المعلومات الأساسية */}\n          <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-diwan-600 rounded-full\"></div>\n              المعلومات الأساسية\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"amount\" className=\"text-sm font-medium text-gray-700 flex items-center gap-1\">\n                  المبلغ (دينار أردني)\n                  <span className=\"text-red-500\">*</span>\n                </Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"amount\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    {...register('amount', { valueAsNumber: true })}\n                    className={`h-12 text-lg font-medium transition-all duration-200 ${\n                      errors.amount\n                        ? 'border-red-300 focus:border-red-500 focus:ring-red-200'\n                        : 'border-gray-200 focus:border-diwan-500 focus:ring-diwan-100'\n                    }`}\n                    placeholder=\"0.00\"\n                  />\n                  <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm\">\n                    د.أ\n                  </div>\n                </div>\n                {errors.amount && (\n                  <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <span className=\"w-1 h-1 bg-red-500 rounded-full\"></span>\n                    {errors.amount.message}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"date\" className=\"text-sm font-medium text-gray-700 flex items-center gap-1\">\n                  التاريخ\n                  <span className=\"text-red-500\">*</span>\n                </Label>\n                <Input\n                  id=\"date\"\n                  type=\"date\"\n                  {...register('date')}\n                  className={`h-12 transition-all duration-200 ${\n                    errors.date\n                      ? 'border-red-300 focus:border-red-500 focus:ring-red-200'\n                      : 'border-gray-200 focus:border-diwan-500 focus:ring-diwan-100'\n                  }`}\n                />\n                {errors.date && (\n                  <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <span className=\"w-1 h-1 bg-red-500 rounded-full\"></span>\n                    {errors.date.message}\n                  </p>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* القسم الثاني: تفاصيل الإيراد */}\n          <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-gold-600 rounded-full\"></div>\n              تفاصيل الإيراد\n            </h3>\n            <div className=\"space-y-6\">\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"source\" className=\"text-sm font-medium text-gray-700 flex items-center gap-1\">\n                  مصدر الإيراد\n                  <span className=\"text-red-500\">*</span>\n                </Label>\n                <Input\n                  id=\"source\"\n                  {...register('source')}\n                  placeholder=\"مثال: اشتراك شهري، تبرع، رسوم فعالية\"\n                  className={`h-12 transition-all duration-200 ${\n                    errors.source\n                      ? 'border-red-300 focus:border-red-500 focus:ring-red-200'\n                      : 'border-gray-200 focus:border-diwan-500 focus:ring-diwan-100'\n                  }`}\n                />\n                {errors.source && (\n                  <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <span className=\"w-1 h-1 bg-red-500 rounded-full\"></span>\n                    {errors.source.message}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"space-y-3\">\n                  <Label htmlFor=\"type\" className=\"text-sm font-medium text-gray-700\">\n                    نوع الإيراد\n                  </Label>\n                  <Select\n                    value={selectedType}\n                    onValueChange={(value) => setValue('type', value as any)}\n                  >\n                    <SelectTrigger className=\"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200\">\n                      <SelectValue placeholder=\"اختر نوع الإيراد\" />\n                    </SelectTrigger>\n                    <SelectContent className=\"border-gray-200 shadow-lg\">\n                      <SelectItem value=\"SUBSCRIPTION\" className=\"hover:bg-diwan-50 focus:bg-diwan-50\">\n                        <div className=\"flex items-center gap-2\">\n                          <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                          اشتراكات\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"DONATION\" className=\"hover:bg-green-50 focus:bg-green-50\">\n                        <div className=\"flex items-center gap-2\">\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                          تبرعات\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"EVENT\" className=\"hover:bg-purple-50 focus:bg-purple-50\">\n                        <div className=\"flex items-center gap-2\">\n                          <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                          فعاليات\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"OTHER\" className=\"hover:bg-gray-50 focus:bg-gray-50\">\n                        <div className=\"flex items-center gap-2\">\n                          <div className=\"w-2 h-2 bg-gray-500 rounded-full\"></div>\n                          أخرى\n                        </div>\n                      </SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <Label htmlFor=\"memberSearch\" className=\"text-sm font-medium text-gray-700 flex items-center gap-1\">\n                    العضو\n                    <span className=\"text-red-500\">*</span>\n                  </Label>\n                  <div className=\"relative\" ref={memberSearchRef}>\n                    <div className=\"relative\">\n                      <Search className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                      <Input\n                        id=\"memberSearch\"\n                        type=\"text\"\n                        placeholder=\"ابحث عن عضو...\"\n                        value={memberSearch}\n                        onChange={(e) => setMemberSearch(e.target.value)}\n                        onFocus={handleMemberSearchFocus}\n                        className={`h-12 pr-12 pl-12 transition-all duration-200 ${\n                          errors.memberId\n                            ? 'border-red-300 focus:border-red-500 focus:ring-red-200'\n                            : 'border-gray-200 focus:border-diwan-500 focus:ring-diwan-100'\n                        }`}\n                      />\n                      {selectedMember && (\n                        <button\n                          type=\"button\"\n                          onClick={handleClearMember}\n                          className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200\"\n                        >\n                          <X className=\"w-5 h-5\" />\n                        </button>\n                      )}\n                    </div>\n\n                    {/* قائمة النتائج */}\n                    {showMemberDropdown && (\n                      <div className=\"absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-xl shadow-xl max-h-64 overflow-y-auto\">\n                        {filteredMembers.length === 0 ? (\n                          <div className=\"p-4 text-gray-500 text-center\">\n                            <div className=\"flex flex-col items-center gap-2\">\n                              <Search className=\"w-8 h-8 text-gray-300\" />\n                              <span className=\"text-sm\">\n                                {memberSearch.trim() === '' ? 'ابدأ بالكتابة للبحث عن عضو' : 'لا توجد نتائج مطابقة'}\n                              </span>\n                            </div>\n                          </div>\n                        ) : (\n                          <>\n                            {filteredMembers.map((member) => (\n                              <div\n                                key={member.id}\n                                className=\"p-4 hover:bg-diwan-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150\"\n                                onClick={() => handleSelectMember(member)}\n                              >\n                                <div className=\"flex items-center gap-3\">\n                                  <div className=\"w-8 h-8 bg-diwan-100 rounded-full flex items-center justify-center\">\n                                    <User className=\"w-4 h-4 text-diwan-600\" />\n                                  </div>\n                                  <span className=\"font-medium text-gray-900\">{member.name}</span>\n                                </div>\n                              </div>\n                            ))}\n                          </>\n                        )}\n                      </div>\n                    )}\n                  </div>\n\n                  {/* رسالة الخطأ */}\n                  {errors.memberId && (\n                    <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                      <span className=\"w-1 h-1 bg-red-500 rounded-full\"></span>\n                      {errors.memberId.message}\n                    </p>\n                  )}\n\n                  {/* عرض العضو المختار */}\n                  {selectedMember && (\n                    <div className=\"mt-3 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\n                            <User className=\"w-4 h-4 text-green-600\" />\n                          </div>\n                          <div>\n                            <span className=\"text-green-800 font-semibold\">{selectedMember.name}</span>\n                            <p className=\"text-green-600 text-xs\">العضو المحدد</p>\n                          </div>\n                        </div>\n                        <button\n                          type=\"button\"\n                          onClick={handleClearMember}\n                          className=\"text-green-600 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-white/50\"\n                        >\n                          <X className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* القسم الثالث: معلومات إضافية */}\n          <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-indigo-600 rounded-full\"></div>\n              معلومات إضافية\n            </h3>\n            <div className=\"space-y-6\">\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"description\" className=\"text-sm font-medium text-gray-700\">\n                  الوصف (اختياري)\n                </Label>\n                <Input\n                  id=\"description\"\n                  {...register('description')}\n                  placeholder=\"وصف إضافي للإيراد\"\n                  className=\"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200\"\n                />\n              </div>\n\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"notes\" className=\"text-sm font-medium text-gray-700\">\n                  ملاحظات (اختياري)\n                </Label>\n                <Textarea\n                  id=\"notes\"\n                  {...register('notes')}\n                  placeholder=\"أي ملاحظات إضافية حول هذا الإيراد\"\n                  rows={4}\n                  className=\"border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200 resize-none\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* أزرار التحكم */}\n          <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100 mt-6\">\n            <div className=\"flex flex-col sm:flex-row justify-end gap-4 sm:gap-3\">\n              {/* زر الإلغاء */}\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                disabled={loading}\n                className=\"group relative h-14 px-8 border-2 border-gray-300 text-gray-700 hover:border-red-400 hover:text-red-600 bg-white hover:bg-red-50 transition-all duration-300 font-semibold rounded-xl shadow-sm hover:shadow-md transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n              >\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-5 h-5 rounded-full bg-gray-200 group-hover:bg-red-200 transition-colors duration-300 flex items-center justify-center\">\n                    <X className=\"w-3 h-3 text-gray-600 group-hover:text-red-600 transition-colors duration-300\" />\n                  </div>\n                  <span className=\"text-base\">إلغاء</span>\n                </div>\n                {/* تأثير الخلفية المتحركة */}\n                <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-red-50 to-pink-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10\"></div>\n              </Button>\n\n              {/* زر الحفظ */}\n              <Button\n                type=\"submit\"\n                disabled={loading}\n                className=\"group relative h-14 px-10 bg-gradient-to-r from-diwan-600 via-diwan-700 to-blue-600 hover:from-diwan-700 hover:via-diwan-800 hover:to-blue-700 text-white font-bold rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none overflow-hidden\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"relative\">\n                      <div className=\"w-5 h-5 border-3 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                      <div className=\"absolute inset-0 w-5 h-5 border-3 border-transparent border-t-white/60 rounded-full animate-ping\"></div>\n                    </div>\n                    <span className=\"text-base\">جاري الحفظ...</span>\n                  </div>\n                ) : (\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-6 h-6 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-all duration-300\">\n                      <DollarSign className=\"w-4 h-4 text-white\" />\n                    </div>\n                    <span className=\"text-base\">{income ? 'حفظ التعديلات' : 'حفظ الإيراد'}</span>\n                    <div className=\"w-2 h-2 rounded-full bg-white/40 group-hover:bg-white/60 transition-all duration-300\"></div>\n                  </div>\n                )}\n\n                {/* تأثير الضوء المتحرك */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-out\"></div>\n\n                {/* تأثير الخلفية المتوهجة */}\n                <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-diwan-400/20 via-blue-400/20 to-indigo-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl -z-10\"></div>\n              </Button>\n            </div>\n\n            {/* خط فاصل زخرفي */}\n            <div className=\"flex items-center justify-center mt-4 pt-4 border-t border-gray-100\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-2 h-2 rounded-full bg-diwan-200\"></div>\n                <div className=\"w-1 h-1 rounded-full bg-diwan-300\"></div>\n                <div className=\"w-2 h-2 rounded-full bg-diwan-200\"></div>\n              </div>\n            </div>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAMA;AAOA;AAAA;AAAA;AAAA;;;AAvBA;;;;;;;;;;;;AAyBA,MAAM,eAAe,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,MAAM,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAgB;QAAY;QAAS;KAAQ,EAAE,OAAO,CAAC;IACrE,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AA4Be,SAAS,aAAa,EACnC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,SAAS,IAAI,EACK;;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE/C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAe;QACvB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,QAAQ;YACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5C,QAAQ;YACR,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;QACZ;IACF;IAEA,MAAM,eAAe,MAAM;IAE3B,oBAAoB;IACpB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO,IAAI,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM;gBACR;gBAEA,IAAI,QAAQ;oBACV,oBAAoB;oBACpB,MAAM;wBACJ,QAAQ,OAAO,MAAM;wBACrB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wBAC/B,QAAQ,OAAO,MAAM;wBACrB,MAAM,OAAO,IAAI;wBACjB,aAAa,OAAO,WAAW,IAAI;wBACnC,OAAO,OAAO,KAAK,IAAI;wBACvB,UAAU,OAAO,MAAM,EAAE,MAAM;oBACjC;oBAEA,IAAI,OAAO,MAAM,EAAE;wBACjB,kBAAkB,OAAO,MAAM;wBAC/B,gBAAgB,OAAO,MAAM,CAAC,IAAI;oBACpC,OAAO;wBACL,kBAAkB;wBAClB,gBAAgB;oBAClB;gBACF,OAAO;oBACL,mBAAmB;oBACnB,MAAM;wBACJ,QAAQ;wBACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wBAC5C,QAAQ;wBACR,MAAM;wBACN,aAAa;wBACb,OAAO;wBACP,UAAU;oBACZ;oBACA,kBAAkB;oBAClB,gBAAgB;gBAClB;gBAEA,sBAAsB;YACxB;QACF;iCAAG;QAAC;QAAM;QAAQ;KAAM;IAExB,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,aAAa,IAAI,OAAO,IAAI;gBAC9B,mBAAmB,QAAQ,KAAK,CAAC,GAAG,KAAK,mBAAmB;;YAC9D,OAAO;gBACL,MAAM,WAAW,QAAQ,MAAM;uDAAC,CAAA,SAC9B,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,WAAW;sDAC3D,KAAK,CAAC,GAAG,IAAI,mBAAmB;;gBAClC,mBAAmB;YACrB;QACF;iCAAG;QAAC;QAAc;KAAQ;IAE1B,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;6DAAqB,CAAC;oBAC1B,IAAI,gBAAgB,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACtF,sBAAsB;oBACxB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;0CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;iCAAG,EAAE;IAEL,aAAa;IACb,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,gBAAgB,OAAO,IAAI;QAC3B,sBAAsB;QACtB,SAAS,YAAY,OAAO,EAAE;IAChC;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,gBAAgB;QAChB,SAAS,YAAY;IACvB;IAEA,kBAAkB;IAClB,MAAM,0BAA0B;QAC9B,sBAAsB;QACtB,IAAI,aAAa,IAAI,OAAO,IAAI;YAC9B,mBAAmB,QAAQ,KAAK,CAAC,GAAG;QACtC;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,WAAW;YAEX,iBAAiB;YACjB,MAAM,aAAa;gBACjB,GAAG,IAAI;gBACP,QAAQ,OAAO,KAAK,MAAM;gBAC1B,MAAM,IAAI,KAAK,KAAK,IAAI;gBACxB,UAAU,KAAK,QAAQ;gBACvB,aAAa,KAAK,WAAW,EAAE,UAAU;gBACzC,OAAO,KAAK,KAAK,EAAE,UAAU;YAC/B;YAEA,MAAM,YAAY,CAAC,CAAC;YACpB,MAAM,MAAM,YAAY,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,GAAG;YACtD,MAAM,SAAS,YAAY,QAAQ;YAEnC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;YACjC;YAEA,MAAM,YAAY,2BAA2B;YAC7C,aAAa;YACb;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,MAAM,OAAO,IAAI;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,gDAAgD;IAChD,oBAAoB;IACpB,6CAA6C;IAC7C,uCAAuC;IACvC,qCAAqC;IACrC,kCAAkC;IAClC,2BAA2B;IAC3B,MAAM;IACN,IAAI;IAEJ,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,SAAS,kBAAkB;;;;;;sDAE9B,6LAAC;4CAAE,WAAU;sDACV,SACG,8DACA;;;;;;;;;;;;;;;;;;;;;;;;8BAOZ,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAI,WAAU;;;;;;wCAA0C;;;;;;;8CAG3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAS,WAAU;;wDAA4D;sEAE5F,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,KAAI;4DACH,GAAG,SAAS,UAAU;gEAAE,eAAe;4DAAK,EAAE;4DAC/C,WAAW,CAAC,qDAAqD,EAC/D,OAAO,MAAM,GACT,2DACA,+DACJ;4DACF,aAAY;;;;;;sEAEd,6LAAC;4DAAI,WAAU;sEAA2E;;;;;;;;;;;;gDAI3F,OAAO,MAAM,kBACZ,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;;;;;;wDACf,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;sDAK5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAO,WAAU;;wDAA4D;sEAE1F,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEjC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,OAAO;oDACpB,WAAW,CAAC,iCAAiC,EAC3C,OAAO,IAAI,GACP,2DACA,+DACJ;;;;;;gDAEH,OAAO,IAAI,kBACV,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;;;;;;wDACf,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAI,WAAU;;;;;;wCAAyC;;;;;;;8CAG1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAS,WAAU;;wDAA4D;sEAE5F,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEjC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,SAAS;oDACtB,aAAY;oDACZ,WAAW,CAAC,iCAAiC,EAC3C,OAAO,MAAM,GACT,2DACA,+DACJ;;;;;;gDAEH,OAAO,MAAM,kBACZ,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;;;;;;wDACf,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;sDAK5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAO,WAAU;sEAAoC;;;;;;sEAGpE,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO;4DACP,eAAe,CAAC,QAAU,SAAS,QAAQ;;8EAE3C,6LAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;;sFACvB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;4EAAe,WAAU;sFACzC,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;;;;;;oFAAyC;;;;;;;;;;;;sFAI5D,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;4EAAW,WAAU;sFACrC,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;;;;;;oFAA0C;;;;;;;;;;;;sFAI7D,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;4EAAQ,WAAU;sFAClC,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;;;;;;oFAA2C;;;;;;;;;;;;sFAI9D,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;4EAAQ,WAAU;sFAClC,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;;;;;;oFAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAQlE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAe,WAAU;;gEAA4D;8EAElG,6LAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;4DAAW,KAAK;;8EAC7B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,6LAAC,oIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,aAAY;4EACZ,OAAO;4EACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4EAC/C,SAAS;4EACT,WAAW,CAAC,6CAA6C,EACvD,OAAO,QAAQ,GACX,2DACA,+DACJ;;;;;;wEAEH,gCACC,6LAAC;4EACC,MAAK;4EACL,SAAS;4EACT,WAAU;sFAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gFAAC,WAAU;;;;;;;;;;;;;;;;;gEAMlB,oCACC,6LAAC;oEAAI,WAAU;8EACZ,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,yMAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;8FAClB,6LAAC;oFAAK,WAAU;8FACb,aAAa,IAAI,OAAO,KAAK,+BAA+B;;;;;;;;;;;;;;;;6FAKnE;kFACG,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;gFAEC,WAAU;gFACV,SAAS,IAAM,mBAAmB;0FAElC,cAAA,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAI,WAAU;sGACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gGAAC,WAAU;;;;;;;;;;;sGAElB,6LAAC;4FAAK,WAAU;sGAA6B,OAAO,IAAI;;;;;;;;;;;;+EARrD,OAAO,EAAE;;;;;;;;;;;;;;;;;wDAmB3B,OAAO,QAAQ,kBACd,6LAAC;4DAAE,WAAU;;8EACX,6LAAC;oEAAK,WAAU;;;;;;gEACf,OAAO,QAAQ,CAAC,OAAO;;;;;;;wDAK3B,gCACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;0FAElB,6LAAC;;kGACC,6LAAC;wFAAK,WAAU;kGAAgC,eAAe,IAAI;;;;;;kGACnE,6LAAC;wFAAE,WAAU;kGAAyB;;;;;;;;;;;;;;;;;;kFAG1C,6LAAC;wEACC,MAAK;wEACL,SAAS;wEACT,WAAU;kFAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAW7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAI,WAAU;;;;;;wCAA2C;;;;;;;8CAG5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAc,WAAU;8DAAoC;;;;;;8DAG3E,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,cAAc;oDAC3B,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;8DAAoC;;;;;;8DAGrE,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACF,GAAG,SAAS,QAAQ;oDACrB,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,aAAa;4CAC5B,UAAU;4CACV,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;sEAEf,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;8DAG9B,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU;4CACV,WAAU;;gDAET,wBACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEjB,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;yEAG9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;sEAExB,6LAAC;4DAAK,WAAU;sEAAa,SAAS,kBAAkB;;;;;;sEACxD,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAKnB,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAKnB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;GAxiBwB;;QAqBlB,iKAAA,CAAA,UAAO;;;KArBW", "debugId": null}}, {"offset": {"line": 2037, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/dashboard/incomes/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport {\n  Plus,\n  Search,\n  Edit,\n  Trash2,\n  Filter,\n  TrendingUp,\n  DollarSign,\n  Users,\n  Calendar,\n  FileText\n} from 'lucide-react'\nimport { formatCurrency, formatDate, getIncomeTypeText } from '@/lib/utils'\nimport IncomeDialog from '@/components/incomes/income-dialog'\nimport jsPDF from 'jspdf'\nimport html2canvas from 'html2canvas'\n\ninterface Income {\n  id: string\n  amount: number\n  date: string\n  source: string\n  type: string\n  description?: string\n  member?: {\n    id: string\n    name: string\n  }\n  createdBy: {\n    name: string\n  }\n}\n\ninterface IncomesResponse {\n  incomes: Income[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    pages: number\n  }\n}\n\nexport default function IncomesPage() {\n  const { data: session } = useSession()\n  const [incomes, setIncomes] = useState<Income[]>([])\n  const [loading, setLoading] = useState(true)\n  const [search, setSearch] = useState('')\n  const [type, setType] = useState('all')\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    pages: 0,\n  })\n  const [stats, setStats] = useState({\n    totalAmount: 0,\n    totalCount: 0,\n    byType: [] as Array<{ type: string; _sum: { amount: number }; _count: number }>\n  })\n  const [isIncomeDialogOpen, setIsIncomeDialogOpen] = useState(false)\n  const [editingIncome, setEditingIncome] = useState<Income | null>(null)\n\n  // جلب الإيرادات\n  const fetchIncomes = useCallback(async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams({\n        search,\n        type,\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString(),\n      })\n\n      const response = await fetch(`/api/incomes?${params}`)\n      if (!response.ok) throw new Error('فشل في جلب الإيرادات')\n\n      const data: IncomesResponse = await response.json()\n      setIncomes(data.incomes)\n      setPagination(data.pagination)\n    } catch (error) {\n      console.error('خطأ في جلب الإيرادات:', error)\n    } finally {\n      setLoading(false)\n    }\n  }, [search, type, pagination.page, pagination.limit])\n\n  // جلب الإحصائيات\n  const fetchStats = async () => {\n    try {\n      const response = await fetch('/api/incomes?limit=1000')\n      if (!response.ok) return\n\n      const data: IncomesResponse = await response.json()\n      const totalAmount = data.incomes.reduce((sum, income) => sum + income.amount, 0)\n      const totalCount = data.incomes.length\n\n      // حساب الإحصائيات حسب النوع\n      const byType = data.incomes.reduce((acc: Array<{ type: string; _sum: { amount: number }; _count: number }>, income) => {\n        const existing = acc.find((item) => item.type === income.type)\n        if (existing) {\n          existing._sum.amount += income.amount\n          existing._count += 1\n        } else {\n          acc.push({\n            type: income.type,\n            _sum: { amount: income.amount },\n            _count: 1\n          })\n        }\n        return acc\n      }, [])\n\n      setStats({ totalAmount, totalCount, byType })\n    } catch (error) {\n      console.error('خطأ في جلب الإحصائيات:', error)\n    }\n  }\n\n  useEffect(() => {\n    fetchIncomes()\n  }, [fetchIncomes])\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  // تعديل إيراد\n  const handleEditIncome = (income: Income) => {\n    setEditingIncome(income)\n    setIsIncomeDialogOpen(true)\n  }\n\n  // حذف إيراد\n  const handleDeleteIncome = async (id: string) => {\n    if (!confirm('هل أنت متأكد من حذف هذا الإيراد؟\\n\\nهذا الإجراء لا يمكن التراجع عنه.')) return\n\n    try {\n      const response = await fetch(`/api/incomes/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        alert(error.error || 'فشل في حذف الإيراد')\n        return\n      }\n\n      alert('تم حذف الإيراد بنجاح')\n      fetchIncomes()\n      fetchStats()\n    } catch (error) {\n      console.error('خطأ في حذف الإيراد:', error)\n      alert('حدث خطأ في حذف الإيراد')\n    }\n  }\n\n  // إضافة إيراد جديد\n  const handleAddNewIncome = () => {\n    setEditingIncome(null)\n    setIsIncomeDialogOpen(true)\n  }\n\n  // إغلاق نافذة التعديل\n  const handleCloseDialog = () => {\n    setIsIncomeDialogOpen(false)\n    setEditingIncome(null)\n  }\n\n  // تصدير البيانات إلى PDF\n  const handleExportIncomes = async () => {\n    try {\n      // جلب جميع الإيرادات للتصدير\n      const response = await fetch('/api/incomes?limit=1000')\n      if (!response.ok) throw new Error('فشل في جلب البيانات')\n\n      const data: IncomesResponse = await response.json()\n      const allIncomes = data.incomes\n\n      // حساب الإحصائيات\n      const totalAmount = allIncomes.reduce((sum, income) => sum + income.amount, 0)\n      const totalCount = allIncomes.length\n      const avgAmount = totalCount > 0 ? totalAmount / totalCount : 0\n\n      // حساب الإحصائيات حسب النوع\n      const byType = allIncomes.reduce((acc: Array<{ type: string; amount: number; count: number }>, income) => {\n        const existing = acc.find((item) => item.type === income.type)\n        if (existing) {\n          existing.amount += income.amount\n          existing.count += 1\n        } else {\n          acc.push({\n            type: income.type,\n            amount: income.amount,\n            count: 1\n          })\n        }\n        return acc\n      }, [])\n\n      // إنشاء HTML للتقرير\n      const currentDate = new Date().toLocaleDateString('ar-SA', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      })\n\n      const htmlContent = `\n        <!DOCTYPE html>\n        <html dir=\"rtl\" lang=\"ar\">\n        <head>\n          <meta charset=\"UTF-8\">\n          <style>\n            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');\n\n            * {\n              margin: 0;\n              padding: 0;\n              box-sizing: border-box;\n            }\n\n            body {\n              font-family: 'Noto Sans Arabic', Arial, sans-serif;\n              direction: rtl;\n              background: white;\n              color: #333;\n              line-height: 1.6;\n              padding: 20px;\n            }\n\n            .header {\n              text-align: center;\n              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n              color: white;\n              padding: 30px;\n              border-radius: 15px;\n              margin-bottom: 30px;\n              box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n            }\n\n            .header h1 {\n              font-size: 32px;\n              font-weight: 700;\n              margin-bottom: 10px;\n            }\n\n            .header h2 {\n              font-size: 24px;\n              font-weight: 600;\n              margin-bottom: 15px;\n            }\n\n            .header .date {\n              font-size: 16px;\n              opacity: 0.9;\n            }\n\n            .stats {\n              display: grid;\n              grid-template-columns: repeat(3, 1fr);\n              gap: 20px;\n              margin-bottom: 30px;\n            }\n\n            .stat-card {\n              background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n              color: white;\n              padding: 25px;\n              border-radius: 15px;\n              text-align: center;\n              box-shadow: 0 8px 25px rgba(0,0,0,0.1);\n            }\n\n            .stat-card.green {\n              background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n            }\n\n            .stat-card.blue {\n              background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n            }\n\n            .stat-card h3 {\n              font-size: 16px;\n              margin-bottom: 10px;\n              font-weight: 600;\n            }\n\n            .stat-card .value {\n              font-size: 24px;\n              font-weight: 700;\n            }\n\n            .table-container {\n              background: white;\n              border-radius: 15px;\n              overflow: hidden;\n              box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n              margin-bottom: 30px;\n            }\n\n            .table-header {\n              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n              color: white;\n              padding: 20px;\n              text-align: center;\n            }\n\n            .table-header h3 {\n              font-size: 20px;\n              font-weight: 600;\n            }\n\n            table {\n              width: 100%;\n              border-collapse: collapse;\n              font-size: 14px;\n            }\n\n            th {\n              background: #f8f9fa;\n              padding: 15px 10px;\n              text-align: center;\n              font-weight: 600;\n              color: #495057;\n              border-bottom: 2px solid #dee2e6;\n            }\n\n            td {\n              padding: 12px 10px;\n              text-align: center;\n              border-bottom: 1px solid #dee2e6;\n            }\n\n            tr:nth-child(even) {\n              background-color: #f8f9fa;\n            }\n\n            .amount {\n              font-weight: 700;\n              color: #28a745;\n            }\n\n            .type-badge {\n              padding: 5px 12px;\n              border-radius: 20px;\n              font-size: 12px;\n              font-weight: 600;\n              color: white;\n            }\n\n            .type-subscription { background: #007bff; }\n            .type-donation { background: #28a745; }\n            .type-event { background: #6f42c1; }\n            .type-other { background: #6c757d; }\n\n            .summary {\n              background: white;\n              border-radius: 15px;\n              padding: 25px;\n              box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n              margin-bottom: 30px;\n            }\n\n            .summary h3 {\n              color: #495057;\n              margin-bottom: 20px;\n              font-size: 18px;\n              font-weight: 600;\n              text-align: center;\n            }\n\n            .summary-grid {\n              display: grid;\n              grid-template-columns: repeat(2, 1fr);\n              gap: 15px;\n            }\n\n            .summary-item {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 10px;\n              border-right: 4px solid #007bff;\n            }\n\n            .summary-item .label {\n              font-weight: 600;\n              color: #495057;\n              margin-bottom: 5px;\n            }\n\n            .summary-item .value {\n              font-size: 16px;\n              font-weight: 700;\n              color: #007bff;\n            }\n\n            .footer {\n              text-align: center;\n              margin-top: 40px;\n              padding: 20px;\n              background: #f8f9fa;\n              border-radius: 10px;\n              color: #6c757d;\n            }\n\n            @media print {\n              body { padding: 0; }\n              .header { margin-bottom: 20px; }\n              .stats { margin-bottom: 20px; }\n              .table-container { margin-bottom: 20px; }\n              .summary { margin-bottom: 20px; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>تقرير الإيرادات</h1>\n            <h2>ديوان أبو علوش</h2>\n            <div class=\"date\">تاريخ التقرير: ${currentDate}</div>\n          </div>\n\n          <div class=\"stats\">\n            <div class=\"stat-card\">\n              <h3>إجمالي الإيرادات</h3>\n              <div class=\"value\">${formatCurrency(totalAmount)}</div>\n            </div>\n            <div class=\"stat-card green\">\n              <h3>عدد الإيرادات</h3>\n              <div class=\"value\">${totalCount} إيراد</div>\n            </div>\n            <div class=\"stat-card blue\">\n              <h3>متوسط الإيراد</h3>\n              <div class=\"value\">${formatCurrency(avgAmount)}</div>\n            </div>\n          </div>\n\n          <div class=\"table-container\">\n            <div class=\"table-header\">\n              <h3>تفاصيل الإيرادات</h3>\n            </div>\n            <table>\n              <thead>\n                <tr>\n                  <th>المصدر</th>\n                  <th>المبلغ (د.أ)</th>\n                  <th>النوع</th>\n                  <th>العضو</th>\n                  <th>التاريخ</th>\n                </tr>\n              </thead>\n              <tbody>\n                ${allIncomes.map(income => `\n                  <tr>\n                    <td>${income.source}</td>\n                    <td class=\"amount\">${income.amount.toFixed(2)}</td>\n                    <td>\n                      <span class=\"type-badge type-${income.type.toLowerCase()}\">\n                        ${getIncomeTypeText(income.type)}\n                      </span>\n                    </td>\n                    <td>${income.member?.name || 'غير محدد'}</td>\n                    <td>${formatDate(income.date)}</td>\n                  </tr>\n                `).join('')}\n              </tbody>\n            </table>\n          </div>\n\n          <div class=\"summary\">\n            <h3>ملخص الإيرادات حسب النوع</h3>\n            <div class=\"summary-grid\">\n              ${byType.map((typeStats: { type: string; amount: number; count: number }) => `\n                <div class=\"summary-item\">\n                  <div class=\"label\">${getIncomeTypeText(typeStats.type)}</div>\n                  <div class=\"value\">${formatCurrency(typeStats.amount)} (${typeStats.count} إيراد)</div>\n                </div>\n              `).join('')}\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>ديوان أبو علوش - نظام إدارة العائلة</p>\n            <p>تم إنشاء هذا التقرير في ${currentDate}</p>\n          </div>\n        </body>\n        </html>\n      `\n\n      // إنشاء عنصر مؤقت لعرض HTML\n      const tempDiv = document.createElement('div')\n      tempDiv.innerHTML = htmlContent\n      tempDiv.style.position = 'absolute'\n      tempDiv.style.left = '-9999px'\n      tempDiv.style.top = '0'\n      tempDiv.style.width = '210mm' // عرض A4\n      document.body.appendChild(tempDiv)\n\n      // انتظار تحميل الخطوط\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      // تحويل HTML إلى canvas\n      const canvas = await html2canvas(tempDiv, {\n        scale: 2,\n        useCORS: true,\n        allowTaint: true,\n        backgroundColor: '#ffffff',\n        width: 794, // عرض A4 بالبكسل\n        height: 1123, // ارتفاع A4 بالبكسل\n      })\n\n      // إزالة العنصر المؤقت\n      document.body.removeChild(tempDiv)\n\n      // إنشاء PDF\n      const pdf = new jsPDF('p', 'mm', 'a4')\n      const imgData = canvas.toDataURL('image/png')\n\n      // حساب الأبعاد\n      const imgWidth = 210 // عرض A4\n      const pageHeight = 297 // ارتفاع A4\n      const imgHeight = (canvas.height * imgWidth) / canvas.width\n      let heightLeft = imgHeight\n\n      let position = 0\n\n      // إضافة الصفحة الأولى\n      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)\n      heightLeft -= pageHeight\n\n      // إضافة صفحات إضافية إذا لزم الأمر\n      while (heightLeft >= 0) {\n        position = heightLeft - imgHeight\n        pdf.addPage()\n        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)\n        heightLeft -= pageHeight\n      }\n\n      // حفظ الملف\n      const fileName = `تقرير_الإيرادات_${new Date().toISOString().split('T')[0]}.pdf`\n      pdf.save(fileName)\n\n      // رسالة نجاح\n      setTimeout(() => {\n        alert('تم إنشاء تقرير PDF بنجاح! ✅')\n      }, 500)\n\n    } catch (error) {\n      console.error('خطأ في تصدير PDF:', error)\n      alert('حدث خطأ في تصدير التقرير ❌')\n    }\n  }\n\n  const canEdit = session?.user.role !== 'VIEWER'\n  const canDelete = session?.user.role === 'ADMIN'\n\n  return (\n    <div className=\"space-y-8\">\n      {/* رأس الصفحة المحسن */}\n      <div className=\"text-center mb-8\">\n        <div className=\"bg-gradient-to-r from-green-600 to-emerald-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden\">\n          {/* خلفية متحركة */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 opacity-30 animate-pulse\"></div>\n\n          {/* المحتوى */}\n          <div className=\"relative z-10\">\n            <div className=\"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm\">\n              <TrendingUp className=\"w-10 h-10 text-white\" />\n            </div>\n\n            <h1 className=\"text-5xl font-black mb-4 text-white\">\n              إدارة الإيرادات\n            </h1>\n\n            <p className=\"text-xl font-semibold mb-6 text-green-100\">\n              عرض وإدارة إيرادات الديوان بكفاءة وسهولة\n            </p>\n\n            <div className=\"flex items-center justify-center space-x-2 space-x-reverse\">\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n              <div className=\"h-1 w-8 rounded-full bg-white bg-opacity-40\"></div>\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* أزرار الإجراءات */}\n      <div className=\"flex justify-center mb-8\">\n        <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n            <Button\n              onClick={handleExportIncomes}\n              className=\"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold\"\n            >\n              <FileText className=\"w-5 h-5 ml-2\" />\n              تصدير PDF\n            </Button>\n            {canEdit && (\n              <Button\n                onClick={handleAddNewIncome}\n                className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold\"\n              >\n                <Plus className=\"w-5 h-5 ml-2\" />\n                إضافة إيراد جديد\n              </Button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* إحصائيات سريعة محسنة */}\n      <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>إجمالي الإيرادات</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#28a745' }}>\n              <DollarSign className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {formatCurrency(stats.totalAmount)}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              إجمالي المبالغ\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>عدد الإيرادات</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#007bff' }}>\n              <TrendingUp className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.totalCount}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              إجمالي العمليات\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative sm:col-span-2 lg:col-span-1\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>متوسط الإيراد</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#800020' }}>\n              <Users className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {formatCurrency(stats.totalCount > 0 ? stats.totalAmount / stats.totalCount : 0)}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              متوسط القيمة\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* أدوات البحث والتصفية المحسنة */}\n      <Card className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\">\n            <div className=\"w-2 h-2 bg-diwan-600 rounded-full\"></div>\n            البحث والتصفية\n          </h3>\n          <p className=\"text-gray-600 text-sm\">ابحث وصفي الإيرادات حسب المعايير المختلفة</p>\n        </div>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col lg:flex-row gap-6\">\n            <div className=\"relative flex-1\">\n              <div className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400\">\n                <Search className=\"w-5 h-5\" />\n              </div>\n              <Input\n                placeholder=\"البحث في الإيرادات (المصدر، الوصف، العضو...)\"\n                value={search}\n                onChange={(e) => setSearch(e.target.value)}\n                className=\"h-12 pr-12 text-base border-gray-200 focus:border-diwan-500 focus:ring-diwan-100 transition-all duration-200 rounded-xl\"\n              />\n            </div>\n            <div className=\"relative\">\n              <select\n                value={type}\n                onChange={(e) => setType(e.target.value)}\n                className=\"h-12 px-4 pr-10 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-diwan-500 focus:ring-4 focus:ring-diwan-100 transition-all duration-200 bg-white text-gray-700 font-medium min-w-[200px] appearance-none cursor-pointer\"\n              >\n                <option value=\"all\">🔍 جميع الأنواع</option>\n                <option value=\"SUBSCRIPTION\">💳 اشتراكات</option>\n                <option value=\"DONATION\">💝 تبرعات</option>\n                <option value=\"EVENT\">🎉 فعاليات</option>\n                <option value=\"OTHER\">📋 أخرى</option>\n              </select>\n              <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none\">\n                <Filter className=\"w-4 h-4 text-gray-400\" />\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* جدول الإيرادات المحسن */}\n      <Card className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\">\n            <div className=\"w-2 h-2 bg-green-600 rounded-full\"></div>\n            قائمة الإيرادات\n          </h3>\n          <p className=\"text-gray-600 text-sm\">جميع إيرادات الديوان مع تفاصيلها الكاملة</p>\n        </div>\n        <CardContent className=\"p-0\">\n          {loading ? (\n            <div className=\"flex flex-col justify-center items-center h-64 bg-gray-50\">\n              <div className=\"w-12 h-12 border-4 border-diwan-600 border-t-transparent rounded-full animate-spin mb-4\"></div>\n              <div className=\"text-gray-600 font-medium\">جاري تحميل الإيرادات...</div>\n              <div className=\"text-gray-400 text-sm mt-1\">يرجى الانتظار</div>\n            </div>\n          ) : incomes.length === 0 ? (\n            <div className=\"flex flex-col justify-center items-center h-64 bg-gray-50\">\n              <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4\">\n                <TrendingUp className=\"w-8 h-8 text-gray-400\" />\n              </div>\n              <div className=\"text-gray-600 font-medium mb-2\">لا توجد إيرادات</div>\n              <div className=\"text-gray-400 text-sm\">ابدأ بإضافة إيراد جديد</div>\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <Table>\n                <TableHeader>\n                  <TableRow className=\"bg-gray-50 hover:bg-gray-50\">\n                    <TableHead className=\"font-semibold text-gray-700 py-4\">المصدر</TableHead>\n                    <TableHead className=\"font-semibold text-gray-700 py-4\">المبلغ</TableHead>\n                    <TableHead className=\"font-semibold text-gray-700 py-4\">النوع</TableHead>\n                    <TableHead className=\"font-semibold text-gray-700 py-4\">العضو</TableHead>\n                    <TableHead className=\"font-semibold text-gray-700 py-4\">التاريخ</TableHead>\n                    <TableHead className=\"font-semibold text-gray-700 py-4 text-center\">الإجراءات</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {incomes.map((income) => (\n                    <TableRow key={income.id} className=\"hover:bg-blue-50/50 transition-colors duration-200 border-b border-gray-100\">\n                      <TableCell className=\"py-4\">\n                        <div className=\"flex items-start gap-3\">\n                          <div className=\"w-10 h-10 bg-gradient-to-br from-diwan-100 to-blue-100 rounded-xl flex items-center justify-center flex-shrink-0\">\n                            <DollarSign className=\"w-5 h-5 text-diwan-600\" />\n                          </div>\n                          <div>\n                            <div className=\"font-semibold text-gray-900 mb-1\">{income.source}</div>\n                            {income.description && (\n                              <div className=\"text-sm text-gray-500 leading-relaxed\">{income.description}</div>\n                            )}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"py-4\">\n                        <div className=\"flex items-center gap-2\">\n                          <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                          <div className=\"font-bold text-lg text-green-600\">\n                            {formatCurrency(income.amount)}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"py-4\">\n                        <Badge\n                          variant=\"secondary\"\n                          className={`px-3 py-1 rounded-full font-medium ${\n                            income.type === 'SUBSCRIPTION' ? 'bg-blue-100 text-blue-700 border-blue-200' :\n                            income.type === 'DONATION' ? 'bg-green-100 text-green-700 border-green-200' :\n                            income.type === 'EVENT' ? 'bg-purple-100 text-purple-700 border-purple-200' :\n                            'bg-gray-100 text-gray-700 border-gray-200'\n                          }`}\n                        >\n                          {getIncomeTypeText(income.type)}\n                        </Badge>\n                      </TableCell>\n                      <TableCell className=\"py-4\">\n                        <div className=\"flex items-center gap-2\">\n                          <div className=\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center\">\n                            <Users className=\"w-4 h-4 text-gray-600\" />\n                          </div>\n                          <span className=\"font-medium text-gray-700\">\n                            {income.member?.name || 'غير محدد'}\n                          </span>\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"py-4\">\n                        <div className=\"flex items-center gap-2 text-gray-600\">\n                          <Calendar className=\"w-4 h-4\" />\n                          <span className=\"font-medium\">{formatDate(income.date)}</span>\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"py-4\">\n                        <div className=\"flex items-center justify-center gap-2\">\n                          {canEdit && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => handleEditIncome(income)}\n                              className=\"h-9 w-9 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200\"\n                              title=\"تعديل\"\n                            >\n                              <Edit className=\"w-4 h-4\" />\n                            </Button>\n                          )}\n                          {canDelete && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => handleDeleteIncome(income.id)}\n                              className=\"h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200\"\n                              title=\"حذف\"\n                            >\n                              <Trash2 className=\"w-4 h-4\" />\n                            </Button>\n                          )}\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* التصفح المحسن */}\n      {pagination.pages > 1 && (\n        <Card className=\"bg-white rounded-2xl shadow-lg border border-gray-100\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4\">\n              <div className=\"flex items-center gap-2 text-gray-600\">\n                <span className=\"text-sm font-medium\">\n                  عرض {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} من {pagination.total} إيراد\n                </span>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <Button\n                  variant=\"outline\"\n                  disabled={pagination.page === 1}\n                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}\n                  className=\"h-10 px-4 border-2 border-gray-200 hover:border-diwan-500 hover:text-diwan-600 transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <span className=\"font-medium\">السابق</span>\n                </Button>\n                <div className=\"flex items-center gap-2\">\n                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {\n                    const pageNum = i + 1;\n                    return (\n                      <button\n                        key={pageNum}\n                        onClick={() => setPagination(prev => ({ ...prev, page: pageNum }))}\n                        className={`w-10 h-10 rounded-lg font-semibold transition-all duration-200 ${\n                          pagination.page === pageNum\n                            ? 'bg-diwan-600 text-white shadow-lg'\n                            : 'bg-gray-100 text-gray-600 hover:bg-diwan-100 hover:text-diwan-600'\n                        }`}\n                      >\n                        {pageNum}\n                      </button>\n                    );\n                  })}\n                </div>\n                <Button\n                  variant=\"outline\"\n                  disabled={pagination.page === pagination.pages}\n                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n                  className=\"h-10 px-4 border-2 border-gray-200 hover:border-diwan-500 hover:text-diwan-600 transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <span className=\"font-medium\">التالي</span>\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* إحصائيات حسب النوع المحسنة */}\n      {stats.byType.length > 0 && (\n        <Card className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-gray-50 to-purple-50 p-6 border-b border-gray-100\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-purple-600 rounded-full\"></div>\n              الإيرادات حسب النوع\n            </h3>\n            <p className=\"text-gray-600 text-sm\">توزيع الإيرادات على الأنواع المختلفة</p>\n          </div>\n          <CardContent className=\"p-6\">\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n              {stats.byType.map((typeStats, index) => {\n                const colors = [\n                  { bg: 'from-blue-50 to-indigo-50', border: 'border-blue-200', text: 'text-blue-700', icon: 'bg-blue-100', dot: 'bg-blue-500' },\n                  { bg: 'from-green-50 to-emerald-50', border: 'border-green-200', text: 'text-green-700', icon: 'bg-green-100', dot: 'bg-green-500' },\n                  { bg: 'from-purple-50 to-pink-50', border: 'border-purple-200', text: 'text-purple-700', icon: 'bg-purple-100', dot: 'bg-purple-500' },\n                  { bg: 'from-orange-50 to-red-50', border: 'border-orange-200', text: 'text-orange-700', icon: 'bg-orange-100', dot: 'bg-orange-500' }\n                ];\n                const color = colors[index % colors.length];\n\n                return (\n                  <div\n                    key={typeStats.type}\n                    className={`relative overflow-hidden bg-gradient-to-br ${color.bg} ${color.border} border-2 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]`}\n                  >\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent\"></div>\n                    <div className=\"relative\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <div className={`p-3 ${color.icon} rounded-xl shadow-sm`}>\n                          {typeStats.type === 'SUBSCRIPTION' && <Users className=\"w-5 h-5 text-blue-600\" />}\n                          {typeStats.type === 'DONATION' && <DollarSign className=\"w-5 h-5 text-green-600\" />}\n                          {typeStats.type === 'EVENT' && <Calendar className=\"w-5 h-5 text-purple-600\" />}\n                          {typeStats.type === 'OTHER' && <TrendingUp className=\"w-5 h-5 text-orange-600\" />}\n                        </div>\n                        <div className={`w-3 h-3 ${color.dot} rounded-full animate-pulse`}></div>\n                      </div>\n                      <div className=\"text-sm font-semibold text-gray-600 mb-2\">\n                        {getIncomeTypeText(typeStats.type)}\n                      </div>\n                      <div className={`text-2xl font-bold ${color.text} mb-2`}>\n                        {formatCurrency(typeStats._sum.amount || 0)}\n                      </div>\n                      <div className=\"flex items-center gap-2\">\n                        <div className={`w-2 h-2 ${color.dot} rounded-full`}></div>\n                        <span className=\"text-sm text-gray-500 font-medium\">\n                          {typeStats._count} إيراد\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* حوار إضافة/تعديل إيراد */}\n      <IncomeDialog\n        open={isIncomeDialogOpen}\n        onOpenChange={handleCloseDialog}\n        income={editingIncome}\n        onSuccess={() => {\n          fetchIncomes()\n          fetchStats()\n        }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;;;AA/BA;;;;;;;;;;;;;AA2De,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,aAAa;QACb,YAAY;QACZ,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC/B,IAAI;gBACF,WAAW;gBACX,MAAM,SAAS,IAAI,gBAAgB;oBACjC;oBACA;oBACA,MAAM,WAAW,IAAI,CAAC,QAAQ;oBAC9B,OAAO,WAAW,KAAK,CAAC,QAAQ;gBAClC;gBAEA,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,QAAQ;gBACrD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;gBAElC,MAAM,OAAwB,MAAM,SAAS,IAAI;gBACjD,WAAW,KAAK,OAAO;gBACvB,cAAc,KAAK,UAAU;YAC/B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC,SAAU;gBACR,WAAW;YACb;QACF;gDAAG;QAAC;QAAQ;QAAM,WAAW,IAAI;QAAE,WAAW,KAAK;KAAC;IAEpD,iBAAiB;IACjB,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAElB,MAAM,OAAwB,MAAM,SAAS,IAAI;YACjD,MAAM,cAAc,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;YAC9E,MAAM,aAAa,KAAK,OAAO,CAAC,MAAM;YAEtC,4BAA4B;YAC5B,MAAM,SAAS,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAwE;gBAC1G,MAAM,WAAW,IAAI,IAAI,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK,OAAO,IAAI;gBAC7D,IAAI,UAAU;oBACZ,SAAS,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM;oBACrC,SAAS,MAAM,IAAI;gBACrB,OAAO;oBACL,IAAI,IAAI,CAAC;wBACP,MAAM,OAAO,IAAI;wBACjB,MAAM;4BAAE,QAAQ,OAAO,MAAM;wBAAC;wBAC9B,QAAQ;oBACV;gBACF;gBACA,OAAO;YACT,GAAG,EAAE;YAEL,SAAS;gBAAE;gBAAa;gBAAY;YAAO;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,cAAc;IACd,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;QACjB,sBAAsB;IACxB;IAEA,YAAY;IACZ,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,yEAAyE;QAEtF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE;gBACjD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,KAAK,IAAI;gBACrB;YACF;YAEA,MAAM;YACN;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,qBAAqB;QACzB,iBAAiB;QACjB,sBAAsB;IACxB;IAEA,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,sBAAsB;QACtB,iBAAiB;IACnB;IAEA,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,IAAI;YACF,6BAA6B;YAC7B,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAwB,MAAM,SAAS,IAAI;YACjD,MAAM,aAAa,KAAK,OAAO;YAE/B,kBAAkB;YAClB,MAAM,cAAc,WAAW,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;YAC5E,MAAM,aAAa,WAAW,MAAM;YACpC,MAAM,YAAY,aAAa,IAAI,cAAc,aAAa;YAE9D,4BAA4B;YAC5B,MAAM,SAAS,WAAW,MAAM,CAAC,CAAC,KAA6D;gBAC7F,MAAM,WAAW,IAAI,IAAI,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK,OAAO,IAAI;gBAC7D,IAAI,UAAU;oBACZ,SAAS,MAAM,IAAI,OAAO,MAAM;oBAChC,SAAS,KAAK,IAAI;gBACpB,OAAO;oBACL,IAAI,IAAI,CAAC;wBACP,MAAM,OAAO,IAAI;wBACjB,QAAQ,OAAO,MAAM;wBACrB,OAAO;oBACT;gBACF;gBACA,OAAO;YACT,GAAG,EAAE;YAEL,qBAAqB;YACrB,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBACzD,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;YAEA,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAmNkB,EAAE,YAAY;;;;;;iCAM1B,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;;;;iCAI9B,EAAE,WAAW;;;;iCAIb,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;;;;;;;;;;;;;;;;;;;gBAmB7C,EAAE,WAAW,GAAG,CAAC,CAAA,SAAU,CAAC;;wBAEpB,EAAE,OAAO,MAAM,CAAC;uCACD,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG;;mDAEf,EAAE,OAAO,IAAI,CAAC,WAAW,GAAG;wBACvD,EAAE,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,IAAI,EAAE;;;wBAGjC,EAAE,OAAO,MAAM,EAAE,QAAQ,WAAW;wBACpC,EAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI,EAAE;;gBAElC,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;cAQd,EAAE,OAAO,GAAG,CAAC,CAAC,YAA+D,CAAC;;qCAEvD,EAAE,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,IAAI,EAAE;qCACpC,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,MAAM,EAAE,EAAE,EAAE,UAAU,KAAK,CAAC;;cAE9E,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;uCAMa,EAAE,YAAY;;;;MAI/C,CAAC;YAED,4BAA4B;YAC5B,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,SAAS,GAAG;YACpB,QAAQ,KAAK,CAAC,QAAQ,GAAG;YACzB,QAAQ,KAAK,CAAC,IAAI,GAAG;YACrB,QAAQ,KAAK,CAAC,GAAG,GAAG;YACpB,QAAQ,KAAK,CAAC,KAAK,GAAG,QAAQ,SAAS;;YACvC,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,wBAAwB;YACxB,MAAM,SAAS,MAAM,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,SAAS;gBACxC,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,iBAAiB;gBACjB,OAAO;gBACP,QAAQ;YACV;YAEA,sBAAsB;YACtB,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,YAAY;YACZ,MAAM,MAAM,IAAI,sJAAA,CAAA,UAAK,CAAC,KAAK,MAAM;YACjC,MAAM,UAAU,OAAO,SAAS,CAAC;YAEjC,eAAe;YACf,MAAM,WAAW,IAAI,SAAS;;YAC9B,MAAM,aAAa,IAAI,YAAY;;YACnC,MAAM,YAAY,AAAC,OAAO,MAAM,GAAG,WAAY,OAAO,KAAK;YAC3D,IAAI,aAAa;YAEjB,IAAI,WAAW;YAEf,sBAAsB;YACtB,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;YACpD,cAAc;YAEd,mCAAmC;YACnC,MAAO,cAAc,EAAG;gBACtB,WAAW,aAAa;gBACxB,IAAI,OAAO;gBACX,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;gBACpD,cAAc;YAChB;YAEA,YAAY;YACZ,MAAM,WAAW,CAAC,gBAAgB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAChF,IAAI,IAAI,CAAC;YAET,aAAa;YACb,WAAW;gBACT,MAAM;YACR,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR;IACF;IAEA,MAAM,UAAU,SAAS,KAAK,SAAS;IACvC,MAAM,YAAY,SAAS,KAAK,SAAS;IAEzC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAGxB,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAIpD,6LAAC;oCAAE,WAAU;8CAA4C;;;;;;8CAIzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAGtC,yBACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,6LAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI1B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;;;;;;kDAEnC,6LAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAMtE,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,6LAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI1B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,MAAM,UAAU;;;;;;kDAEnB,6LAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAMtE,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,6LAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIrB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,UAAU,GAAG,IAAI,MAAM,WAAW,GAAG,MAAM,UAAU,GAAG;;;;;;kDAEhF,6LAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAQxE,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAI,WAAU;;;;;;oCAA0C;;;;;;;0CAG3D,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACvC,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;sDAExB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAI,WAAU;;;;;;oCAA0C;;;;;;;0CAG3D,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,wBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;8CAA4B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;;;;;;mCAE5C,QAAQ,MAAM,KAAK,kBACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;8CAAiC;;;;;;8CAChD,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;iDAGzC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAmC;;;;;;8DACxD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAmC;;;;;;8DACxD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAmC;;;;;;8DACxD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAmC;;;;;;8DACxD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAmC;;;;;;8DACxD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA+C;;;;;;;;;;;;;;;;;kDAGxE,6LAAC,oIAAA,CAAA,YAAS;kDACP,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,WAAQ;gDAAiB,WAAU;;kEAClC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;;;;;;8EAExB,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFAAoC,OAAO,MAAM;;;;;;wEAC/D,OAAO,WAAW,kBACjB,6LAAC;4EAAI,WAAU;sFAAyC,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;kEAKlF,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;8EACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM;;;;;;;;;;;;;;;;;kEAInC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAW,CAAC,mCAAmC,EAC7C,OAAO,IAAI,KAAK,iBAAiB,8CACjC,OAAO,IAAI,KAAK,aAAa,iDAC7B,OAAO,IAAI,KAAK,UAAU,oDAC1B,6CACA;sEAED,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,IAAI;;;;;;;;;;;kEAGlC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,6LAAC;oEAAK,WAAU;8EACb,OAAO,MAAM,EAAE,QAAQ;;;;;;;;;;;;;;;;;kEAI9B,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAe,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI;;;;;;;;;;;;;;;;;kEAGzD,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,yBACC,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,iBAAiB;oEAChC,WAAU;oEACV,OAAM;8EAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;gEAGnB,2BACC,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,mBAAmB,OAAO,EAAE;oEAC3C,WAAU;oEACV,OAAM;8EAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAxEb,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAuFrC,WAAW,KAAK,GAAG,mBAClB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAsB;wCAC9B,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAI;wCAAE;wCAAI,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;wCAAE;wCAAK,WAAW,KAAK;wCAAC;;;;;;;;;;;;0CAGjJ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,UAAU,WAAW,IAAI,KAAK;wCAC9B,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,KAAK,IAAI,GAAG;gDAAE,CAAC;wCACtE,WAAU;kDAEV,cAAA,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI,CAAC;4CAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,WAAW,KAAK;wCAAE,GAAG,CAAC,GAAG;4CACzD,MAAM,UAAU,IAAI;4CACpB,qBACE,6LAAC;gDAEC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM;wDAAQ,CAAC;gDAChE,WAAW,CAAC,+DAA+D,EACzE,WAAW,IAAI,KAAK,UAChB,sCACA,qEACJ;0DAED;+CARI;;;;;wCAWX;;;;;;kDAEF,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,UAAU,WAAW,IAAI,KAAK,WAAW,KAAK;wCAC9C,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,KAAK,IAAI,GAAG;gDAAE,CAAC;wCACtE,WAAU;kDAEV,cAAA,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASzC,MAAM,MAAM,CAAC,MAAM,GAAG,mBACrB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAI,WAAU;;;;;;oCAA2C;;;;;;;0CAG5D,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW;gCAC5B,MAAM,SAAS;oCACb;wCAAE,IAAI;wCAA6B,QAAQ;wCAAmB,MAAM;wCAAiB,MAAM;wCAAe,KAAK;oCAAc;oCAC7H;wCAAE,IAAI;wCAA+B,QAAQ;wCAAoB,MAAM;wCAAkB,MAAM;wCAAgB,KAAK;oCAAe;oCACnI;wCAAE,IAAI;wCAA6B,QAAQ;wCAAqB,MAAM;wCAAmB,MAAM;wCAAiB,KAAK;oCAAgB;oCACrI;wCAAE,IAAI;wCAA4B,QAAQ;wCAAqB,MAAM;wCAAmB,MAAM;wCAAiB,KAAK;oCAAgB;iCACrI;gCACD,MAAM,QAAQ,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;gCAE3C,qBACE,6LAAC;oCAEC,WAAW,CAAC,2CAA2C,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,kGAAkG,CAAC;;sDAErL,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC;;gEACrD,UAAU,IAAI,KAAK,gCAAkB,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEACtD,UAAU,IAAI,KAAK,4BAAc,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEACvD,UAAU,IAAI,KAAK,yBAAW,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAClD,UAAU,IAAI,KAAK,yBAAW,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;sEAEvD,6LAAC;4DAAI,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,2BAA2B,CAAC;;;;;;;;;;;;8DAEnE,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,IAAI;;;;;;8DAEnC,6LAAC;oDAAI,WAAW,CAAC,mBAAmB,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC;8DACpD,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,IAAI,CAAC,MAAM,IAAI;;;;;;8DAE3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,aAAa,CAAC;;;;;;sEACnD,6LAAC;4DAAK,WAAU;;gEACb,UAAU,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;mCAvBnB,UAAU,IAAI;;;;;4BA6BzB;;;;;;;;;;;;;;;;;0BAOR,6LAAC,oJAAA,CAAA,UAAY;gBACX,MAAM;gBACN,cAAc;gBACd,QAAQ;gBACR,WAAW;oBACT;oBACA;gBACF;;;;;;;;;;;;AAIR;GA55BwB;;QACI,iJAAA,CAAA,aAAU;;;KADd", "debugId": null}}]}