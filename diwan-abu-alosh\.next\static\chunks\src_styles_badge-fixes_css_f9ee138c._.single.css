/* [project]/src/styles/badge-fixes.css [app-client] (css) */
.badge, [class*="badge"], [data-badge], .tag, [class*="tag"], [data-tag] {
  color: #fff !important;
}

.badge-default, .badge-primary {
  color: #fff !important;
  background-color: #3b82f6 !important;
  border-color: #2563eb !important;
}

.badge-secondary {
  color: #fff !important;
  background-color: #6b7280 !important;
  border-color: #4b5563 !important;
}

.badge-success {
  color: #fff !important;
  background-color: #10b981 !important;
  border-color: #059669 !important;
}

.badge-warning {
  color: #fff !important;
  background-color: #f59e0b !important;
  border-color: #d97706 !important;
}

.badge-error, .badge-danger, .badge-destructive {
  color: #fff !important;
  background-color: #ef4444 !important;
  border-color: #dc2626 !important;
}

.badge-info {
  color: #fff !important;
  background-color: #3b82f6 !important;
  border-color: #2563eb !important;
}

.badge-outline {
  color: #fff !important;
  background-color: #0000 !important;
  border-width: 1px !important;
  border-color: #fff !important;
}

.status-active, .badge-active {
  color: #fff !important;
  background-color: #10b981 !important;
  border-color: #059669 !important;
}

.status-late, .badge-late {
  color: #fff !important;
  background-color: #f59e0b !important;
  border-color: #d97706 !important;
}

.status-inactive, .badge-inactive {
  color: #fff !important;
  background-color: #ef4444 !important;
  border-color: #dc2626 !important;
}

.status-suspended, .badge-suspended {
  color: #fff !important;
  background-color: #8b5cf6 !important;
  border-color: #7c3aed !important;
}

.status-archived, .badge-archived {
  color: #fff !important;
  background-color: #6b7280 !important;
  border-color: #4b5563 !important;
}

.category-general, .badge-general {
  color: #fff !important;
  background-color: #3b82f6 !important;
  border-color: #2563eb !important;
}

.category-maintenance, .badge-maintenance {
  color: #fff !important;
  background-color: #f59e0b !important;
  border-color: #d97706 !important;
}

.category-events, .badge-events {
  color: #fff !important;
  background-color: #8b5cf6 !important;
  border-color: #7c3aed !important;
}

.category-utilities, .badge-utilities {
  color: #fff !important;
  background-color: #06b6d4 !important;
  border-color: #0891b2 !important;
}

.type-monthly, .badge-monthly {
  color: #fff !important;
  background-color: #10b981 !important;
  border-color: #059669 !important;
}

.type-donation, .badge-donation {
  color: #fff !important;
  background-color: #f59e0b !important;
  border-color: #d97706 !important;
}

.type-special, .badge-special {
  color: #fff !important;
  background-color: #8b5cf6 !important;
  border-color: #7c3aed !important;
}

table .badge, table [class*="badge"], .table .badge, .table [class*="badge"], td .badge, td [class*="badge"], .card .badge, .card [class*="badge"], [class*="card"] .badge, [class*="card"] [class*="badge"], .dialog .badge, .dialog [class*="badge"], [role="dialog"] .badge, [role="dialog"] [class*="badge"], form .badge, form [class*="badge"], .form-group .badge, .form-group [class*="badge"] {
  color: #fff !important;
}

.bg-green-100, .bg-green-200 {
  color: #fff !important;
  background-color: #10b981 !important;
}

.bg-amber-100, .bg-amber-200, .bg-yellow-100, .bg-yellow-200 {
  color: #fff !important;
  background-color: #f59e0b !important;
}

.bg-red-100, .bg-red-200 {
  color: #fff !important;
  background-color: #ef4444 !important;
}

.bg-blue-100, .bg-blue-200 {
  color: #fff !important;
  background-color: #3b82f6 !important;
}

.bg-purple-100, .bg-purple-200, .bg-violet-100, .bg-violet-200 {
  color: #fff !important;
  background-color: #8b5cf6 !important;
}

.bg-gray-100, .bg-gray-200, .bg-slate-100, .bg-slate-200 {
  color: #fff !important;
  background-color: #6b7280 !important;
}

.bg-orange-100, .bg-orange-200 {
  color: #fff !important;
  background-color: #f97316 !important;
}

.bg-cyan-100, .bg-cyan-200 {
  color: #fff !important;
  background-color: #06b6d4 !important;
}

.bg-pink-100, .bg-pink-200 {
  color: #fff !important;
  background-color: #ec4899 !important;
}

.bg-indigo-100, .bg-indigo-200 {
  color: #fff !important;
  background-color: #6366f1 !important;
}

.text-green-800, .text-green-700, .text-green-600, .text-amber-800, .text-amber-700, .text-amber-600, .text-yellow-800, .text-yellow-700, .text-yellow-600, .text-red-800, .text-red-700, .text-red-600, .text-blue-800, .text-blue-700, .text-blue-600, .text-purple-800, .text-purple-700, .text-purple-600, .text-violet-800, .text-violet-700, .text-violet-600, .text-gray-800, .text-gray-700, .text-gray-600, .text-slate-800, .text-slate-700, .text-slate-600, .members-page .badge, .incomes-page .badge, .expenses-page .badge, .gallery-page .badge, .reports-page .badge, .settings-page .badge {
  color: #fff !important;
}

.badge:hover, [class*="badge"]:hover {
  color: #fff !important;
  opacity: .9 !important;
}

.badge:focus, [class*="badge"]:focus {
  color: #fff !important;
  outline: 2px solid #ffffff80 !important;
}

.badge:active, [class*="badge"]:active {
  color: #fff !important;
  opacity: .8 !important;
}

.badge:disabled, .badge[disabled], [class*="badge"]:disabled, [class*="badge"][disabled] {
  color: #fff !important;
  opacity: .6 !important;
  background-color: #9ca3af !important;
}

.badge-sm, .badge-small, .badge-lg, .badge-large, .badge .icon, .badge svg, [class*="badge"] .icon, [class*="badge"] svg {
  color: #fff !important;
}

@media (prefers-color-scheme: dark) {
  .badge, [class*="badge"], .tag, [class*="tag"] {
    color: #fff !important;
  }
}

.badge-primary, .badge-secondary, .badge-success, .badge-danger, .badge-warning, .badge-info, .badge-light, .badge-dark, .MuiBadge-badge, .MuiChip-root, .custom-badge, .status-badge, .category-badge, .type-badge, .member-status-badge, .income-type-badge, .expense-category-badge, .activity-badge {
  color: #fff !important;
}

.badge, [class*="badge"] {
  transition: all .2s ease-in-out !important;
}

.badge:hover, [class*="badge"]:hover {
  box-shadow: 0 2px 4px #0003 !important;
}

/*# sourceMappingURL=src_styles_badge-fixes_css_f9ee138c._.single.css.map*/