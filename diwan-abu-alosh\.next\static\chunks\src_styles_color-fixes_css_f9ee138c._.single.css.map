{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/color-fixes.css"], "sourcesContent": ["/* إصلاحات الألوان الطارئة لديوان أبو علوش */\n\n/* إصلاح رؤوس الجداول - تطبيق قوي جداً */\nth,\nthead th,\ntable th,\n.table th,\n[role=\"columnheader\"],\n[data-testid*=\"table\"] th,\n[class*=\"TableHead\"],\n[class*=\"table-head\"] {\n  background-color: #191970 !important;\n  color: white !important;\n  font-weight: 600 !important;\n  border: none !important;\n}\n\n/* إصلاح خاص لمكونات Shadcn UI */\n[data-radix-collection-item],\n[data-state=\"open\"] th,\n[data-state=\"closed\"] th {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n/* إصلاح الأزرار الأساسية */\nbutton[style*=\"007bff\"],\n.btn-primary,\n.bg-primary {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\nbutton[style*=\"28a745\"],\n.btn-success,\n.bg-success {\n  background-color: #28a745 !important;\n  border-color: #28a745 !important;\n  color: white !important;\n}\n\nbutton[style*=\"dc3545\"],\n.btn-danger,\n.bg-danger {\n  background-color: #dc3545 !important;\n  border-color: #dc3545 !important;\n  color: white !important;\n}\n\n/* إصلاح ألوان النصوص - تحسين التباين */\n.text-blue-600,\n.text-blue-500 {\n  color: #0056cc !important; /* أزرق أغمق للوضوح */\n}\n\n.text-green-600,\n.text-green-500 {\n  color: #1e7e34 !important; /* أخضر أغمق للوضوح */\n}\n\n.text-red-600,\n.text-red-500 {\n  color: #c82333 !important; /* أحمر أغمق للوضوح */\n}\n\n/* تحسين وضوح النصوص العامة */\np, span, div, label, td, li {\n  color: #333333 !important;\n  font-weight: 500 !important; /* وزن خط أثقل للوضوح */\n}\n\n/* تحسين النصوص على الخلفيات الفاتحة */\n.bg-white p,\n.bg-white span,\n.bg-white div,\n.bg-white label {\n  color: #212529 !important; /* لون أغمق للوضوح */\n}\n\n/* إصلاح خلفيات البطاقات */\n.bg-gradient-to-br {\n  background: white !important;\n}\n\n/* تحسين النصوص في البطاقات */\n.card h1,\n.card h2,\n.card h3,\n.card h4,\n.card h5,\n.card h6 {\n  color: #191970 !important;\n  font-weight: 700 !important;\n}\n\n.card p,\n.card span,\n.card div {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تحسين عناوين البطاقات */\n.card-title,\n[data-testid=\"card-title\"],\nh3[class*=\"text-sm\"] {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n/* تحسين النصوص في بطاقات الإحصائيات */\n.stats-card-value,\ndiv[class*=\"text-2xl\"],\n.text-2xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n.stats-card-title,\n.stats-card-description {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n/* تطبيق قوي للنصوص في البطاقات */\n[role=\"region\"] h3,\n[role=\"region\"] div[class*=\"text-2xl\"],\n.card-content div,\n.card-header h3 {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n/* تحسين خاص لبطاقات الأعضاء */\n.members-stats .card-title,\n.members-stats h3 {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n.members-stats .text-2xl,\n.members-stats div[class*=\"font-bold\"] {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* إصلاح الأيقونات الملونة */\n.text-blue-600 svg,\n.text-green-600 svg,\n.text-red-600 svg {\n  color: inherit !important;\n}\n\n/* إصلاح أزرار الإجراءات في الجداول - تحسين الوضوح */\n.table-actions button {\n  padding: 0.5rem !important;\n  border-radius: 0.5rem !important;\n  transition: all 0.2s ease !important;\n  border: 1px solid transparent !important;\n}\n\n/* أزرار الإجراءات بألوان محسنة للوضوح */\nbutton[style*=\"color: rgb(0, 123, 255)\"],\n.table-actions .text-blue-600 {\n  color: #0056cc !important; /* أزرق أغمق */\n  background-color: rgba(0, 86, 204, 0.1) !important;\n  border-color: rgba(0, 86, 204, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(0, 123, 255)\"]:hover,\n.table-actions .text-blue-600:hover {\n  background-color: rgba(0, 86, 204, 0.2) !important;\n  color: #003d82 !important;\n}\n\nbutton[style*=\"color: rgb(40, 167, 69)\"],\n.table-actions .text-green-600 {\n  color: #1e7e34 !important; /* أخضر أغمق */\n  background-color: rgba(30, 126, 52, 0.1) !important;\n  border-color: rgba(30, 126, 52, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(40, 167, 69)\"]:hover,\n.table-actions .text-green-600:hover {\n  background-color: rgba(30, 126, 52, 0.2) !important;\n  color: #155724 !important;\n}\n\nbutton[style*=\"color: rgb(220, 53, 69)\"],\n.table-actions .text-red-600 {\n  color: #c82333 !important; /* أحمر أغمق */\n  background-color: rgba(200, 35, 51, 0.1) !important;\n  border-color: rgba(200, 35, 51, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(220, 53, 69)\"]:hover,\n.table-actions .text-red-600:hover {\n  background-color: rgba(200, 35, 51, 0.2) !important;\n  color: #a71e2a !important;\n}\n\nbutton[style*=\"color: rgb(128, 0, 32)\"],\n.table-actions .text-purple-600 {\n  color: #800020 !important;\n  background-color: rgba(128, 0, 32, 0.1) !important;\n  border-color: rgba(128, 0, 32, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(128, 0, 32)\"]:hover,\n.table-actions .text-purple-600:hover {\n  background-color: rgba(128, 0, 32, 0.2) !important;\n  color: #660019 !important;\n}\n\nbutton[style*=\"color: rgb(255, 193, 7)\"],\n.table-actions .text-orange-600 {\n  color: #b8860b !important; /* أصفر أغمق للوضوح */\n  background-color: rgba(184, 134, 11, 0.1) !important;\n  border-color: rgba(184, 134, 11, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(255, 193, 7)\"]:hover,\n.table-actions .text-orange-600:hover {\n  background-color: rgba(184, 134, 11, 0.2) !important;\n  color: #9a7209 !important;\n}\n\n/* إصلاح شامل لجميع عناصر الجدول */\ntable,\n.table {\n  background-color: white !important;\n}\n\ntable tbody tr:hover,\n.table tbody tr:hover {\n  background-color: #f8f9fa !important;\n}\n\ntable td,\n.table td {\n  color: #212529 !important; /* لون أغمق للوضوح */\n  border-bottom: 1px solid #f3f4f6 !important;\n  font-weight: 500 !important; /* وزن خط أثقل */\n}\n\n/* تحسين النصوص في خلايا الجدول */\ntable td span,\ntable td div,\n.table td span,\n.table td div {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تحسين الشارات في الجداول */\n.badge {\n  font-weight: 600 !important;\n  font-size: 0.75rem !important;\n  padding: 0.375rem 0.75rem !important;\n}\n\n/* إصلاح أزرار التصفح */\n.pagination button {\n  background-color: white !important;\n  border: 1px solid #dee2e6 !important;\n  color: #333333 !important;\n}\n\n.pagination button:hover {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\n.pagination button:disabled {\n  background-color: #f8f9fa !important;\n  border-color: #dee2e6 !important;\n  color: #6c757d !important;\n}\n\n/* إصلاح أزرار البحث والتصفية */\n.search-container button {\n  background-color: #007bff !important;\n  color: white !important;\n  border: none !important;\n}\n\n.search-container button:hover {\n  background-color: #0056cc !important;\n}\n\n/* إصلاح النماذج */\ninput,\nselect,\ntextarea {\n  background-color: white !important;\n  color: #333333 !important;\n  border: 1px solid #dee2e6 !important;\n}\n\ninput:focus,\nselect:focus,\ntextarea:focus {\n  border-color: #007bff !important;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;\n}\n\n/* إصلاح الشارات */\n.badge,\n.badge-primary {\n  background-color: rgba(0, 123, 255, 0.1) !important;\n  color: #007bff !important;\n  border: 1px solid rgba(0, 123, 255, 0.2) !important;\n}\n\n.badge-success {\n  background-color: rgba(40, 167, 69, 0.1) !important;\n  color: #28a745 !important;\n  border: 1px solid rgba(40, 167, 69, 0.2) !important;\n}\n\n.badge-danger {\n  background-color: rgba(220, 53, 69, 0.1) !important;\n  color: #dc3545 !important;\n  border: 1px solid rgba(220, 53, 69, 0.2) !important;\n}\n\n.badge-warning {\n  background-color: rgba(25, 25, 112, 0.1) !important;\n  color: #191970 !important;\n  border: 1px solid rgba(25, 25, 112, 0.2) !important;\n}\n\n/* إصلاح التنبيهات */\n.alert-primary {\n  background-color: rgba(0, 123, 255, 0.1) !important;\n  border-color: #007bff !important;\n  color: #004085 !important;\n}\n\n.alert-success {\n  background-color: rgba(40, 167, 69, 0.1) !important;\n  border-color: #28a745 !important;\n  color: #155724 !important;\n}\n\n.alert-danger {\n  background-color: rgba(220, 53, 69, 0.1) !important;\n  border-color: #dc3545 !important;\n  color: #721c24 !important;\n}\n\n/* إصلاح الروابط */\na {\n  color: #007bff !important;\n}\n\na:hover {\n  color: #0056cc !important;\n}\n\n/* إصلاح عام للألوان */\n.text-primary {\n  color: #007bff !important;\n}\n\n.text-success {\n  color: #28a745 !important;\n}\n\n.text-danger {\n  color: #dc3545 !important;\n}\n\n.text-warning {\n  color: #191970 !important;\n}\n\n.text-info {\n  color: #17a2b8 !important;\n}\n\n/* إصلاح الخلفيات */\n.bg-primary {\n  background-color: #007bff !important;\n  color: white !important;\n}\n\n.bg-success {\n  background-color: #28a745 !important;\n  color: white !important;\n}\n\n.bg-danger {\n  background-color: #dc3545 !important;\n  color: white !important;\n}\n\n.bg-warning {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n.bg-info {\n  background-color: #17a2b8 !important;\n  color: white !important;\n}\n\n/* إصلاح الحدود */\n.border-primary {\n  border-color: #007bff !important;\n}\n\n.border-success {\n  border-color: #28a745 !important;\n}\n\n.border-danger {\n  border-color: #dc3545 !important;\n}\n\n.border-warning {\n  border-color: #191970 !important;\n}\n\n.border-info {\n  border-color: #17a2b8 !important;\n}\n\n/* تحسينات شاملة لوضوح النصوص */\nbody {\n  font-family: 'Cairo', 'Almarai', system-ui, sans-serif !important;\n  -webkit-font-smoothing: antialiased !important;\n  -moz-osx-font-smoothing: grayscale !important;\n}\n\n/* تحسين التباين للنصوص الصغيرة */\n.text-xs,\n.text-sm {\n  font-weight: 600 !important;\n  color: #495057 !important;\n}\n\n/* تحسين النصوص في النماذج */\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  color: #6c757d !important;\n  font-weight: 500 !important;\n}\ninput::placeholder,\ntextarea::placeholder,\nselect option {\n  color: #6c757d !important;\n  font-weight: 500 !important;\n}\n\n/* تحسين النصوص في القوائم المنسدلة */\n.dropdown-menu {\n  color: #212529 !important;\n}\n\n.dropdown-item {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n.dropdown-item:hover {\n  background-color: #f8f9fa !important;\n  color: #191970 !important;\n}\n\n/* تحسين النصوص في التبويبات */\n.nav-link {\n  color: #495057 !important;\n  font-weight: 500 !important;\n}\n\n.nav-link.active {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* تحسين النصوص في الشريط الجانبي */\n.sidebar a {\n  color: #e9ecef !important;\n  font-weight: 500 !important;\n}\n\n.sidebar a:hover {\n  color: white !important;\n  font-weight: 600 !important;\n}\n\n/* تحسين عام للقراءة */\n* {\n  text-rendering: optimizeLegibility !important;\n}\n\n/* تحسين النصوص على الخلفيات الملونة */\n.bg-primary *,\n.bg-success *,\n.bg-danger *,\n.bg-warning *,\n.bg-info * {\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;;;AAeA;;;;;AAQA;;;;;;AAQA;;;;;;AAQA;;;;;;AASA;;;;AAKA;;;;AAKA;;;;AAMA;;;;;AAMA;;;;AAQA;;;;AAKA;;;;;AAUA;;;;;AAQA;;;;;;AASA;;;;;;AAQA;;;;;AAgBA;;;;;;AAOA;;;;;;AAQA;;;;AAOA;;;;;;;AAQA;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAOA;;;;AAKA;;;;AAKA;;;;;;AAQA;;;;;AASA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;;AAMA;;;;AAKA;;;;;;AAQA;;;;;AAQA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;AAOA;;;;;AAOA;;;;;AAIA;;;;;AAQA;;;;AAIA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;AAKA"}}]}