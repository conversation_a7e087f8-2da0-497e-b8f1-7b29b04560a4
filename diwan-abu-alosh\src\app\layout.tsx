import type { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import SessionProvider from "@/components/providers/session-provider";
import { SettingsProvider } from "@/components/settings/settings-provider";
import { Toaster } from "@/components/ui/sonner";
import DynamicHead from "@/components/layout/dynamic-head";
import ErrorBoundary from "@/components/error-boundary";
import "./globals.css";
import "../styles/updated-colors.css";
import "../styles/sidebar-colors.css";
import "../styles/button-colors.css";
import "../styles/header-colors.css";
import "../styles/page-layout.css";
import "../styles/table-styles.css";
import "../styles/color-fixes.css";
import "../styles/members-text-fix.css";
import "../styles/global-stats-fix.css";
import "../styles/final-colors.css";
import "../styles/enhanced-dialog.css";
import "../styles/dropdown-fixes.css";
import "../styles/button-fixes.css";

export const metadata: Metadata = {
  title: "ديوان آل أبو علوش",
  description: "نظام إدارة ديوان آل أبو علوش - إدارة الأعضاء والإيرادات والمصروفات",
  keywords: ["ديوان", "إدارة", "أعضاء", "إيرادات", "مصروفات"],
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await getServerSession(authOptions);

  return (
    <html lang="ar" dir="rtl">
      <body className="antialiased">
        <SessionProvider session={session}>
          <SettingsProvider>
            <ErrorBoundary>
              <DynamicHead />
              {children}
              <Toaster />
            </ErrorBoundary>
          </SettingsProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
