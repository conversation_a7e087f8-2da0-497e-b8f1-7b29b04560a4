/* [project]/src/styles/button-fixes.css [app-client] (css) */
.btn-outline, button[data-variant="outline"], [class*="outline"] {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

.btn-outline:hover, button[data-variant="outline"]:hover, [class*="outline"]:hover {
  color: #000 !important;
  background-color: #f9fafb !important;
  border-color: #9ca3af !important;
}

.btn-outline:focus, button[data-variant="outline"]:focus, [class*="outline"]:focus {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px #3b82f633 !important;
}

.btn-outline:active, button[data-variant="outline"]:active, [class*="outline"]:active {
  color: #000 !important;
  background-color: #f3f4f6 !important;
  border-color: #6b7280 !important;
}

.border-primary-300, .border-diwan-300, .border-secondary-300 {
  border-color: #d1d5db !important;
}

.text-primary-700, .text-diwan-700, .text-secondary-700 {
  color: #000 !important;
}

.hover\:bg-primary-50:hover, .hover\:bg-diwan-50:hover, .hover\:bg-secondary-50:hover {
  background-color: #f9fafb !important;
}

.hover\:border-primary-400:hover, .hover\:border-diwan-400:hover, .hover\:border-secondary-400:hover {
  border-color: #9ca3af !important;
}

form .btn-outline, form button[data-variant="outline"], .form-group .btn-outline, .form-group button[data-variant="outline"], .dialog .btn-outline, .dialog button[data-variant="outline"], [role="dialog"] .btn-outline, [role="dialog"] button[data-variant="outline"], .table .btn-outline, .table button[data-variant="outline"], table .btn-outline, table button[data-variant="outline"], .card .btn-outline, .card button[data-variant="outline"], [class*="card"] .btn-outline, [class*="card"] button[data-variant="outline"], .btn-outline.btn-sm, .btn-outline[data-size="sm"], button[data-variant="outline"][data-size="sm"], .btn-outline.btn-lg, .btn-outline[data-size="lg"], button[data-variant="outline"][data-size="lg"] {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

.btn-outline:disabled, .btn-outline[disabled], button[data-variant="outline"]:disabled, button[data-variant="outline"][disabled] {
  color: #9ca3af !important;
  opacity: .6 !important;
  background-color: #f9fafb !important;
  border-color: #e5e7eb !important;
}

.btn-outline .icon, .btn-outline svg, button[data-variant="outline"] .icon, button[data-variant="outline"] svg, .btn-outline:hover .icon, .btn-outline:hover svg, button[data-variant="outline"]:hover .icon, button[data-variant="outline"]:hover svg {
  color: #000 !important;
}

.sidebar .btn-outline, .sidebar button[data-variant="outline"], [class*="sidebar"] .btn-outline, [class*="sidebar"] button[data-variant="outline"], .header .btn-outline, .header button[data-variant="outline"], [class*="header"] .btn-outline, [class*="header"] button[data-variant="outline"], .nav .btn-outline, .nav button[data-variant="outline"], nav .btn-outline, nav button[data-variant="outline"], .footer .btn-outline, .footer button[data-variant="outline"], footer .btn-outline, footer button[data-variant="outline"] {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

.bg-transparent {
  background-color: #fff !important;
}

.border-2.border-primary-300, .border-2.border-diwan-300, .border-2.border-secondary-300 {
  border-width: 2px !important;
  border-color: #d1d5db !important;
}

@media (prefers-color-scheme: dark) {
  .btn-outline, button[data-variant="outline"], [class*="outline"] {
    color: #000 !important;
    background-color: #fff !important;
    border-color: #d1d5db !important;
  }
}

.button-outline, .btn-white, .button-white, .outline-button {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

.button-outline:hover, .btn-white:hover, .button-white:hover, .outline-button:hover {
  color: #000 !important;
  background-color: #f9fafb !important;
  border-color: #9ca3af !important;
}

.member-dialog .btn-outline, .upload-dialog .btn-outline, .settings-dialog .btn-outline, .members-page .btn-outline, .incomes-page .btn-outline, .expenses-page .btn-outline, .gallery-page .btn-outline, .reports-page .btn-outline {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

.btn-outline.loading, .btn-outline[data-loading="true"], button[data-variant="outline"].loading, button[data-variant="outline"][data-loading="true"] {
  color: #000 !important;
  opacity: .7 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

.btn-outline, button[data-variant="outline"] {
  transition: all .2s ease-in-out !important;
}

.btn-outline:hover, button[data-variant="outline"]:hover {
  box-shadow: 0 2px 4px #0000001a !important;
}

.btn-outline:focus, button[data-variant="outline"]:focus {
  box-shadow: 0 0 0 2px #3b82f633 !important;
}

/*# sourceMappingURL=src_styles_button-fixes_css_f9ee138c._.single.css.map*/