module.exports = {

"[project]/node_modules/html2pdf.js/dist/html2pdf.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_b1f81813._.js",
  "server/chunks/ssr/node_modules_jspdf_dist_jspdf_es_min_7b78882d.js",
  "server/chunks/ssr/node_modules_html2canvas_dist_html2canvas_esm_b1a14c17.js",
  "server/chunks/ssr/node_modules_html2pdf_js_dist_html2pdf_d00cdf16.js",
  "server/chunks/ssr/node_modules_fflate_esm_index_mjs_1cd3b76a._.js",
  "server/chunks/ssr/[externals]_module_74f3e68c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/html2pdf.js/dist/html2pdf.js [app-ssr] (ecmascript)");
    });
});
}}),

};