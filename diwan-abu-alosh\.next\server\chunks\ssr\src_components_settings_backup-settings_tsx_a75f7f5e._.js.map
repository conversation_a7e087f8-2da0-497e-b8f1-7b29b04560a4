{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/settings/backup-settings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Switch } from '@/components/ui/switch'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport {\n  Database,\n  Download,\n  Upload,\n  Clock,\n  HardDrive,\n  Cloud,\n  FileText,\n  Archive,\n  RefreshCw,\n  AlertTriangle,\n  CheckCircle,\n  Calendar,\n  Settings\n} from 'lucide-react'\n\ninterface BackupSettingsProps {\n  settings: any\n  onChange: (settings: any) => void\n  canEdit: boolean\n}\n\ninterface BackupSettingsData {\n  // إعدادات النسخ الاحتياطي التلقائي\n  autoBackup: {\n    enabled: boolean\n    frequency: 'daily' | 'weekly' | 'monthly'\n    time: string\n    retentionDays: number\n    includeFiles: boolean\n    includeDatabase: boolean\n    includeSettings: boolean\n  }\n  \n  // إعدادات التخزين\n  storage: {\n    location: 'local' | 'cloud' | 'both'\n    localPath: string\n    cloudProvider: string\n    cloudCredentials: {\n      accessKey: string\n      secretKey: string\n      bucket: string\n      region: string\n    }\n  }\n  \n  // إعدادات الاستيراد والتصدير\n  importExport: {\n    allowDataExport: boolean\n    allowDataImport: boolean\n    exportFormats: string[]\n    maxFileSize: number // MB\n    requireConfirmation: boolean\n  }\n  \n  // إعدادات قاعدة البيانات\n  database: {\n    enableOptimization: boolean\n    autoVacuum: boolean\n    compressionLevel: number\n    encryptBackups: boolean\n  }\n}\n\nconst defaultSettings: BackupSettingsData = {\n  autoBackup: {\n    enabled: true,\n    frequency: 'daily',\n    time: '02:00',\n    retentionDays: 30,\n    includeFiles: true,\n    includeDatabase: true,\n    includeSettings: true\n  },\n  \n  storage: {\n    location: 'local',\n    localPath: './backups',\n    cloudProvider: '',\n    cloudCredentials: {\n      accessKey: '',\n      secretKey: '',\n      bucket: '',\n      region: ''\n    }\n  },\n  \n  importExport: {\n    allowDataExport: true,\n    allowDataImport: true,\n    exportFormats: ['json', 'csv', 'xlsx'],\n    maxFileSize: 100,\n    requireConfirmation: true\n  },\n  \n  database: {\n    enableOptimization: true,\n    autoVacuum: true,\n    compressionLevel: 6,\n    encryptBackups: true\n  }\n}\n\nexport default function BackupSettings({ settings, onChange, canEdit }: BackupSettingsProps) {\n  const [localSettings, setLocalSettings] = useState<BackupSettingsData>(defaultSettings)\n  const [backupProgress, setBackupProgress] = useState(0)\n  const [isBackingUp, setIsBackingUp] = useState(false)\n  const [lastBackup, setLastBackup] = useState<Date | null>(null)\n  const [backupSize, setBackupSize] = useState('0 MB')\n\n  useEffect(() => {\n    if (settings) {\n      setLocalSettings({ ...defaultSettings, ...settings })\n    }\n    loadBackupInfo()\n  }, [settings])\n\n  const handleChange = (key: string, value: any) => {\n    const keys = key.split('.')\n    let newSettings = { ...localSettings }\n    \n    if (keys.length === 1) {\n      newSettings = { ...newSettings, [keys[0]]: value }\n    } else if (keys.length === 2) {\n      newSettings = {\n        ...newSettings,\n        [keys[0]]: {\n          ...newSettings[keys[0] as keyof BackupSettingsData],\n          [keys[1]]: value\n        }\n      }\n    } else if (keys.length === 3) {\n      newSettings = {\n        ...newSettings,\n        [keys[0]]: {\n          ...newSettings[keys[0] as keyof BackupSettingsData],\n          [keys[1]]: {\n            ...(newSettings[keys[0] as keyof BackupSettingsData] as any)[keys[1]],\n            [keys[2]]: value\n          }\n        }\n      }\n    }\n    \n    setLocalSettings(newSettings)\n    onChange(newSettings)\n  }\n\n  const loadBackupInfo = async () => {\n    try {\n      // هنا يمكن إضافة API call لجلب معلومات النسخ الاحتياطية\n      // محاكاة البيانات\n      setLastBackup(new Date(Date.now() - 24 * 60 * 60 * 1000)) // أمس\n      setBackupSize('45.2 MB')\n    } catch (error) {\n      console.error('خطأ في تحميل معلومات النسخ الاحتياطية:', error)\n    }\n  }\n\n  const createBackup = async () => {\n    if (!canEdit) return\n    \n    setIsBackingUp(true)\n    setBackupProgress(0)\n    \n    try {\n      // محاكاة عملية النسخ الاحتياطي\n      for (let i = 0; i <= 100; i += 10) {\n        setBackupProgress(i)\n        await new Promise(resolve => setTimeout(resolve, 200))\n      }\n      \n      setLastBackup(new Date())\n      alert('تم إنشاء النسخة الاحتياطية بنجاح!')\n    } catch {\n      alert('فشل في إنشاء النسخة الاحتياطية')\n    } finally {\n      setIsBackingUp(false)\n      setBackupProgress(0)\n    }\n  }\n\n  const restoreBackup = async () => {\n    if (!canEdit) return\n    \n    if (confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {\n      try {\n        // هنا يمكن إضافة API call لاستعادة النسخة الاحتياطية\n        alert('تم استعادة النسخة الاحتياطية بنجاح!')\n      } catch {\n        alert('فشل في استعادة النسخة الاحتياطية')\n      }\n    }\n  }\n\n  const exportData = async (format: string) => {\n    if (!canEdit) return\n    \n    try {\n      // هنا يمكن إضافة API call لتصدير البيانات\n      const link = document.createElement('a')\n      link.href = `/api/export?format=${format}`\n      link.download = `diwan-data.${format}`\n      link.click()\n    } catch {\n      alert('فشل في تصدير البيانات')\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Backup information */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Database className=\"w-5 h-5 text-blue-600\" />\n            معلومات النسخ الاحتياطية\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n              <Calendar className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />\n              <p className=\"text-sm text-gray-600\">آخر نسخة احتياطية</p>\n              <p className=\"font-semibold\">\n                {lastBackup ? lastBackup.toLocaleDateString('ar-SA') : 'لا توجد'}\n              </p>\n            </div>\n\n            <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n              <HardDrive className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n              <p className=\"text-sm text-gray-600\">حجم النسخة الاحتياطية</p>\n              <p className=\"font-semibold\">{backupSize}</p>\n            </div>\n\n            <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n              <CheckCircle className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n              <p className=\"text-sm text-gray-600\">الحالة</p>\n              <Badge variant=\"outline\" className=\"text-green-600 border-green-200\">\n                جاهز\n              </Badge>\n            </div>\n          </div>\n\n          {isBackingUp && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">جاري إنشاء النسخة الاحتياطية...</span>\n                <span className=\"text-sm font-medium\">{backupProgress}%</span>\n              </div>\n              <Progress value={backupProgress} className=\"w-full\" />\n            </div>\n          )}\n\n          <div className=\"flex gap-3\">\n            <Button\n              onClick={createBackup}\n              disabled={!canEdit || isBackingUp}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isBackingUp ? (\n                <RefreshCw className=\"w-4 h-4 ml-2 animate-spin\" />\n              ) : (\n                <Archive className=\"w-4 h-4 ml-2\" />\n              )}\n              إنشاء نسخة احتياطية\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              onClick={restoreBackup}\n              disabled={!canEdit || !lastBackup}\n            >\n              <Upload className=\"w-4 h-4 ml-2\" />\n              استعادة النسخة الاحتياطية\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* إعدادات النسخ الاحتياطي التلقائي */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Clock className=\"w-5 h-5 text-green-600\" />\n            النسخ الاحتياطي التلقائي\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <Label>تفعيل النسخ الاحتياطي التلقائي</Label>\n              <p className=\"text-sm text-gray-600\">إنشاء نسخ احتياطية تلقائياً</p>\n            </div>\n            <Switch\n              checked={localSettings.autoBackup.enabled}\n              onCheckedChange={(checked) => handleChange('autoBackup.enabled', checked)}\n              disabled={!canEdit}\n            />\n          </div>\n\n          {localSettings.autoBackup.enabled && (\n            <>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label>تكرار النسخ الاحتياطي</Label>\n                  <Select\n                    value={localSettings.autoBackup.frequency}\n                    onValueChange={(value) => handleChange('autoBackup.frequency', value)}\n                    disabled={!canEdit}\n                  >\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"daily\">يومياً</SelectItem>\n                      <SelectItem value=\"weekly\">أسبوعياً</SelectItem>\n                      <SelectItem value=\"monthly\">شهرياً</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"backupTime\">وقت النسخ الاحتياطي</Label>\n                  <Input\n                    id=\"backupTime\"\n                    type=\"time\"\n                    value={localSettings.autoBackup.time}\n                    onChange={(e) => handleChange('autoBackup.time', e.target.value)}\n                    disabled={!canEdit}\n                  />\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label>مدة الاحتفاظ بالنسخ الاحتياطية (يوم)</Label>\n                <Select\n                  value={localSettings.autoBackup.retentionDays.toString()}\n                  onValueChange={(value) => handleChange('autoBackup.retentionDays', parseInt(value))}\n                  disabled={!canEdit}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"7\">أسبوع واحد</SelectItem>\n                    <SelectItem value=\"30\">شهر واحد</SelectItem>\n                    <SelectItem value=\"90\">3 أشهر</SelectItem>\n                    <SelectItem value=\"365\">سنة واحدة</SelectItem>\n                    <SelectItem value=\"0\">دائماً</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-3\">\n                <Label>محتويات النسخة الاحتياطية</Label>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"space-y-1\">\n                      <Label>قاعدة البيانات</Label>\n                      <p className=\"text-sm text-gray-600\">جميع البيانات والجداول</p>\n                    </div>\n                    <Switch\n                      checked={localSettings.autoBackup.includeDatabase}\n                      onCheckedChange={(checked) => handleChange('autoBackup.includeDatabase', checked)}\n                      disabled={!canEdit}\n                    />\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"space-y-1\">\n                      <Label>الملفات المرفوعة</Label>\n                      <p className=\"text-sm text-gray-600\">الصور والمستندات</p>\n                    </div>\n                    <Switch\n                      checked={localSettings.autoBackup.includeFiles}\n                      onCheckedChange={(checked) => handleChange('autoBackup.includeFiles', checked)}\n                      disabled={!canEdit}\n                    />\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"space-y-1\">\n                      <Label>إعدادات النظام</Label>\n                      <p className=\"text-sm text-gray-600\">التكوينات والإعدادات</p>\n                    </div>\n                    <Switch\n                      checked={localSettings.autoBackup.includeSettings}\n                      onCheckedChange={(checked) => handleChange('autoBackup.includeSettings', checked)}\n                      disabled={!canEdit}\n                    />\n                  </div>\n                </div>\n              </div>\n            </>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* إعدادات التخزين */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Cloud className=\"w-5 h-5 text-purple-600\" />\n            إعدادات التخزين\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label>موقع التخزين</Label>\n            <Select\n              value={localSettings.storage.location}\n              onValueChange={(value) => handleChange('storage.location', value)}\n              disabled={!canEdit}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"local\">محلي فقط</SelectItem>\n                <SelectItem value=\"cloud\">سحابي فقط</SelectItem>\n                <SelectItem value=\"both\">محلي وسحابي</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {(localSettings.storage.location === 'local' || localSettings.storage.location === 'both') && (\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"localPath\">مسار التخزين المحلي</Label>\n              <Input\n                id=\"localPath\"\n                value={localSettings.storage.localPath}\n                onChange={(e) => handleChange('storage.localPath', e.target.value)}\n                disabled={!canEdit}\n                placeholder=\"./backups\"\n              />\n            </div>\n          )}\n\n          {(localSettings.storage.location === 'cloud' || localSettings.storage.location === 'both') && (\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label>مزود الخدمة السحابية</Label>\n                <Select\n                  value={localSettings.storage.cloudProvider}\n                  onValueChange={(value) => handleChange('storage.cloudProvider', value)}\n                  disabled={!canEdit}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"اختر مزود الخدمة\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"aws\">Amazon S3</SelectItem>\n                    <SelectItem value=\"google\">Google Cloud Storage</SelectItem>\n                    <SelectItem value=\"azure\">Microsoft Azure</SelectItem>\n                    <SelectItem value=\"digitalocean\">DigitalOcean Spaces</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {localSettings.storage.cloudProvider && (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"accessKey\">مفتاح الوصول</Label>\n                    <Input\n                      id=\"accessKey\"\n                      type=\"password\"\n                      value={localSettings.storage.cloudCredentials.accessKey}\n                      onChange={(e) => handleChange('storage.cloudCredentials.accessKey', e.target.value)}\n                      disabled={!canEdit}\n                      placeholder=\"Access Key\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"secretKey\">المفتاح السري</Label>\n                    <Input\n                      id=\"secretKey\"\n                      type=\"password\"\n                      value={localSettings.storage.cloudCredentials.secretKey}\n                      onChange={(e) => handleChange('storage.cloudCredentials.secretKey', e.target.value)}\n                      disabled={!canEdit}\n                      placeholder=\"Secret Key\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"bucket\">اسم الحاوية</Label>\n                    <Input\n                      id=\"bucket\"\n                      value={localSettings.storage.cloudCredentials.bucket}\n                      onChange={(e) => handleChange('storage.cloudCredentials.bucket', e.target.value)}\n                      disabled={!canEdit}\n                      placeholder=\"bucket-name\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"region\">المنطقة</Label>\n                    <Input\n                      id=\"region\"\n                      value={localSettings.storage.cloudCredentials.region}\n                      onChange={(e) => handleChange('storage.cloudCredentials.region', e.target.value)}\n                      disabled={!canEdit}\n                      placeholder=\"us-east-1\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* إعدادات الاستيراد والتصدير */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <FileText className=\"w-5 h-5 text-orange-600\" />\n            الاستيراد والتصدير\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>السماح بتصدير البيانات</Label>\n                <p className=\"text-sm text-gray-600\">تمكين المستخدمين من تصدير البيانات</p>\n              </div>\n              <Switch\n                checked={localSettings.importExport.allowDataExport}\n                onCheckedChange={(checked) => handleChange('importExport.allowDataExport', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>السماح باستيراد البيانات</Label>\n                <p className=\"text-sm text-gray-600\">تمكين المستخدمين من استيراد البيانات</p>\n              </div>\n              <Switch\n                checked={localSettings.importExport.allowDataImport}\n                onCheckedChange={(checked) => handleChange('importExport.allowDataImport', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>يتطلب تأكيد</Label>\n                <p className=\"text-sm text-gray-600\">طلب تأكيد قبل الاستيراد/التصدير</p>\n              </div>\n              <Switch\n                checked={localSettings.importExport.requireConfirmation}\n                onCheckedChange={(checked) => handleChange('importExport.requireConfirmation', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"maxFileSize\">الحد الأقصى لحجم الملف (MB)</Label>\n            <Input\n              id=\"maxFileSize\"\n              type=\"number\"\n              min=\"1\"\n              max=\"1000\"\n              value={localSettings.importExport.maxFileSize}\n              onChange={(e) => handleChange('importExport.maxFileSize', parseInt(e.target.value))}\n              disabled={!canEdit}\n            />\n          </div>\n\n          <div className=\"space-y-3\">\n            <Label>تصدير البيانات</Label>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={() => exportData('json')}\n                disabled={!canEdit || !localSettings.importExport.allowDataExport}\n                className=\"flex items-center gap-2\"\n              >\n                <Download className=\"w-4 h-4\" />\n                JSON\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={() => exportData('csv')}\n                disabled={!canEdit || !localSettings.importExport.allowDataExport}\n                className=\"flex items-center gap-2\"\n              >\n                <Download className=\"w-4 h-4\" />\n                CSV\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={() => exportData('xlsx')}\n                disabled={!canEdit || !localSettings.importExport.allowDataExport}\n                className=\"flex items-center gap-2\"\n              >\n                <Download className=\"w-4 h-4\" />\n                Excel\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={() => exportData('pdf')}\n                disabled={!canEdit || !localSettings.importExport.allowDataExport}\n                className=\"flex items-center gap-2\"\n              >\n                <Download className=\"w-4 h-4\" />\n                PDF\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* إعدادات قاعدة البيانات */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Settings className=\"w-5 h-5 text-red-600\" />\n            إعدادات قاعدة البيانات\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تحسين قاعدة البيانات</Label>\n                <p className=\"text-sm text-gray-600\">تحسين الأداء تلقائياً</p>\n              </div>\n              <Switch\n                checked={localSettings.database.enableOptimization}\n                onCheckedChange={(checked) => handleChange('database.enableOptimization', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>التنظيف التلقائي</Label>\n                <p className=\"text-sm text-gray-600\">إزالة البيانات المحذوفة تلقائياً</p>\n              </div>\n              <Switch\n                checked={localSettings.database.autoVacuum}\n                onCheckedChange={(checked) => handleChange('database.autoVacuum', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تشفير النسخ الاحتياطية</Label>\n                <p className=\"text-sm text-gray-600\">حماية النسخ الاحتياطية بالتشفير</p>\n              </div>\n              <Switch\n                checked={localSettings.database.encryptBackups}\n                onCheckedChange={(checked) => handleChange('database.encryptBackups', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label>مستوى الضغط</Label>\n            <Select\n              value={localSettings.database.compressionLevel.toString()}\n              onValueChange={(value) => handleChange('database.compressionLevel', parseInt(value))}\n              disabled={!canEdit}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"1\">منخفض (سريع)</SelectItem>\n                <SelectItem value=\"3\">متوسط</SelectItem>\n                <SelectItem value=\"6\">عالي (افتراضي)</SelectItem>\n                <SelectItem value=\"9\">أقصى (بطيء)</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          <div className=\"p-4 bg-amber-50 border border-amber-200 rounded-lg\">\n            <div className=\"flex items-start gap-3\">\n              <AlertTriangle className=\"w-5 h-5 text-amber-600 mt-0.5\" />\n              <div>\n                <p className=\"text-amber-800 font-medium\">تحذير مهم</p>\n                <p className=\"text-amber-700 text-sm mt-1\">\n                  تأكد من إنشاء نسخة احتياطية قبل تغيير إعدادات قاعدة البيانات.\n                  بعض التغييرات قد تتطلب إعادة تشغيل النظام.\n                </p>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;;;;;;AA4EA,MAAM,kBAAsC;IAC1C,YAAY;QACV,SAAS;QACT,WAAW;QACX,MAAM;QACN,eAAe;QACf,cAAc;QACd,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,SAAS;QACP,UAAU;QACV,WAAW;QACX,eAAe;QACf,kBAAkB;YAChB,WAAW;YACX,WAAW;YACX,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,cAAc;QACZ,iBAAiB;QACjB,iBAAiB;QACjB,eAAe;YAAC;YAAQ;YAAO;SAAO;QACtC,aAAa;QACb,qBAAqB;IACvB;IAEA,UAAU;QACR,oBAAoB;QACpB,YAAY;QACZ,kBAAkB;QAClB,gBAAgB;IAClB;AACF;AAEe,SAAS,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAuB;IACzF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,iBAAiB;gBAAE,GAAG,eAAe;gBAAE,GAAG,QAAQ;YAAC;QACrD;QACA;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe,CAAC,KAAa;QACjC,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,cAAc;YAAE,GAAG,aAAa;QAAC;QAErC,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,cAAc;gBAAE,GAAG,WAAW;gBAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAAM;QACnD,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;YAC5B,cAAc;gBACZ,GAAG,WAAW;gBACd,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;oBACT,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,CAA6B;oBACnD,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBACb;YACF;QACF,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;YAC5B,cAAc;gBACZ,GAAG,WAAW;gBACd,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;oBACT,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,CAA6B;oBACnD,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;wBACT,GAAG,AAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAA6B,AAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBACrE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;oBACb;gBACF;YACF;QACF;QAEA,iBAAiB;QACjB,SAAS;IACX;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,wDAAwD;YACxD,kBAAkB;YAClB,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,OAAO,MAAM;;YAChE,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS;QAEd,eAAe;QACf,kBAAkB;QAElB,IAAI;YACF,+BAA+B;YAC/B,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,GAAI;gBACjC,kBAAkB;gBAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,cAAc,IAAI;YAClB,MAAM;QACR,EAAE,OAAM;YACN,MAAM;QACR,SAAU;YACR,eAAe;YACf,kBAAkB;QACpB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS;QAEd,IAAI,QAAQ,8EAA8E;YACxF,IAAI;gBACF,qDAAqD;gBACrD,MAAM;YACR,EAAE,OAAM;gBACN,MAAM;YACR;QACF;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,0CAA0C;YAC1C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG,CAAC,mBAAmB,EAAE,QAAQ;YAC1C,KAAK,QAAQ,GAAG,CAAC,WAAW,EAAE,QAAQ;YACtC,KAAK,KAAK;QACZ,EAAE,OAAM;YACN,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAIlD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DACV,aAAa,WAAW,kBAAkB,CAAC,WAAW;;;;;;;;;;;;kDAI3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAiB;;;;;;;;;;;;kDAGhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAkC;;;;;;;;;;;;;;;;;;4BAMxE,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;;oDAAuB;oDAAe;;;;;;;;;;;;;kDAExD,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,OAAO;wCAAgB,WAAU;;;;;;;;;;;;0CAI/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,WAAW;wCACtB,WAAU;;4CAET,4BACC,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;qEAErB,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CACnB;;;;;;;kDAIJ,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU,CAAC,WAAW,CAAC;;0DAEvB,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA2B;;;;;;;;;;;;kCAIhD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,cAAc,UAAU,CAAC,OAAO;wCACzC,iBAAiB,CAAC,UAAY,aAAa,sBAAsB;wCACjE,UAAU,CAAC;;;;;;;;;;;;4BAId,cAAc,UAAU,CAAC,OAAO,kBAC/B;;kDACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,cAAc,UAAU,CAAC,SAAS;wDACzC,eAAe,CAAC,QAAU,aAAa,wBAAwB;wDAC/D,UAAU,CAAC;;0EAEX,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,8OAAC,kIAAA,CAAA,gBAAa;;kFACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAQ;;;;;;kFAC1B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAS;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAKlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,UAAU,CAAC,IAAI;wDACpC,UAAU,CAAC,IAAM,aAAa,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDAC/D,UAAU,CAAC;;;;;;;;;;;;;;;;;;kDAKjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,UAAU,CAAC,aAAa,CAAC,QAAQ;gDACtD,eAAe,CAAC,QAAU,aAAa,4BAA4B,SAAS;gDAC5E,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;;;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,cAAc,UAAU,CAAC,eAAe;gEACjD,iBAAiB,CAAC,UAAY,aAAa,8BAA8B;gEACzE,UAAU,CAAC;;;;;;;;;;;;kEAIf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,cAAc,UAAU,CAAC,YAAY;gEAC9C,iBAAiB,CAAC,UAAY,aAAa,2BAA2B;gEACtE,UAAU,CAAC;;;;;;;;;;;;kEAIf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,cAAc,UAAU,CAAC,eAAe;gEACjD,iBAAiB,CAAC,UAAY,aAAa,8BAA8B;gEACzE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW3B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAIjD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,cAAc,OAAO,CAAC,QAAQ;wCACrC,eAAe,CAAC,QAAU,aAAa,oBAAoB;wCAC3D,UAAU,CAAC;;0DAEX,8OAAC,kIAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;4BAK9B,CAAC,cAAc,OAAO,CAAC,QAAQ,KAAK,WAAW,cAAc,OAAO,CAAC,QAAQ,KAAK,MAAM,mBACvF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO,cAAc,OAAO,CAAC,SAAS;wCACtC,UAAU,CAAC,IAAM,aAAa,qBAAqB,EAAE,MAAM,CAAC,KAAK;wCACjE,UAAU,CAAC;wCACX,aAAY;;;;;;;;;;;;4BAKjB,CAAC,cAAc,OAAO,CAAC,QAAQ,KAAK,WAAW,cAAc,OAAO,CAAC,QAAQ,KAAK,MAAM,mBACvF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,OAAO,CAAC,aAAa;gDAC1C,eAAe,CAAC,QAAU,aAAa,yBAAyB;gDAChE,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAe;;;;;;;;;;;;;;;;;;;;;;;;oCAKtC,cAAc,OAAO,CAAC,aAAa,kBAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,OAAO,CAAC,gBAAgB,CAAC,SAAS;wDACvD,UAAU,CAAC,IAAM,aAAa,sCAAsC,EAAE,MAAM,CAAC,KAAK;wDAClF,UAAU,CAAC;wDACX,aAAY;;;;;;;;;;;;0DAGhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,OAAO,CAAC,gBAAgB,CAAC,SAAS;wDACvD,UAAU,CAAC,IAAM,aAAa,sCAAsC,EAAE,MAAM,CAAC,KAAK;wDAClF,UAAU,CAAC;wDACX,aAAY;;;;;;;;;;;;0DAGhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAS;;;;;;kEACxB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,cAAc,OAAO,CAAC,gBAAgB,CAAC,MAAM;wDACpD,UAAU,CAAC,IAAM,aAAa,mCAAmC,EAAE,MAAM,CAAC,KAAK;wDAC/E,UAAU,CAAC;wDACX,aAAY;;;;;;;;;;;;0DAGhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAS;;;;;;kEACxB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,cAAc,OAAO,CAAC,gBAAgB,CAAC,MAAM;wDACpD,UAAU,CAAC,IAAM,aAAa,mCAAmC,EAAE,MAAM,CAAC,KAAK;wDAC/E,UAAU,CAAC;wDACX,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAIpD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,YAAY,CAAC,eAAe;gDACnD,iBAAiB,CAAC,UAAY,aAAa,gCAAgC;gDAC3E,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,YAAY,CAAC,eAAe;gDACnD,iBAAiB,CAAC,UAAY,aAAa,gCAAgC;gDAC3E,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,YAAY,CAAC,mBAAmB;gDACvD,iBAAiB,CAAC,UAAY,aAAa,oCAAoC;gDAC/E,UAAU,CAAC;;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO,cAAc,YAAY,CAAC,WAAW;wCAC7C,UAAU,CAAC,IAAM,aAAa,4BAA4B,SAAS,EAAE,MAAM,CAAC,KAAK;wCACjF,UAAU,CAAC;;;;;;;;;;;;0CAIf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,WAAW;gDAC1B,UAAU,CAAC,WAAW,CAAC,cAAc,YAAY,CAAC,eAAe;gDACjE,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,WAAW;gDAC1B,UAAU,CAAC,WAAW,CAAC,cAAc,YAAY,CAAC,eAAe;gDACjE,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,WAAW;gDAC1B,UAAU,CAAC,WAAW,CAAC,cAAc,YAAY,CAAC,eAAe;gDACjE,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,WAAW;gDAC1B,UAAU,CAAC,WAAW,CAAC,cAAc,YAAY,CAAC,eAAe;gDACjE,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;;;;;;kCAIjD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,QAAQ,CAAC,kBAAkB;gDAClD,iBAAiB,CAAC,UAAY,aAAa,+BAA+B;gDAC1E,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,QAAQ,CAAC,UAAU;gDAC1C,iBAAiB,CAAC,UAAY,aAAa,uBAAuB;gDAClE,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,QAAQ,CAAC,cAAc;gDAC9C,iBAAiB,CAAC,UAAY,aAAa,2BAA2B;gDACtE,UAAU,CAAC;;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,cAAc,QAAQ,CAAC,gBAAgB,CAAC,QAAQ;wCACvD,eAAe,CAAC,QAAU,aAAa,6BAA6B,SAAS;wCAC7E,UAAU,CAAC;;0DAEX,8OAAC,kIAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;kEACtB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;kEACtB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;kEACtB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D", "debugId": null}}]}