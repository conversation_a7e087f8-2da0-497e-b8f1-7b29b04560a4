/* [project]/src/styles/table-fixes.css [app-client] (css) */
:root {
  --navy-50: #f8fafc;
  --navy-100: #f1f5f9;
  --navy-200: #e2e8f0;
  --navy-300: #cbd5e1;
  --navy-400: #94a3b8;
  --navy-500: #64748b;
  --navy-600: #475569;
  --navy-700: #334155;
  --navy-800: #1e293b;
  --navy-900: #0f172a;
}

table, .table, [role="table"] {
  color: var(--navy-800) !important;
}

th, thead th, .table-head, .table-header, [role="columnheader"] {
  color: var(--navy-700) !important;
  font-weight: 600 !important;
}

td, tbody td, .table-cell, .table-data, [role="cell"], tr, .table-row, [role="row"], .enhanced-table, .enhanced-table th, .enhanced-table td {
  color: var(--navy-800) !important;
}

.enhanced-table thead th {
  color: var(--navy-700) !important;
  font-weight: 600 !important;
}

.members-table, .members-table th, .members-table td {
  color: var(--navy-800) !important;
}

.members-table thead th {
  color: var(--navy-700) !important;
}

.incomes-table, .incomes-table th, .incomes-table td {
  color: var(--navy-800) !important;
}

.incomes-table thead th {
  color: var(--navy-700) !important;
}

.expenses-table, .expenses-table th, .expenses-table td {
  color: var(--navy-800) !important;
}

.expenses-table thead th {
  color: var(--navy-700) !important;
}

.reports-table, .reports-table th, .reports-table td {
  color: var(--navy-800) !important;
}

.reports-table thead th {
  color: var(--navy-700) !important;
}

.dialog table, .dialog th, .dialog td, [role="dialog"] table, [role="dialog"] th, [role="dialog"] td {
  color: var(--navy-800) !important;
}

.dialog thead th, [role="dialog"] thead th {
  color: var(--navy-700) !important;
}

.card table, .card th, .card td, [class*="card"] table, [class*="card"] th, [class*="card"] td {
  color: var(--navy-800) !important;
}

.card thead th, [class*="card"] thead th {
  color: var(--navy-700) !important;
}

.text-slate-700, .text-slate-800, .text-gray-700, .text-gray-800 {
  color: var(--navy-800) !important;
}

.text-slate-600, .text-gray-600 {
  color: var(--navy-700) !important;
}

.table-navy, .navy-table {
  color: var(--navy-800) !important;
}

.table-navy th, .navy-table th {
  color: var(--navy-700) !important;
  font-weight: 600 !important;
}

.table-responsive, .responsive-table {
  color: var(--navy-800) !important;
}

.table-responsive th, .responsive-table th {
  color: var(--navy-700) !important;
}

.sortable-table, .table-sortable {
  color: var(--navy-800) !important;
}

.sortable-table th, .table-sortable th {
  color: var(--navy-700) !important;
}

.filterable-table, .table-filterable {
  color: var(--navy-800) !important;
}

.filterable-table th, .table-filterable th {
  color: var(--navy-700) !important;
}

.searchable-table, .table-searchable {
  color: var(--navy-800) !important;
}

.searchable-table th, .table-searchable th {
  color: var(--navy-700) !important;
}

.paginated-table, .table-paginated {
  color: var(--navy-800) !important;
}

.paginated-table th, .table-paginated th {
  color: var(--navy-700) !important;
}

.members-page table, .incomes-page table, .expenses-page table, .reports-page table, .gallery-page table {
  color: var(--navy-800) !important;
}

.members-page thead th, .incomes-page thead th, .expenses-page thead th, .reports-page thead th, .gallery-page thead th {
  color: var(--navy-700) !important;
}

.table-loading, .loading-table {
  color: var(--navy-800) !important;
}

.table-empty, .empty-table {
  color: var(--navy-600) !important;
}

.table-hover tr:hover, .hover-table tr:hover {
  color: var(--navy-900) !important;
}

.table-selected, .selected-table, tr.selected, tr[data-selected="true"] {
  color: var(--navy-900) !important;
  background-color: var(--navy-50) !important;
}

.table-disabled, .disabled-table, tr.disabled, tr[disabled] {
  color: var(--navy-400) !important;
  opacity: .6 !important;
}

.sort-icon, .table-sort-icon, .sortable-icon {
  color: var(--navy-600) !important;
}

.sort-icon.active, .table-sort-icon.active, .sortable-icon.active {
  color: var(--navy-800) !important;
}

table a, .table a, td a, th a {
  text-decoration: underline;
  color: var(--navy-700) !important;
}

table a:hover, .table a:hover, td a:hover, th a:hover {
  color: var(--navy-900) !important;
}

table button, .table button, td button, th button {
  color: inherit !important;
}

.table-caption, .table-description, .table-helper-text {
  color: var(--navy-600) !important;
}

@media (prefers-color-scheme: dark) {
  table, .table, [role="table"], th, td, tr {
    color: var(--navy-800) !important;
  }

  thead th {
    color: var(--navy-700) !important;
  }
}

.table-primary, .table-secondary, .table-success, .table-info, .table-warning, .table-danger {
  color: var(--navy-800) !important;
}

.table-primary th, .table-secondary th, .table-success th, .table-info th, .table-warning th, .table-danger th {
  color: var(--navy-700) !important;
}

.MuiTable-root, .MuiTableHead-root, .MuiTableBody-root, .MuiTableRow-root, .MuiTableCell-root {
  color: var(--navy-800) !important;
}

.MuiTableHead-root .MuiTableCell-root {
  color: var(--navy-700) !important;
  font-weight: 600 !important;
}

/*# sourceMappingURL=src_styles_table-fixes_css_f9ee138c._.single.css.map*/