{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WsUnalvfLC8q6thQbM7XpflHInutTa69tutWo9d2/Nc=", "__NEXT_PREVIEW_MODE_ID": "d4e95752d4f9c360392ea9166eb6786d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2be80da5955c33353cc6281fb1c54c29a108636020f490dc3d71fc60d979fedb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "199c0fbbd929bb905c6a88dbfdbe86ef5125eb70d3b0d42ae933b2932d5a714a"}}}, "instrumentation": null, "functions": {}}