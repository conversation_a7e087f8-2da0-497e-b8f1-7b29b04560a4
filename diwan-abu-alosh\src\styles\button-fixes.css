/* إصلاحات ألوان الأزرار من نوع outline */

/* إصلاح الأزرار من نوع outline */
.btn-outline,
button[data-variant="outline"],
[class*="outline"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

.btn-outline:hover,
button[data-variant="outline"]:hover,
[class*="outline"]:hover {
  background-color: #f9fafb !important; /* gray-50 */
  color: black !important;
  border-color: #9ca3af !important; /* gray-400 */
}

.btn-outline:focus,
button[data-variant="outline"]:focus,
[class*="outline"]:focus {
  background-color: white !important;
  color: black !important;
  border-color: #3b82f6 !important; /* blue-500 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.btn-outline:active,
button[data-variant="outline"]:active,
[class*="outline"]:active {
  background-color: #f3f4f6 !important; /* gray-100 */
  color: black !important;
  border-color: #6b7280 !important; /* gray-500 */
}

/* إصلاح أزرار outline محددة بالفئات */
.border-primary-300,
.border-diwan-300,
.border-secondary-300 {
  border-color: #d1d5db !important; /* gray-300 */
}

.text-primary-700,
.text-diwan-700,
.text-secondary-700 {
  color: black !important;
}

.hover\:bg-primary-50:hover,
.hover\:bg-diwan-50:hover,
.hover\:bg-secondary-50:hover {
  background-color: #f9fafb !important; /* gray-50 */
}

.hover\:border-primary-400:hover,
.hover\:border-diwan-400:hover,
.hover\:border-secondary-400:hover {
  border-color: #9ca3af !important; /* gray-400 */
}

/* إصلاح أزرار outline في النماذج */
form .btn-outline,
form button[data-variant="outline"],
.form-group .btn-outline,
.form-group button[data-variant="outline"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline في الحوارات */
.dialog .btn-outline,
.dialog button[data-variant="outline"],
[role="dialog"] .btn-outline,
[role="dialog"] button[data-variant="outline"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline في الجداول */
.table .btn-outline,
.table button[data-variant="outline"],
table .btn-outline,
table button[data-variant="outline"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline في البطاقات */
.card .btn-outline,
.card button[data-variant="outline"],
[class*="card"] .btn-outline,
[class*="card"] button[data-variant="outline"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline الصغيرة */
.btn-outline.btn-sm,
.btn-outline[data-size="sm"],
button[data-variant="outline"][data-size="sm"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline الكبيرة */
.btn-outline.btn-lg,
.btn-outline[data-size="lg"],
button[data-variant="outline"][data-size="lg"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline المعطلة */
.btn-outline:disabled,
.btn-outline[disabled],
button[data-variant="outline"]:disabled,
button[data-variant="outline"][disabled] {
  background-color: #f9fafb !important; /* gray-50 */
  color: #9ca3af !important; /* gray-400 */
  border-color: #e5e7eb !important; /* gray-200 */
  opacity: 0.6 !important;
}

/* إصلاح أزرار outline مع أيقونات */
.btn-outline .icon,
.btn-outline svg,
button[data-variant="outline"] .icon,
button[data-variant="outline"] svg {
  color: black !important;
}

.btn-outline:hover .icon,
.btn-outline:hover svg,
button[data-variant="outline"]:hover .icon,
button[data-variant="outline"]:hover svg {
  color: black !important;
}

/* إصلاح أزرار outline في الشريط الجانبي */
.sidebar .btn-outline,
.sidebar button[data-variant="outline"],
[class*="sidebar"] .btn-outline,
[class*="sidebar"] button[data-variant="outline"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline في الهيدر */
.header .btn-outline,
.header button[data-variant="outline"],
[class*="header"] .btn-outline,
[class*="header"] button[data-variant="outline"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline في التنقل */
.nav .btn-outline,
.nav button[data-variant="outline"],
nav .btn-outline,
nav button[data-variant="outline"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline في الفوتر */
.footer .btn-outline,
.footer button[data-variant="outline"],
footer .btn-outline,
footer button[data-variant="outline"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline مع فئات Tailwind محددة */
.bg-transparent {
  background-color: white !important;
}

.border-2.border-primary-300,
.border-2.border-diwan-300,
.border-2.border-secondary-300 {
  border-color: #d1d5db !important; /* gray-300 */
  border-width: 2px !important;
}

/* إصلاح أزرار outline في الوضع الداكن */
@media (prefers-color-scheme: dark) {
  .btn-outline,
  button[data-variant="outline"],
  [class*="outline"] {
    background-color: white !important;
    color: black !important;
    border-color: #d1d5db !important; /* gray-300 */
  }
}

/* إصلاح أزرار outline مع فئات مخصصة */
.button-outline,
.btn-white,
.button-white,
.outline-button {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

.button-outline:hover,
.btn-white:hover,
.button-white:hover,
.outline-button:hover {
  background-color: #f9fafb !important; /* gray-50 */
  color: black !important;
  border-color: #9ca3af !important; /* gray-400 */
}

/* إصلاح أزرار outline في مكونات محددة */
.member-dialog .btn-outline,
.upload-dialog .btn-outline,
.settings-dialog .btn-outline {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline في صفحات محددة */
.members-page .btn-outline,
.incomes-page .btn-outline,
.expenses-page .btn-outline,
.gallery-page .btn-outline,
.reports-page .btn-outline {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح أزرار outline مع حالات خاصة */
.btn-outline.loading,
.btn-outline[data-loading="true"],
button[data-variant="outline"].loading,
button[data-variant="outline"][data-loading="true"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
  opacity: 0.7 !important;
}

/* إصلاح أزرار outline مع تأثيرات الانتقال */
.btn-outline,
button[data-variant="outline"] {
  transition: all 0.2s ease-in-out !important;
}

/* إصلاح أزرار outline مع تأثيرات الظل */
.btn-outline:hover,
button[data-variant="outline"]:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.btn-outline:focus,
button[data-variant="outline"]:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}
