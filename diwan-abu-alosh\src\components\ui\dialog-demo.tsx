"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON><PERSON>, DialogFooter } from './dialog'
import { Button } from './button'
import { <PERSON><PERSON><PERSON>, MousePointer, Move, Eye, Info } from 'lucide-react'
import MouseTracker, { MouseInfoPanel } from './mouse-tracker'

interface DialogDemoProps {
  className?: string
}

export default function DialogDemo({ className }: DialogDemoProps) {
  const [basicDialogOpen, setBasicDialogOpen] = useState(false)
  const [mouseFollowDialogOpen, setMouseFollowDialogOpen] = useState(false)
  const [staticDialogOpen, setStaticDialogOpen] = useState(false)
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false)
  const [showMouseTracker, setShowMouseTracker] = useState(false)
  const [showMouseInfo, setShowMouseInfo] = useState(false)

  return (
    <div className={`space-y-4 p-6 ${className}`}>
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">تجربة الشاشات المنبثقة مع التمرير المحسن</h2>
        <p className="text-gray-600">اختبر التمرير بعجلة الماوس داخل الشاشات المنبثقة لرؤية المحتوى كاملاً</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* حوار قصير */}
        <Button
          onClick={() => setBasicDialogOpen(true)}
          className="flex items-center gap-2 h-20 flex-col justify-center"
          variant="outline"
        >
          <Eye className="w-6 h-6" />
          <span>حوار قصير</span>
        </Button>

        {/* حوار طويل مع تمرير */}
        <Button
          onClick={() => setMouseFollowDialogOpen(true)}
          className="flex items-center gap-2 h-20 flex-col justify-center"
          variant="outline"
        >
          <Move className="w-6 h-6" />
          <span>حوار طويل مع تمرير</span>
        </Button>

        {/* حوار الإعدادات */}
        <Button
          onClick={() => setSettingsDialogOpen(true)}
          className="flex items-center gap-2 h-20 flex-col justify-center"
          variant="outline"
        >
          <Settings className="w-6 h-6" />
          <span>إعدادات متقدمة</span>
        </Button>
      </div>

      {/* حوار قصير */}
      <Dialog
        open={basicDialogOpen}
        onOpenChange={setBasicDialogOpen}
        enableScrollInteraction={true}
        maxHeight="80vh"
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5 text-blue-600" />
              حوار قصير
            </DialogTitle>
          </DialogHeader>
          <div className="p-6">
            <p className="text-gray-600 mb-4">
              هذا حوار قصير لا يحتاج تمرير. المحتوى يظهر كاملاً بدون الحاجة لعجلة الماوس.
            </p>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">الميزات:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• محتوى قصير ومباشر</li>
                <li>• لا يحتاج تمرير</li>
                <li>• سهل القراءة</li>
                <li>• تفاعل سريع</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setBasicDialogOpen(false)}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>



      {/* حوار طويل مع تمرير */}
      <Dialog
        open={mouseFollowDialogOpen}
        onOpenChange={setMouseFollowDialogOpen}
        enableScrollInteraction={true}
        maxHeight="70vh"
      >
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Move className="w-5 h-5 text-purple-600" />
              حوار طويل مع تمرير محسن
            </DialogTitle>
          </DialogHeader>
          <div className="p-6 space-y-4">
            <p className="text-gray-600">
              هذا حوار طويل يحتوي على محتوى كثير. استخدم عجلة الماوس للتمرير لأعلى وأسفل لرؤية المحتوى كاملاً.
            </p>

            {/* محتوى طويل للاختبار */}
            {[1, 2, 3, 4, 5, 6, 7, 8].map((section) => (
              <div key={section} className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-900 mb-2">القسم {section}</h4>
                <p className="text-sm text-purple-800 mb-2">
                  هذا نص تجريبي للقسم رقم {section}. يحتوي على معلومات مهمة تحتاج للتمرير لرؤيتها.
                </p>
                <ul className="text-sm text-purple-700 space-y-1">
                  <li>• نقطة مهمة رقم 1 في القسم {section}</li>
                  <li>• نقطة مهمة رقم 2 في القسم {section}</li>
                  <li>• نقطة مهمة رقم 3 في القسم {section}</li>
                </ul>
              </div>
            ))}

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">💡 تعليمات التمرير:</h4>
              <ol className="text-sm text-blue-700 space-y-1">
                <li>1. استخدم عجلة الماوس للتمرير لأعلى وأسفل</li>
                <li>2. يمكنك أيضاً استخدام شريط التمرير على الجانب</li>
                <li>3. التمرير سلس ومحسن للأداء</li>
                <li>4. المحتوى محمي من التمرير خارج الحوار</li>
              </ol>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">✨ الميزات المحسنة:</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• شريط تمرير مخصص وجميل</li>
                <li>• تمرير سلس ومرن</li>
                <li>• حماية من التمرير خارج الحوار</li>
                <li>• تحسين للأجهزة اللمسية</li>
                <li>• دعم جميع المتصفحات</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setMouseFollowDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button onClick={() => setMouseFollowDialogOpen(false)}>
              موافق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار الإعدادات */}
      <Dialog
        open={settingsDialogOpen}
        onOpenChange={setSettingsDialogOpen}
        enableScrollInteraction={true}
        maxHeight="85vh"
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-indigo-600" />
              إعدادات التمرير المحسن
            </DialogTitle>
          </DialogHeader>
          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">الخصائص المتاحة</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span className="text-sm font-medium">enableScrollInteraction</span>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">true</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span className="text-sm font-medium">maxHeight</span>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">90vh</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span className="text-sm font-medium">smooth-scroll</span>
                    <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">CSS</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">التحسينات</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>تمرير سلس بعجلة الماوس</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>شريط تمرير مخصص وجميل</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>حماية من التمرير خارج الحوار</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span>دعم الأجهزة اللمسية</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-indigo-50 p-4 rounded-lg">
              <h4 className="font-semibold text-indigo-900 mb-2">💡 كيفية الاستخدام</h4>
              <ul className="text-sm text-indigo-800 space-y-1">
                <li>• استخدم عجلة الماوس للتمرير داخل الحوار</li>
                <li>• يمكنك سحب شريط التمرير للتنقل السريع</li>
                <li>• التمرير محمي من التأثير على الصفحة الخلفية</li>
                <li>• يعمل على جميع الأجهزة والمتصفحات</li>
              </ul>
            </div>

            {/* محتوى إضافي للاختبار */}
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">أمثلة إضافية للتمرير</h4>
              {[1, 2, 3, 4, 5].map((item) => (
                <div key={item} className="bg-gray-50 p-4 rounded-lg">
                  <h5 className="font-medium text-gray-800 mb-2">مثال {item}</h5>
                  <p className="text-sm text-gray-600">
                    هذا مثال رقم {item} لاختبار التمرير. يحتوي على نص كافي لإظهار كيفية عمل التمرير السلس داخل الحوار.
                    يمكنك استخدام عجلة الماوس أو شريط التمرير للتنقل بين هذه الأمثلة بسهولة.
                  </p>
                </div>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setSettingsDialogOpen(false)}>
              فهمت
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
