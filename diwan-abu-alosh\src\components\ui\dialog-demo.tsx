"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON><PERSON>, DialogFooter } from './dialog'
import { Button } from './button'
import { <PERSON><PERSON><PERSON>, MousePointer, Move, Eye, Info } from 'lucide-react'
import MouseTracker, { MouseInfoPanel } from './mouse-tracker'

interface DialogDemoProps {
  className?: string
}

export default function DialogDemo({ className }: DialogDemoProps) {
  const [basicDialogOpen, setBasicDialogOpen] = useState(false)
  const [mouseFollowDialogOpen, setMouseFollowDialogOpen] = useState(false)
  const [staticDialogOpen, setStaticDialogOpen] = useState(false)
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false)
  const [showMouseTracker, setShowMouseTracker] = useState(false)
  const [showMouseInfo, setShowMouseInfo] = useState(false)

  return (
    <div className={`space-y-4 p-6 ${className}`}>
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">تجربة الشاشات المنبثقة المحسنة</h2>
        <p className="text-gray-600">اختبر الأنواع المختلفة من الشاشات المنبثقة التفاعلية</p>
      </div>

      {/* أدوات التحكم في تتبع الماوس */}
      <div className="flex justify-center gap-4 mb-6">
        <Button
          variant={showMouseTracker ? "default" : "outline"}
          size="sm"
          onClick={() => setShowMouseTracker(!showMouseTracker)}
          className="flex items-center gap-2"
        >
          <MousePointer className="w-4 h-4" />
          {showMouseTracker ? 'إخفاء مؤشر الماوس' : 'إظهار مؤشر الماوس'}
        </Button>

        <Button
          variant={showMouseInfo ? "default" : "outline"}
          size="sm"
          onClick={() => setShowMouseInfo(!showMouseInfo)}
          className="flex items-center gap-2"
        >
          <Info className="w-4 h-4" />
          {showMouseInfo ? 'إخفاء معلومات الماوس' : 'إظهار معلومات الماوس'}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* الحوار الأساسي مع تتبع الماوس */}
        <Button
          onClick={() => setBasicDialogOpen(true)}
          className="flex items-center gap-2 h-20 flex-col justify-center"
          variant="outline"
        >
          <MousePointer className="w-6 h-6" />
          <span>حوار يتبع الماوس</span>
        </Button>

        {/* حوار بدون تتبع الماوس */}
        <Button
          onClick={() => setStaticDialogOpen(true)}
          className="flex items-center gap-2 h-20 flex-col justify-center"
          variant="outline"
        >
          <Eye className="w-6 h-6" />
          <span>حوار ثابت</span>
        </Button>

        {/* حوار متقدم */}
        <Button
          onClick={() => setMouseFollowDialogOpen(true)}
          className="flex items-center gap-2 h-20 flex-col justify-center"
          variant="outline"
        >
          <Move className="w-6 h-6" />
          <span>حوار متقدم</span>
        </Button>

        {/* حوار الإعدادات */}
        <Button
          onClick={() => setSettingsDialogOpen(true)}
          className="flex items-center gap-2 h-20 flex-col justify-center"
          variant="outline"
        >
          <Settings className="w-6 h-6" />
          <span>إعدادات الحوار</span>
        </Button>
      </div>

      {/* الحوار الأساسي مع تتبع الماوس */}
      <Dialog 
        open={basicDialogOpen} 
        onOpenChange={setBasicDialogOpen}
        followMouse={true}
        centerOnOpen={true}
        smoothTransition={true}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MousePointer className="w-5 h-5 text-blue-600" />
              حوار يتبع الماوس
            </DialogTitle>
          </DialogHeader>
          <div className="p-6">
            <p className="text-gray-600 mb-4">
              هذا الحوار يتبع حركة الماوس ويتحرك معه بسلاسة. حرك الماوس لترى كيف يتفاعل الحوار!
            </p>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">الميزات:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• يتبع حركة الماوس</li>
                <li>• انتقال سلس</li>
                <li>• يبقى داخل حدود الشاشة</li>
                <li>• تأثيرات بصرية محسنة</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setBasicDialogOpen(false)}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار بدون تتبع الماوس */}
      <Dialog 
        open={staticDialogOpen} 
        onOpenChange={setStaticDialogOpen}
        followMouse={false}
        centerOnOpen={true}
        smoothTransition={false}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5 text-green-600" />
              حوار ثابت
            </DialogTitle>
          </DialogHeader>
          <div className="p-6">
            <p className="text-gray-600 mb-4">
              هذا الحوار يبقى في المركز ولا يتبع حركة الماوس. مناسب للمحتوى الذي يحتاج تركيز ثابت.
            </p>
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">الميزات:</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• موضع ثابت في المركز</li>
                <li>• لا يتأثر بحركة الماوس</li>
                <li>• مناسب للنماذج الطويلة</li>
                <li>• أداء محسن</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setStaticDialogOpen(false)}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار متقدم */}
      <Dialog 
        open={mouseFollowDialogOpen} 
        onOpenChange={setMouseFollowDialogOpen}
        followMouse={true}
        centerOnOpen={false}
        smoothTransition={true}
      >
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Move className="w-5 h-5 text-purple-600" />
              حوار متقدم مع تتبع الماوس
            </DialogTitle>
          </DialogHeader>
          <div className="p-6 space-y-4">
            <p className="text-gray-600">
              حوار متقدم مع ميزات إضافية وتفاعل محسن مع الماوس.
            </p>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-900 mb-2">التفاعل</h4>
                <p className="text-sm text-purple-800">
                  يتفاعل مع حركة الماوس بطريقة ذكية ومرنة
                </p>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-900 mb-2">الأداء</h4>
                <p className="text-sm text-orange-800">
                  محسن للأداء باستخدام requestAnimationFrame
                </p>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">تعليمات الاستخدام:</h4>
              <ol className="text-sm text-gray-700 space-y-1">
                <li>1. حرك الماوس لترى الحوار يتبعه</li>
                <li>2. لاحظ كيف يبقى داخل حدود الشاشة</li>
                <li>3. جرب تغيير حجم النافذة</li>
              </ol>
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setMouseFollowDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button onClick={() => setMouseFollowDialogOpen(false)}>
              موافق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار الإعدادات */}
      <Dialog 
        open={settingsDialogOpen} 
        onOpenChange={setSettingsDialogOpen}
        followMouse={true}
        centerOnOpen={true}
        smoothTransition={true}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-indigo-600" />
              إعدادات الحوار التفاعلي
            </DialogTitle>
          </DialogHeader>
          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">الخصائص المتاحة</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span className="text-sm font-medium">followMouse</span>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">true</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span className="text-sm font-medium">centerOnOpen</span>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">true</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span className="text-sm font-medium">smoothTransition</span>
                    <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">true</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">التحسينات</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>تحسين الأداء مع requestAnimationFrame</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>حماية من الخروج عن حدود الشاشة</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>انتقالات سلسة ومرنة</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span>تأثيرات بصرية محسنة</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-indigo-50 p-4 rounded-lg">
              <h4 className="font-semibold text-indigo-900 mb-2">💡 نصائح للاستخدام</h4>
              <ul className="text-sm text-indigo-800 space-y-1">
                <li>• استخدم followMouse=true للحوارات التفاعلية</li>
                <li>• استخدم followMouse=false للنماذج الطويلة</li>
                <li>• centerOnOpen=true يضع الحوار في المركز عند الفتح</li>
                <li>• smoothTransition=true يضيف انتقالات سلسة</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setSettingsDialogOpen(false)}>
              فهمت
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* أدوات تتبع الماوس */}
      <MouseTracker show={showMouseTracker} />
      <MouseInfoPanel show={showMouseInfo} position="top-right" />
    </div>
  )
}
