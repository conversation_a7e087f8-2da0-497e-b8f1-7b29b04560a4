"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface MouseTrackerProps {
  show?: boolean
  className?: string
}

interface MousePosition {
  x: number
  y: number
}

export default function MouseTracker({ show = false, className }: MouseTrackerProps) {
  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 })
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (!show) return

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
      setIsVisible(true)
    }

    const handleMouseLeave = () => {
      setIsVisible(false)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [show])

  if (!show || !isVisible) return null

  return (
    <>
      {/* مؤشر الماوس الرئيسي */}
      <div
        className={cn(
          "fixed w-4 h-4 bg-blue-500 rounded-full pointer-events-none z-[9999] opacity-70 transition-all duration-100 ease-out",
          "shadow-lg border-2 border-white",
          className
        )}
        style={{
          left: `${mousePosition.x - 8}px`,
          top: `${mousePosition.y - 8}px`,
          transform: 'translate3d(0, 0, 0)',
        }}
      />
      
      {/* الدائرة الخارجية */}
      <div
        className="fixed w-8 h-8 border-2 border-blue-400 rounded-full pointer-events-none z-[9998] opacity-50 transition-all duration-200 ease-out"
        style={{
          left: `${mousePosition.x - 16}px`,
          top: `${mousePosition.y - 16}px`,
          transform: 'translate3d(0, 0, 0)',
        }}
      />
      
      {/* معلومات الإحداثيات */}
      <div
        className="fixed bg-gray-900 text-white text-xs px-2 py-1 rounded shadow-lg pointer-events-none z-[9997] font-mono"
        style={{
          left: `${mousePosition.x + 15}px`,
          top: `${mousePosition.y - 25}px`,
          transform: 'translate3d(0, 0, 0)',
        }}
      >
        {mousePosition.x}, {mousePosition.y}
      </div>
    </>
  )
}

// مكون لإظهار معلومات تفصيلية عن الماوس
interface MouseInfoPanelProps {
  show?: boolean
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
}

export function MouseInfoPanel({ show = false, position = 'top-left' }: MouseInfoPanelProps) {
  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 })
  const [velocity, setVelocity] = useState<MousePosition>({ x: 0, y: 0 })
  const [lastPosition, setLastPosition] = useState<MousePosition>({ x: 0, y: 0 })
  const [lastTime, setLastTime] = useState<number>(Date.now())

  useEffect(() => {
    if (!show) return

    const handleMouseMove = (e: MouseEvent) => {
      const currentTime = Date.now()
      const deltaTime = currentTime - lastTime
      
      if (deltaTime > 0) {
        const deltaX = e.clientX - lastPosition.x
        const deltaY = e.clientY - lastPosition.y
        
        setVelocity({
          x: Math.round((deltaX / deltaTime) * 1000), // pixels per second
          y: Math.round((deltaY / deltaTime) * 1000)
        })
      }
      
      setMousePosition({ x: e.clientX, y: e.clientY })
      setLastPosition({ x: e.clientX, y: e.clientY })
      setLastTime(currentTime)
    }

    document.addEventListener('mousemove', handleMouseMove)

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
    }
  }, [show, lastPosition, lastTime])

  if (!show) return null

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  }

  return (
    <div className={cn(
      "fixed bg-gray-900/90 backdrop-blur-sm text-white p-4 rounded-lg shadow-xl z-[9996] font-mono text-sm",
      "border border-gray-700",
      positionClasses[position]
    )}>
      <div className="space-y-2">
        <div className="text-blue-400 font-semibold border-b border-gray-700 pb-2">
          معلومات الماوس
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-gray-400 text-xs">الإحداثيات</div>
            <div className="text-white">
              X: {mousePosition.x}px<br/>
              Y: {mousePosition.y}px
            </div>
          </div>
          
          <div>
            <div className="text-gray-400 text-xs">السرعة</div>
            <div className="text-white">
              X: {velocity.x}px/s<br/>
              Y: {velocity.y}px/s
            </div>
          </div>
        </div>
        
        <div>
          <div className="text-gray-400 text-xs">معلومات الشاشة</div>
          <div className="text-white text-xs">
            العرض: {window.innerWidth}px<br/>
            الارتفاع: {window.innerHeight}px
          </div>
        </div>
        
        <div>
          <div className="text-gray-400 text-xs">المنطقة</div>
          <div className="text-white text-xs">
            {mousePosition.x < window.innerWidth / 2 ? 'يسار' : 'يمين'} - 
            {mousePosition.y < window.innerHeight / 2 ? 'أعلى' : 'أسفل'}
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook مخصص لتتبع الماوس
export function useMousePosition() {
  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 })

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    document.addEventListener('mousemove', handleMouseMove)

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
    }
  }, [])

  return mousePosition
}

// Hook لحساب سرعة الماوس
export function useMouseVelocity() {
  const [velocity, setVelocity] = useState<MousePosition>({ x: 0, y: 0 })
  const [lastPosition, setLastPosition] = useState<MousePosition>({ x: 0, y: 0 })
  const [lastTime, setLastTime] = useState<number>(Date.now())

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const currentTime = Date.now()
      const deltaTime = currentTime - lastTime
      
      if (deltaTime > 0) {
        const deltaX = e.clientX - lastPosition.x
        const deltaY = e.clientY - lastPosition.y
        
        setVelocity({
          x: Math.round((deltaX / deltaTime) * 1000),
          y: Math.round((deltaY / deltaTime) * 1000)
        })
      }
      
      setLastPosition({ x: e.clientX, y: e.clientY })
      setLastTime(currentTime)
    }

    document.addEventListener('mousemove', handleMouseMove)

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
    }
  }, [lastPosition, lastTime])

  return velocity
}

// Hook للتحقق من منطقة الماوس
export function useMouseRegion() {
  const mousePosition = useMousePosition()
  
  const region = {
    horizontal: mousePosition.x < window.innerWidth / 2 ? 'left' : 'right',
    vertical: mousePosition.y < window.innerHeight / 2 ? 'top' : 'bottom',
    quadrant: `${mousePosition.y < window.innerHeight / 2 ? 'top' : 'bottom'}-${mousePosition.x < window.innerWidth / 2 ? 'left' : 'right'}`
  }
  
  return region
}
