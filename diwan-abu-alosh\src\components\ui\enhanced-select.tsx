"use client"

import * as React from "react"
import { ChevronDown, Check, Search, X } from "lucide-react"
import { cn } from "@/lib/utils"

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
  description?: string
}

interface EnhancedSelectProps {
  options: SelectOption[]
  value?: string
  defaultValue?: string
  placeholder?: string
  disabled?: boolean
  className?: string
  onChange?: (value: string) => void
  searchable?: boolean
  clearable?: boolean
  size?: 'sm' | 'md' | 'lg'
  error?: boolean
  helperText?: string
}

export function EnhancedSelect({
  options,
  value,
  defaultValue,
  placeholder = "اختر خياراً...",
  disabled = false,
  className,
  onChange,
  searchable = false,
  clearable = false,
  size = 'md',
  error = false,
  helperText
}: EnhancedSelectProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [selectedValue, setSelectedValue] = React.useState(value || defaultValue || '')
  const [searchTerm, setSearchTerm] = React.useState('')
  const selectRef = React.useRef<HTMLDivElement>(null)
  const searchInputRef = React.useRef<HTMLInputElement>(null)

  const sizeClasses = {
    sm: 'h-9 px-3 text-sm',
    md: 'h-11 px-4 text-base',
    lg: 'h-13 px-5 text-lg'
  }

  const filteredOptions = React.useMemo(() => {
    if (!searchable || !searchTerm) return options
    return options.filter(option =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      option.description?.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [options, searchTerm, searchable])

  const selectedOption = options.find(option => option.value === selectedValue)

  React.useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value)
    }
  }, [value])

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  React.useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isOpen, searchable])

  const handleSelect = (optionValue: string) => {
    setSelectedValue(optionValue)
    setIsOpen(false)
    setSearchTerm('')
    onChange?.(optionValue)
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    setSelectedValue('')
    setSearchTerm('')
    onChange?.('')
  }

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen)
      if (!isOpen) {
        setSearchTerm('')
      }
    }
  }

  return (
    <div ref={selectRef} className="relative w-full">
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className={cn(
          "relative w-full rounded-xl border-2 bg-white text-right font-medium transition-all duration-200 focus:outline-none focus:ring-4 disabled:cursor-not-allowed disabled:opacity-50",
          sizeClasses[size],
          error
            ? "border-red-300 text-red-700 focus:border-red-500 focus:ring-red-500/20"
            : "border-gray-300 text-black hover:border-gray-400 focus:border-blue-500 focus:ring-blue-500/20",
          isOpen && !error && "border-blue-500 ring-4 ring-blue-500/20",
          isOpen && error && "border-red-500 ring-4 ring-red-500/20",
          className
        )}
      >
        <span className={cn(
          "block truncate text-right",
          !selectedOption && "text-gray-500"
        )}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        
        <div className="absolute inset-y-0 left-0 flex items-center pl-3">
          {clearable && selectedValue && !disabled && (
            <button
              onClick={handleClear}
              className="ml-2 rounded-full p-1 hover:bg-gray-100 transition-colors"
              type="button"
            >
              <X className="h-4 w-4 text-gray-600" />
            </button>
          )}
          <ChevronDown
            className={cn(
              "h-5 w-5 text-gray-600 transition-transform duration-200",
              isOpen && "rotate-180"
            )}
          />
        </div>
      </button>

      {helperText && (
        <p className={cn(
          "mt-1 text-sm",
          error ? "text-red-600" : "text-gray-600"
        )}>
          {helperText}
        </p>
      )}

      {isOpen && (
        <div className="absolute z-50 mt-2 w-full rounded-xl border border-gray-300 bg-white shadow-xl backdrop-blur-sm">
          {searchable && (
            <div className="p-3 border-b border-gray-200">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="البحث في الخيارات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 pr-10 text-sm text-black focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                />
              </div>
            </div>
          )}
          
          <div className="max-h-60 overflow-auto py-1">
            {filteredOptions.length === 0 ? (
              <div className="px-4 py-6 text-center">
                <div className="text-gray-500 mb-2">
                  <Search className="h-8 w-8 mx-auto" />
                </div>
                <p className="text-sm text-gray-600 font-medium">
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد خيارات متاحة'}
                </p>
              </div>
            ) : (
              filteredOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => !option.disabled && handleSelect(option.value)}
                  disabled={option.disabled}
                  className={cn(
                    "relative w-full px-4 py-3 text-right transition-colors hover:bg-blue-50 focus:bg-blue-50 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50",
                    selectedValue === option.value && "bg-blue-100 text-blue-800 font-semibold"
                  )}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 text-right">
                      <div className="text-sm font-medium text-black">
                        {option.label}
                      </div>
                      {option.description && (
                        <div className="text-xs text-gray-600 mt-1">
                          {option.description}
                        </div>
                      )}
                    </div>
                    {selectedValue === option.value && (
                      <Check className="h-4 w-4 text-blue-600 mr-3" />
                    )}
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// مكونات مساعدة للاستخدام السريع
export function SearchableSelect(props: Omit<EnhancedSelectProps, 'searchable'>) {
  return <EnhancedSelect {...props} searchable />
}

export function ClearableSelect(props: Omit<EnhancedSelectProps, 'clearable'>) {
  return <EnhancedSelect {...props} clearable />
}

export function SearchableClearableSelect(props: Omit<EnhancedSelectProps, 'searchable' | 'clearable'>) {
  return <EnhancedSelect {...props} searchable clearable />
}

// مكون مبسط للاستخدام مع مصفوفة نصوص
interface SimpleSelectProps {
  options: string[]
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  searchable?: boolean
  clearable?: boolean
}

export function SimpleSelect({ options, searchable, clearable, ...props }: SimpleSelectProps) {
  const selectOptions: SelectOption[] = options.map(option => ({
    value: option,
    label: option
  }))

  return (
    <EnhancedSelect
      {...props}
      options={selectOptions}
      searchable={searchable}
      clearable={clearable}
    />
  )
}
