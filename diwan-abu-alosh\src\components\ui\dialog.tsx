"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { X } from "lucide-react"

interface DialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children: React.ReactNode
  enableScrollInteraction?: boolean // خيار لتفعيل التفاعل مع التمرير
  maxHeight?: string // الحد الأقصى للارتفاع
}

interface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const Dialog = ({
  open,
  onOpenChange,
  children,
  enableScrollInteraction = true,
  maxHeight = "90vh"
}: DialogProps) => {
  const dialogRef = React.useRef<HTMLDivElement>(null)
  const containerRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onOpenChange?.(false)
      }
    }

    const handleWheel = (e: WheelEvent) => {
      if (!open || !enableScrollInteraction || !dialogRef.current) return

      // السماح بالتمرير داخل الحوار فقط
      const dialogElement = dialogRef.current
      const isScrollable = dialogElement.scrollHeight > dialogElement.clientHeight

      if (isScrollable) {
        // التحقق من أن الماوس داخل منطقة الحوار
        const rect = dialogElement.getBoundingClientRect()
        const isInsideDialog = e.clientX >= rect.left && e.clientX <= rect.right &&
                              e.clientY >= rect.top && e.clientY <= rect.bottom

        if (isInsideDialog) {
          // السماح بالتمرير الطبيعي داخل الحوار
          return
        }
      }

      // منع التمرير خارج الحوار
      e.preventDefault()
    }

    if (open) {
      document.addEventListener('keydown', handleEscape)
      if (enableScrollInteraction) {
        document.addEventListener('wheel', handleWheel, { passive: false })
      }
      // عدم منع التمرير في الخلفية للسماح بالتمرير داخل الحوار
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.removeEventListener('wheel', handleWheel)
    }
  }, [open, onOpenChange, enableScrollInteraction])

  if (!open) return null

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
    >
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300"
        onClick={() => onOpenChange?.(false)}
      />
      <div
        ref={dialogRef}
        className={cn(
          "relative z-50 w-full overflow-hidden animate-in fade-in-0 zoom-in-95 duration-300",
          "smooth-scroll", // فئة CSS للتمرير السلس
          enableScrollInteraction ? "overflow-y-auto" : "overflow-hidden"
        )}
        style={{
          maxHeight: maxHeight,
          transform: 'translate3d(0, 0, 0)', // تحسين الأداء
          willChange: 'transform', // تحسين الأداء
        }}
      >
        {children}
      </div>
    </div>
  )
}

const DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200",
        "backdrop-blur-sm bg-white/95", // تحسين الشفافية
        "hover:shadow-3xl transition-shadow duration-300", // تأثير الظل عند التمرير
        "max-h-full overflow-y-auto smooth-scroll", // تمكين التمرير السلس
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
DialogContent.displayName = "DialogContent"

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200",
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200",
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

const DialogTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-xl font-bold leading-none tracking-tight text-slate-800",
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = "DialogTitle"

const DialogDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-slate-600 font-medium", className)}
    {...props}
  />
))
DialogDescription.displayName = "DialogDescription"

const DialogTrigger = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ className, ...props }, ref) => (
  <button
    ref={ref}
    className={className}
    {...props}
  />
))
DialogTrigger.displayName = "DialogTrigger"

const DialogClose = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & { onOpenChange?: (open: boolean) => void }
>(({ className, onOpenChange, ...props }, ref) => (
  <button
    ref={ref}
    type="button"
    onClick={() => onOpenChange?.(false)}
    className={cn(
      "absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10",
      className
    )}
    {...props}
  >
    <X className="h-4 w-4 text-slate-600" />
    <span className="sr-only">إغلاق</span>
  </button>
))
DialogClose.displayName = "DialogClose"

export {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
}
