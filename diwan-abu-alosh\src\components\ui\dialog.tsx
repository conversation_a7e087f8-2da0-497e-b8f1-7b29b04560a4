"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { X } from "lucide-react"

interface DialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children: React.ReactNode
  followMouse?: boolean // خيار لتفعيل/إلغاء تتبع الماوس
  centerOnOpen?: boolean // خيار لتوسيط الحوار عند الفتح
  smoothTransition?: boolean // خيار للانتقال السلس
}

interface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const Dialog = ({
  open,
  onOpenChange,
  children,
  followMouse = true,
  centerOnOpen = true,
  smoothTransition = true
}: DialogProps) => {
  const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 })
  const [dialogPosition, setDialogPosition] = React.useState({ x: 0, y: 0 })
  const [isInitialized, setIsInitialized] = React.useState(false)
  const dialogRef = React.useRef<HTMLDivElement>(null)
  const containerRef = React.useRef<HTMLDivElement>(null)
  const animationFrameRef = React.useRef<number>()

  // تهيئة موضع الحوار عند الفتح
  React.useEffect(() => {
    if (open && !isInitialized && dialogRef.current && centerOnOpen) {
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      const dialogRect = dialogRef.current.getBoundingClientRect()

      const centerX = (viewportWidth - dialogRect.width) / 2
      const centerY = (viewportHeight - dialogRect.height) / 2

      setDialogPosition({ x: centerX, y: centerY })
      setIsInitialized(true)
    } else if (!open) {
      setIsInitialized(false)
    }
  }, [open, centerOnOpen, isInitialized])

  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onOpenChange?.(false)
      }
    }

    const updateDialogPosition = (mouseX: number, mouseY: number) => {
      if (!dialogRef.current) return

      // الحصول على أبعاد الشاشة والحوار
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      const dialogRect = dialogRef.current.getBoundingClientRect()

      // حساب الموضع المثالي للحوار بناءً على موضع الماوس
      let newX = mouseX - dialogRect.width / 2
      let newY = mouseY - dialogRect.height / 2

      // التأكد من أن الحوار يبقى داخل حدود الشاشة مع هامش أمان
      const padding = 20
      newX = Math.max(padding, Math.min(newX, viewportWidth - dialogRect.width - padding))
      newY = Math.max(padding, Math.min(newY, viewportHeight - dialogRect.height - padding))

      // تطبيق التحريك السلس باستخدام requestAnimationFrame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        setDialogPosition({ x: newX, y: newY })
      })
    }

    const handleMouseMove = (e: MouseEvent) => {
      if (!open || !followMouse || !isInitialized) return

      const mouseX = e.clientX
      const mouseY = e.clientY
      setMousePosition({ x: mouseX, y: mouseY })

      updateDialogPosition(mouseX, mouseY)
    }

    const handleResize = () => {
      if (!open || !dialogRef.current) return

      // إعادة حساب الموضع عند تغيير حجم النافذة
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      const dialogRect = dialogRef.current.getBoundingClientRect()

      const padding = 20
      let newX = dialogPosition.x
      let newY = dialogPosition.y

      newX = Math.max(padding, Math.min(newX, viewportWidth - dialogRect.width - padding))
      newY = Math.max(padding, Math.min(newY, viewportHeight - dialogRect.height - padding))

      setDialogPosition({ x: newX, y: newY })
    }

    if (open) {
      document.addEventListener('keydown', handleEscape)
      if (followMouse) {
        document.addEventListener('mousemove', handleMouseMove, { passive: true })
      }
      window.addEventListener('resize', handleResize)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('resize', handleResize)
      document.body.style.overflow = 'unset'

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [open, onOpenChange, followMouse, isInitialized, dialogPosition])

  if (!open) return null

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 z-50"
    >
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300"
        onClick={() => onOpenChange?.(false)}
      />
      <div
        ref={dialogRef}
        className={cn(
          "absolute z-50 max-h-[90vh] overflow-y-auto animate-in fade-in-0 zoom-in-95 duration-300",
          smoothTransition ? "transition-all ease-out duration-200" : "",
          followMouse ? "cursor-move" : ""
        )}
        style={{
          left: `${dialogPosition.x}px`,
          top: `${dialogPosition.y}px`,
          transform: 'translate3d(0, 0, 0)', // تحسين الأداء
          willChange: 'transform', // تحسين الأداء
          transition: smoothTransition ? 'left 0.2s ease-out, top 0.2s ease-out' : 'none',
        }}
        onMouseEnter={() => {
          // إضافة تأثير بصري عند تمرير الماوس
          if (dialogRef.current && followMouse) {
            dialogRef.current.style.filter = 'drop-shadow(0 25px 25px rgb(0 0 0 / 0.15))'
          }
        }}
        onMouseLeave={() => {
          // إزالة التأثير البصري
          if (dialogRef.current) {
            dialogRef.current.style.filter = 'drop-shadow(0 20px 25px rgb(0 0 0 / 0.1))'
          }
        }}
      >
        {children}
      </div>

      {/* مؤشر بصري لموضع الماوس (اختياري) */}
      {followMouse && process.env.NODE_ENV === 'development' && (
        <div
          className="absolute w-2 h-2 bg-red-500 rounded-full pointer-events-none z-40 opacity-50"
          style={{
            left: `${mousePosition.x - 4}px`,
            top: `${mousePosition.y - 4}px`,
            transition: 'all 0.1s ease-out',
          }}
        />
      )}
    </div>
  )
}

const DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200 p-0 overflow-hidden",
        "backdrop-blur-sm bg-white/95", // تحسين الشفافية
        "hover:shadow-3xl transition-shadow duration-300", // تأثير الظل عند التمرير
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
DialogContent.displayName = "DialogContent"

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200",
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200",
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

const DialogTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-xl font-bold leading-none tracking-tight text-slate-800",
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = "DialogTitle"

const DialogDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-slate-600 font-medium", className)}
    {...props}
  />
))
DialogDescription.displayName = "DialogDescription"

const DialogTrigger = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ className, ...props }, ref) => (
  <button
    ref={ref}
    className={className}
    {...props}
  />
))
DialogTrigger.displayName = "DialogTrigger"

const DialogClose = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & { onOpenChange?: (open: boolean) => void }
>(({ className, onOpenChange, ...props }, ref) => (
  <button
    ref={ref}
    type="button"
    onClick={() => onOpenChange?.(false)}
    className={cn(
      "absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10",
      className
    )}
    {...props}
  >
    <X className="h-4 w-4 text-slate-600" />
    <span className="sr-only">إغلاق</span>
  </button>
))
DialogClose.displayName = "DialogClose"

export {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
}
