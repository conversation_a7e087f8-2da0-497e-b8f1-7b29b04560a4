{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/settings/security-settings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Switch } from '@/components/ui/switch'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\n// import { Textarea } from '@/components/ui/textarea'\nimport DetailedPermissions from './detailed-permissions'\nimport {\n  Shield,\n  Lock,\n  Clock,\n  Users,\n  CheckCircle,\n  History,\n  UserCheck,\n  Settings,\n  UserPlus,\n  Edit,\n  Trash2,\n  Mail,\n  Phone\n} from 'lucide-react'\n\ninterface SecuritySettingsProps {\n  settings: any\n  onChange: (settings: any) => void\n  canEdit: boolean\n}\n\ninterface SecuritySettingsData {\n  // سياسة كلمات المرور\n  passwordPolicy: {\n    minLength: number\n    requireUppercase: boolean\n    requireLowercase: boolean\n    requireNumbers: boolean\n    requireSpecialChars: boolean\n    preventReuse: number\n    expirationDays: number\n  }\n\n  // إعدادات الجلسة\n  sessionSettings: {\n    timeout: number // بالدقائق\n    maxConcurrentSessions: number\n    requireReauth: boolean\n    rememberMe: boolean\n    rememberMeDuration: number // بالأيام\n  }\n\n  // إعدادات تسجيل الدخول\n  loginSettings: {\n    maxFailedAttempts: number\n    lockoutDuration: number // بالدقائق\n    enableCaptcha: boolean\n    enableTwoFactor: boolean\n    allowedIPs: string[]\n    blockedIPs: string[]\n  }\n\n  // إعدادات التدقيق والسجلات\n  auditSettings: {\n    enableAuditLog: boolean\n    logLoginAttempts: boolean\n    logDataChanges: boolean\n    logSystemEvents: boolean\n    retentionDays: number\n    enableRealTimeAlerts: boolean\n  }\n\n  // إعدادات الصلاحيات\n  permissionSettings: {\n    defaultRole: string\n    allowSelfRegistration: boolean\n    requireAdminApproval: boolean\n    enableRoleHierarchy: boolean\n    maxUsersPerRole: {\n      ADMIN: number\n      DATA_ENTRY: number\n      VIEWER: number\n    }\n  }\n\n  // إعدادات الأمان المتقدمة\n  advancedSecurity: {\n    enableEncryption: boolean\n    enableSSL: boolean\n    enableCSRF: boolean\n    enableXSS: boolean\n    enableSQLInjection: boolean\n    enableRateLimit: boolean\n    rateLimitRequests: number\n    rateLimitWindow: number // بالدقائق\n  }\n}\n\nconst defaultSettings: SecuritySettingsData = {\n  passwordPolicy: {\n    minLength: 8,\n    requireUppercase: true,\n    requireLowercase: true,\n    requireNumbers: true,\n    requireSpecialChars: false,\n    preventReuse: 5,\n    expirationDays: 90\n  },\n\n  sessionSettings: {\n    timeout: 30,\n    maxConcurrentSessions: 3,\n    requireReauth: false,\n    rememberMe: true,\n    rememberMeDuration: 30\n  },\n\n  loginSettings: {\n    maxFailedAttempts: 5,\n    lockoutDuration: 15,\n    enableCaptcha: false,\n    enableTwoFactor: false,\n    allowedIPs: [],\n    blockedIPs: []\n  },\n\n  auditSettings: {\n    enableAuditLog: true,\n    logLoginAttempts: true,\n    logDataChanges: true,\n    logSystemEvents: true,\n    retentionDays: 365,\n    enableRealTimeAlerts: true\n  },\n\n  permissionSettings: {\n    defaultRole: 'VIEWER',\n    allowSelfRegistration: false,\n    requireAdminApproval: true,\n    enableRoleHierarchy: true,\n    maxUsersPerRole: {\n      ADMIN: 3,\n      DATA_ENTRY: 10,\n      VIEWER: 100\n    }\n  },\n\n  advancedSecurity: {\n    enableEncryption: true,\n    enableSSL: true,\n    enableCSRF: true,\n    enableXSS: true,\n    enableSQLInjection: true,\n    enableRateLimit: true,\n    rateLimitRequests: 100,\n    rateLimitWindow: 15\n  }\n}\n\nexport default function SecuritySettings({ settings, onChange, canEdit }: SecuritySettingsProps) {\n  const [localSettings, setLocalSettings] = useState<SecuritySettingsData>(defaultSettings)\n  const [showAdvanced, setShowAdvanced] = useState(false)\n  const [showUserManagement, setShowUserManagement] = useState(false)\n  const [users, setUsers] = useState<any[]>([])\n  const [showAddUser, setShowAddUser] = useState(false)\n  const [newUser, setNewUser] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    role: 'VIEWER',\n    password: '',\n    confirmPassword: ''\n  })\n  const [editingUser, setEditingUser] = useState<any>(null)\n  const [loading, setLoading] = useState(false)\n  const [showDetailedPermissions, setShowDetailedPermissions] = useState(false)\n\n  useEffect(() => {\n    if (settings) {\n      setLocalSettings({ ...defaultSettings, ...settings })\n    }\n  }, [settings])\n\n  // تحميل المستخدمين\n  useEffect(() => {\n    if (showUserManagement) {\n      loadUsers()\n    }\n  }, [showUserManagement])\n\n  const loadUsers = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/admin/users')\n      if (response.ok) {\n        const data = await response.json()\n        setUsers(data)\n      }\n    } catch (error) {\n      console.error('خطأ في تحميل المستخدمين:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChange = (key: string, value: any) => {\n    const keys = key.split('.')\n    let newSettings = { ...localSettings }\n\n    if (keys.length === 1) {\n      newSettings = { ...newSettings, [keys[0]]: value }\n    } else if (keys.length === 2) {\n      newSettings = {\n        ...newSettings,\n        [keys[0]]: {\n          ...newSettings[keys[0] as keyof SecuritySettingsData],\n          [keys[1]]: value\n        }\n      }\n    } else if (keys.length === 3) {\n      newSettings = {\n        ...newSettings,\n        [keys[0]]: {\n          ...newSettings[keys[0] as keyof SecuritySettingsData],\n          [keys[1]]: {\n            ...(newSettings[keys[0] as keyof SecuritySettingsData] as any)[keys[1]],\n            [keys[2]]: value\n          }\n        }\n      }\n    }\n\n    setLocalSettings(newSettings)\n    onChange(newSettings)\n  }\n\n  // إضافة مستخدم جديد\n  const handleAddUser = async () => {\n    if (!newUser.name || !newUser.email || !newUser.password) {\n      alert('يرجى ملء جميع الحقول المطلوبة')\n      return\n    }\n\n    if (newUser.password !== newUser.confirmPassword) {\n      alert('كلمات المرور غير متطابقة')\n      return\n    }\n\n    try {\n      setLoading(true)\n      const response = await fetch('/api/admin/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: newUser.name,\n          email: newUser.email,\n          phone: newUser.phone,\n          role: newUser.role,\n          password: newUser.password\n        }),\n      })\n\n      if (response.ok) {\n        alert('تم إضافة المستخدم بنجاح')\n        setNewUser({\n          name: '',\n          email: '',\n          phone: '',\n          role: 'VIEWER',\n          password: '',\n          confirmPassword: ''\n        })\n        setShowAddUser(false)\n        loadUsers()\n      } else {\n        const error = await response.json()\n        alert(error.message || 'فشل في إضافة المستخدم')\n      }\n    } catch (error) {\n      console.error('خطأ في إضافة المستخدم:', error)\n      alert('حدث خطأ أثناء إضافة المستخدم')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // تحديث مستخدم\n  const handleUpdateUser = async (userId: string, updates: any) => {\n    try {\n      setLoading(true)\n      const response = await fetch(`/api/admin/users/${userId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(updates),\n      })\n\n      if (response.ok) {\n        alert('تم تحديث المستخدم بنجاح')\n        setEditingUser(null)\n        loadUsers()\n      } else {\n        const error = await response.json()\n        alert(error.message || 'فشل في تحديث المستخدم')\n      }\n    } catch (error) {\n      console.error('خطأ في تحديث المستخدم:', error)\n      alert('حدث خطأ أثناء تحديث المستخدم')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // حذف مستخدم\n  const handleDeleteUser = async (userId: string, userName: string) => {\n    if (!confirm(`هل أنت متأكد من حذف المستخدم \"${userName}\"؟\\n\\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً ولا يمكن التراجع عن هذا الإجراء.`)) {\n      return\n    }\n\n    try {\n      setLoading(true)\n      const response = await fetch(`/api/admin/users/${userId}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        alert('تم حذف المستخدم بنجاح')\n        loadUsers()\n      } else {\n        const error = await response.json()\n        alert(error.message || 'فشل في حذف المستخدم')\n      }\n    } catch (error) {\n      console.error('خطأ في حذف المستخدم:', error)\n      alert('حدث خطأ أثناء حذف المستخدم')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getRoleText = (role: string) => {\n    switch (role) {\n      case 'ADMIN': return 'مدير'\n      case 'DATA_ENTRY': return 'مدخل بيانات'\n      case 'VIEWER': return 'مطلع'\n      case 'MEMBER_VIEWER': return 'مطلع على عضو'\n      case 'GALLERY_VIEWER': return 'مطلع على المعرض'\n      case 'MEMBER': return 'عضو'\n      default: return role\n    }\n  }\n\n  const getRoleBadgeColor = (role: string) => {\n    switch (role) {\n      case 'ADMIN': return 'bg-red-100 text-red-800'\n      case 'DATA_ENTRY': return 'bg-blue-100 text-blue-800'\n      case 'VIEWER': return 'bg-green-100 text-green-800'\n      case 'MEMBER_VIEWER': return 'bg-purple-100 text-purple-800'\n      case 'GALLERY_VIEWER': return 'bg-orange-100 text-orange-800'\n      case 'MEMBER': return 'bg-gray-100 text-gray-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Password policy */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Lock className=\"w-5 h-5 text-blue-600\" />\n            سياسة كلمات المرور\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"minLength\">الحد الأدنى لطول كلمة المرور</Label>\n              <Select\n                value={localSettings.passwordPolicy.minLength.toString()}\n                onValueChange={(value) => handleChange('passwordPolicy.minLength', parseInt(value))}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"6\">6 أحرف</SelectItem>\n                  <SelectItem value=\"8\">8 أحرف</SelectItem>\n                  <SelectItem value=\"10\">10 أحرف</SelectItem>\n                  <SelectItem value=\"12\">12 حرف</SelectItem>\n                  <SelectItem value=\"16\">16 حرف</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"expirationDays\">انتهاء صلاحية كلمة المرور (يوم)</Label>\n              <Select\n                value={localSettings.passwordPolicy.expirationDays.toString()}\n                onValueChange={(value) => handleChange('passwordPolicy.expirationDays', parseInt(value))}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"30\">30 يوم</SelectItem>\n                  <SelectItem value=\"60\">60 يوم</SelectItem>\n                  <SelectItem value=\"90\">90 يوم</SelectItem>\n                  <SelectItem value=\"180\">180 يوم</SelectItem>\n                  <SelectItem value=\"365\">سنة واحدة</SelectItem>\n                  <SelectItem value=\"0\">بدون انتهاء</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>يجب أن تحتوي على أحرف كبيرة</Label>\n                <p className=\"text-sm text-gray-600\">A-Z</p>\n              </div>\n              <Switch\n                checked={localSettings.passwordPolicy.requireUppercase}\n                onCheckedChange={(checked) => handleChange('passwordPolicy.requireUppercase', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>يجب أن تحتوي على أحرف صغيرة</Label>\n                <p className=\"text-sm text-gray-600\">a-z</p>\n              </div>\n              <Switch\n                checked={localSettings.passwordPolicy.requireLowercase}\n                onCheckedChange={(checked) => handleChange('passwordPolicy.requireLowercase', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>يجب أن تحتوي على أرقام</Label>\n                <p className=\"text-sm text-gray-600\">0-9</p>\n              </div>\n              <Switch\n                checked={localSettings.passwordPolicy.requireNumbers}\n                onCheckedChange={(checked) => handleChange('passwordPolicy.requireNumbers', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>يجب أن تحتوي على رموز خاصة</Label>\n                <p className=\"text-sm text-gray-600\">!@#$%^&*</p>\n              </div>\n              <Switch\n                checked={localSettings.passwordPolicy.requireSpecialChars}\n                onCheckedChange={(checked) => handleChange('passwordPolicy.requireSpecialChars', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"preventReuse\">منع إعادة استخدام كلمات المرور السابقة</Label>\n            <Select\n              value={localSettings.passwordPolicy.preventReuse.toString()}\n              onValueChange={(value) => handleChange('passwordPolicy.preventReuse', parseInt(value))}\n              disabled={!canEdit}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"0\">السماح بإعادة الاستخدام</SelectItem>\n                <SelectItem value=\"3\">آخر 3 كلمات مرور</SelectItem>\n                <SelectItem value=\"5\">آخر 5 كلمات مرور</SelectItem>\n                <SelectItem value=\"10\">آخر 10 كلمات مرور</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Session settings */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Clock className=\"w-5 h-5 text-green-600\" />\n            إعدادات الجلسة\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>مدة انتهاء الجلسة (دقيقة)</Label>\n              <Select\n                value={localSettings.sessionSettings.timeout.toString()}\n                onValueChange={(value) => handleChange('sessionSettings.timeout', parseInt(value))}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"15\">15 دقيقة</SelectItem>\n                  <SelectItem value=\"30\">30 دقيقة</SelectItem>\n                  <SelectItem value=\"60\">ساعة واحدة</SelectItem>\n                  <SelectItem value=\"120\">ساعتان</SelectItem>\n                  <SelectItem value=\"480\">8 ساعات</SelectItem>\n                  <SelectItem value=\"1440\">24 ساعة</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>الحد الأقصى للجلسات المتزامنة</Label>\n              <Select\n                value={localSettings.sessionSettings.maxConcurrentSessions.toString()}\n                onValueChange={(value) => handleChange('sessionSettings.maxConcurrentSessions', parseInt(value))}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"1\">جلسة واحدة فقط</SelectItem>\n                  <SelectItem value=\"2\">جلستان</SelectItem>\n                  <SelectItem value=\"3\">3 جلسات</SelectItem>\n                  <SelectItem value=\"5\">5 جلسات</SelectItem>\n                  <SelectItem value=\"10\">10 جلسات</SelectItem>\n                  <SelectItem value=\"0\">بدون حد</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تذكرني</Label>\n                <p className=\"text-sm text-gray-600\">السماح بحفظ بيانات تسجيل الدخول</p>\n              </div>\n              <Switch\n                checked={localSettings.sessionSettings.rememberMe}\n                onCheckedChange={(checked) => handleChange('sessionSettings.rememberMe', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            {localSettings.sessionSettings.rememberMe && (\n              <div className=\"space-y-2\">\n                <Label>مدة \"تذكرني\" (يوم)</Label>\n                <Select\n                  value={localSettings.sessionSettings.rememberMeDuration.toString()}\n                  onValueChange={(value) => handleChange('sessionSettings.rememberMeDuration', parseInt(value))}\n                  disabled={!canEdit}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"7\">أسبوع واحد</SelectItem>\n                    <SelectItem value=\"30\">شهر واحد</SelectItem>\n                    <SelectItem value=\"90\">3 أشهر</SelectItem>\n                    <SelectItem value=\"365\">سنة واحدة</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            )}\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>إعادة المصادقة للعمليات الحساسة</Label>\n                <p className=\"text-sm text-gray-600\">طلب كلمة المرور مرة أخرى</p>\n              </div>\n              <Switch\n                checked={localSettings.sessionSettings.requireReauth}\n                onCheckedChange={(checked) => handleChange('sessionSettings.requireReauth', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Login settings */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <UserCheck className=\"w-5 h-5 text-purple-600\" />\n            إعدادات تسجيل الدخول\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>الحد الأقصى لمحاولات الدخول الفاشلة</Label>\n              <Select\n                value={localSettings.loginSettings.maxFailedAttempts.toString()}\n                onValueChange={(value) => handleChange('loginSettings.maxFailedAttempts', parseInt(value))}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"3\">3 محاولات</SelectItem>\n                  <SelectItem value=\"5\">5 محاولات</SelectItem>\n                  <SelectItem value=\"10\">10 محاولات</SelectItem>\n                  <SelectItem value=\"0\">بدون حد</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>مدة الحظر (دقيقة)</Label>\n              <Select\n                value={localSettings.loginSettings.lockoutDuration.toString()}\n                onValueChange={(value) => handleChange('loginSettings.lockoutDuration', parseInt(value))}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"5\">5 دقائق</SelectItem>\n                  <SelectItem value=\"15\">15 دقيقة</SelectItem>\n                  <SelectItem value=\"30\">30 دقيقة</SelectItem>\n                  <SelectItem value=\"60\">ساعة واحدة</SelectItem>\n                  <SelectItem value=\"1440\">24 ساعة</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تفعيل CAPTCHA</Label>\n                <p className=\"text-sm text-gray-600\">التحقق من أنك لست روبوت</p>\n              </div>\n              <Switch\n                checked={localSettings.loginSettings.enableCaptcha}\n                onCheckedChange={(checked) => handleChange('loginSettings.enableCaptcha', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>المصادقة الثنائية</Label>\n                <p className=\"text-sm text-gray-600\">طبقة أمان إضافية</p>\n              </div>\n              <Switch\n                checked={localSettings.loginSettings.enableTwoFactor}\n                onCheckedChange={(checked) => handleChange('loginSettings.enableTwoFactor', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* إعدادات التدقيق */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <History className=\"w-5 h-5 text-orange-600\" />\n            إعدادات التدقيق والسجلات\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تفعيل سجل التدقيق</Label>\n                <p className=\"text-sm text-gray-600\">تسجيل جميع العمليات</p>\n              </div>\n              <Switch\n                checked={localSettings.auditSettings.enableAuditLog}\n                onCheckedChange={(checked) => handleChange('auditSettings.enableAuditLog', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تسجيل محاولات الدخول</Label>\n                <p className=\"text-sm text-gray-600\">الناجحة والفاشلة</p>\n              </div>\n              <Switch\n                checked={localSettings.auditSettings.logLoginAttempts}\n                onCheckedChange={(checked) => handleChange('auditSettings.logLoginAttempts', checked)}\n                disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تسجيل تغييرات البيانات</Label>\n                <p className=\"text-sm text-gray-600\">إضافة، تعديل، حذف</p>\n              </div>\n              <Switch\n                checked={localSettings.auditSettings.logDataChanges}\n                onCheckedChange={(checked) => handleChange('auditSettings.logDataChanges', checked)}\n                disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تسجيل أحداث النظام</Label>\n                <p className=\"text-sm text-gray-600\">بدء التشغيل، الأخطاء، التحديثات</p>\n              </div>\n              <Switch\n                checked={localSettings.auditSettings.logSystemEvents}\n                onCheckedChange={(checked) => handleChange('auditSettings.logSystemEvents', checked)}\n                disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>التنبيهات الفورية</Label>\n                <p className=\"text-sm text-gray-600\">إشعار فوري للأحداث المهمة</p>\n              </div>\n              <Switch\n                checked={localSettings.auditSettings.enableRealTimeAlerts}\n                onCheckedChange={(checked) => handleChange('auditSettings.enableRealTimeAlerts', checked)}\n                disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label>مدة الاحتفاظ بالسجلات (يوم)</Label>\n            <Select\n              value={localSettings.auditSettings.retentionDays.toString()}\n              onValueChange={(value) => handleChange('auditSettings.retentionDays', parseInt(value))}\n              disabled={!canEdit || !localSettings.auditSettings.enableAuditLog}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"30\">30 يوم</SelectItem>\n                <SelectItem value=\"90\">90 يوم</SelectItem>\n                <SelectItem value=\"180\">180 يوم</SelectItem>\n                <SelectItem value=\"365\">سنة واحدة</SelectItem>\n                <SelectItem value=\"1095\">3 سنوات</SelectItem>\n                <SelectItem value=\"0\">دائماً</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* إدارة المستخدمين */}\n      <Card className=\"border-2 border-indigo-100 shadow-lg\">\n        <CardHeader className=\"bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-t-lg\">\n          <CardTitle className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-2 bg-white bg-opacity-20 rounded-lg\">\n                <UserPlus className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">إدارة المستخدمين</h3>\n                <p className=\"text-indigo-100 text-sm mt-1\">إضافة وتعديل وحذف حسابات المستخدمين</p>\n              </div>\n            </div>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setShowUserManagement(!showUserManagement)}\n              disabled={!canEdit}\n              className=\"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30\"\n            >\n              {showUserManagement ? 'إخفاء' : 'عرض'} المستخدمين\n            </Button>\n          </CardTitle>\n        </CardHeader>\n        {showUserManagement && (\n          <CardContent className=\"space-y-6 bg-gradient-to-br from-gray-50 to-indigo-50\">\n            {/* زر إضافة مستخدم جديد */}\n            <div className=\"flex justify-between items-center p-6 bg-white rounded-xl shadow-sm border border-gray-100\">\n              <div>\n                <h3 className=\"text-xl font-bold text-gray-900 flex items-center gap-2\">\n                  <Users className=\"w-5 h-5 text-indigo-600\" />\n                  قائمة المستخدمين\n                </h3>\n                <p className=\"text-sm text-gray-600 mt-1\">إدارة حسابات المستخدمين وصلاحياتهم في النظام</p>\n                <div className=\"flex items-center gap-4 mt-2 text-xs text-gray-500\">\n                  <span>إجمالي المستخدمين: {users.length}</span>\n                  <span>•</span>\n                  <span>المديرون: {users.filter(u => u.role === 'ADMIN').length}</span>\n                  <span>•</span>\n                  <span>مدخلو البيانات: {users.filter(u => u.role === 'DATA_ENTRY').length}</span>\n                  <span>•</span>\n                  <span>المطلعون: {users.filter(u => u.role === 'VIEWER').length}</span>\n                </div>\n              </div>\n              <div className=\"flex gap-3\">\n                <Button\n                  onClick={() => setShowDetailedPermissions(true)}\n                  disabled={!canEdit || loading}\n                  variant=\"outline\"\n                  className=\"border-indigo-300 text-indigo-600 hover:bg-indigo-50\"\n                >\n                  <Settings className=\"w-4 h-4 ml-2\" />\n                  الصلاحيات المفصلة\n                </Button>\n                <Button\n                  onClick={() => setShowAddUser(true)}\n                  disabled={!canEdit || loading}\n                  className=\"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300\"\n                >\n                  <UserPlus className=\"w-4 h-4 ml-2\" />\n                  إضافة مستخدم\n                </Button>\n              </div>\n            </div>\n\n            {/* قائمة المستخدمين */}\n            {loading ? (\n              <div className=\"flex items-center justify-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"></div>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {users.length === 0 ? (\n                  <div className=\"text-center py-12 bg-white rounded-xl border-2 border-dashed border-gray-200\">\n                    <div className=\"w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <Users className=\"w-10 h-10 text-indigo-400\" />\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">لا توجد مستخدمين مسجلين</h3>\n                    <p className=\"text-gray-600 mb-4\">ابدأ بإضافة أول مستخدم في النظام</p>\n                    <Button\n                      onClick={() => setShowAddUser(true)}\n                      disabled={!canEdit || loading}\n                      className=\"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white\"\n                    >\n                      <UserPlus className=\"w-4 h-4 ml-2\" />\n                      إضافة أول مستخدم\n                    </Button>\n                  </div>\n                ) : (\n                  users.map((user) => (\n                    <div key={user.id} className=\"group flex items-center justify-between p-6 border border-gray-200 rounded-xl bg-white hover:bg-gradient-to-r hover:from-indigo-50 hover:to-blue-50 hover:border-indigo-200 transition-all duration-300 shadow-sm hover:shadow-md\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-4\">\n                          <div className=\"w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\">\n                            <span className=\"text-white font-bold text-lg\">\n                              {user.name.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div>\n                            <h4 className=\"font-bold text-gray-900 text-lg group-hover:text-indigo-700 transition-colors duration-300\">{user.name}</h4>\n                            <div className=\"flex items-center gap-6 text-sm text-gray-600 mt-1\">\n                              <span className=\"flex items-center gap-2\">\n                                <Mail className=\"w-4 h-4 text-indigo-500\" />\n                                {user.email}\n                              </span>\n                              {user.phone && (\n                                <span className=\"flex items-center gap-2\">\n                                  <Phone className=\"w-4 h-4 text-green-500\" />\n                                  {user.phone}\n                                </span>\n                              )}\n                              <span className=\"text-xs text-gray-500\">\n                                انضم في {new Date(user.createdAt).toLocaleDateString('ar-SA')}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center gap-4\">\n                        <Badge className={`${getRoleBadgeColor(user.role)} px-3 py-1 text-sm font-semibold`}>\n                          {getRoleText(user.role)}\n                        </Badge>\n                        {canEdit && (\n                          <div className=\"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => setEditingUser(user)}\n                              disabled={loading}\n                              className=\"bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300\"\n                            >\n                              <Edit className=\"w-4 h-4\" />\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => handleDeleteUser(user.id, user.name)}\n                              disabled={loading}\n                              className=\"bg-red-50 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300\"\n                            >\n                              <Trash2 className=\"w-4 h-4\" />\n                            </Button>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            )}\n          </CardContent>\n        )}\n      </Card>\n\n      {/* إعدادات الصلاحيات */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Users className=\"w-5 h-5 text-red-600\" />\n            إعدادات الصلاحيات\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label>الدور الافتراضي للمستخدمين الجدد</Label>\n            <Select\n              value={localSettings.permissionSettings.defaultRole}\n              onValueChange={(value) => handleChange('permissionSettings.defaultRole', value)}\n              disabled={!canEdit}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"VIEWER\">مطلع (عرض فقط)</SelectItem>\n                <SelectItem value=\"DATA_ENTRY\">مدخل بيانات</SelectItem>\n                <SelectItem value=\"ADMIN\">مدير (غير مستحسن)</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>السماح بالتسجيل الذاتي</Label>\n                <p className=\"text-sm text-gray-600\">المستخدمون يمكنهم إنشاء حسابات</p>\n              </div>\n              <Switch\n                checked={localSettings.permissionSettings.allowSelfRegistration}\n                onCheckedChange={(checked) => handleChange('permissionSettings.allowSelfRegistration', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>يتطلب موافقة المدير</Label>\n                <p className=\"text-sm text-gray-600\">الحسابات الجديدة تحتاج موافقة</p>\n              </div>\n              <Switch\n                checked={localSettings.permissionSettings.requireAdminApproval}\n                onCheckedChange={(checked) => handleChange('permissionSettings.requireAdminApproval', checked)}\n                disabled={!canEdit || !localSettings.permissionSettings.allowSelfRegistration}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تفعيل التسلسل الهرمي للأدوار</Label>\n                <p className=\"text-sm text-gray-600\">المدير يمكنه إدارة جميع الأدوار</p>\n              </div>\n              <Switch\n                checked={localSettings.permissionSettings.enableRoleHierarchy}\n                onCheckedChange={(checked) => handleChange('permissionSettings.enableRoleHierarchy', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-3\">\n            <Label>الحد الأقصى للمستخدمين لكل دور</Label>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"maxAdmins\">المديرون</Label>\n                <Input\n                  id=\"maxAdmins\"\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"10\"\n                  value={localSettings.permissionSettings.maxUsersPerRole.ADMIN}\n                  onChange={(e) => handleChange('permissionSettings.maxUsersPerRole.ADMIN', parseInt(e.target.value))}\n                  disabled={!canEdit}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"maxDataEntry\">مدخلو البيانات</Label>\n                <Input\n                  id=\"maxDataEntry\"\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"50\"\n                  value={localSettings.permissionSettings.maxUsersPerRole.DATA_ENTRY}\n                  onChange={(e) => handleChange('permissionSettings.maxUsersPerRole.DATA_ENTRY', parseInt(e.target.value))}\n                  disabled={!canEdit}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"maxViewers\">المطلعون</Label>\n                <Input\n                  id=\"maxViewers\"\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"1000\"\n                  value={localSettings.permissionSettings.maxUsersPerRole.VIEWER}\n                  onChange={(e) => handleChange('permissionSettings.maxUsersPerRole.VIEWER', parseInt(e.target.value))}\n                  disabled={!canEdit}\n                />\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* الإعدادات المتقدمة */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              <Shield className=\"w-5 h-5 text-red-600\" />\n              إعدادات الأمان المتقدمة\n            </div>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setShowAdvanced(!showAdvanced)}\n            >\n              {showAdvanced ? 'إخفاء' : 'عرض'} المتقدم\n            </Button>\n          </CardTitle>\n        </CardHeader>\n        {showAdvanced && (\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-1\">\n                  <Label>تشفير البيانات</Label>\n                  <p className=\"text-sm text-gray-600\">تشفير البيانات الحساسة</p>\n                </div>\n                <Switch\n                  checked={localSettings.advancedSecurity.enableEncryption}\n                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableEncryption', checked)}\n                  disabled={!canEdit}\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-1\">\n                  <Label>إجبار SSL/HTTPS</Label>\n                  <p className=\"text-sm text-gray-600\">اتصال آمن فقط</p>\n                </div>\n                <Switch\n                  checked={localSettings.advancedSecurity.enableSSL}\n                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableSSL', checked)}\n                  disabled={!canEdit}\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-1\">\n                  <Label>حماية CSRF</Label>\n                  <p className=\"text-sm text-gray-600\">منع هجمات التزوير</p>\n                </div>\n                <Switch\n                  checked={localSettings.advancedSecurity.enableCSRF}\n                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableCSRF', checked)}\n                  disabled={!canEdit}\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-1\">\n                  <Label>حماية XSS</Label>\n                  <p className=\"text-sm text-gray-600\">منع هجمات البرمجة النصية</p>\n                </div>\n                <Switch\n                  checked={localSettings.advancedSecurity.enableXSS}\n                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableXSS', checked)}\n                  disabled={!canEdit}\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-1\">\n                  <Label>حماية SQL Injection</Label>\n                  <p className=\"text-sm text-gray-600\">منع هجمات قواعد البيانات</p>\n                </div>\n                <Switch\n                  checked={localSettings.advancedSecurity.enableSQLInjection}\n                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableSQLInjection', checked)}\n                  disabled={!canEdit}\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-1\">\n                  <Label>تحديد معدل الطلبات</Label>\n                  <p className=\"text-sm text-gray-600\">منع الهجمات المكثفة</p>\n                </div>\n                <Switch\n                  checked={localSettings.advancedSecurity.enableRateLimit}\n                  onCheckedChange={(checked) => handleChange('advancedSecurity.enableRateLimit', checked)}\n                  disabled={!canEdit}\n                />\n              </div>\n            </div>\n\n            {localSettings.advancedSecurity.enableRateLimit && (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"rateLimitRequests\">عدد الطلبات المسموحة</Label>\n                  <Input\n                    id=\"rateLimitRequests\"\n                    type=\"number\"\n                    min=\"10\"\n                    max=\"1000\"\n                    value={localSettings.advancedSecurity.rateLimitRequests}\n                    onChange={(e) => handleChange('advancedSecurity.rateLimitRequests', parseInt(e.target.value))}\n                    disabled={!canEdit}\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"rateLimitWindow\">النافزة الزمنية (دقيقة)</Label>\n                  <Input\n                    id=\"rateLimitWindow\"\n                    type=\"number\"\n                    min=\"1\"\n                    max=\"60\"\n                    value={localSettings.advancedSecurity.rateLimitWindow}\n                    onChange={(e) => handleChange('advancedSecurity.rateLimitWindow', parseInt(e.target.value))}\n                    disabled={!canEdit}\n                  />\n                </div>\n              </div>\n            )}\n          </CardContent>\n        )}\n      </Card>\n\n      {/* نموذج إضافة مستخدم جديد */}\n      {showAddUser && (\n        <Card className=\"border-2 border-indigo-200 bg-gradient-to-br from-indigo-50 to-blue-50 shadow-xl\">\n          <CardHeader className=\"bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-t-lg\">\n            <CardTitle className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 bg-white bg-opacity-20 rounded-lg\">\n                  <UserPlus className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold\">إضافة مستخدم جديد</h3>\n                  <p className=\"text-indigo-100 text-sm mt-1\">إنشاء حساب مستخدم جديد في النظام</p>\n                </div>\n              </div>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setShowAddUser(false)}\n                className=\"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30\"\n              >\n                إلغاء\n              </Button>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"newUserName\">الاسم الكامل *</Label>\n                <Input\n                  id=\"newUserName\"\n                  value={newUser.name}\n                  onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}\n                  placeholder=\"أدخل الاسم الكامل\"\n                  disabled={loading}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"newUserEmail\">البريد الإلكتروني *</Label>\n                <Input\n                  id=\"newUserEmail\"\n                  type=\"email\"\n                  value={newUser.email}\n                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}\n                  placeholder=\"أدخل البريد الإلكتروني\"\n                  disabled={loading}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"newUserPhone\">رقم الهاتف</Label>\n                <Input\n                  id=\"newUserPhone\"\n                  value={newUser.phone}\n                  onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}\n                  placeholder=\"أدخل رقم الهاتف\"\n                  disabled={loading}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"newUserRole\">الصلاحية *</Label>\n                <Select\n                  value={newUser.role}\n                  onValueChange={(value) => setNewUser({ ...newUser, role: value })}\n                  disabled={loading}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"VIEWER\">مطلع (عرض فقط)</SelectItem>\n                    <SelectItem value=\"DATA_ENTRY\">مدخل بيانات</SelectItem>\n                    <SelectItem value=\"MEMBER_VIEWER\">مطلع على عضو معين</SelectItem>\n                    <SelectItem value=\"GALLERY_VIEWER\">مطلع على المعرض فقط</SelectItem>\n                    <SelectItem value=\"ADMIN\">مدير</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"newUserPassword\">كلمة المرور *</Label>\n                <Input\n                  id=\"newUserPassword\"\n                  type=\"password\"\n                  value={newUser.password}\n                  onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}\n                  placeholder=\"أدخل كلمة المرور\"\n                  disabled={loading}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"newUserConfirmPassword\">تأكيد كلمة المرور *</Label>\n                <Input\n                  id=\"newUserConfirmPassword\"\n                  type=\"password\"\n                  value={newUser.confirmPassword}\n                  onChange={(e) => setNewUser({ ...newUser, confirmPassword: e.target.value })}\n                  placeholder=\"أعد إدخال كلمة المرور\"\n                  disabled={loading}\n                />\n              </div>\n            </div>\n\n            <div className=\"flex justify-end gap-3 pt-4\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowAddUser(false)}\n                disabled={loading}\n              >\n                إلغاء\n              </Button>\n              <Button\n                onClick={handleAddUser}\n                disabled={loading || !newUser.name || !newUser.email || !newUser.password}\n                className=\"bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white\"\n              >\n                {loading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"></div>\n                ) : (\n                  <UserPlus className=\"w-4 h-4 ml-2\" />\n                )}\n                إضافة المستخدم\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* نموذج تعديل مستخدم */}\n      {editingUser && (\n        <Card className=\"border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-xl\">\n          <CardHeader className=\"bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-lg\">\n            <CardTitle className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 bg-white bg-opacity-20 rounded-lg\">\n                  <Edit className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold\">تعديل المستخدم</h3>\n                  <p className=\"text-blue-100 text-sm mt-1\">{editingUser.name}</p>\n                </div>\n              </div>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setEditingUser(null)}\n                className=\"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30\"\n              >\n                إلغاء\n              </Button>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"editUserName\">الاسم الكامل</Label>\n                <Input\n                  id=\"editUserName\"\n                  value={editingUser.name}\n                  onChange={(e) => setEditingUser({ ...editingUser, name: e.target.value })}\n                  disabled={loading}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"editUserEmail\">البريد الإلكتروني</Label>\n                <Input\n                  id=\"editUserEmail\"\n                  type=\"email\"\n                  value={editingUser.email}\n                  onChange={(e) => setEditingUser({ ...editingUser, email: e.target.value })}\n                  disabled={loading}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"editUserPhone\">رقم الهاتف</Label>\n                <Input\n                  id=\"editUserPhone\"\n                  value={editingUser.phone || ''}\n                  onChange={(e) => setEditingUser({ ...editingUser, phone: e.target.value })}\n                  disabled={loading}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"editUserRole\">الصلاحية</Label>\n                <Select\n                  value={editingUser.role}\n                  onValueChange={(value) => setEditingUser({ ...editingUser, role: value })}\n                  disabled={loading}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"VIEWER\">مطلع (عرض فقط)</SelectItem>\n                    <SelectItem value=\"DATA_ENTRY\">مدخل بيانات</SelectItem>\n                    <SelectItem value=\"MEMBER_VIEWER\">مطلع على عضو معين</SelectItem>\n                    <SelectItem value=\"GALLERY_VIEWER\">مطلع على المعرض فقط</SelectItem>\n                    <SelectItem value=\"ADMIN\">مدير</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end gap-3 pt-4\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setEditingUser(null)}\n                disabled={loading}\n              >\n                إلغاء\n              </Button>\n              <Button\n                onClick={() => handleUpdateUser(editingUser.id, {\n                  name: editingUser.name,\n                  email: editingUser.email,\n                  phone: editingUser.phone,\n                  role: editingUser.role\n                })}\n                disabled={loading}\n                className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white\"\n              >\n                {loading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"></div>\n                ) : (\n                  <CheckCircle className=\"w-4 h-4 ml-2\" />\n                )}\n                حفظ التغييرات\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* مكون الصلاحيات المفصلة */}\n      {showDetailedPermissions && (\n        <DetailedPermissions\n          users={users}\n          onClose={() => setShowDetailedPermissions(false)}\n        />\n      )}\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD;AACtD;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;;AAqGA,MAAM,kBAAwC;IAC5C,gBAAgB;QACd,WAAW;QACX,kBAAkB;QAClB,kBAAkB;QAClB,gBAAgB;QAChB,qBAAqB;QACrB,cAAc;QACd,gBAAgB;IAClB;IAEA,iBAAiB;QACf,SAAS;QACT,uBAAuB;QACvB,eAAe;QACf,YAAY;QACZ,oBAAoB;IACtB;IAEA,eAAe;QACb,mBAAmB;QACnB,iBAAiB;QACjB,eAAe;QACf,iBAAiB;QACjB,YAAY,EAAE;QACd,YAAY,EAAE;IAChB;IAEA,eAAe;QACb,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,iBAAiB;QACjB,eAAe;QACf,sBAAsB;IACxB;IAEA,oBAAoB;QAClB,aAAa;QACb,uBAAuB;QACvB,sBAAsB;QACtB,qBAAqB;QACrB,iBAAiB;YACf,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;IACF;IAEA,kBAAkB;QAChB,kBAAkB;QAClB,WAAW;QACX,YAAY;QACZ,WAAW;QACX,oBAAoB;QACpB,iBAAiB;QACjB,mBAAmB;QACnB,iBAAiB;IACnB;AACF;AAEe,SAAS,iBAAiB,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAyB;IAC7F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;IACnB;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,iBAAiB;gBAAE,GAAG,eAAe;gBAAE,GAAG,QAAQ;YAAC;QACrD;IACF,GAAG;QAAC;KAAS;IAEb,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,oBAAoB;YACtB;QACF;IACF,GAAG;QAAC;KAAmB;IAEvB,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC,KAAa;QACjC,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,cAAc;YAAE,GAAG,aAAa;QAAC;QAErC,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,cAAc;gBAAE,GAAG,WAAW;gBAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAAM;QACnD,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;YAC5B,cAAc;gBACZ,GAAG,WAAW;gBACd,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;oBACT,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,CAA+B;oBACrD,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBACb;YACF;QACF,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;YAC5B,cAAc;gBACZ,GAAG,WAAW;gBACd,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;oBACT,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,CAA+B;oBACrD,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;wBACT,GAAG,AAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAA+B,AAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBACvE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;oBACb;gBACF;YACF;QACF;QAEA,iBAAiB;QACjB,SAAS;IACX;IAEA,oBAAoB;IACpB,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,QAAQ,EAAE;YACxD,MAAM;YACN;QACF;QAEA,IAAI,QAAQ,QAAQ,KAAK,QAAQ,eAAe,EAAE;YAChD,MAAM;YACN;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,KAAK;oBACpB,OAAO,QAAQ,KAAK;oBACpB,MAAM,QAAQ,IAAI;oBAClB,UAAU,QAAQ,QAAQ;gBAC5B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,WAAW;oBACT,MAAM;oBACN,OAAO;oBACP,OAAO;oBACP,MAAM;oBACN,UAAU;oBACV,iBAAiB;gBACnB;gBACA,eAAe;gBACf;YACF,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,OAAO,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,eAAe;IACf,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,eAAe;gBACf;YACF,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,OAAO,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,aAAa;IACb,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE,SAAS,mGAAmG,CAAC,GAAG;YAC5J;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBACzD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN;YACF,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,OAAO,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAkB,OAAO;YAC9B,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAkB,OAAO;YAC9B,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAI9C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,cAAc,CAAC,SAAS,CAAC,QAAQ;gDACtD,eAAe,CAAC,QAAU,aAAa,4BAA4B,SAAS;gDAC5E,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAK7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,cAAc,CAAC,cAAc,CAAC,QAAQ;gDAC3D,eAAe,CAAC,QAAU,aAAa,iCAAiC,SAAS;gDACjF,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,cAAc,CAAC,gBAAgB;gDACtD,iBAAiB,CAAC,UAAY,aAAa,mCAAmC;gDAC9E,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,cAAc,CAAC,gBAAgB;gDACtD,iBAAiB,CAAC,UAAY,aAAa,mCAAmC;gDAC9E,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,cAAc,CAAC,cAAc;gDACpD,iBAAiB,CAAC,UAAY,aAAa,iCAAiC;gDAC5E,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,cAAc,CAAC,mBAAmB;gDACzD,iBAAiB,CAAC,UAAY,aAAa,sCAAsC;gDACjF,UAAU,CAAC;;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAe;;;;;;kDAC9B,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,cAAc,cAAc,CAAC,YAAY,CAAC,QAAQ;wCACzD,eAAe,CAAC,QAAU,aAAa,+BAA+B,SAAS;wCAC/E,UAAU,CAAC;;0DAEX,8OAAC,kIAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;kEACtB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;kEACtB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;kEACtB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA2B;;;;;;;;;;;;kCAIhD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,eAAe,CAAC,OAAO,CAAC,QAAQ;gDACrD,eAAe,CAAC,QAAU,aAAa,2BAA2B,SAAS;gDAC3E,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;;;;;;;;;;;;;;;;;;;kDAK/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,eAAe,CAAC,qBAAqB,CAAC,QAAQ;gDACnE,eAAe,CAAC,QAAU,aAAa,yCAAyC,SAAS;gDACzF,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,eAAe,CAAC,UAAU;gDACjD,iBAAiB,CAAC,UAAY,aAAa,8BAA8B;gDACzE,UAAU,CAAC;;;;;;;;;;;;oCAId,cAAc,eAAe,CAAC,UAAU,kBACvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,eAAe,CAAC,kBAAkB,CAAC,QAAQ;gDAChE,eAAe,CAAC,QAAU,aAAa,sCAAsC,SAAS;gDACtF,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;kDAMhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,eAAe,CAAC,aAAa;gDACpD,iBAAiB,CAAC,UAAY,aAAa,iCAAiC;gDAC5E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAIrD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,aAAa,CAAC,iBAAiB,CAAC,QAAQ;gDAC7D,eAAe,CAAC,QAAU,aAAa,mCAAmC,SAAS;gDACnF,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;;;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,aAAa,CAAC,eAAe,CAAC,QAAQ;gDAC3D,eAAe,CAAC,QAAU,aAAa,iCAAiC,SAAS;gDACjF,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,aAAa,CAAC,aAAa;gDAClD,iBAAiB,CAAC,UAAY,aAAa,+BAA+B;gDAC1E,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,aAAa,CAAC,eAAe;gDACpD,iBAAiB,CAAC,UAAY,aAAa,iCAAiC;gDAC5E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAInD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,aAAa,CAAC,cAAc;gDACnD,iBAAiB,CAAC,UAAY,aAAa,gCAAgC;gDAC3E,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,aAAa,CAAC,gBAAgB;gDACrD,iBAAiB,CAAC,UAAY,aAAa,kCAAkC;gDAC7E,UAAU,CAAC,WAAW,CAAC,cAAc,aAAa,CAAC,cAAc;;;;;;;;;;;;kDAIrE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,aAAa,CAAC,cAAc;gDACnD,iBAAiB,CAAC,UAAY,aAAa,gCAAgC;gDAC3E,UAAU,CAAC,WAAW,CAAC,cAAc,aAAa,CAAC,cAAc;;;;;;;;;;;;kDAIrE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,aAAa,CAAC,eAAe;gDACpD,iBAAiB,CAAC,UAAY,aAAa,iCAAiC;gDAC5E,UAAU,CAAC,WAAW,CAAC,cAAc,aAAa,CAAC,cAAc;;;;;;;;;;;;kDAIrE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,aAAa,CAAC,oBAAoB;gDACzD,iBAAiB,CAAC,UAAY,aAAa,sCAAsC;gDACjF,UAAU,CAAC,WAAW,CAAC,cAAc,aAAa,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAKvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,cAAc,aAAa,CAAC,aAAa,CAAC,QAAQ;wCACzD,eAAe,CAAC,QAAU,aAAa,+BAA+B,SAAS;wCAC/E,UAAU,CAAC,WAAW,CAAC,cAAc,aAAa,CAAC,cAAc;;0DAEjE,8OAAC,kIAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAK;;;;;;kEACvB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAK;;;;;;kEACvB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;8CAGhD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,sBAAsB,CAAC;oCACtC,UAAU,CAAC;oCACX,WAAU;;wCAET,qBAAqB,UAAU;wCAAM;;;;;;;;;;;;;;;;;;oBAI3C,oCACC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAA4B;;;;;;;0DAG/C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAK;4DAAoB,MAAM,MAAM;;;;;;;kEACtC,8OAAC;kEAAK;;;;;;kEACN,8OAAC;;4DAAK;4DAAW,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;;;;;;;kEAC7D,8OAAC;kEAAK;;;;;;kEACN,8OAAC;;4DAAK;4DAAiB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc,MAAM;;;;;;;kEACxE,8OAAC;kEAAK;;;;;;kEACN,8OAAC;;4DAAK;4DAAW,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;;;;;;;;;;;;;;;;;;;kDAGlE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,2BAA2B;gDAC1C,UAAU,CAAC,WAAW;gDACtB,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,eAAe;gDAC9B,UAAU,CAAC,WAAW;gDACtB,WAAU;;kEAEV,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAO1C,wBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;qDAGjB,8OAAC;gCAAI,WAAU;0CACZ,MAAM,MAAM,KAAK,kBAChB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,eAAe;4CAC9B,UAAU,CAAC,WAAW;4CACtB,WAAU;;8DAEV,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;2CAKzC,MAAM,GAAG,CAAC,CAAC,qBACT,8OAAC;wCAAkB,WAAU;;0DAC3B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sEAGpC,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA8F,KAAK,IAAI;;;;;;8EACrH,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFACf,KAAK,KAAK;;;;;;;wEAEZ,KAAK,KAAK,kBACT,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAChB,KAAK,KAAK;;;;;;;sFAGf,8OAAC;4EAAK,WAAU;;gFAAwB;gFAC7B,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM/D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAW,GAAG,kBAAkB,KAAK,IAAI,EAAE,gCAAgC,CAAC;kEAChF,YAAY,KAAK,IAAI;;;;;;oDAEvB,yBACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,eAAe;gEAC9B,UAAU;gEACV,WAAU;0EAEV,cAAA,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;gEAClD,UAAU;gEACV,WAAU;0EAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uCAlDlB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAiE/B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;;;;;;kCAI9C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,cAAc,kBAAkB,CAAC,WAAW;wCACnD,eAAe,CAAC,QAAU,aAAa,kCAAkC;wCACzE,UAAU,CAAC;;0DAEX,8OAAC,kIAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAa;;;;;;kEAC/B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;0CAKhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,kBAAkB,CAAC,qBAAqB;gDAC/D,iBAAiB,CAAC,UAAY,aAAa,4CAA4C;gDACvF,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,kBAAkB,CAAC,oBAAoB;gDAC9D,iBAAiB,CAAC,UAAY,aAAa,2CAA2C;gDACtF,UAAU,CAAC,WAAW,CAAC,cAAc,kBAAkB,CAAC,qBAAqB;;;;;;;;;;;;kDAIjF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,kBAAkB,CAAC,mBAAmB;gDAC7D,iBAAiB,CAAC,UAAY,aAAa,0CAA0C;gDACrF,UAAU,CAAC;;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,cAAc,kBAAkB,CAAC,eAAe,CAAC,KAAK;wDAC7D,UAAU,CAAC,IAAM,aAAa,4CAA4C,SAAS,EAAE,MAAM,CAAC,KAAK;wDACjG,UAAU,CAAC;;;;;;;;;;;;0DAGf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAe;;;;;;kEAC9B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,cAAc,kBAAkB,CAAC,eAAe,CAAC,UAAU;wDAClE,UAAU,CAAC,IAAM,aAAa,iDAAiD,SAAS,EAAE,MAAM,CAAC,KAAK;wDACtG,UAAU,CAAC;;;;;;;;;;;;0DAGf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,cAAc,kBAAkB,CAAC,eAAe,CAAC,MAAM;wDAC9D,UAAU,CAAC,IAAM,aAAa,6CAA6C,SAAS,EAAE,MAAM,CAAC,KAAK;wDAClG,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAyB;;;;;;;8CAG7C,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,gBAAgB,CAAC;;wCAE/B,eAAe,UAAU;wCAAM;;;;;;;;;;;;;;;;;;oBAIrC,8BACC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,gBAAgB,CAAC,gBAAgB;gDACxD,iBAAiB,CAAC,UAAY,aAAa,qCAAqC;gDAChF,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,gBAAgB,CAAC,SAAS;gDACjD,iBAAiB,CAAC,UAAY,aAAa,8BAA8B;gDACzE,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,gBAAgB,CAAC,UAAU;gDAClD,iBAAiB,CAAC,UAAY,aAAa,+BAA+B;gDAC1E,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,gBAAgB,CAAC,SAAS;gDACjD,iBAAiB,CAAC,UAAY,aAAa,8BAA8B;gDACzE,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,gBAAgB,CAAC,kBAAkB;gDAC1D,iBAAiB,CAAC,UAAY,aAAa,uCAAuC;gDAClF,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,gBAAgB,CAAC,eAAe;gDACvD,iBAAiB,CAAC,UAAY,aAAa,oCAAoC;gDAC/E,UAAU,CAAC;;;;;;;;;;;;;;;;;;4BAKhB,cAAc,gBAAgB,CAAC,eAAe,kBAC7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,cAAc,gBAAgB,CAAC,iBAAiB;gDACvD,UAAU,CAAC,IAAM,aAAa,sCAAsC,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC3F,UAAU,CAAC;;;;;;;;;;;;kDAGf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,cAAc,gBAAgB,CAAC,eAAe;gDACrD,UAAU,CAAC,IAAM,aAAa,oCAAoC,SAAS,EAAE,MAAM,CAAC,KAAK;gDACzF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUxB,6BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;8CAGhD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAKL,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,QAAQ,IAAI;gDACnB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC/D,aAAY;gDACZ,UAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,QAAQ,KAAK;gDACpB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChE,aAAY;gDACZ,UAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,QAAQ,KAAK;gDACpB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChE,aAAY;gDACZ,UAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,QAAQ,IAAI;gDACnB,eAAe,CAAC,QAAU,WAAW;wDAAE,GAAG,OAAO;wDAAE,MAAM;oDAAM;gDAC/D,UAAU;;kEAEV,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAa;;;;;;0EAC/B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAgB;;;;;;0EAClC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAiB;;;;;;0EACnC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;kDAIhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,QAAQ,QAAQ;gDACvB,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACnE,aAAY;gDACZ,UAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAyB;;;;;;0DACxC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,QAAQ,eAAe;gDAC9B,UAAU,CAAC,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC1E,aAAY;gDACZ,UAAU;;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe;wCAC9B,UAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,QAAQ;wCACzE,WAAU;;4CAET,wBACC,8OAAC;gDAAI,WAAU;;;;;qEAEf,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACpB;;;;;;;;;;;;;;;;;;;;;;;;;YASX,6BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAA8B,YAAY,IAAI;;;;;;;;;;;;;;;;;;8CAG/D,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAKL,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACvE,UAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,YAAY,KAAK;gDACxB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACxE,UAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,YAAY,KAAK,IAAI;gDAC5B,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACxE,UAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,YAAY,IAAI;gDACvB,eAAe,CAAC,QAAU,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM;oDAAM;gDACvE,UAAU;;kEAEV,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAa;;;;;;0EAC/B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAgB;;;;;;0EAClC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAiB;;;;;;0EACnC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe;wCAC9B,UAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,iBAAiB,YAAY,EAAE,EAAE;gDAC9C,MAAM,YAAY,IAAI;gDACtB,OAAO,YAAY,KAAK;gDACxB,OAAO,YAAY,KAAK;gDACxB,MAAM,YAAY,IAAI;4CACxB;wCACA,UAAU;wCACV,WAAU;;4CAET,wBACC,8OAAC;gDAAI,WAAU;;;;;qEAEf,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACvB;;;;;;;;;;;;;;;;;;;;;;;;;YASX,yCACC,8OAAC,yJAAA,CAAA,UAAmB;gBAClB,OAAO;gBACP,SAAS,IAAM,2BAA2B;;;;;;;;;;;;AAKpD", "debugId": null}}]}