/* إصلاحات ألوان الجداول - اللون الكحلي */

/* تعريف اللون الكحلي */
:root {
  --navy-50: #f8fafc;
  --navy-100: #f1f5f9;
  --navy-200: #e2e8f0;
  --navy-300: #cbd5e1;
  --navy-400: #94a3b8;
  --navy-500: #64748b;
  --navy-600: #475569;
  --navy-700: #334155;
  --navy-800: #1e293b;
  --navy-900: #0f172a;
}

/* إصلاح ألوان الجداول العامة */
table,
.table,
[role="table"] {
  color: var(--navy-800) !important;
}

/* إصلاح ألوان رؤوس الجداول */
th,
thead th,
.table-head,
.table-header,
[role="columnheader"] {
  color: var(--navy-700) !important;
  font-weight: 600 !important;
}

/* إصلاح ألوان خلايا الجداول */
td,
tbody td,
.table-cell,
.table-data,
[role="cell"] {
  color: var(--navy-800) !important;
}

/* إصلاح ألوان صفوف الجداول */
tr,
.table-row,
[role="row"] {
  color: var(--navy-800) !important;
}

/* إصلاح ألوان الجداول المحسنة */
.enhanced-table,
.enhanced-table th,
.enhanced-table td {
  color: var(--navy-800) !important;
}

.enhanced-table thead th {
  color: var(--navy-700) !important;
  font-weight: 600 !important;
}

/* إصلاح ألوان جداول الأعضاء */
.members-table,
.members-table th,
.members-table td {
  color: var(--navy-800) !important;
}

.members-table thead th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان جداول الإيرادات */
.incomes-table,
.incomes-table th,
.incomes-table td {
  color: var(--navy-800) !important;
}

.incomes-table thead th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان جداول المصروفات */
.expenses-table,
.expenses-table th,
.expenses-table td {
  color: var(--navy-800) !important;
}

.expenses-table thead th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان جداول التقارير */
.reports-table,
.reports-table th,
.reports-table td {
  color: var(--navy-800) !important;
}

.reports-table thead th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول في الحوارات */
.dialog table,
.dialog th,
.dialog td,
[role="dialog"] table,
[role="dialog"] th,
[role="dialog"] td {
  color: var(--navy-800) !important;
}

.dialog thead th,
[role="dialog"] thead th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول في البطاقات */
.card table,
.card th,
.card td,
[class*="card"] table,
[class*="card"] th,
[class*="card"] td {
  color: var(--navy-800) !important;
}

.card thead th,
[class*="card"] thead th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان فئات Tailwind المحددة */
.text-slate-700,
.text-slate-800,
.text-gray-700,
.text-gray-800 {
  color: var(--navy-800) !important;
}

.text-slate-600,
.text-gray-600 {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول مع فئات مخصصة */
.table-navy,
.navy-table {
  color: var(--navy-800) !important;
}

.table-navy th,
.navy-table th {
  color: var(--navy-700) !important;
  font-weight: 600 !important;
}

/* إصلاح ألوان الجداول الاستجابية */
.table-responsive,
.responsive-table {
  color: var(--navy-800) !important;
}

.table-responsive th,
.responsive-table th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول المرتبة */
.sortable-table,
.table-sortable {
  color: var(--navy-800) !important;
}

.sortable-table th,
.table-sortable th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول القابلة للتصفية */
.filterable-table,
.table-filterable {
  color: var(--navy-800) !important;
}

.filterable-table th,
.table-filterable th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول مع البحث */
.searchable-table,
.table-searchable {
  color: var(--navy-800) !important;
}

.searchable-table th,
.table-searchable th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول مع التصفح */
.paginated-table,
.table-paginated {
  color: var(--navy-800) !important;
}

.paginated-table th,
.table-paginated th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول في صفحات محددة */
.members-page table,
.incomes-page table,
.expenses-page table,
.reports-page table,
.gallery-page table {
  color: var(--navy-800) !important;
}

.members-page thead th,
.incomes-page thead th,
.expenses-page thead th,
.reports-page thead th,
.gallery-page thead th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول مع حالات خاصة */
.table-loading,
.loading-table {
  color: var(--navy-800) !important;
}

.table-empty,
.empty-table {
  color: var(--navy-600) !important;
}

/* إصلاح ألوان الجداول مع التمرير */
.table-hover tr:hover,
.hover-table tr:hover {
  color: var(--navy-900) !important;
}

/* إصلاح ألوان الجداول المحددة */
.table-selected,
.selected-table,
tr.selected,
tr[data-selected="true"] {
  color: var(--navy-900) !important;
  background-color: var(--navy-50) !important;
}

/* إصلاح ألوان الجداول المعطلة */
.table-disabled,
.disabled-table,
tr.disabled,
tr[disabled] {
  color: var(--navy-400) !important;
  opacity: 0.6 !important;
}

/* إصلاح ألوان أيقونات الترتيب */
.sort-icon,
.table-sort-icon,
.sortable-icon {
  color: var(--navy-600) !important;
}

.sort-icon.active,
.table-sort-icon.active,
.sortable-icon.active {
  color: var(--navy-800) !important;
}

/* إصلاح ألوان الروابط في الجداول */
table a,
.table a,
td a,
th a {
  color: var(--navy-700) !important;
  text-decoration: underline;
}

table a:hover,
.table a:hover,
td a:hover,
th a:hover {
  color: var(--navy-900) !important;
}

/* إصلاح ألوان الأزرار في الجداول */
table button,
.table button,
td button,
th button {
  color: inherit !important;
}

/* إصلاح ألوان النصوص المساعدة في الجداول */
.table-caption,
.table-description,
.table-helper-text {
  color: var(--navy-600) !important;
}

/* إصلاح ألوان الجداول في الوضع الداكن */
@media (prefers-color-scheme: dark) {
  table,
  .table,
  [role="table"],
  th,
  td,
  tr {
    color: var(--navy-800) !important;
  }
  
  thead th {
    color: var(--navy-700) !important;
  }
}

/* إصلاح ألوان الجداول مع فئات Bootstrap */
.table-primary,
.table-secondary,
.table-success,
.table-info,
.table-warning,
.table-danger {
  color: var(--navy-800) !important;
}

.table-primary th,
.table-secondary th,
.table-success th,
.table-info th,
.table-warning th,
.table-danger th {
  color: var(--navy-700) !important;
}

/* إصلاح ألوان الجداول مع فئات Material UI */
.MuiTable-root,
.MuiTableHead-root,
.MuiTableBody-root,
.MuiTableRow-root,
.MuiTableCell-root {
  color: var(--navy-800) !important;
}

.MuiTableHead-root .MuiTableCell-root {
  color: var(--navy-700) !important;
  font-weight: 600 !important;
}
