{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // This interface extends the base input props\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-11 w-full rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-sm font-medium text-secondary-700 transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-400 focus-visible:outline-none focus-visible:border-primary-500 focus-visible:ring-4 focus-visible:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 hover:border-secondary-300\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gbACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning:\n          \"border-transparent bg-slate-100 text-slate-800 hover:bg-slate-200\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { X } from \"lucide-react\"\n\ninterface DialogProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n  enableScrollInteraction?: boolean // خيار لتفعيل التفاعل مع التمرير\n  maxHeight?: string // الحد الأقصى للارتفاع\n}\n\ninterface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Dialog = ({\n  open,\n  onOpenChange,\n  children,\n  enableScrollInteraction = true,\n  maxHeight = \"90vh\"\n}: DialogProps) => {\n  const dialogRef = React.useRef<HTMLDivElement>(null)\n  const containerRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onOpenChange?.(false)\n      }\n    }\n\n    const handleWheel = (e: WheelEvent) => {\n      if (!open || !enableScrollInteraction || !dialogRef.current) return\n\n      // السماح بالتمرير داخل الحوار فقط\n      const dialogElement = dialogRef.current\n      const isScrollable = dialogElement.scrollHeight > dialogElement.clientHeight\n\n      if (isScrollable) {\n        // التحقق من أن الماوس داخل منطقة الحوار\n        const rect = dialogElement.getBoundingClientRect()\n        const isInsideDialog = e.clientX >= rect.left && e.clientX <= rect.right &&\n                              e.clientY >= rect.top && e.clientY <= rect.bottom\n\n        if (isInsideDialog) {\n          // السماح بالتمرير الطبيعي داخل الحوار\n          return\n        }\n      }\n\n      // منع التمرير خارج الحوار\n      e.preventDefault()\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEscape)\n      if (enableScrollInteraction) {\n        document.addEventListener('wheel', handleWheel, { passive: false })\n      }\n      // عدم منع التمرير في الخلفية للسماح بالتمرير داخل الحوار\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.removeEventListener('wheel', handleWheel)\n    }\n  }, [open, onOpenChange, enableScrollInteraction])\n\n  if (!open) return null\n\n  return (\n    <div\n      ref={containerRef}\n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n    >\n      <div\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300\"\n        onClick={() => onOpenChange?.(false)}\n      />\n      <div\n        ref={dialogRef}\n        className={cn(\n          \"relative z-50 w-full overflow-hidden animate-in fade-in-0 zoom-in-95 duration-300\",\n          \"smooth-scroll\", // فئة CSS للتمرير السلس\n          enableScrollInteraction ? \"overflow-y-auto\" : \"overflow-hidden\"\n        )}\n        style={{\n          maxHeight: maxHeight,\n          transform: 'translate3d(0, 0, 0)', // تحسين الأداء\n          willChange: 'transform', // تحسين الأداء\n        }}\n      >\n        {children}\n      </div>\n    </div>\n  )\n}\n\nconst DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200\",\n        \"backdrop-blur-sm bg-white/95\", // تحسين الشفافية\n        \"hover:shadow-3xl transition-shadow duration-300\", // تأثير الظل عند التمرير\n        \"max-h-full overflow-y-auto smooth-scroll\", // تمكين التمرير السلس\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogContent.displayName = \"DialogContent\"\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-bold leading-none tracking-tight text-slate-800\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = \"DialogTitle\"\n\nconst DialogDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-slate-600 font-medium\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = \"DialogDescription\"\n\nconst DialogTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement>\n>(({ className, ...props }, ref) => (\n  <button\n    ref={ref}\n    className={className}\n    {...props}\n  />\n))\nDialogTrigger.displayName = \"DialogTrigger\"\n\nconst DialogClose = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & { onOpenChange?: (open: boolean) => void }\n>(({ className, onOpenChange, ...props }, ref) => (\n  <button\n    ref={ref}\n    type=\"button\"\n    onClick={() => onOpenChange?.(false)}\n    className={cn(\n      \"absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10\",\n      className\n    )}\n    {...props}\n  >\n    <X className=\"h-4 w-4 text-slate-600\" />\n    <span className=\"sr-only\">إغلاق</span>\n  </button>\n))\nDialogClose.displayName = \"DialogClose\"\n\nexport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n  DialogClose,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAkBA,MAAM,SAAS,CAAC,EACd,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,0BAA0B,IAAI,EAC9B,YAAY,MAAM,EACN;IACZ,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAkB;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAkB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,eAAe;YACjB;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,UAAU,OAAO,EAAE;YAE7D,kCAAkC;YAClC,MAAM,gBAAgB,UAAU,OAAO;YACvC,MAAM,eAAe,cAAc,YAAY,GAAG,cAAc,YAAY;YAE5E,IAAI,cAAc;gBAChB,wCAAwC;gBACxC,MAAM,OAAO,cAAc,qBAAqB;gBAChD,MAAM,iBAAiB,EAAE,OAAO,IAAI,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,KAAK,KAAK,IAClD,EAAE,OAAO,IAAI,KAAK,GAAG,IAAI,EAAE,OAAO,IAAI,KAAK,MAAM;gBAEvE,IAAI,gBAAgB;oBAClB,sCAAsC;oBACtC;gBACF;YACF;YAEA,0BAA0B;YAC1B,EAAE,cAAc;QAClB;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,IAAI,yBAAyB;gBAC3B,SAAS,gBAAgB,CAAC,SAAS,aAAa;oBAAE,SAAS;gBAAM;YACnE;QACA,yDAAyD;QAC3D;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,SAAS;QACxC;IACF,GAAG;QAAC;QAAM;QAAc;KAAwB;IAEhD,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAEV,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAEhC,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA,iBACA,0BAA0B,oBAAoB;gBAEhD,OAAO;oBACL,WAAW;oBACX,WAAW;oBACX,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAIT;AAEA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA,gCACA,mDACA,4CACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE,oBACxC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,SAAS,IAAM,eAAe;QAC9B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wRACA;QAED,GAAG,KAAK;;0BAET,8OAAC,4LAAA,CAAA,IAAC;gBAAC,WAAU;;;;;;0BACb,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {\n  // This interface extends the base label props\n}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = \"Label\"\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  // This interface extends the base textarea props\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-11 w-full items-center justify-between rounded-xl border-2 border-gray-300 bg-white px-4 py-3 text-base font-medium text-black transition-all duration-200 hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/20 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-5 w-5 text-gray-600 transition-transform duration-200 data-[state=open]:rotate-180\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1 text-black\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4 text-gray-600\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1 text-black\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4 text-gray-600\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-xl border border-gray-300 bg-white text-black shadow-xl backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-2\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-2 pl-10 pr-3 text-sm font-bold text-gray-700 bg-gray-50\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-pointer select-none items-center rounded-lg py-3 pl-10 pr-3 text-sm font-medium text-black transition-colors hover:bg-blue-50 focus:bg-blue-100 focus:text-blue-800 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[state=checked]:bg-blue-100 data-[state=checked]:text-blue-800 data-[state=checked]:font-semibold\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-3 flex h-4 w-4 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4 text-blue-600\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8UACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ydACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yWACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/gallery/upload-photo-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogFooter,\n} from '@/components/ui/dialog'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Upload, X, Image as ImageIcon, Folder, Calendar } from 'lucide-react'\nimport { Badge } from '@/components/ui/badge'\n\ninterface UploadPhotoDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess: () => void\n  defaultFolderId?: string\n  defaultActivityId?: string\n}\n\ninterface Activity {\n  id: string\n  title: string\n}\n\ninterface GalleryFolder {\n  id: string\n  title: string\n}\n\nexport default function UploadPhotoDialog({\n  open,\n  onOpenChange,\n  onSuccess,\n  defaultFolderId,\n  defaultActivityId,\n}: UploadPhotoDialogProps) {\n  const [title, setTitle] = useState('')\n  const [description, setDescription] = useState('')\n  const [activityId, setActivityId] = useState('none')\n  const [folderId, setFolderId] = useState('none')\n  const [selectedFile, setSelectedFile] = useState<File | null>(null)\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null)\n  const [uploading, setUploading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [activities, setActivities] = useState<Activity[]>([])\n  const [folders, setFolders] = useState<GalleryFolder[]>([])\n  const [loadingActivities, setLoadingActivities] = useState(false)\n  const [loadingFolders, setLoadingFolders] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  // تعيين القيم الافتراضية\n  useEffect(() => {\n    if (open) {\n      if (defaultFolderId) {\n        setFolderId(defaultFolderId)\n        setActivityId('none')\n      } else if (defaultActivityId) {\n        setActivityId(defaultActivityId)\n        setFolderId('none')\n      }\n    }\n  }, [open, defaultFolderId, defaultActivityId])\n\n  // جلب الأنشطة والمجلدات من API\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoadingActivities(true)\n        setLoadingFolders(true)\n\n        // جلب الأنشطة\n        const activitiesResponse = await fetch('/api/activities')\n        if (activitiesResponse.ok) {\n          const activitiesData = await activitiesResponse.json()\n          setActivities(activitiesData.activities || [])\n        }\n\n        // جلب المجلدات\n        const foldersResponse = await fetch('/api/gallery-folders')\n        if (foldersResponse.ok) {\n          const foldersData = await foldersResponse.json()\n          setFolders(foldersData.folders || [])\n        }\n      } catch (error) {\n        console.error('خطأ في جلب البيانات:', error)\n      } finally {\n        setLoadingActivities(false)\n        setLoadingFolders(false)\n      }\n    }\n\n    if (open) {\n      fetchData()\n    }\n  }, [open])\n\n  const handleFileSelect = (file: File) => {\n    // التحقق من نوع الملف\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']\n    if (!allowedTypes.includes(file.type)) {\n      setError('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)')\n      return\n    }\n\n    // التحقق من حجم الملف (5MB كحد أقصى)\n    const maxSize = 5 * 1024 * 1024 // 5MB\n    if (file.size > maxSize) {\n      setError('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت')\n      return\n    }\n\n    setSelectedFile(file)\n    setError(null)\n\n    // إنشاء معاينة للصورة\n    const reader = new FileReader()\n    reader.onload = (e) => {\n      setPreviewUrl(e.target?.result as string)\n    }\n    reader.readAsDataURL(file)\n  }\n\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file) {\n      handleFileSelect(file)\n    }\n  }\n\n  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault()\n    const file = event.dataTransfer.files[0]\n    if (file) {\n      handleFileSelect(file)\n    }\n  }\n\n  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault()\n  }\n\n  const removeFile = () => {\n    setSelectedFile(null)\n    setPreviewUrl(null)\n    if (fileInputRef.current) {\n      fileInputRef.current.value = ''\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!selectedFile) {\n      setError('يرجى اختيار صورة')\n      return\n    }\n\n    if (!title.trim()) {\n      setError('يرجى إدخال عنوان للصورة')\n      return\n    }\n\n    setUploading(true)\n    setError(null)\n\n    try {\n      // رفع الصورة أولاً\n      const formData = new FormData()\n      formData.append('file', selectedFile)\n      formData.append('type', 'gallery')\n\n      const uploadResponse = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      })\n\n      if (!uploadResponse.ok) {\n        const errorData = await uploadResponse.json()\n        throw new Error(errorData.error || 'فشل في رفع الصورة')\n      }\n\n      const uploadData = await uploadResponse.json()\n\n      // إضافة الصورة إلى معرض الصور\n      const galleryResponse = await fetch('/api/gallery', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          title: title.trim(),\n          description: description.trim() || undefined,\n          imagePath: uploadData.filePath,\n          activityId: activityId && activityId !== 'none' ? activityId : undefined,\n          folderId: folderId && folderId !== 'none' ? folderId : undefined,\n        }),\n      })\n\n      if (!galleryResponse.ok) {\n        const errorData = await galleryResponse.json()\n        throw new Error(errorData.error || 'فشل في إضافة الصورة إلى المعرض')\n      }\n\n      // إعادة تعيين النموذج\n      setTitle('')\n      setDescription('')\n      setActivityId('none')\n      setFolderId('none')\n      setSelectedFile(null)\n      setPreviewUrl(null)\n      \n      onSuccess()\n      onOpenChange(false)\n    } catch (error: any) {\n      console.error('خطأ في رفع الصورة:', error)\n      setError(error.message || 'حدث خطأ في رفع الصورة')\n    } finally {\n      setUploading(false)\n    }\n  }\n\n  const openFileDialog = () => {\n    fileInputRef.current?.click()\n  }\n\n  return (\n    <Dialog\n      open={open}\n      onOpenChange={onOpenChange}\n      enableScrollInteraction={true}\n      maxHeight=\"95vh\"\n    >\n      <DialogContent className=\"max-w-[50vw] bg-gradient-to-br from-white to-gray-50\">\n        <DialogHeader className=\"pb-6 border-b border-gray-100\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"bg-diwan-100 rounded-full p-3\">\n              <Upload className=\"w-6 h-6 text-diwan-600\" />\n            </div>\n            <div>\n              <DialogTitle className=\"text-2xl font-bold text-diwan-700\">رفع صورة جديدة</DialogTitle>\n              <p className=\"text-sm text-gray-600 mt-1\">أضف صورة جديدة إلى معرض الصور مع إمكانية ربطها بمجلد أو نشاط</p>\n            </div>\n          </div>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8 pt-6\">\n          {/* منطقة رفع الصورة المحسنة */}\n          <div className=\"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\">\n            <div className=\"flex items-center gap-2 mb-4\">\n              <ImageIcon className=\"w-5 h-5 text-diwan-600\" />\n              <Label className=\"text-lg font-semibold text-gray-800\">اختيار الصورة *</Label>\n            </div>\n\n            {!selectedFile ? (\n              <div\n                className=\"drop-zone border-2 border-dashed border-diwan-300 rounded-2xl p-12 text-center cursor-pointer hover:border-diwan-500 hover:bg-gradient-to-br hover:from-diwan-50 hover:to-blue-50 transition-all duration-300 bg-gradient-to-br from-gray-50 via-white to-gray-50 group\"\n                onDrop={handleDrop}\n                onDragOver={handleDragOver}\n                onClick={openFileDialog}\n              >\n                <div className=\"bg-gradient-to-br from-diwan-100 to-diwan-200 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                  <ImageIcon className=\"w-10 h-10 text-diwan-700\" />\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">اختر صورة للرفع</h3>\n                <p className=\"text-gray-600 mb-6 text-lg\">اسحب الصورة هنا أو انقر للاختيار من جهازك</p>\n\n                <div className=\"bg-gray-50 rounded-xl p-4 mb-4\">\n                  <div className=\"flex items-center justify-center gap-3 text-sm text-gray-600 mb-3\">\n                    <span className=\"font-medium\">الأنواع المدعومة:</span>\n                  </div>\n                  <div className=\"flex items-center justify-center gap-2\">\n                    <Badge variant=\"outline\" className=\"text-sm font-medium bg-green-50 text-green-700 border-green-200\">JPG</Badge>\n                    <Badge variant=\"outline\" className=\"text-sm font-medium bg-blue-50 text-blue-700 border-blue-200\">PNG</Badge>\n                    <Badge variant=\"outline\" className=\"text-sm font-medium bg-purple-50 text-purple-700 border-purple-200\">WebP</Badge>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center justify-center gap-2 text-sm text-gray-500\">\n                  <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>حد أقصى: 5 ميجابايت</span>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                <div className=\"relative group\">\n                  <div className=\"relative w-full h-80 rounded-2xl overflow-hidden border-2 border-diwan-200 bg-gray-100 shadow-lg\">\n                    <img\n                      src={previewUrl!}\n                      alt=\"معاينة الصورة\"\n                      className=\"w-full h-full object-cover\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-6\">\n                      <Button\n                        type=\"button\"\n                        variant=\"destructive\"\n                        size=\"sm\"\n                        onClick={removeFile}\n                        className=\"bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-3 h-12 shadow-xl hover:shadow-2xl transition-all duration-200 rounded-xl\"\n                        disabled={uploading}\n                      >\n                        <X className=\"w-5 h-5 ml-2\" />\n                        إزالة الصورة\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"bg-green-100 rounded-full p-2\">\n                        <svg className=\"w-5 h-5 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-semibold text-gray-900\">{selectedFile.name}</p>\n                        <p className=\"text-xs text-gray-600\">\n                          {(selectedFile.size / (1024 * 1024)).toFixed(2)} ميجابايت • تم اختيار الصورة بنجاح\n                        </p>\n                      </div>\n                    </div>\n                    <Badge className=\"bg-green-100 text-green-800 border-green-200 font-medium\">\n                      جاهز للرفع\n                    </Badge>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileChange}\n              className=\"hidden\"\n              disabled={uploading}\n            />\n          </div>\n\n          {/* معلومات الصورة */}\n          <div className=\"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\">\n            <div className=\"flex items-center gap-2 mb-6\">\n              <svg className=\"w-5 h-5 text-diwan-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\" clipRule=\"evenodd\" />\n              </svg>\n              <Label className=\"text-lg font-semibold text-gray-800\">معلومات الصورة</Label>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {/* عنوان الصورة */}\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"title\" className=\"text-sm font-semibold text-gray-700 flex items-center gap-2\">\n                  <span className=\"w-2 h-2 bg-red-500 rounded-full\"></span>\n                  العنوان\n                </Label>\n                <Input\n                  id=\"title\"\n                  value={title}\n                  onChange={(e) => setTitle(e.target.value)}\n                  placeholder=\"أدخل عنوان واضح ومميز للصورة\"\n                  disabled={uploading}\n                  required\n                  className=\"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base\"\n                />\n              </div>\n\n              {/* ربط بمجلد */}\n              <div className=\"space-y-3\">\n                <Label className=\"text-sm font-semibold text-gray-700 flex items-center gap-2\">\n                  <Folder className=\"w-4 h-4 text-purple-600\" />\n                  ربط بمجلد (اختياري)\n                </Label>\n                <Select value={folderId} onValueChange={(value) => {\n                  setFolderId(value)\n                  if (value !== 'none') setActivityId('none') // إلغاء اختيار النشاط\n                }} disabled={uploading || loadingFolders}>\n                  <SelectTrigger className=\"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base\">\n                    <SelectValue placeholder={loadingFolders ? \"جاري التحميل...\" : \"اختر مجلد لتنظيم الصورة\"} />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"none\">\n                      <div className=\"flex items-center\">\n                        <ImageIcon className=\"w-4 h-4 ml-2 text-gray-400\" />\n                        بدون ربط (صور عامة)\n                      </div>\n                    </SelectItem>\n                    {folders.map((folder) => (\n                      <SelectItem key={folder.id} value={folder.id}>\n                        <div className=\"flex items-center\">\n                          <Folder className=\"w-4 h-4 ml-2 text-purple-600\" />\n                          {folder.title}\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {/* ربط بنشاط */}\n              <div className=\"space-y-3\">\n                <Label className=\"text-sm font-semibold text-gray-700 flex items-center gap-2\">\n                  <Calendar className=\"w-4 h-4 text-green-600\" />\n                  ربط بنشاط (اختياري)\n                </Label>\n                <Select value={activityId} onValueChange={(value) => {\n                  setActivityId(value)\n                  if (value !== 'none') setFolderId('none') // إلغاء اختيار المجلد\n                }} disabled={uploading || loadingActivities || folderId !== 'none'}>\n                  <SelectTrigger className=\"h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base\">\n                    <SelectValue placeholder={loadingActivities ? \"جاري التحميل...\" : folderId !== 'none' ? \"تم اختيار مجلد\" : \"اختر نشاط مرتبط\"} />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"none\">\n                      <div className=\"flex items-center\">\n                        <ImageIcon className=\"w-4 h-4 ml-2 text-gray-400\" />\n                        بدون ربط\n                      </div>\n                    </SelectItem>\n                    {activities.map((activity) => (\n                      <SelectItem key={activity.id} value={activity.id}>\n                        <div className=\"flex items-center\">\n                          <Calendar className=\"w-4 h-4 ml-2 text-green-600\" />\n                          {activity.title}\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* وصف الصورة */}\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"description\" className=\"text-sm font-semibold text-gray-700 flex items-center gap-2\">\n                  <svg className=\"w-4 h-4 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\" clipRule=\"evenodd\" />\n                  </svg>\n                  الوصف (اختياري)\n                </Label>\n                <Textarea\n                  id=\"description\"\n                  value={description}\n                  onChange={(e) => setDescription(e.target.value)}\n                  placeholder=\"أضف وصفاً تفصيلياً للصورة، المناسبة، أو الحدث المصور...\"\n                  disabled={uploading}\n                  rows={4}\n                  className=\"border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 resize-none text-base\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* رسالة الخطأ */}\n          {error && (\n            <div className=\"bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-5 shadow-sm\">\n              <div className=\"flex items-start gap-3\">\n                <div className=\"flex-shrink-0 bg-red-100 rounded-full p-2\">\n                  <svg className=\"w-5 h-5 text-red-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-semibold text-red-800 mb-1\">حدث خطأ</h4>\n                  <p className=\"text-sm text-red-700\">{error}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          <DialogFooter className=\"gap-4 pt-8 border-t border-gray-100\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n              disabled={uploading}\n              className=\"flex-1 h-14 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-semibold text-base rounded-xl transition-all duration-200\"\n            >\n              <X className=\"w-5 h-5 ml-2\" />\n              إلغاء\n            </Button>\n            <Button\n              type=\"submit\"\n              disabled={uploading || !selectedFile || !title.trim()}\n              className=\"flex-1 h-14 bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 text-white font-bold text-base shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl border-0 transform hover:scale-105 active:scale-95\"\n            >\n              {uploading ? (\n                <>\n                  <div className=\"w-6 h-6 ml-2 border-3 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span className=\"font-bold\">جاري الرفع...</span>\n                </>\n              ) : (\n                <>\n                  <div className=\"bg-white/20 rounded-full p-1 ml-2\">\n                    <Upload className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <span className=\"font-bold\">رفع الصورة</span>\n                </>\n              )}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AAOA;AAAA;AAAA;AAAA;AAAA;AACA;AAtBA;;;;;;;;;;;AA0Ce,SAAS,kBAAkB,EACxC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,eAAe,EACf,iBAAiB,EACM;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,IAAI,iBAAiB;gBACnB,YAAY;gBACZ,cAAc;YAChB,OAAO,IAAI,mBAAmB;gBAC5B,cAAc;gBACd,YAAY;YACd;QACF;IACF,GAAG;QAAC;QAAM;QAAiB;KAAkB;IAE7C,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,qBAAqB;gBACrB,kBAAkB;gBAElB,cAAc;gBACd,MAAM,qBAAqB,MAAM,MAAM;gBACvC,IAAI,mBAAmB,EAAE,EAAE;oBACzB,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;oBACpD,cAAc,eAAe,UAAU,IAAI,EAAE;gBAC/C;gBAEA,eAAe;gBACf,MAAM,kBAAkB,MAAM,MAAM;gBACpC,IAAI,gBAAgB,EAAE,EAAE;oBACtB,MAAM,cAAc,MAAM,gBAAgB,IAAI;oBAC9C,WAAW,YAAY,OAAO,IAAI,EAAE;gBACtC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACxC,SAAU;gBACR,qBAAqB;gBACrB,kBAAkB;YACpB;QACF;QAEA,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,CAAC;QACxB,sBAAsB;QACtB,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAa;QAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,SAAS;YACT;QACF;QAEA,qCAAqC;QACrC,MAAM,UAAU,IAAI,OAAO,KAAK,MAAM;;QACtC,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,sBAAsB;QACtB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,cAAc,EAAE,MAAM,EAAE;QAC1B;QACA,OAAO,aAAa,CAAC;IACvB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QACpB,MAAM,OAAO,MAAM,YAAY,CAAC,KAAK,CAAC,EAAE;QACxC,IAAI,MAAM;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc;IACtB;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,cAAc;QACd,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc;YACjB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,mBAAmB;YACnB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,iBAAiB,MAAM,MAAM,eAAe;gBAChD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtB,MAAM,YAAY,MAAM,eAAe,IAAI;gBAC3C,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,aAAa,MAAM,eAAe,IAAI;YAE5C,8BAA8B;YAC9B,MAAM,kBAAkB,MAAM,MAAM,gBAAgB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,MAAM,IAAI;oBACjB,aAAa,YAAY,IAAI,MAAM;oBACnC,WAAW,WAAW,QAAQ;oBAC9B,YAAY,cAAc,eAAe,SAAS,aAAa;oBAC/D,UAAU,YAAY,aAAa,SAAS,WAAW;gBACzD;YACF;YAEA,IAAI,CAAC,gBAAgB,EAAE,EAAE;gBACvB,MAAM,YAAY,MAAM,gBAAgB,IAAI;gBAC5C,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,sBAAsB;YACtB,SAAS;YACT,eAAe;YACf,cAAc;YACd,YAAY;YACZ,gBAAgB;YAChB,cAAc;YAEd;YACA,aAAa;QACf,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB;QACrB,aAAa,OAAO,EAAE;IACxB;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,MAAM;QACN,cAAc;QACd,yBAAyB;QACzB,WAAU;kBAEV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,8OAAC;;kDACC,8OAAC,kIAAA,CAAA,cAAW;wCAAC,WAAU;kDAAoC;;;;;;kDAC3D,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;8BAKhD,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsC;;;;;;;;;;;;gCAGxD,CAAC,6BACA,8OAAC;oCACC,WAAU;oCACV,QAAQ;oCACR,YAAY;oCACZ,SAAS;;sDAET,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAE1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;8DAEhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAkE;;;;;;sEACrG,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAA+D;;;;;;sEAClG,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAqE;;;;;;;;;;;;;;;;;;sDAI5G,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAmI,UAAS;;;;;;;;;;;8DAEzK,8OAAC;8DAAK;;;;;;;;;;;;;;;;;yDAIV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,KAAK;wDACL,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;4DACV,UAAU;;8EAEV,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;sDAOtC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;oEAAyB,MAAK;oEAAe,SAAQ;8EAClE,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAwI,UAAS;;;;;;;;;;;;;;;;0EAGhL,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAuC,aAAa,IAAI;;;;;;kFACrE,8OAAC;wEAAE,WAAU;;4EACV,CAAC,aAAa,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;kEAItD,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA2D;;;;;;;;;;;;;;;;;;;;;;;8CAQpF,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;oCACV,UAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAyB,MAAK;4CAAe,SAAQ;sDAClE,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAiF,UAAS;;;;;;;;;;;sDAEvH,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsC;;;;;;;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;;sEAC/B,8OAAC;4DAAK,WAAU;;;;;;wDAAyC;;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,aAAY;oDACZ,UAAU;oDACV,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAA4B;;;;;;;8DAGhD,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAU,eAAe,CAAC;wDACvC,YAAY;wDACZ,IAAI,UAAU,QAAQ,cAAc,QAAQ,sBAAsB;;oDACpE;oDAAG,UAAU,aAAa;;sEACxB,8OAAC,kIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAa,iBAAiB,oBAAoB;;;;;;;;;;;sEAEjE,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAChB,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oMAAA,CAAA,QAAS;gFAAC,WAAU;;;;;;4EAA+B;;;;;;;;;;;;gEAIvD,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAiB,OAAO,OAAO,EAAE;kFAC1C,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,sMAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFACjB,OAAO,KAAK;;;;;;;uEAHA,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAYpC,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAA2B;;;;;;;8DAGjD,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAY,eAAe,CAAC;wDACzC,cAAc;wDACd,IAAI,UAAU,QAAQ,YAAY,QAAQ,sBAAsB;;oDAClE;oDAAG,UAAU,aAAa,qBAAqB,aAAa;;sEAC1D,8OAAC,kIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAa,oBAAoB,oBAAoB,aAAa,SAAS,mBAAmB;;;;;;;;;;;sEAE7G,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAChB,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oMAAA,CAAA,QAAS;gFAAC,WAAU;;;;;;4EAA+B;;;;;;;;;;;;gEAIvD,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,aAAU;wEAAmB,OAAO,SAAS,EAAE;kFAC9C,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFACnB,SAAS,KAAK;;;;;;;uEAHF,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;sDAYpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAc,WAAU;;sEACrC,8OAAC;4DAAI,WAAU;4DAAwB,MAAK;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAiF,UAAS;;;;;;;;;;;wDACjH;;;;;;;8DAGR,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,aAAY;oDACZ,UAAU;oDACV,MAAM;oDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;wBAOjB,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAuB,MAAK;4CAAe,SAAQ;sDAChE,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU;oCACV,WAAU;;sDAEV,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGhC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,aAAa,CAAC,gBAAgB,CAAC,MAAM,IAAI;oCACnD,WAAU;8CAET,0BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;qEAG9B;;0DACE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 1662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/gallery/folder-view.tsx"], "sourcesContent": ["'use client'\n\n// import { useState } from 'react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport {\n  Folder,\n  Image as ImageIcon,\n  Calendar,\n  Users,\n  ArrowRight,\n  Eye,\n  Edit,\n  Trash2,\n  // MoreVertical,\n  Plus\n} from 'lucide-react'\n\n\ninterface GalleryFolder {\n  id: string\n  title: string\n  description?: string\n  photosCount: number\n  coverPhoto?: {\n    id: string\n    imagePath: string\n    title: string\n  } | null\n  type: 'activity' | 'general' | 'folder'\n}\n\ninterface FolderViewProps {\n  folders: GalleryFolder[]\n  onFolderClick: (folder: GalleryFolder) => void\n  onEditFolder?: (folder: GalleryFolder) => void\n  onDeleteFolder?: (folder: GalleryFolder) => void\n  canEdit?: boolean\n  canDelete?: boolean\n  loading?: boolean\n}\n\nexport default function FolderView({\n  folders,\n  onFolderClick,\n  onEditFolder,\n  onDeleteFolder,\n  canEdit = false,\n  canDelete = false,\n  loading\n}: FolderViewProps) {\n  if (loading) {\n    return (\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\">\n        {[...Array(8)].map((_, index) => (\n          <Card key={index} className=\"animate-pulse\">\n            <CardContent className=\"p-0\">\n              <div className=\"aspect-video bg-gray-200 rounded-t-lg\"></div>\n              <div className=\"p-4 space-y-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    )\n  }\n\n  if (folders.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <Folder className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد مجلدات</h3>\n        <p className=\"text-gray-500\">لم يتم العثور على أي مجلدات</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\">\n      {folders.map((folder) => (\n        <Card\n          key={folder.id}\n          className=\"group hover:shadow-lg transition-all duration-200 border-2 hover:border-diwan-300 relative\"\n        >\n          <CardContent className=\"p-0\">\n            {/* أزرار التحكم */}\n            {(canEdit || canDelete) && (folder.type === 'activity' || folder.type === 'folder') && (\n              <div className=\"absolute top-2 left-2 z-10 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity\">\n                {canEdit && onEditFolder && (\n                  <Button\n                    variant=\"secondary\"\n                    size=\"sm\"\n                    className=\"h-8 w-8 p-0 bg-white/90 hover:bg-white\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      onEditFolder(folder)\n                    }}\n                    title=\"تعديل المجلد\"\n                  >\n                    <Edit className=\"h-3 w-3\" />\n                  </Button>\n                )}\n                {canDelete && onDeleteFolder && (\n                  <Button\n                    variant=\"secondary\"\n                    size=\"sm\"\n                    className=\"h-8 w-8 p-0 bg-white/90 hover:bg-white text-red-600 hover:text-red-700\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      onDeleteFolder(folder)\n                    }}\n                    title=\"حذف المجلد\"\n                  >\n                    <Trash2 className=\"h-3 w-3\" />\n                  </Button>\n                )}\n              </div>\n            )}\n\n            {/* صورة الغلاف */}\n            <div\n              className=\"relative aspect-video overflow-hidden rounded-t-lg bg-gradient-to-br from-diwan-50 to-diwan-100 cursor-pointer\"\n              onClick={() => onFolderClick(folder)}\n            >\n              {folder.coverPhoto ? (\n                <img\n                  src={folder.coverPhoto.imagePath}\n                  alt={folder.coverPhoto.title}\n                  className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200\"\n                />\n              ) : (\n                <div className=\"w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100\">\n                  <div className=\"bg-diwan-100 rounded-full p-4 mb-3\">\n                    <Folder className=\"w-8 h-8 text-diwan-600\" />\n                  </div>\n                  <p className=\"text-sm font-medium text-gray-600 mb-1\">مجلد فارغ</p>\n                  <p className=\"text-xs text-gray-500 text-center px-2\">\n                    {folder.type === 'folder' ? 'مجلد جديد' : 'لا توجد صور'}\n                  </p>\n                  {folder.photosCount === 0 && (\n                    <div className=\"mt-2 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium\">\n                      جديد\n                    </div>\n                  )}\n                </div>\n              )}\n              \n              {/* أيقونة النوع */}\n              <div className=\"absolute top-2 right-2\">\n                <Badge\n                  variant=\"secondary\"\n                  className={`text-xs ${\n                    folder.type === 'activity'\n                      ? 'bg-green-100 text-green-700'\n                      : folder.type === 'folder'\n                      ? 'bg-purple-100 text-purple-700'\n                      : 'bg-blue-100 text-blue-700'\n                  }`}\n                >\n                  {folder.type === 'activity' ? (\n                    <>\n                      <Calendar className=\"w-3 h-3 ml-1\" />\n                      نشاط\n                    </>\n                  ) : folder.type === 'folder' ? (\n                    <>\n                      <Folder className=\"w-3 h-3 ml-1\" />\n                      مجلد\n                    </>\n                  ) : (\n                    <>\n                      <Users className=\"w-3 h-3 ml-1\" />\n                      عام\n                    </>\n                  )}\n                </Badge>\n              </div>\n\n              {/* عدد الصور */}\n              <div className=\"absolute bottom-2 left-2\">\n                <Badge\n                  className={`text-xs ${\n                    folder.photosCount === 0\n                      ? 'bg-orange-500/80 text-white'\n                      : 'bg-black/70 text-white'\n                  }`}\n                >\n                  <ImageIcon className=\"w-3 h-3 ml-1\" />\n                  {folder.photosCount === 0 ? 'فارغ' : `${folder.photosCount} صورة`}\n                </Badge>\n              </div>\n\n              {/* زر العرض */}\n              <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center\">\n                <Button\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                  onClick={() => onFolderClick(folder)}\n                >\n                  {folder.photosCount === 0 ? (\n                    <>\n                      <Plus className=\"w-4 h-4 ml-2\" />\n                      إضافة صور\n                    </>\n                  ) : (\n                    <>\n                      <Eye className=\"w-4 h-4 ml-2\" />\n                      عرض الصور\n                    </>\n                  )}\n                </Button>\n              </div>\n            </div>\n\n            {/* معلومات المجلد */}\n            <div className=\"p-4\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1 min-w-0\">\n                  <h3 className=\"text-sm font-medium text-gray-900 truncate group-hover:text-diwan-700 transition-colors\">\n                    {folder.title}\n                  </h3>\n                  {folder.description && (\n                    <p className=\"text-xs text-gray-500 mt-1 line-clamp-2\">\n                      {folder.description}\n                    </p>\n                  )}\n                </div>\n                <ArrowRight className=\"w-4 h-4 text-gray-400 group-hover:text-diwan-500 transition-colors flex-shrink-0 mr-2\" />\n              </div>\n\n              {/* إحصائيات إضافية */}\n              <div className=\"mt-3 flex items-center justify-between text-xs text-gray-500\">\n                <span>\n                  {folder.photosCount === 1 \n                    ? 'صورة واحدة' \n                    : folder.photosCount === 2 \n                    ? 'صورتان' \n                    : `${folder.photosCount} صور`\n                  }\n                </span>\n                <span className=\"flex items-center\">\n                  <Folder className=\"w-3 h-3 ml-1\" />\n                  مجلد\n                </span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      ))}\n    </div>\n  )\n}\n\n// مكون إضافي لعرض إحصائيات المجلدات\nexport function FoldersStats({ folders }: { folders: GalleryFolder[] }) {\n  const totalPhotos = folders.reduce((sum, folder) => sum + folder.photosCount, 0)\n  const activityFolders = folders.filter(f => f.type === 'activity').length\n  const generalFolders = folders.filter(f => f.type === 'general').length\n\n  return (\n    <div className=\"grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6\">\n      <Card>\n        <CardContent className=\"p-4 text-center\">\n          <Folder className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />\n          <div className=\"text-2xl font-bold text-blue-600\">{folders.length}</div>\n          <div className=\"text-sm text-gray-600\">إجمالي المجلدات</div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardContent className=\"p-4 text-center\">\n          <ImageIcon className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n          <div className=\"text-2xl font-bold text-green-600\">{totalPhotos}</div>\n          <div className=\"text-sm text-gray-600\">إجمالي الصور</div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardContent className=\"p-4 text-center\">\n          <Calendar className=\"w-8 h-8 text-purple-600 mx-auto mb-2\" />\n          <div className=\"text-2xl font-bold text-purple-600\">{activityFolders}</div>\n          <div className=\"text-sm text-gray-600\">مجلدات الأنشطة</div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardContent className=\"p-4 text-center\">\n          <Users className=\"w-8 h-8 text-orange-600 mx-auto mb-2\" />\n          <div className=\"text-2xl font-bold text-orange-600\">{generalFolders}</div>\n          <div className=\"text-sm text-gray-600\">المجلدات العامة</div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA,mCAAmC;AACnC;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AA2Ce,SAAS,WAAW,EACjC,OAAO,EACP,aAAa,EACb,YAAY,EACZ,cAAc,EACd,UAAU,KAAK,EACf,YAAY,KAAK,EACjB,OAAO,EACS;IAChB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC,gIAAA,CAAA,OAAI;oBAAa,WAAU;8BAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;mBALV;;;;;;;;;;IAYnB;IAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;8BAClB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,gIAAA,CAAA,OAAI;gBAEH,WAAU;0BAEV,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;wBAEpB,CAAC,WAAW,SAAS,KAAK,CAAC,OAAO,IAAI,KAAK,cAAc,OAAO,IAAI,KAAK,QAAQ,mBAChF,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,8BACV,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,aAAa;oCACf;oCACA,OAAM;8CAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;gCAGnB,aAAa,gCACZ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,eAAe;oCACjB;oCACA,OAAM;8CAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAO1B,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc;;gCAE5B,OAAO,UAAU,iBAChB,8OAAC;oCACC,KAAK,OAAO,UAAU,CAAC,SAAS;oCAChC,KAAK,OAAO,UAAU,CAAC,KAAK;oCAC5B,WAAU;;;;;yDAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAE,WAAU;sDAAyC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDACV,OAAO,IAAI,KAAK,WAAW,cAAc;;;;;;wCAE3C,OAAO,WAAW,KAAK,mBACtB,8OAAC;4CAAI,WAAU;sDAAgF;;;;;;;;;;;;8CAQrG,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,WAAW,CAAC,QAAQ,EAClB,OAAO,IAAI,KAAK,aACZ,gCACA,OAAO,IAAI,KAAK,WAChB,kCACA,6BACJ;kDAED,OAAO,IAAI,KAAK,2BACf;;8DACE,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;2DAGrC,OAAO,IAAI,KAAK,yBAClB;;8DACE,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;yEAIrC;;8DACE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAQ1C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCACJ,WAAW,CAAC,QAAQ,EAClB,OAAO,WAAW,KAAK,IACnB,gCACA,0BACJ;;0DAEF,8OAAC,oMAAA,CAAA,QAAS;gDAAC,WAAU;;;;;;4CACpB,OAAO,WAAW,KAAK,IAAI,SAAS,GAAG,OAAO,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;8CAKrE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAE5B,OAAO,WAAW,KAAK,kBACtB;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;yEAInC;;8DACE,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAS1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,OAAO,KAAK;;;;;;gDAEd,OAAO,WAAW,kBACjB,8OAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;;;;;;;sDAIzB,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAIxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDACE,OAAO,WAAW,KAAK,IACpB,eACA,OAAO,WAAW,KAAK,IACvB,WACA,GAAG,OAAO,WAAW,CAAC,IAAI,CAAC;;;;;;sDAGjC,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;eAjKtC,OAAO,EAAE;;;;;;;;;;AA2KxB;AAGO,SAAS,aAAa,EAAE,OAAO,EAAgC;IACpE,MAAM,cAAc,QAAQ,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,WAAW,EAAE;IAC9E,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,MAAM;IACzE,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;IAEvE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAI,WAAU;sCAAoC,QAAQ,MAAM;;;;;;sCACjE,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAI3C,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,oMAAA,CAAA,QAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAI,WAAU;sCAAqC;;;;;;sCACpD,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAI3C,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;sCAAsC;;;;;;sCACrD,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAI3C,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAI,WAAU;sCAAsC;;;;;;sCACrD,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAKjD", "debugId": null}}, {"offset": {"line": 2286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/gallery/create-activity-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport '@/styles/gallery.css'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogFooter,\n} from '@/components/ui/dialog'\nimport { Calendar, MapPin, Plus, Folder, Sparkles } from 'lucide-react'\n\ninterface CreateActivityDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess: () => void\n}\n\nexport default function CreateActivityDialog({\n  open,\n  onOpenChange,\n  onSuccess,\n}: CreateActivityDialogProps) {\n  const [title, setTitle] = useState('')\n  const [description, setDescription] = useState('')\n  const [location, setLocation] = useState('')\n  const [startDate, setStartDate] = useState('')\n  const [endDate, setEndDate] = useState('')\n  const [creating, setCreating] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!title.trim()) {\n      setError('يرجى إدخال عنوان النشاط')\n      return\n    }\n\n    if (!startDate) {\n      setError('يرجى إدخال تاريخ بداية النشاط')\n      return\n    }\n\n    setCreating(true)\n    setError(null)\n\n    try {\n      const response = await fetch('/api/gallery-folders', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          title: title.trim(),\n          description: description.trim() || undefined,\n          location: location.trim() || undefined,\n          startDate: startDate || undefined,\n          endDate: endDate || undefined,\n        }),\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'فشل في إنشاء المجلد')\n      }\n\n      const data = await response.json()\n      console.log('تم إنشاء المجلد بنجاح:', data)\n\n      // إعادة تعيين النموذج\n      setTitle('')\n      setDescription('')\n      setLocation('')\n      setStartDate('')\n      setEndDate('')\n      setError(null)\n\n      // إغلاق النافذة أولاً\n      onOpenChange(false)\n\n      // ثم تحديث القائمة\n      setTimeout(() => {\n        onSuccess()\n      }, 100)\n    } catch (error: any) {\n      console.error('خطأ في إنشاء المجلد:', error)\n      setError(error.message || 'حدث خطأ في إنشاء المجلد')\n    } finally {\n      setCreating(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-[50vw] max-h-[90vh] overflow-y-auto\">\n        <DialogHeader className=\"text-center pb-6\">\n          <div className=\"mx-auto w-16 h-16 bg-gradient-to-br from-diwan-500 to-diwan-600 rounded-full flex items-center justify-center mb-4\">\n            <Folder className=\"w-8 h-8 text-white\" />\n          </div>\n          <DialogTitle className=\"text-2xl font-bold text-diwan-600 mb-2\">\n            إنشاء مجلد جديد\n          </DialogTitle>\n          <p className=\"text-gray-600 text-sm\">\n            أنشئ مجلد جديد لتنظيم صور النشاط أو المناسبة\n          </p>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* شريط التقدم */}\n          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n            <div\n              className=\"bg-gradient-to-r from-diwan-500 to-diwan-600 h-2 rounded-full transition-all duration-300\"\n              style={{\n                width: `${\n                  title.trim() && startDate ? '100%' :\n                  title.trim() || startDate ? '50%' : '25%'\n                }%`\n              }}\n            ></div>\n          </div>\n          {/* معلومات المجلد الأساسية */}\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3\">\n                <Sparkles className=\"w-4 h-4 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-blue-800\">معلومات المجلد</h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* عنوان المجلد */}\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"title\" className=\"text-sm font-medium text-gray-700 flex items-center\">\n                  <Folder className=\"w-4 h-4 ml-2 text-diwan-600\" />\n                  اسم المجلد *\n                </Label>\n                <Input\n                  id=\"title\"\n                  value={title}\n                  onChange={(e) => setTitle(e.target.value)}\n                  placeholder=\"مثال: حفل زفاف أحمد محمد\"\n                  disabled={creating}\n                  required\n                  className=\"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white\"\n                />\n              </div>\n\n              {/* موقع النشاط */}\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"location\" className=\"text-sm font-medium text-gray-700 flex items-center\">\n                  <MapPin className=\"w-4 h-4 ml-2 text-green-600\" />\n                  الموقع (اختياري)\n                </Label>\n                <Input\n                  id=\"location\"\n                  value={location}\n                  onChange={(e) => setLocation(e.target.value)}\n                  placeholder=\"مثال: قاعة الأفراح الكبرى\"\n                  disabled={creating}\n                  className=\"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white\"\n                />\n              </div>\n            </div>\n\n            {/* وصف المجلد */}\n            <div className=\"space-y-3 mt-6\">\n              <Label htmlFor=\"description\" className=\"text-sm font-medium text-gray-700\">\n                وصف المجلد (اختياري)\n              </Label>\n              <Textarea\n                id=\"description\"\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"أضف وصفاً مفصلاً للمجلد والمناسبة...\"\n                disabled={creating}\n                rows={4}\n                className=\"enhanced-border border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white resize-none\"\n              />\n            </div>\n          </div>\n\n          {/* تواريخ النشاط */}\n          <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3\">\n                <Calendar className=\"w-4 h-4 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-green-800\">تواريخ النشاط</h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* تاريخ البداية */}\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"startDate\" className=\"text-sm font-medium text-gray-700 flex items-center\">\n                  <Calendar className=\"w-4 h-4 ml-2 text-green-600\" />\n                  تاريخ البداية *\n                </Label>\n                <Input\n                  id=\"startDate\"\n                  type=\"date\"\n                  value={startDate}\n                  onChange={(e) => setStartDate(e.target.value)}\n                  disabled={creating}\n                  required\n                  className=\"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white\"\n                />\n              </div>\n\n              {/* تاريخ النهاية */}\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"endDate\" className=\"text-sm font-medium text-gray-700 flex items-center\">\n                  <Calendar className=\"w-4 h-4 ml-2 text-orange-600\" />\n                  تاريخ النهاية (اختياري)\n                </Label>\n                <Input\n                  id=\"endDate\"\n                  type=\"date\"\n                  value={endDate}\n                  onChange={(e) => setEndDate(e.target.value)}\n                  disabled={creating}\n                  min={startDate}\n                  className=\"enhanced-border h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 bg-white\"\n                />\n              </div>\n            </div>\n\n            <div className=\"mt-4 p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-lg border border-green-200\">\n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"w-5 h-5 text-green-600 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"mr-3\">\n                  <p className=\"text-sm font-medium text-green-800\">نصيحة مفيدة</p>\n                  <p className=\"text-sm text-green-700 mt-1\">\n                    إذا لم تحدد تاريخ النهاية، سيتم استخدام تاريخ البداية كتاريخ النهاية تلقائياً\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* معاينة المجلد */}\n          {title.trim() && (\n            <div className=\"bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                  <Folder className=\"w-4 h-4 text-white\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-purple-800\">معاينة المجلد</h3>\n              </div>\n\n              <div className=\"bg-white rounded-lg p-4 border border-purple-200\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-diwan-500 to-diwan-600 rounded-lg flex items-center justify-center mr-4\">\n                    <Folder className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-semibold text-gray-900\">{title}</h4>\n                    {description && (\n                      <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">{description}</p>\n                    )}\n                    <div className=\"flex items-center mt-2 text-xs text-gray-500\">\n                      {location && (\n                        <span className=\"flex items-center ml-4\">\n                          <MapPin className=\"w-3 h-3 ml-1\" />\n                          {location}\n                        </span>\n                      )}\n                      {startDate && (\n                        <span className=\"flex items-center\">\n                          <Calendar className=\"w-3 h-3 ml-1\" />\n                          {new Date(startDate).toLocaleDateString('ar-JO')}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-xs text-gray-500\">0 صورة</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* رسالة الخطأ */}\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 rounded-xl p-4\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"w-5 h-5 text-red-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"mr-3\">\n                  <p className=\"text-sm font-medium text-red-800\">{error}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          <DialogFooter className=\"gap-4 pt-6 border-t border-gray-100\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n              disabled={creating}\n              className=\"enhanced-button flex-1 h-14 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-semibold text-base rounded-xl transition-all duration-200\"\n            >\n              إلغاء\n            </Button>\n            <Button\n              type=\"submit\"\n              disabled={creating || !title.trim() || !startDate}\n              className=\"enhanced-button flex-1 h-14 bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 hover:from-purple-700 hover:via-purple-800 hover:to-indigo-800 text-white font-bold text-base shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl border-0 transform hover:scale-105 active:scale-95\"\n            >\n              {creating ? (\n                <>\n                  <div className=\"w-6 h-6 ml-2 border-3 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span className=\"font-bold\">جاري الإنشاء...</span>\n                </>\n              ) : (\n                <>\n                  <div className=\"bg-white/20 rounded-full p-1 ml-2\">\n                    <Plus className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <span className=\"font-bold\">إنشاء المجلد</span>\n                </>\n              )}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAfA;;;;;;;;;;AAuBe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,YAAY,EACZ,SAAS,EACiB;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,WAAW;YACd,SAAS;YACT;QACF;QAEA,YAAY;QACZ,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,MAAM,IAAI;oBACjB,aAAa,YAAY,IAAI,MAAM;oBACnC,UAAU,SAAS,IAAI,MAAM;oBAC7B,WAAW,aAAa;oBACxB,SAAS,WAAW;gBACtB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,sBAAsB;YACtB,SAAS;YACT,eAAe;YACf,YAAY;YACZ,aAAa;YACb,WAAW;YACX,SAAS;YAET,sBAAsB;YACtB,aAAa;YAEb,mBAAmB;YACnB,WAAW;gBACT;YACF,GAAG;QACL,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,YAAY;QACd;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAyC;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,GACL,MAAM,IAAI,MAAM,YAAY,SAC5B,MAAM,IAAI,MAAM,YAAY,QAAQ,MACrC,CAAC,CAAC;gCACL;;;;;;;;;;;sCAIJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;;;;;;;8CAGtD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;;sEAC/B,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAgC;;;;;;;8DAGpD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,aAAY;oDACZ,UAAU;oDACV,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAW,WAAU;;sEAClC,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAgC;;;;;;;8DAGpD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,aAAY;oDACZ,UAAU;oDACV,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;sDAAoC;;;;;;sDAG3E,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,aAAY;4CACZ,UAAU;4CACV,MAAM;4CACN,WAAU;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;;sEACnC,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAgC;;;;;;;8DAGtD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC5C,UAAU;oDACV,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;sEACjC,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAGvD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC1C,UAAU;oDACV,KAAK;oDACL,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAgC,MAAK;oDAAe,SAAQ;8DACzE,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAmI,UAAS;;;;;;;;;;;;;;;;0DAG3K,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASlD,MAAM,IAAI,oBACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;;;;;;;8CAGxD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;oDAC5C,6BACC,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;kEAE1D,8OAAC;wDAAI,WAAU;;4DACZ,0BACC,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEACjB;;;;;;;4DAGJ,2BACC,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,IAAI,KAAK,WAAW,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;0DAKhD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQhD,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAuB,MAAK;4CAAe,SAAQ;sDAChE,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;sCAMzD,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,YAAY,CAAC,MAAM,IAAI,MAAM,CAAC;oCACxC,WAAU;8CAET,yBACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;qEAG9B;;0DACE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 3098, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/gallery/edit-activity-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  <PERSON>alogHeader,\n  DialogTitle,\n  DialogFooter,\n} from '@/components/ui/dialog'\nimport { Calendar, MapPin, Edit } from 'lucide-react'\n\ninterface Activity {\n  id: string\n  title: string\n  description?: string\n  location?: string\n  startDate: string\n  endDate?: string\n}\n\ninterface EditActivityDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess: () => void\n  activity: Activity | null\n}\n\nexport default function EditActivityDialog({\n  open,\n  onOpenChange,\n  onSuccess,\n  activity,\n}: EditActivityDialogProps) {\n  const [title, setTitle] = useState('')\n  const [description, setDescription] = useState('')\n  const [location, setLocation] = useState('')\n  const [startDate, setStartDate] = useState('')\n  const [endDate, setEndDate] = useState('')\n  const [updating, setUpdating] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  // تحديث الحقول عند تغيير النشاط\n  useEffect(() => {\n    if (activity && open) {\n      setTitle(activity.title || '')\n      setDescription(activity.description || '')\n      setLocation(activity.location || '')\n      setStartDate(activity.startDate ? new Date(activity.startDate).toISOString().split('T')[0] : '')\n      setEndDate(activity.endDate ? new Date(activity.endDate).toISOString().split('T')[0] : '')\n      setError(null)\n    }\n  }, [activity, open])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!activity) return\n\n    if (!title.trim()) {\n      setError('يرجى إدخال عنوان النشاط')\n      return\n    }\n\n    if (!startDate) {\n      setError('يرجى إدخال تاريخ بداية النشاط')\n      return\n    }\n\n    setUpdating(true)\n    setError(null)\n\n    try {\n      const response = await fetch(`/api/activities/${activity.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          title: title.trim(),\n          description: description.trim() || undefined,\n          location: location.trim() || undefined,\n          startDate: new Date(startDate).toISOString(),\n          endDate: endDate ? new Date(endDate).toISOString() : new Date(startDate).toISOString(),\n        }),\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'فشل في تحديث النشاط')\n      }\n\n      onSuccess()\n      onOpenChange(false)\n    } catch (error: any) {\n      console.error('خطأ في تحديث النشاط:', error)\n      setError(error.message || 'حدث خطأ في تحديث النشاط')\n    } finally {\n      setUpdating(false)\n    }\n  }\n\n  if (!activity) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center\">\n            <Edit className=\"w-5 h-5 ml-2\" />\n            تعديل النشاط\n          </DialogTitle>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* عنوان النشاط */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"title\">عنوان النشاط *</Label>\n            <Input\n              id=\"title\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              placeholder=\"مثال: حفل زفاف أحمد محمد\"\n              disabled={updating}\n              required\n            />\n          </div>\n\n          {/* وصف النشاط */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"description\">وصف النشاط</Label>\n            <Textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"وصف مختصر للنشاط (اختياري)\"\n              disabled={updating}\n              rows={3}\n            />\n          </div>\n\n          {/* موقع النشاط */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"location\">الموقع</Label>\n            <div className=\"relative\">\n              <MapPin className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <Input\n                id=\"location\"\n                value={location}\n                onChange={(e) => setLocation(e.target.value)}\n                placeholder=\"موقع إقامة النشاط (اختياري)\"\n                disabled={updating}\n                className=\"pr-10\"\n              />\n            </div>\n          </div>\n\n          {/* تاريخ البداية */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"startDate\">تاريخ البداية *</Label>\n            <div className=\"relative\">\n              <Calendar className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <Input\n                id=\"startDate\"\n                type=\"date\"\n                value={startDate}\n                onChange={(e) => setStartDate(e.target.value)}\n                disabled={updating}\n                required\n                className=\"pr-10\"\n              />\n            </div>\n          </div>\n\n          {/* تاريخ النهاية */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"endDate\">تاريخ النهاية</Label>\n            <div className=\"relative\">\n              <Calendar className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <Input\n                id=\"endDate\"\n                type=\"date\"\n                value={endDate}\n                onChange={(e) => setEndDate(e.target.value)}\n                disabled={updating}\n                min={startDate}\n                className=\"pr-10\"\n              />\n            </div>\n          </div>\n\n          {/* رسالة الخطأ */}\n          {error && (\n            <div className=\"text-red-600 text-sm bg-red-50 p-3 rounded-md\">\n              {error}\n            </div>\n          )}\n\n          <DialogFooter>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n              disabled={updating}\n            >\n              إلغاء\n            </Button>\n            <Button\n              type=\"submit\"\n              disabled={updating || !title.trim() || !startDate}\n              className=\"bg-diwan-600 hover:bg-diwan-700\"\n            >\n              {updating ? (\n                <>\n                  <Edit className=\"w-4 h-4 ml-2 animate-spin\" />\n                  جاري التحديث...\n                </>\n              ) : (\n                <>\n                  <Edit className=\"w-4 h-4 ml-2\" />\n                  حفظ التغييرات\n                </>\n              )}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAdA;;;;;;;;;AAgCe,SAAS,mBAAmB,EACzC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,QAAQ,EACgB;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM;YACpB,SAAS,SAAS,KAAK,IAAI;YAC3B,eAAe,SAAS,WAAW,IAAI;YACvC,YAAY,SAAS,QAAQ,IAAI;YACjC,aAAa,SAAS,SAAS,GAAG,IAAI,KAAK,SAAS,SAAS,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;YAC7F,WAAW,SAAS,OAAO,GAAG,IAAI,KAAK,SAAS,OAAO,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;YACvF,SAAS;QACX;IACF,GAAG;QAAC;QAAU;KAAK;IAEnB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,WAAW;YACd,SAAS;YACT;QACF;QAEA,YAAY;QACZ,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE,EAAE;gBAC7D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,MAAM,IAAI;oBACjB,aAAa,YAAY,IAAI,MAAM;oBACnC,UAAU,SAAS,IAAI,MAAM;oBAC7B,WAAW,IAAI,KAAK,WAAW,WAAW;oBAC1C,SAAS,UAAU,IAAI,KAAK,SAAS,WAAW,KAAK,IAAI,KAAK,WAAW,WAAW;gBACtF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA;YACA,aAAa;QACf,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,YAAY;QACd;IACF;IAEA,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,2MAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;8BAKrC,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,UAAU;oCACV,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;oCACZ,UAAU;oCACV,MAAM;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAW;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,aAAY;4CACZ,UAAU;4CACV,WAAU;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAY;;;;;;8CAC3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5C,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAU;;;;;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,UAAU;4CACV,KAAK;4CACL,WAAU;;;;;;;;;;;;;;;;;;wBAMf,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,YAAY,CAAC,MAAM,IAAI,MAAM,CAAC;oCACxC,WAAU;8CAET,yBACC;;0DACE,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA8B;;qEAIhD;;0DACE,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD", "debugId": null}}, {"offset": {"line": 3494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/dashboard/gallery/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport '@/styles/gallery.css'\nimport { useSession } from 'next-auth/react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Dialog,\n  DialogContent,\n\n} from '@/components/ui/dialog'\nimport {\n  Plus,\n  Search,\n  Image as ImageIcon,\n  Upload,\n  Download,\n  Eye,\n  Edit,\n  Trash2,\n  Calendar,\n  User,\n\n  Folder,\n  X\n} from 'lucide-react'\nimport UploadPhotoDialog from '@/components/gallery/upload-photo-dialog'\nimport FolderView, { FoldersStats } from '@/components/gallery/folder-view'\nimport CreateActivityDialog from '@/components/gallery/create-activity-dialog'\nimport EditActivityDialog from '@/components/gallery/edit-activity-dialog'\nimport { useRouter } from 'next/navigation'\n\ninterface Photo {\n  id: string\n  title: string\n  description?: string\n  imagePath: string\n  uploadedAt: string\n  uploader: {\n    name: string\n  }\n  activityId?: string\n  activity?: {\n    id: string\n    title: string\n  }\n}\n\ninterface GalleryFolder {\n  id: string\n  title: string\n  description?: string\n  photosCount: number\n  coverPhoto?: {\n    id: string\n    imagePath: string\n    title: string\n  } | null\n  type: 'activity' | 'general' | 'folder'\n}\n\nexport default function GalleryPage() {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [photos, setPhotos] = useState<Photo[]>([])\n  const [folders, setFolders] = useState<GalleryFolder[]>([])\n  const [loading, setLoading] = useState(true)\n  const [search, setSearch] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('all')\n  const [viewMode, setViewMode] = useState<'folders' | 'photos'>('folders') // عرض المجلدات أو الصور\n  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)\n  const [isViewerOpen, setIsViewerOpen] = useState(false)\n  const [isUploadOpen, setIsUploadOpen] = useState(false)\n  const [isCreateActivityOpen, setIsCreateActivityOpen] = useState(false)\n  const [isEditActivityOpen, setIsEditActivityOpen] = useState(false)\n  const [selectedActivity, setSelectedActivity] = useState<{ id: string; title: string; description?: string } | null>(null)\n\n  const categories = [\n    { value: 'all', label: 'جميع الفئات' },\n    { value: 'activities', label: 'الأنشطة' },\n    { value: 'members', label: 'الأعضاء' },\n    { value: 'events', label: 'المناسبات' },\n    { value: 'meetings', label: 'الاجتماعات' },\n    { value: 'other', label: 'أخرى' },\n  ]\n\n  // جلب المجلدات\n  const fetchFolders = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/gallery?groupBy=activity')\n      if (!response.ok) {\n        throw new Error('فشل في جلب المجلدات')\n      }\n\n      const data = await response.json()\n      setFolders(data.folders || [])\n    } catch (error) {\n      console.error('خطأ في جلب المجلدات:', error)\n      setFolders([])\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // جلب الصور\n  const fetchPhotos = useCallback(async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams({\n        search,\n        category: selectedCategory,\n      })\n\n      const response = await fetch(`/api/gallery?${params}`)\n      if (!response.ok) {\n        throw new Error('فشل في جلب الصور')\n      }\n\n      const data = await response.json()\n      setPhotos(data.photos || [])\n    } catch (error) {\n      console.error('خطأ في جلب الصور:', error)\n      setPhotos([])\n    } finally {\n      setLoading(false)\n    }\n  }, [search, selectedCategory])\n\n  useEffect(() => {\n    if (session) {\n      if (viewMode === 'folders') {\n        fetchFolders()\n      } else {\n        fetchPhotos()\n      }\n    }\n  }, [search, selectedCategory, session, viewMode])\n\n  // فتح عارض الصور\n  const handleViewPhoto = (photo: Photo) => {\n    setSelectedPhoto(photo)\n    setIsViewerOpen(true)\n  }\n\n  // حذف صورة\n  const handleDeletePhoto = async (photoId: string) => {\n    if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) return\n\n    try {\n      const response = await fetch(`/api/gallery/${photoId}`, {\n        method: 'DELETE',\n      })\n\n      if (!response.ok) {\n        throw new Error('فشل في حذف الصورة')\n      }\n\n      // إعادة جلب الصور\n      fetchPhotos()\n    } catch (error) {\n      console.error('خطأ في حذف الصورة:', error)\n      alert('حدث خطأ في حذف الصورة')\n    }\n  }\n\n  // فتح مجلد\n  const handleFolderClick = (folder: GalleryFolder) => {\n    if (folder.type === 'folder') {\n      router.push(`/dashboard/gallery/folder/${folder.id}?type=folder`)\n    } else if (folder.type === 'activity') {\n      router.push(`/dashboard/gallery/folder/${folder.id}?type=activity`)\n    } else {\n      router.push(`/dashboard/gallery/folder/general`)\n    }\n  }\n\n  // تعديل مجلد\n  const handleEditFolder = async (folder: GalleryFolder) => {\n    try {\n      if (folder.type === 'activity') {\n        const response = await fetch(`/api/activities/${folder.id}`)\n        if (response.ok) {\n          const activity = await response.json()\n          setSelectedActivity(activity)\n          setIsEditActivityOpen(true)\n        }\n      } else if (folder.type === 'folder') {\n        const response = await fetch(`/api/gallery-folders/${folder.id}`)\n        if (response.ok) {\n          const folderData = await response.json()\n          setSelectedActivity(folderData)\n          setIsEditActivityOpen(true)\n        }\n      }\n    } catch (error) {\n      console.error('خطأ في جلب بيانات المجلد:', error)\n      alert('حدث خطأ في جلب بيانات المجلد')\n    }\n  }\n\n  // حذف مجلد\n  const handleDeleteFolder = async (folder: GalleryFolder) => {\n    if (folder.type === 'general') return\n\n    const confirmMessage = folder.photosCount > 0\n      ? `هذا المجلد يحتوي على ${folder.photosCount} صورة. لا يمكن حذفه إلا بعد حذف جميع الصور أو نقلها إلى مجلد آخر.`\n      : `هل أنت متأكد من حذف المجلد \"${folder.title}\"؟`\n\n    if (!confirm(confirmMessage)) return\n\n    try {\n      const apiUrl = folder.type === 'activity'\n        ? `/api/activities/${folder.id}`\n        : `/api/gallery-folders/${folder.id}`\n\n      const response = await fetch(apiUrl, {\n        method: 'DELETE',\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'فشل في حذف المجلد')\n      }\n\n      // إعادة جلب المجلدات\n      fetchFolders()\n    } catch (error: unknown) {\n      console.error('خطأ في حذف المجلد:', error)\n      alert((error as Error).message || 'حدث خطأ في حذف المجلد')\n    }\n  }\n\n  const canEdit = session?.user.role !== 'VIEWER'\n  const canDelete = session?.user.role === 'ADMIN'\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">معرض الصور</h1>\n          <p className=\"text-gray-600\">عرض وإدارة صور الديوان والأنشطة</p>\n        </div>\n        <div className=\"flex justify-center items-center h-64\">\n          <div className=\"text-gray-500\">جاري التحميل...</div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* رأس الصفحة المحسن */}\n      <div className=\"text-center mb-8\">\n        <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden\">\n          {/* خلفية متحركة */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-500 opacity-30 animate-pulse\"></div>\n\n          {/* المحتوى */}\n          <div className=\"relative z-10\">\n            <div className=\"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm\">\n              <ImageIcon className=\"w-10 h-10 text-white\" />\n            </div>\n\n            <h1 className=\"text-5xl font-black mb-4 text-white\">\n              معرض الصور\n            </h1>\n\n            <p className=\"text-xl font-semibold mb-6 text-purple-100\">\n              عرض وإدارة صور الديوان والأنشطة والمناسبات\n            </p>\n\n            <div className=\"flex items-center justify-center space-x-2 space-x-reverse\">\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n              <div className=\"h-1 w-8 rounded-full bg-white bg-opacity-40\"></div>\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* أزرار التحكم */}\n      <div className=\"flex justify-center mb-8\">\n        <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n\n            {/* أزرار التبديل بين العرض */}\n            <div className=\"flex bg-gray-100 rounded-xl p-1 border border-gray-200\">\n              <Button\n                variant={viewMode === 'folders' ? 'default' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setViewMode('folders')}\n                className={`text-sm transition-all px-4 py-2 rounded-lg ${\n                  viewMode === 'folders'\n                    ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-md'\n                    : 'text-gray-600 hover:bg-gray-200'\n                }`}\n              >\n                <Folder className=\"w-4 h-4 ml-2\" />\n                المجلدات\n              </Button>\n              <Button\n                variant={viewMode === 'photos' ? 'default' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setViewMode('photos')}\n                className={`text-sm transition-all px-4 py-2 rounded-lg ${\n                  viewMode === 'photos'\n                    ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-md'\n                    : 'text-gray-600 hover:bg-gray-200'\n                }`}\n              >\n                <ImageIcon className=\"w-4 h-4 ml-2\" />\n                جميع الصور\n              </Button>\n            </div>\n\n            {/* أزرار الإجراءات */}\n            {canEdit && (\n              <div className=\"flex gap-3\">\n                {/* زر إنشاء مجلد جديد - يظهر فقط في عرض المجلدات */}\n                {viewMode === 'folders' && (\n                  <Button\n                    onClick={() => setIsCreateActivityOpen(true)}\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold\"\n                  >\n                    <Plus className=\"w-5 h-5 ml-2\" />\n                    إنشاء مجلد جديد\n                  </Button>\n                )}\n\n                <Button\n                  onClick={() => setIsUploadOpen(true)}\n                  className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold\"\n                >\n                  <Upload className=\"w-5 h-5 ml-2\" />\n                  رفع صور جديدة\n                </Button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* إحصائيات سريعة محسنة */}\n      {viewMode === 'folders' ? (\n        <FoldersStats folders={folders} />\n      ) : (\n        <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4\">\n          <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n            <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl\"></div>\n\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n              <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>إجمالي الصور</CardTitle>\n              <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#007bff' }}>\n                <ImageIcon className=\"h-7 w-7 text-white\" />\n              </div>\n            </CardHeader>\n\n            <CardContent className=\"relative z-10\">\n              <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n                {photos.length}\n              </div>\n              <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n                صورة في المعرض\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n            <div className=\"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl\"></div>\n\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n              <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>صور الأنشطة</CardTitle>\n              <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#28a745' }}>\n                <Calendar className=\"h-7 w-7 text-white\" />\n              </div>\n            </CardHeader>\n\n            <CardContent className=\"relative z-10\">\n              <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n                {photos.filter(p => p.activityId).length}\n              </div>\n              <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n                صورة مرتبطة بالأنشطة\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n            <div className=\"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl\"></div>\n\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n              <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>صور عامة</CardTitle>\n              <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#800020' }}>\n                <User className=\"h-7 w-7 text-white\" />\n              </div>\n            </CardHeader>\n\n            <CardContent className=\"relative z-10\">\n              <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n                {photos.filter(p => !p.activityId).length}\n              </div>\n              <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n                صورة غير مصنفة\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500 to-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n            <div className=\"bg-gradient-to-r from-orange-500 to-orange-600 p-1 rounded-t-xl\"></div>\n\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n              <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>المجلدات</CardTitle>\n              <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#fd7e14' }}>\n                <Folder className=\"h-7 w-7 text-white\" />\n              </div>\n            </CardHeader>\n\n            <CardContent className=\"relative z-10\">\n              <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n                {folders.length}\n              </div>\n              <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n                مجلد منظم\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* أدوات البحث والتصفية المحسنة - تظهر فقط في عرض الصور */}\n      {viewMode === 'photos' && (\n        <Card className=\"border-0 shadow-md\">\n          <CardContent className=\"pt-6\">\n            <div className=\"flex flex-col lg:flex-row gap-4\">\n              {/* شريط البحث */}\n              <div className=\"relative flex-1\">\n                <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                <Input\n                  placeholder=\"البحث في الصور بالعنوان أو الوصف...\"\n                  value={search}\n                  onChange={(e) => setSearch(e.target.value)}\n                  className=\"search-input pr-12 h-11 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500\"\n                />\n              </div>\n\n              {/* فلتر الفئات */}\n              <div className=\"flex gap-3\">\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  className=\"px-4 py-2 h-11 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-diwan-500 focus:border-diwan-500 bg-white min-w-[150px]\"\n                >\n                  {categories.map(category => (\n                    <option key={category.value} value={category.value}>\n                      {category.label}\n                    </option>\n                  ))}\n                </select>\n\n                {/* زر مسح الفلاتر */}\n                {(search || selectedCategory !== 'all') && (\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => {\n                      setSearch('')\n                      setSelectedCategory('all')\n                    }}\n                    className=\"h-11 px-4 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 font-medium\"\n                  >\n                    <X className=\"w-4 h-4 ml-2\" />\n                    مسح الفلاتر\n                  </Button>\n                )}\n              </div>\n            </div>\n\n            {/* عرض نتائج البحث */}\n            {search && (\n              <div className=\"mt-4 text-sm text-gray-600\">\n                <span className=\"font-medium\">{photos.length}</span> نتيجة للبحث عن &quot;{search}&quot;\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n\n      {/* المحتوى الرئيسي */}\n      <Card className=\"border-0 shadow-md\">\n        <CardContent className=\"p-6\">\n          {viewMode === 'folders' ? (\n            <FolderView\n              folders={folders}\n              onFolderClick={handleFolderClick}\n              onEditFolder={handleEditFolder}\n              onDeleteFolder={handleDeleteFolder}\n              canEdit={canEdit}\n              canDelete={canDelete}\n              loading={loading}\n            />\n          ) : (\n            <>\n              {photos.length === 0 ? (\n                <div className=\"text-center py-16\">\n                  <div className=\"bg-gray-50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6\">\n                    <ImageIcon className=\"w-12 h-12 text-gray-400\" />\n                  </div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد صور</h3>\n                  <p className=\"text-gray-500 mb-6\">\n                    {search ? 'لم يتم العثور على صور تطابق البحث' : 'ابدأ بإضافة صور إلى المعرض'}\n                  </p>\n                  {canEdit && !search && (\n                    <Button\n                      onClick={() => setIsUploadOpen(true)}\n                      className=\"bg-diwan-600 hover:bg-diwan-700 text-white font-semibold px-8 py-3 h-12 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200\"\n                    >\n                      <Upload className=\"w-5 h-5 ml-2\" />\n                      رفع أول صورة\n                    </Button>\n                  )}\n                </div>\n              ) : (\n                <div className=\"gallery-grid\">\n                  {photos.map((photo) => (\n                    <div key={photo.id} className=\"gallery-card group relative bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100\">\n                      {/* الصورة */}\n                      <div className=\"aspect-square overflow-hidden bg-gray-100 relative\">\n                        <img\n                          src={photo.imagePath}\n                          alt={photo.title}\n                          className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 cursor-pointer\"\n                          onClick={() => handleViewPhoto(photo)}\n                        />\n\n                        {/* أزرار التحكم العائمة المحسنة */}\n                        <div className=\"floating-buttons absolute top-3 left-3 flex gap-2\">\n                          <Button\n                            variant=\"secondary\"\n                            size=\"sm\"\n                            onClick={() => handleViewPhoto(photo)}\n                            className=\"h-9 w-9 p-0 bg-blue-600 hover:bg-blue-700 text-white shadow-lg border-0 rounded-full\"\n                            title=\"عرض الصورة\"\n                          >\n                            <Eye className=\"w-4 h-4\" />\n                          </Button>\n                          {canEdit && (\n                            <Button\n                              variant=\"secondary\"\n                              size=\"sm\"\n                              className=\"h-9 w-9 p-0 bg-green-600 hover:bg-green-700 text-white shadow-lg border-0 rounded-full\"\n                              title=\"تعديل الصورة\"\n                            >\n                              <Edit className=\"w-4 h-4\" />\n                            </Button>\n                          )}\n                          <Button\n                            variant=\"secondary\"\n                            size=\"sm\"\n                            className=\"h-9 w-9 p-0 bg-purple-600 hover:bg-purple-700 text-white shadow-lg border-0 rounded-full\"\n                            title=\"تحميل الصورة\"\n                            onClick={() => {\n                              const link = document.createElement('a')\n                              link.href = photo.imagePath\n                              link.download = photo.title\n                              link.click()\n                            }}\n                          >\n                            <Download className=\"w-4 h-4\" />\n                          </Button>\n                          {canDelete && (\n                            <Button\n                              variant=\"secondary\"\n                              size=\"sm\"\n                              onClick={() => handleDeletePhoto(photo.id)}\n                              className=\"h-9 w-9 p-0 bg-red-600 hover:bg-red-700 text-white shadow-lg border-0 rounded-full\"\n                              title=\"حذف الصورة\"\n                            >\n                              <Trash2 className=\"w-4 h-4\" />\n                            </Button>\n                          )}\n                        </div>\n\n                        {/* شارة الفئة */}\n                        <div className=\"absolute bottom-2 right-2\">\n                          <Badge\n                            variant={photo.activityId ? \"default\" : \"secondary\"}\n                            className={`text-xs ${photo.activityId ? 'bg-diwan-600' : 'bg-gray-500'}`}\n                          >\n                            {photo.activityId ? 'نشاط' : 'عام'}\n                          </Badge>\n                        </div>\n                      </div>\n\n                      {/* معلومات الصورة */}\n                      <div className=\"p-4\">\n                        <h3 className=\"text-sm font-semibold text-gray-900 truncate mb-1\">\n                          {photo.title}\n                        </h3>\n                        {photo.description && (\n                          <p className=\"text-xs text-gray-500 line-clamp-2 mb-2\">\n                            {photo.description}\n                          </p>\n                        )}\n\n                        {/* معلومات إضافية */}\n                        <div className=\"flex items-center justify-between text-xs text-gray-400\">\n                          <span>{photo.uploader.name}</span>\n                          <span>{new Date(photo.uploadedAt).toLocaleDateString('ar-JO')}</span>\n                        </div>\n\n                        {/* النشاط المرتبط */}\n                        {photo.activity && (\n                          <div className=\"mt-2 text-xs text-diwan-600 bg-diwan-50 px-2 py-1 rounded truncate\">\n                            {photo.activity.title}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* عارض الصور المحسن */}\n      <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>\n        <DialogContent className=\"max-w-6xl max-h-[95vh] overflow-hidden p-0\">\n          {selectedPhoto && (\n            <div className=\"flex flex-col h-full\">\n              {/* رأس العارض */}\n              <div className=\"flex items-center justify-between p-6 border-b bg-white\">\n                <div>\n                  <h2 className=\"text-xl font-bold text-gray-900\">{selectedPhoto.title}</h2>\n                  <p className=\"text-sm text-gray-500\">\n                    رفعت بواسطة {selectedPhoto.uploader.name} في {new Date(selectedPhoto.uploadedAt).toLocaleDateString('ar-JO')}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-3\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => {\n                      const link = document.createElement('a')\n                      link.href = selectedPhoto.imagePath\n                      link.download = selectedPhoto.title\n                      link.click()\n                    }}\n                    className=\"bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 font-medium px-4 py-2 h-9\"\n                  >\n                    <Download className=\"w-4 h-4 ml-2\" />\n                    تحميل\n                  </Button>\n                  {canEdit && (\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      className=\"bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300 font-medium px-4 py-2 h-9\"\n                    >\n                      <Edit className=\"w-4 h-4 ml-2\" />\n                      تعديل\n                    </Button>\n                  )}\n                  {canDelete && (\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => {\n                        setIsViewerOpen(false)\n                        handleDeletePhoto(selectedPhoto.id)\n                      }}\n                      className=\"bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 font-medium px-4 py-2 h-9\"\n                    >\n                      <Trash2 className=\"w-4 h-4 ml-2\" />\n                      حذف\n                    </Button>\n                  )}\n                </div>\n              </div>\n\n              {/* الصورة الرئيسية */}\n              <div className=\"flex-1 bg-gray-900 flex items-center justify-center p-4\">\n                <img\n                  src={selectedPhoto.imagePath}\n                  alt={selectedPhoto.title}\n                  className=\"max-w-full max-h-full object-contain rounded-lg shadow-2xl\"\n                />\n              </div>\n\n              {/* معلومات الصورة */}\n              <div className=\"p-6 bg-gray-50 border-t\">\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                  {/* الوصف */}\n                  <div className=\"lg:col-span-2\">\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">الوصف</h4>\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">\n                      {selectedPhoto.description || 'لا يوجد وصف لهذه الصورة'}\n                    </p>\n                  </div>\n\n                  {/* التفاصيل */}\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 mb-3\">التفاصيل</h4>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-500\">الفئة:</span>\n                        <Badge variant={selectedPhoto.activityId ? \"default\" : \"secondary\"} className=\"text-xs\">\n                          {selectedPhoto.activityId ? 'نشاط' : 'عام'}\n                        </Badge>\n                      </div>\n\n                      {selectedPhoto.activity && (\n                        <div className=\"flex items-center justify-between\">\n                          <span className=\"text-gray-500\">النشاط:</span>\n                          <span className=\"text-diwan-600 font-medium\">{selectedPhoto.activity.title}</span>\n                        </div>\n                      )}\n\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-500\">تاريخ الرفع:</span>\n                        <span className=\"text-gray-900\">{new Date(selectedPhoto.uploadedAt).toLocaleDateString('ar-JO')}</span>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-500\">رفعت بواسطة:</span>\n                        <span className=\"text-gray-900\">{selectedPhoto.uploader.name}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </DialogContent>\n      </Dialog>\n\n      {/* مكون رفع الصور */}\n      <UploadPhotoDialog\n        open={isUploadOpen}\n        onOpenChange={setIsUploadOpen}\n        onSuccess={() => {\n          if (viewMode === 'folders') {\n            fetchFolders()\n          } else {\n            fetchPhotos()\n          }\n        }}\n      />\n\n      {/* مكون إنشاء نشاط جديد */}\n      <CreateActivityDialog\n        open={isCreateActivityOpen}\n        onOpenChange={setIsCreateActivityOpen}\n        onSuccess={fetchFolders}\n      />\n\n      {/* مكون تعديل النشاط */}\n      <EditActivityDialog\n        open={isEditActivityOpen}\n        onOpenChange={setIsEditActivityOpen}\n        onSuccess={fetchFolders}\n        activity={selectedActivity}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AACA;AACA;AAjCA;;;;;;;;;;;;;;;;AAgEe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,WAAW,wBAAwB;;IAClG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8D;IAErH,MAAM,aAAa;QACjB;YAAE,OAAO;YAAO,OAAO;QAAc;QACrC;YAAE,OAAO;YAAc,OAAO;QAAU;QACxC;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAU,OAAO;QAAY;QACtC;YAAE,OAAO;YAAY,OAAO;QAAa;QACzC;YAAE,OAAO;YAAS,OAAO;QAAO;KACjC;IAED,eAAe;IACf,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,WAAW,KAAK,OAAO,IAAI,EAAE;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,WAAW,EAAE;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,YAAY;IACZ,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC;gBACA,UAAU;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,QAAQ;YACrD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU,KAAK,MAAM,IAAI,EAAE;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,UAAU,EAAE;QACd,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAQ;KAAiB;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,IAAI,aAAa,WAAW;gBAC1B;YACF,OAAO;gBACL;YACF;QACF;IACF,GAAG;QAAC;QAAQ;QAAkB;QAAS;KAAS;IAEhD,iBAAiB;IACjB,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,WAAW;IACX,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,oCAAoC;QAEjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE;gBACtD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,kBAAkB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,WAAW;IACX,MAAM,oBAAoB,CAAC;QACzB,IAAI,OAAO,IAAI,KAAK,UAAU;YAC5B,OAAO,IAAI,CAAC,CAAC,0BAA0B,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC;QAClE,OAAO,IAAI,OAAO,IAAI,KAAK,YAAY;YACrC,OAAO,IAAI,CAAC,CAAC,0BAA0B,EAAE,OAAO,EAAE,CAAC,cAAc,CAAC;QACpE,OAAO;YACL,OAAO,IAAI,CAAC,CAAC,iCAAiC,CAAC;QACjD;IACF;IAEA,aAAa;IACb,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,IAAI,OAAO,IAAI,KAAK,YAAY;gBAC9B,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;gBAC3D,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,WAAW,MAAM,SAAS,IAAI;oBACpC,oBAAoB;oBACpB,sBAAsB;gBACxB;YACF,OAAO,IAAI,OAAO,IAAI,KAAK,UAAU;gBACnC,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE;gBAChE,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,aAAa,MAAM,SAAS,IAAI;oBACtC,oBAAoB;oBACpB,sBAAsB;gBACxB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,WAAW;IACX,MAAM,qBAAqB,OAAO;QAChC,IAAI,OAAO,IAAI,KAAK,WAAW;QAE/B,MAAM,iBAAiB,OAAO,WAAW,GAAG,IACxC,CAAC,qBAAqB,EAAE,OAAO,WAAW,CAAC,iEAAiE,CAAC,GAC7G,CAAC,4BAA4B,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC;QAEnD,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,IAAI;YACF,MAAM,SAAS,OAAO,IAAI,KAAK,aAC3B,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE,GAC9B,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE;YAEvC,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,qBAAqB;YACrB;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM,AAAC,MAAgB,OAAO,IAAI;QACpC;IACF;IAEA,MAAM,UAAU,SAAS,KAAK,SAAS;IACvC,MAAM,YAAY,SAAS,KAAK,SAAS;IAEzC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAE/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIvC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAIpD,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAI1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,YAAY,YAAY;wCAC9C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,4CAA4C,EACtD,aAAa,YACT,wEACA,mCACJ;;0DAEF,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGrC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,WAAW,YAAY;wCAC7C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,4CAA4C,EACtD,aAAa,WACT,wEACA,mCACJ;;0DAEF,8OAAC,oMAAA,CAAA,QAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAMzC,yBACC,8OAAC;gCAAI,WAAU;;oCAEZ,aAAa,2BACZ,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,wBAAwB;wCACvC,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAKrC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU9C,aAAa,0BACZ,8OAAC,+IAAA,CAAA,eAAY;gBAAC,SAAS;;;;;qCAEvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,oMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,OAAO,MAAM;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIxB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;;;;;;kDAE1C,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIpB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,OAAO,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,MAAM;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAItB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,QAAQ,MAAM;;;;;;kDAEjB,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;YASzE,aAAa,0BACZ,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,WAAU;sDAET,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;oDAA4B,OAAO,SAAS,KAAK;8DAC/C,SAAS,KAAK;mDADJ,SAAS,KAAK;;;;;;;;;;wCAO9B,CAAC,UAAU,qBAAqB,KAAK,mBACpC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;gDACP,UAAU;gDACV,oBAAoB;4CACtB;4CACA,WAAU;;8DAEV,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;wBAQrC,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAe,OAAO,MAAM;;;;;;gCAAQ;gCAAuB;gCAAO;;;;;;;;;;;;;;;;;;0BAQ5F,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACpB,aAAa,0BACZ,8OAAC,+IAAA,CAAA,UAAU;wBACT,SAAS;wBACT,eAAe;wBACf,cAAc;wBACd,gBAAgB;wBAChB,SAAS;wBACT,WAAW;wBACX,SAAS;;;;;6CAGX;kCACG,OAAO,MAAM,KAAK,kBACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CACV,SAAS,sCAAsC;;;;;;gCAEjD,WAAW,CAAC,wBACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;iDAMzC,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oCAAmB,WAAU;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAK,MAAM,SAAS;oDACpB,KAAK,MAAM,KAAK;oDAChB,WAAU;oDACV,SAAS,IAAM,gBAAgB;;;;;;8DAIjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,gBAAgB;4DAC/B,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;wDAEhB,yBACC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAGpB,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,OAAM;4DACN,SAAS;gEACP,MAAM,OAAO,SAAS,aAAa,CAAC;gEACpC,KAAK,IAAI,GAAG,MAAM,SAAS;gEAC3B,KAAK,QAAQ,GAAG,MAAM,KAAK;gEAC3B,KAAK,KAAK;4DACZ;sEAEA,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;wDAErB,2BACC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,kBAAkB,MAAM,EAAE;4DACzC,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAMxB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDACJ,SAAS,MAAM,UAAU,GAAG,YAAY;wDACxC,WAAW,CAAC,QAAQ,EAAE,MAAM,UAAU,GAAG,iBAAiB,eAAe;kEAExE,MAAM,UAAU,GAAG,SAAS;;;;;;;;;;;;;;;;;sDAMnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,MAAM,KAAK;;;;;;gDAEb,MAAM,WAAW,kBAChB,8OAAC;oDAAE,WAAU;8DACV,MAAM,WAAW;;;;;;8DAKtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,MAAM,QAAQ,CAAC,IAAI;;;;;;sEAC1B,8OAAC;sEAAM,IAAI,KAAK,MAAM,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;gDAItD,MAAM,QAAQ,kBACb,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ,CAAC,KAAK;;;;;;;;;;;;;mCAzFnB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0BAuGhC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAc,cAAc;0BACxC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;8BACtB,+BACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC,cAAc,KAAK;;;;;;0DACpE,8OAAC;gDAAE,WAAU;;oDAAwB;oDACtB,cAAc,QAAQ,CAAC,IAAI;oDAAC;oDAAK,IAAI,KAAK,cAAc,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;kDAGxG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,OAAO,SAAS,aAAa,CAAC;oDACpC,KAAK,IAAI,GAAG,cAAc,SAAS;oDACnC,KAAK,QAAQ,GAAG,cAAc,KAAK;oDACnC,KAAK,KAAK;gDACZ;gDACA,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAGtC,yBACC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAIpC,2BACC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,gBAAgB;oDAChB,kBAAkB,cAAc,EAAE;gDACpC;gDACA,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAQ3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK,cAAc,SAAS;oCAC5B,KAAK,cAAc,KAAK;oCACxB,WAAU;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,cAAc,WAAW,IAAI;;;;;;;;;;;;sDAKlC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAS,cAAc,UAAU,GAAG,YAAY;oEAAa,WAAU;8EAC3E,cAAc,UAAU,GAAG,SAAS;;;;;;;;;;;;wDAIxC,cAAc,QAAQ,kBACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAA8B,cAAc,QAAQ,CAAC,KAAK;;;;;;;;;;;;sEAI9E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAiB,IAAI,KAAK,cAAc,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;sEAGzF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAiB,cAAc,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAY9E,8OAAC,0JAAA,CAAA,UAAiB;gBAChB,MAAM;gBACN,cAAc;gBACd,WAAW;oBACT,IAAI,aAAa,WAAW;wBAC1B;oBACF,OAAO;wBACL;oBACF;gBACF;;;;;;0BAIF,8OAAC,6JAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,cAAc;gBACd,WAAW;;;;;;0BAIb,8OAAC,2JAAA,CAAA,UAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,WAAW;gBACX,UAAU;;;;;;;;;;;;AAIlB", "debugId": null}}]}