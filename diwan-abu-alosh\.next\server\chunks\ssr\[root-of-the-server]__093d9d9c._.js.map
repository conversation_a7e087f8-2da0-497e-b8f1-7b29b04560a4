{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // This interface extends the base input props\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-11 w-full rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-sm font-medium text-secondary-700 transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-400 focus-visible:outline-none focus-visible:border-primary-500 focus-visible:ring-4 focus-visible:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 hover:border-secondary-300\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gbACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-white\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-white hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-white hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-white hover:bg-destructive/80\",\n        outline: \"text-white border-white\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-amber-500 text-white hover:bg-amber-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qLACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm text-navy-800\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-right align-middle font-medium text-navy-700 [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle text-navy-800 [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;YAC5D,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gEAAgE;QAC7E,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-11 w-full items-center justify-between rounded-xl border-2 border-gray-300 bg-white px-4 py-3 text-base font-medium text-black transition-all duration-200 hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/20 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-5 w-5 text-gray-600 transition-transform duration-200 data-[state=open]:rotate-180\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1 text-black\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4 text-gray-600\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1 text-black\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4 text-gray-600\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-xl border border-gray-300 bg-white text-black shadow-xl backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-2\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-2 pl-10 pr-3 text-sm font-bold text-gray-700 bg-gray-50\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-pointer select-none items-center rounded-lg py-3 pl-10 pr-3 text-sm font-medium text-black transition-colors hover:bg-blue-50 focus:bg-blue-100 focus:text-blue-800 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[state=checked]:bg-blue-100 data-[state=checked]:text-blue-800 data-[state=checked]:font-semibold\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-3 flex h-4 w-4 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4 text-blue-600\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8UACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ydACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yWACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {\n  // This interface extends the base label props\n}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = \"Label\"\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  // This interface extends the base textarea props\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { X } from \"lucide-react\"\n\ninterface DialogProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n  enableScrollInteraction?: boolean // خيار لتفعيل التفاعل مع التمرير\n  maxHeight?: string // الحد الأقصى للارتفاع\n}\n\ninterface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Dialog = ({\n  open,\n  onOpenChange,\n  children,\n  enableScrollInteraction = true,\n  maxHeight = \"90vh\"\n}: DialogProps) => {\n  const dialogRef = React.useRef<HTMLDivElement>(null)\n  const containerRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onOpenChange?.(false)\n      }\n    }\n\n    const handleWheel = (e: WheelEvent) => {\n      if (!open || !enableScrollInteraction || !dialogRef.current) return\n\n      // السماح بالتمرير داخل الحوار فقط\n      const dialogElement = dialogRef.current\n      const isScrollable = dialogElement.scrollHeight > dialogElement.clientHeight\n\n      if (isScrollable) {\n        // التحقق من أن الماوس داخل منطقة الحوار\n        const rect = dialogElement.getBoundingClientRect()\n        const isInsideDialog = e.clientX >= rect.left && e.clientX <= rect.right &&\n                              e.clientY >= rect.top && e.clientY <= rect.bottom\n\n        if (isInsideDialog) {\n          // السماح بالتمرير الطبيعي داخل الحوار\n          return\n        }\n      }\n\n      // منع التمرير خارج الحوار\n      e.preventDefault()\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEscape)\n      if (enableScrollInteraction) {\n        document.addEventListener('wheel', handleWheel, { passive: false })\n      }\n      // عدم منع التمرير في الخلفية للسماح بالتمرير داخل الحوار\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.removeEventListener('wheel', handleWheel)\n    }\n  }, [open, onOpenChange, enableScrollInteraction])\n\n  if (!open) return null\n\n  return (\n    <div\n      ref={containerRef}\n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n    >\n      <div\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300\"\n        onClick={() => onOpenChange?.(false)}\n      />\n      <div\n        ref={dialogRef}\n        className={cn(\n          \"relative z-50 w-full overflow-hidden animate-in fade-in-0 zoom-in-95 duration-300\",\n          \"smooth-scroll\", // فئة CSS للتمرير السلس\n          enableScrollInteraction ? \"overflow-y-auto\" : \"overflow-hidden\"\n        )}\n        style={{\n          maxHeight: maxHeight,\n          transform: 'translate3d(0, 0, 0)', // تحسين الأداء\n          willChange: 'transform', // تحسين الأداء\n        }}\n      >\n        {children}\n      </div>\n    </div>\n  )\n}\n\nconst DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200\",\n        \"backdrop-blur-sm bg-white/95\", // تحسين الشفافية\n        \"hover:shadow-3xl transition-shadow duration-300\", // تأثير الظل عند التمرير\n        \"max-h-full overflow-y-auto smooth-scroll\", // تمكين التمرير السلس\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogContent.displayName = \"DialogContent\"\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-bold leading-none tracking-tight text-slate-800\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = \"DialogTitle\"\n\nconst DialogDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-slate-600 font-medium\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = \"DialogDescription\"\n\nconst DialogTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement>\n>(({ className, ...props }, ref) => (\n  <button\n    ref={ref}\n    className={className}\n    {...props}\n  />\n))\nDialogTrigger.displayName = \"DialogTrigger\"\n\nconst DialogClose = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & { onOpenChange?: (open: boolean) => void }\n>(({ className, onOpenChange, ...props }, ref) => (\n  <button\n    ref={ref}\n    type=\"button\"\n    onClick={() => onOpenChange?.(false)}\n    className={cn(\n      \"absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10\",\n      className\n    )}\n    {...props}\n  >\n    <X className=\"h-4 w-4 text-slate-600\" />\n    <span className=\"sr-only\">إغلاق</span>\n  </button>\n))\nDialogClose.displayName = \"DialogClose\"\n\nexport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n  DialogClose,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAkBA,MAAM,SAAS,CAAC,EACd,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,0BAA0B,IAAI,EAC9B,YAAY,MAAM,EACN;IACZ,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAkB;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAkB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,eAAe;YACjB;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,UAAU,OAAO,EAAE;YAE7D,kCAAkC;YAClC,MAAM,gBAAgB,UAAU,OAAO;YACvC,MAAM,eAAe,cAAc,YAAY,GAAG,cAAc,YAAY;YAE5E,IAAI,cAAc;gBAChB,wCAAwC;gBACxC,MAAM,OAAO,cAAc,qBAAqB;gBAChD,MAAM,iBAAiB,EAAE,OAAO,IAAI,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,KAAK,KAAK,IAClD,EAAE,OAAO,IAAI,KAAK,GAAG,IAAI,EAAE,OAAO,IAAI,KAAK,MAAM;gBAEvE,IAAI,gBAAgB;oBAClB,sCAAsC;oBACtC;gBACF;YACF;YAEA,0BAA0B;YAC1B,EAAE,cAAc;QAClB;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,IAAI,yBAAyB;gBAC3B,SAAS,gBAAgB,CAAC,SAAS,aAAa;oBAAE,SAAS;gBAAM;YACnE;QACA,yDAAyD;QAC3D;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,SAAS;QACxC;IACF,GAAG;QAAC;QAAM;QAAc;KAAwB;IAEhD,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAEV,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAEhC,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA,iBACA,0BAA0B,oBAAoB;gBAEhD,OAAO;oBACL,WAAW;oBACX,WAAW;oBACX,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAIT;AAEA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA,gCACA,mDACA,4CACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE,oBACxC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,SAAS,IAAM,eAAe;QAC9B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wRACA;QAED,GAAG,KAAK;;0BAET,8OAAC,4LAAA,CAAA,IAAC;gBAAC,WAAU;;;;;;0BACb,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/expenses/expense-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { DollarSign, Receipt } from 'lucide-react'\n\n// Schema محلي للنموذج (يختلف عن schema الخادم)\nconst expenseFormSchema = z.object({\n  amount: z.number().positive('المبلغ يجب أن يكون أكبر من صفر'),\n  date: z.string().min(1, 'التاريخ مطلوب'),\n  description: z.string().min(1, 'الوصف مطلوب'),\n  category: z.enum(['MEETINGS', 'EVENTS', 'MAINTENANCE', 'SOCIAL', 'GENERAL']).default('GENERAL'),\n  recipient: z.string().optional(),\n  notes: z.string().optional(),\n})\n\ntype ExpenseFormInput = z.infer<typeof expenseFormSchema>\n\ninterface ExpenseDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess?: () => void\n  expense?: {\n    id: string\n    amount: number\n    date: string\n    description: string\n    category: string\n    recipient?: string\n    notes?: string\n  } | null\n}\n\nexport default function ExpenseDialog({\n  open,\n  onOpenChange,\n  onSuccess,\n  expense = null,\n}: ExpenseDialogProps) {\n  const [loading, setLoading] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    reset,\n    setValue,\n    watch,\n    formState: { errors },\n  } = useForm<ExpenseFormInput>({\n    resolver: zodResolver(expenseFormSchema),\n    defaultValues: {\n      amount: 0,\n      date: new Date().toISOString().split('T')[0],\n      description: '',\n      category: 'GENERAL',\n      recipient: '',\n      notes: '',\n    },\n  })\n\n  const selectedCategory = watch('category')\n\n  useEffect(() => {\n    if (open) {\n      if (expense) {\n        // تعديل مصروف موجود\n        reset({\n          amount: expense.amount,\n          date: expense.date.split('T')[0],\n          description: expense.description,\n          category: expense.category as any,\n          recipient: expense.recipient || '',\n          notes: expense.notes || '',\n        })\n      } else {\n        // إضافة مصروف جديد\n        reset({\n          amount: 0,\n          date: new Date().toISOString().split('T')[0],\n          description: '',\n          category: 'GENERAL',\n          recipient: '',\n          notes: '',\n        })\n      }\n    }\n  }, [open, expense, reset])\n\n  const onSubmit = async (data: ExpenseFormInput) => {\n    try {\n      setLoading(true)\n\n      // تنظيف وتحويل البيانات\n      const submitData = {\n        amount: Number(data.amount),\n        date: new Date(data.date),\n        description: data.description.trim(),\n        category: data.category,\n        recipient: data.recipient?.trim() || null,\n        notes: data.notes?.trim() || null,\n      }\n\n      const isEditing = !!expense\n      const url = isEditing ? `/api/expenses/${expense.id}` : '/api/expenses'\n      const method = isEditing ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submitData),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.error || 'حدث خطأ')\n      }\n\n      alert(isEditing ? 'تم تحديث المصروف بنجاح!' : 'تم إضافة المصروف بنجاح!')\n      onSuccess?.()\n      onOpenChange(false)\n    } catch (error: any) {\n      console.error('خطأ في حفظ المصروف:', error)\n      alert(error.message || 'حدث خطأ في حفظ المصروف')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // const getCategoryText = (category: string) => {\n  //   switch (category) {\n  //     case 'MEETINGS': return 'اجتماعات'\n  //     case 'EVENTS': return 'مناسبات'\n  //     case 'MAINTENANCE': return 'إصلاحات'\n  //     case 'SOCIAL': return 'اجتماعية'\n  //     case 'GENERAL': return 'عامة'\n  //     default: return category\n  //   }\n  // }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-[50vw] max-h-[90vh] overflow-y-auto\">\n        <DialogHeader className=\"pb-6 border-b border-gray-100\">\n          <DialogTitle className=\"text-2xl font-bold text-gray-900 flex items-center gap-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center\">\n              <Receipt className=\"w-6 h-6 text-white\" />\n            </div>\n            {expense ? 'تعديل المصروف' : 'إضافة مصروف جديد'}\n          </DialogTitle>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"p-8 space-y-8 bg-gray-50/30\">\n          {/* القسم الأول: المعلومات الأساسية */}\n          <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-red-600 rounded-full\"></div>\n              المعلومات الأساسية\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"amount\" className=\"text-sm font-medium text-gray-700 flex items-center gap-1\">\n                  المبلغ (دينار أردني)\n                  <span className=\"text-red-500\">*</span>\n                </Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"amount\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    {...register('amount', { valueAsNumber: true })}\n                    className={`h-12 pr-12 transition-all duration-200 ${\n                      errors.amount\n                        ? 'border-red-300 focus:border-red-500 focus:ring-red-200'\n                        : 'border-gray-200 focus:border-red-500 focus:ring-red-100'\n                    }`}\n                    placeholder=\"0.00\"\n                  />\n                  <div className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400\">\n                    <DollarSign className=\"w-5 h-5\" />\n                  </div>\n                </div>\n                {errors.amount && (\n                  <p className=\"text-red-500 text-sm flex items-center gap-1\">\n                    <span className=\"w-1 h-1 bg-red-500 rounded-full\"></span>\n                    {errors.amount.message}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"date\" className=\"text-sm font-medium text-gray-700 flex items-center gap-1\">\n                  التاريخ\n                  <span className=\"text-red-500\">*</span>\n                </Label>\n                <Input\n                  id=\"date\"\n                  type=\"date\"\n                  {...register('date')}\n                  className={`h-12 transition-all duration-200 ${\n                    errors.date\n                      ? 'border-red-300 focus:border-red-500 focus:ring-red-200'\n                      : 'border-gray-200 focus:border-red-500 focus:ring-red-100'\n                  }`}\n                />\n                {errors.date && (\n                  <p className=\"text-red-500 text-sm flex items-center gap-1\">\n                    <span className=\"w-1 h-1 bg-red-500 rounded-full\"></span>\n                    {errors.date.message}\n                  </p>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* القسم الثاني: تفاصيل المصروف */}\n          <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-orange-600 rounded-full\"></div>\n              تفاصيل المصروف\n            </h3>\n            <div className=\"space-y-6\">\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"description\" className=\"text-sm font-medium text-gray-700 flex items-center gap-1\">\n                  الوصف\n                  <span className=\"text-red-500\">*</span>\n                </Label>\n                <Input\n                  id=\"description\"\n                  {...register('description')}\n                  placeholder=\"مثال: شراء مستلزمات، دفع فواتير، تكاليف فعالية\"\n                  className={`h-12 transition-all duration-200 ${\n                    errors.description\n                      ? 'border-red-300 focus:border-red-500 focus:ring-red-200'\n                      : 'border-gray-200 focus:border-red-500 focus:ring-red-100'\n                  }`}\n                />\n                {errors.description && (\n                  <p className=\"text-red-500 text-sm flex items-center gap-1\">\n                    <span className=\"w-1 h-1 bg-red-500 rounded-full\"></span>\n                    {errors.description.message}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"space-y-3\">\n                  <Label className=\"text-sm font-medium text-gray-700\">\n                    الفئة\n                  </Label>\n                  <Select value={selectedCategory} onValueChange={(value) => setValue('category', value as any)}>\n                    <SelectTrigger className=\"h-12 border-gray-200 focus:border-red-500 focus:ring-red-100\">\n                      <SelectValue placeholder=\"اختر الفئة\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"GENERAL\">🏢 عامة</SelectItem>\n                      <SelectItem value=\"MEETINGS\">🤝 اجتماعات</SelectItem>\n                      <SelectItem value=\"EVENTS\">🎉 مناسبات</SelectItem>\n                      <SelectItem value=\"MAINTENANCE\">🔧 إصلاحات</SelectItem>\n                      <SelectItem value=\"SOCIAL\">👥 اجتماعية</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <Label htmlFor=\"recipient\" className=\"text-sm font-medium text-gray-700\">\n                    الجهة المستفيدة\n                  </Label>\n                  <Input\n                    id=\"recipient\"\n                    {...register('recipient')}\n                    placeholder=\"مثال: شركة، مورد، مقاول\"\n                    className=\"h-12 border-gray-200 focus:border-red-500 focus:ring-red-100 transition-all duration-200\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <Label htmlFor=\"notes\" className=\"text-sm font-medium text-gray-700\">\n                  ملاحظات إضافية\n                </Label>\n                <Textarea\n                  id=\"notes\"\n                  {...register('notes')}\n                  placeholder=\"أي ملاحظات أو تفاصيل إضافية...\"\n                  className=\"min-h-[100px] border-gray-200 focus:border-red-500 focus:ring-red-100 transition-all duration-200 resize-none\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* أزرار الحفظ والإلغاء */}\n          <div className=\"flex justify-end gap-4 pt-6 border-t border-gray-100\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n              className=\"h-12 px-8 border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:text-gray-800 transition-all duration-200 font-medium\"\n              disabled={loading}\n            >\n              إلغاء\n            </Button>\n            <Button\n              type=\"submit\"\n              disabled={loading}\n              className=\"h-12 px-8 bg-gradient-to-r from-red-600 via-red-700 to-orange-600 hover:from-red-700 hover:via-red-800 hover:to-orange-700 text-white font-bold transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl\"\n            >\n              {loading ? (\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span>جاري الحفظ...</span>\n                </div>\n              ) : (\n                <span>{expense ? 'تحديث المصروف' : 'إضافة المصروف'}</span>\n              )}\n            </Button>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAMA;AAOA;AAAA;AAvBA;;;;;;;;;;;;;AAyBA,+CAA+C;AAC/C,MAAM,oBAAoB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,QAAQ,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,MAAM,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,UAAU,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAU;QAAe;QAAU;KAAU,EAAE,OAAO,CAAC;IACrF,WAAW,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAmBe,SAAS,cAAc,EACpC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,UAAU,IAAI,EACK;IACnB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,QAAQ;YACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5C,aAAa;YACb,UAAU;YACV,WAAW;YACX,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB,MAAM;IAE/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,IAAI,SAAS;gBACX,oBAAoB;gBACpB,MAAM;oBACJ,QAAQ,QAAQ,MAAM;oBACtB,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBAChC,aAAa,QAAQ,WAAW;oBAChC,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS,IAAI;oBAChC,OAAO,QAAQ,KAAK,IAAI;gBAC1B;YACF,OAAO;gBACL,mBAAmB;gBACnB,MAAM;oBACJ,QAAQ;oBACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5C,aAAa;oBACb,UAAU;oBACV,WAAW;oBACX,OAAO;gBACT;YACF;QACF;IACF,GAAG;QAAC;QAAM;QAAS;KAAM;IAEzB,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,WAAW;YAEX,wBAAwB;YACxB,MAAM,aAAa;gBACjB,QAAQ,OAAO,KAAK,MAAM;gBAC1B,MAAM,IAAI,KAAK,KAAK,IAAI;gBACxB,aAAa,KAAK,WAAW,CAAC,IAAI;gBAClC,UAAU,KAAK,QAAQ;gBACvB,WAAW,KAAK,SAAS,EAAE,UAAU;gBACrC,OAAO,KAAK,KAAK,EAAE,UAAU;YAC/B;YAEA,MAAM,YAAY,CAAC,CAAC;YACpB,MAAM,MAAM,YAAY,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,GAAG;YACxD,MAAM,SAAS,YAAY,QAAQ;YAEnC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;YACjC;YAEA,MAAM,YAAY,4BAA4B;YAC9C;YACA,aAAa;QACf,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,MAAM,OAAO,IAAI;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,kDAAkD;IAClD,wBAAwB;IACxB,yCAAyC;IACzC,sCAAsC;IACtC,2CAA2C;IAC3C,uCAAuC;IACvC,oCAAoC;IACpC,+BAA+B;IAC/B,MAAM;IACN,IAAI;IAEJ,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;4BAEpB,UAAU,kBAAkB;;;;;;;;;;;;8BAIjC,8OAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAI,WAAU;;;;;;wCAAwC;;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAS,WAAU;;wDAA4D;sEAE5F,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,KAAI;4DACH,GAAG,SAAS,UAAU;gEAAE,eAAe;4DAAK,EAAE;4DAC/C,WAAW,CAAC,uCAAuC,EACjD,OAAO,MAAM,GACT,2DACA,2DACJ;4DACF,aAAY;;;;;;sEAEd,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAGzB,OAAO,MAAM,kBACZ,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;4DAAK,WAAU;;;;;;wDACf,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;sDAK5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAO,WAAU;;wDAA4D;sEAE1F,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEjC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,OAAO;oDACpB,WAAW,CAAC,iCAAiC,EAC3C,OAAO,IAAI,GACP,2DACA,2DACJ;;;;;;gDAEH,OAAO,IAAI,kBACV,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;4DAAK,WAAU;;;;;;wDACf,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAI,WAAU;;;;;;wCAA2C;;;;;;;8CAG5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAc,WAAU;;wDAA4D;sEAEjG,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEjC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,cAAc;oDAC3B,aAAY;oDACZ,WAAW,CAAC,iCAAiC,EAC3C,OAAO,WAAW,GACd,2DACA,2DACJ;;;;;;gDAEH,OAAO,WAAW,kBACjB,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;4DAAK,WAAU;;;;;;wDACf,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;;sDAKjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAoC;;;;;;sEAGrD,8OAAC,kIAAA,CAAA,SAAM;4DAAC,OAAO;4DAAkB,eAAe,CAAC,QAAU,SAAS,YAAY;;8EAC9E,8OAAC,kIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sFACZ,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;sFAC5B,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAW;;;;;;sFAC7B,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAS;;;;;;sFAC3B,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAc;;;;;;sFAChC,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAS;;;;;;;;;;;;;;;;;;;;;;;;8DAKjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAoC;;;;;;sEAGzE,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACF,GAAG,SAAS,YAAY;4DACzB,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;8DAAoC;;;;;;8DAGrE,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACF,GAAG,SAAS,QAAQ;oDACrB,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;6DAGR,8OAAC;kDAAM,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/dashboard/expenses/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport {\n  Plus,\n  Search,\n  Edit,\n  Trash2,\n  Download,\n  Filter,\n  TrendingDown,\n  DollarSign,\n  Receipt,\n  Calendar\n} from 'lucide-react'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { formatCurrency, formatDate, getExpenseCategoryText } from '@/lib/utils'\nimport ExpenseDialog from '@/components/expenses/expense-dialog'\nimport jsPDF from 'jspdf'\nimport html2canvas from 'html2canvas'\n\ninterface Expense {\n  id: string\n  amount: number\n  date: string\n  description: string\n  category: string\n  recipient?: string\n  notes?: string\n  createdBy: {\n    name: string\n  }\n}\n\ninterface ExpensesResponse {\n  expenses: Expense[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    pages: number\n  }\n}\n\nexport default function ExpensesPage() {\n  const { data: session } = useSession()\n  const [expenses, setExpenses] = useState<Expense[]>([])\n  const [loading, setLoading] = useState(true)\n  const [search, setSearch] = useState('')\n  const [category, setCategory] = useState('all')\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    pages: 0,\n  })\n  const [stats, setStats] = useState({\n    totalAmount: 0,\n    totalCount: 0,\n    byCategory: [] as Array<{ category: string; _sum: { amount: number }; _count: number }>\n  })\n  const [isExpenseDialogOpen, setIsExpenseDialogOpen] = useState(false)\n  const [editingExpense, setEditingExpense] = useState<Expense | null>(null)\n\n  // جلب المصروفات\n  const fetchExpenses = useCallback(async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams({\n        search,\n        category,\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString(),\n      })\n\n      const response = await fetch(`/api/expenses?${params}`)\n      if (!response.ok) throw new Error('فشل في جلب المصروفات')\n\n      const data: ExpensesResponse = await response.json()\n      setExpenses(data.expenses)\n      setPagination(data.pagination)\n    } catch (error) {\n      console.error('خطأ في جلب المصروفات:', error)\n    } finally {\n      setLoading(false)\n    }\n  }, [search, category, pagination.page, pagination.limit])\n\n  // جلب الإحصائيات\n  const fetchStats = async () => {\n    try {\n      const response = await fetch('/api/expenses?limit=1000')\n      if (!response.ok) return\n\n      const data: ExpensesResponse = await response.json()\n      const totalAmount = data.expenses.reduce((sum, expense) => sum + expense.amount, 0)\n      const totalCount = data.expenses.length\n\n      // حساب الإحصائيات حسب الفئة\n      const byCategory = data.expenses.reduce((acc: Array<{ category: string; _sum: { amount: number }; _count: number }>, expense) => {\n        const existing = acc.find((item) => item.category === expense.category)\n        if (existing) {\n          existing._sum.amount += expense.amount\n          existing._count += 1\n        } else {\n          acc.push({\n            category: expense.category,\n            _sum: { amount: expense.amount },\n            _count: 1\n          })\n        }\n        return acc\n      }, [])\n\n      setStats({ totalAmount, totalCount, byCategory })\n    } catch (error) {\n      console.error('خطأ في جلب الإحصائيات:', error)\n    }\n  }\n\n  useEffect(() => {\n    fetchExpenses()\n  }, [fetchExpenses])\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  // تعديل مصروف\n  const handleEditExpense = (expense: Expense) => {\n    setEditingExpense(expense)\n    setIsExpenseDialogOpen(true)\n  }\n\n  // حذف مصروف\n  const handleDeleteExpense = async (id: string) => {\n    if (!confirm('هل أنت متأكد من حذف هذا المصروف؟\\n\\nهذا الإجراء لا يمكن التراجع عنه.')) return\n\n    try {\n      const response = await fetch(`/api/expenses/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        alert(error.error || 'فشل في حذف المصروف')\n        return\n      }\n\n      alert('تم حذف المصروف بنجاح')\n      fetchExpenses()\n      fetchStats()\n    } catch (error) {\n      console.error('خطأ في حذف المصروف:', error)\n      alert('حدث خطأ في حذف المصروف')\n    }\n  }\n\n  // إضافة مصروف جديد\n  const handleAddNewExpense = () => {\n    setEditingExpense(null)\n    setIsExpenseDialogOpen(true)\n  }\n\n  // إغلاق نافذة التعديل\n  const handleCloseDialog = () => {\n    setIsExpenseDialogOpen(false)\n    setEditingExpense(null)\n  }\n\n\n\n  // تصدير البيانات PDF باللغة العربية\n  const handleExportExpensesPDF = () => {\n    // إنشاء عنصر HTML مؤقت للطباعة\n    const printElement = document.createElement('div')\n    printElement.style.cssText = `\n      position: absolute;\n      top: -9999px;\n      left: -9999px;\n      width: 210mm;\n      background: white;\n      padding: 20px;\n      font-family: Arial, sans-serif;\n      direction: rtl;\n      text-align: right;\n      color: #000;\n      font-size: 12px;\n    `\n\n    // محتوى التقرير باللغة العربية\n    printElement.innerHTML = `\n      <div style=\"text-align: center; margin-bottom: 30px; border-bottom: 3px solid #dc2626; padding-bottom: 15px;\">\n        <h1 style=\"color: #dc2626; font-size: 24px; margin: 0; font-weight: bold;\">ديوان أبو علوش</h1>\n        <h2 style=\"color: #666; font-size: 18px; margin: 8px 0; font-weight: normal;\">تقرير المصروفات</h2>\n        <p style=\"color: #888; font-size: 12px; margin: 5px 0;\">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>\n      </div>\n\n      <div style=\"margin-bottom: 20px; background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #dc2626;\">\n        <h3 style=\"color: #dc2626; font-size: 16px; margin: 0 0 15px 0; font-weight: bold;\">ملخص المصروفات</h3>\n        <div style=\"display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;\">\n          <div style=\"text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;\">\n            <div style=\"color: #dc2626; font-size: 20px; font-weight: bold;\">${stats.totalAmount.toFixed(2)} د.أ</div>\n            <div style=\"color: #666; font-size: 11px;\">إجمالي المصروفات</div>\n          </div>\n          <div style=\"text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;\">\n            <div style=\"color: #dc2626; font-size: 20px; font-weight: bold;\">${stats.totalCount}</div>\n            <div style=\"color: #666; font-size: 11px;\">عدد المصروفات</div>\n          </div>\n          <div style=\"text-align: center; background: white; padding: 10px; border-radius: 6px; border: 1px solid #e5e7eb;\">\n            <div style=\"color: #dc2626; font-size: 20px; font-weight: bold;\">${(stats.totalCount > 0 ? stats.totalAmount / stats.totalCount : 0).toFixed(2)} د.أ</div>\n            <div style=\"color: #666; font-size: 11px;\">متوسط المصروف</div>\n          </div>\n        </div>\n      </div>\n\n      <div style=\"margin-bottom: 20px;\">\n        <h3 style=\"color: #dc2626; font-size: 16px; margin: 0 0 15px 0; font-weight: bold; border-bottom: 2px solid #dc2626; padding-bottom: 8px;\">تفاصيل المصروفات</h3>\n        <table style=\"width: 100%; border-collapse: collapse; font-size: 11px; background: white; border-radius: 8px; overflow: hidden;\">\n          <thead>\n            <tr style=\"background: #dc2626; color: white;\">\n              <th style=\"padding: 10px 8px; text-align: right; font-weight: bold;\">الوصف</th>\n              <th style=\"padding: 10px 8px; text-align: center; font-weight: bold;\">المبلغ (د.أ)</th>\n              <th style=\"padding: 10px 8px; text-align: center; font-weight: bold;\">الفئة</th>\n              <th style=\"padding: 10px 8px; text-align: center; font-weight: bold;\">المستفيد</th>\n              <th style=\"padding: 10px 8px; text-align: center; font-weight: bold;\">التاريخ</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${expenses.map((expense, index) => `\n              <tr style=\"background: ${index % 2 === 0 ? '#ffffff' : '#f8f9fa'}; border-bottom: 1px solid #e5e7eb;\">\n                <td style=\"padding: 8px; text-align: right; border-left: 1px solid #e5e7eb;\">\n                  <div style=\"font-weight: bold; color: #374151;\">${expense.description}</div>\n                  ${expense.notes ? `<div style=\"font-size: 9px; color: #6b7280; margin-top: 2px;\">ملاحظة: ${expense.notes}</div>` : ''}\n                </td>\n                <td style=\"padding: 8px; text-align: center; font-weight: bold; color: #dc2626; border-left: 1px solid #e5e7eb;\">${expense.amount.toFixed(2)}</td>\n                <td style=\"padding: 8px; text-align: center; border-left: 1px solid #e5e7eb;\">\n                  <span style=\"background: #fef2f2; color: #dc2626; padding: 4px 8px; border-radius: 4px; font-size: 10px; font-weight: bold;\">\n                    ${getExpenseCategoryText(expense.category)}\n                  </span>\n                </td>\n                <td style=\"padding: 8px; text-align: center; color: #374151; border-left: 1px solid #e5e7eb;\">${expense.recipient || 'غير محدد'}</td>\n                <td style=\"padding: 8px; text-align: center; color: #6b7280; font-size: 10px;\">${formatDate(expense.date)}</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n      </div>\n\n      <div style=\"margin-top: 30px; padding-top: 15px; border-top: 2px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 10px;\">\n        <p style=\"margin: 0;\">ديوان أبو علوش - نظام إدارة العائلة</p>\n        <p style=\"margin: 5px 0 0 0;\">تم إنشاء هذا التقرير في: ${new Date().toLocaleString('ar-SA')}</p>\n      </div>\n    `\n\n    document.body.appendChild(printElement)\n\n    // انتظار قصير ثم إنشاء PDF\n    setTimeout(() => {\n      html2canvas(printElement, {\n        scale: 1.5,\n        useCORS: true,\n        allowTaint: true,\n        backgroundColor: '#ffffff',\n        logging: false\n      }).then(canvas => {\n        const imgData = canvas.toDataURL('image/png')\n        const pdf = new jsPDF('p', 'mm', 'a4')\n\n        const imgWidth = 210\n        const pageHeight = 297\n        const imgHeight = (canvas.height * imgWidth) / canvas.width\n\n        if (imgHeight <= pageHeight) {\n          // صفحة واحدة\n          pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight)\n        } else {\n          // صفحات متعددة\n          let heightLeft = imgHeight\n          let position = 0\n\n          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)\n          heightLeft -= pageHeight\n\n          while (heightLeft >= 0) {\n            position = heightLeft - imgHeight\n            pdf.addPage()\n            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)\n            heightLeft -= pageHeight\n          }\n        }\n\n        // حفظ الملف\n        const fileName = `تقرير_مصروفات_الديوان_${new Date().toISOString().split('T')[0]}.pdf`\n        pdf.save(fileName)\n\n        // إزالة العنصر\n        document.body.removeChild(printElement)\n      }).catch(error => {\n        console.error('خطأ في إنشاء PDF:', error)\n        document.body.removeChild(printElement)\n        alert('حدث خطأ في تصدير التقرير')\n      })\n    }, 300)\n  }\n\n  const canEdit = session?.user.role !== 'VIEWER'\n  const canDelete = session?.user.role === 'ADMIN'\n\n  return (\n    <div className=\"space-y-8\">\n      {/* رأس الصفحة المحسن */}\n      <div className=\"text-center mb-8\">\n        <div className=\"bg-gradient-to-r from-red-600 to-orange-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden\">\n          {/* خلفية متحركة */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-red-400 to-orange-500 opacity-30 animate-pulse\"></div>\n\n          {/* المحتوى */}\n          <div className=\"relative z-10\">\n            <div className=\"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm\">\n              <TrendingDown className=\"w-10 h-10 text-white\" />\n            </div>\n\n            <h1 className=\"text-5xl font-black mb-4 text-white\">\n              إدارة المصروفات\n            </h1>\n\n            <p className=\"text-xl font-semibold mb-6 text-red-100\">\n              عرض وإدارة مصروفات الديوان بكفاءة وسهولة\n            </p>\n\n            <div className=\"flex items-center justify-center space-x-2 space-x-reverse\">\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n              <div className=\"h-1 w-8 rounded-full bg-white bg-opacity-40\"></div>\n              <div className=\"h-1 w-16 rounded-full bg-white bg-opacity-60\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* أزرار الإجراءات */}\n      <div className=\"flex justify-center mb-8\">\n        <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n            <Button\n              onClick={handleExportExpensesPDF}\n              className=\"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold\"\n            >\n              <Download className=\"w-5 h-5 ml-2\" />\n              تصدير PDF\n            </Button>\n            {canEdit && (\n              <Button\n                onClick={handleAddNewExpense}\n                className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold\"\n              >\n                <Plus className=\"w-5 h-5 ml-2\" />\n                إضافة مصروف جديد\n              </Button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* إحصائيات سريعة محسنة */}\n      <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>إجمالي المصروفات</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#dc3545' }}>\n              <DollarSign className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {formatCurrency(stats.totalAmount)}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              إجمالي المبالغ\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500 to-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-orange-500 to-orange-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>عدد المصروفات</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#fd7e14' }}>\n              <TrendingDown className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {stats.totalCount}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              إجمالي العمليات\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative sm:col-span-2 lg:col-span-1\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n\n          <div className=\"bg-gradient-to-r from-yellow-500 to-yellow-600 p-1 rounded-t-xl\"></div>\n\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10\">\n            <CardTitle className=\"text-sm font-bold\" style={{ color: '#333333' }}>متوسط المصروف</CardTitle>\n            <div className=\"p-4 rounded-2xl shadow-lg\" style={{ backgroundColor: '#ffc107' }}>\n              <Receipt className=\"h-7 w-7 text-white\" />\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"relative z-10\">\n            <div className=\"text-4xl font-black mb-2\" style={{ color: '#191970' }}>\n              {formatCurrency(stats.totalCount > 0 ? stats.totalAmount / stats.totalCount : 0)}\n            </div>\n            <p className=\"text-sm font-semibold\" style={{ color: '#6c757d' }}>\n              متوسط القيمة\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* أدوات البحث والتصفية محسنة */}\n      <Card className=\"bg-white/80 backdrop-blur-sm border-gray-200 shadow-lg\">\n        <CardContent className=\"p-8\">\n          <div className=\"flex flex-col lg:flex-row gap-6\">\n            <div className=\"flex-1\">\n              <div className=\"relative group\">\n                <Search className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 group-focus-within:text-red-500 transition-colors duration-200\" />\n                <Input\n                  placeholder=\"البحث في المصروفات... (الوصف، الجهة المستفيدة، الملاحظات)\"\n                  value={search}\n                  onChange={(e) => setSearch(e.target.value)}\n                  className=\"h-12 pr-12 pl-4 border-2 border-gray-200 focus:border-red-500 focus:ring-red-100 rounded-xl text-base transition-all duration-200 bg-white/50 backdrop-blur-sm\"\n                />\n                {search && (\n                  <button\n                    onClick={() => setSearch('')}\n                    className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200\"\n                  >\n                    ✕\n                  </button>\n                )}\n              </div>\n            </div>\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Select value={category} onValueChange={setCategory}>\n                <SelectTrigger className=\"w-full sm:w-[200px] h-12 border-2 border-gray-200 focus:border-red-500 focus:ring-red-100 rounded-xl bg-white/50 backdrop-blur-sm\">\n                  <SelectValue placeholder=\"فلترة حسب الفئة\" />\n                </SelectTrigger>\n                <SelectContent className=\"rounded-xl border-2 border-gray-200 shadow-xl\">\n                  <SelectItem value=\"all\" className=\"rounded-lg\">🏢 جميع الفئات</SelectItem>\n                  <SelectItem value=\"MEETINGS\" className=\"rounded-lg\">🤝 اجتماعات</SelectItem>\n                  <SelectItem value=\"EVENTS\" className=\"rounded-lg\">🎉 مناسبات</SelectItem>\n                  <SelectItem value=\"MAINTENANCE\" className=\"rounded-lg\">🔧 إصلاحات</SelectItem>\n                  <SelectItem value=\"SOCIAL\" className=\"rounded-lg\">👥 اجتماعية</SelectItem>\n                  <SelectItem value=\"GENERAL\" className=\"rounded-lg\">📋 عامة</SelectItem>\n                </SelectContent>\n              </Select>\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"h-12 w-12 border-2 border-gray-200 hover:border-red-500 hover:bg-red-50 rounded-xl transition-all duration-200 bg-white/50 backdrop-blur-sm\"\n              >\n                <Filter className=\"h-5 w-5 text-gray-600\" />\n              </Button>\n            </div>\n          </div>\n          {(search || category !== 'all') && (\n            <div className=\"mt-4 flex items-center gap-2 text-sm text-gray-600\">\n              <span>عرض {expenses.length} من أصل {stats.totalCount} مصروف</span>\n              {search && (\n                <span className=\"px-2 py-1 bg-red-100 text-red-700 rounded-lg\">\n                  البحث: &quot;{search}&quot;\n                </span>\n              )}\n              {category !== 'all' && (\n                <span className=\"px-2 py-1 bg-orange-100 text-orange-700 rounded-lg\">\n                  الفئة: {getExpenseCategoryText(category)}\n                </span>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* جدول المصروفات المحسن */}\n      <Card className=\"bg-white/90 backdrop-blur-sm border-gray-200 shadow-xl overflow-hidden\">\n        <div className=\"bg-gradient-to-r from-red-600 via-red-700 to-orange-600 p-6\">\n          <h3 className=\"text-xl font-bold text-white flex items-center gap-3\">\n            <Receipt className=\"w-6 h-6\" />\n            قائمة المصروفات\n            <span className=\"text-red-200\">({expenses.length})</span>\n          </h3>\n        </div>\n        <CardContent className=\"p-0\">\n          {loading ? (\n            <div className=\"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-gray-50 to-red-50\">\n              <div className=\"w-12 h-12 border-4 border-red-200 border-t-red-600 rounded-full animate-spin mb-4\"></div>\n              <div className=\"text-gray-600 font-medium\">جاري تحميل المصروفات...</div>\n            </div>\n          ) : expenses.length === 0 ? (\n            <div className=\"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-gray-50 to-red-50\">\n              <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4\">\n                <Receipt className=\"w-8 h-8 text-red-600\" />\n              </div>\n              <div className=\"text-gray-600 font-medium mb-2\">لا توجد مصروفات</div>\n              <div className=\"text-gray-500 text-sm\">ابدأ بإضافة مصروف جديد</div>\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <Table>\n                <TableHeader className=\"bg-gray-50/80\">\n                  <TableRow className=\"border-b-2 border-gray-200\">\n                    <TableHead className=\"text-right font-bold text-gray-800 py-4\">الوصف</TableHead>\n                    <TableHead className=\"text-center font-bold text-gray-800 py-4\">المبلغ</TableHead>\n                    <TableHead className=\"text-center font-bold text-gray-800 py-4\">الفئة</TableHead>\n                    <TableHead className=\"text-center font-bold text-gray-800 py-4\">الجهة المستفيدة</TableHead>\n                    <TableHead className=\"text-center font-bold text-gray-800 py-4\">التاريخ</TableHead>\n                    <TableHead className=\"text-center font-bold text-gray-800 py-4\">الإجراءات</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {expenses.map((expense, index) => (\n                    <TableRow\n                      key={expense.id}\n                      className={`border-b border-gray-100 hover:bg-red-50/50 transition-all duration-200 ${\n                        index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'\n                      }`}\n                    >\n                      <TableCell className=\"py-4\">\n                        <div className=\"space-y-1\">\n                          <div className=\"font-semibold text-gray-900\">{expense.description}</div>\n                          {expense.notes && (\n                            <div className=\"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-lg inline-block\">\n                              💬 {expense.notes}\n                            </div>\n                          )}\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-center py-4\">\n                        <div className=\"font-bold text-lg text-red-700 bg-red-50 px-3 py-1 rounded-lg inline-block\">\n                          {formatCurrency(expense.amount)}\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-center py-4\">\n                        <Badge\n                          variant=\"secondary\"\n                          className={`px-3 py-1 font-medium ${\n                            expense.category === 'MEETINGS' ? 'bg-blue-100 text-blue-800' :\n                            expense.category === 'EVENTS' ? 'bg-purple-100 text-purple-800' :\n                            expense.category === 'MAINTENANCE' ? 'bg-orange-100 text-orange-800' :\n                            expense.category === 'SOCIAL' ? 'bg-green-100 text-green-800' :\n                            'bg-gray-100 text-gray-800'\n                          }`}\n                        >\n                          {getExpenseCategoryText(expense.category)}\n                        </Badge>\n                      </TableCell>\n                      <TableCell className=\"text-center py-4\">\n                        <div className=\"text-gray-700 font-medium\">\n                          {expense.recipient || (\n                            <span className=\"text-gray-400 italic\">غير محدد</span>\n                          )}\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-center py-4\">\n                        <div className=\"flex items-center justify-center gap-2 text-gray-600\">\n                          <Calendar className=\"w-4 h-4 text-red-500\" />\n                          <span className=\"font-medium\">{formatDate(expense.date)}</span>\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-center py-4\">\n                        <div className=\"flex items-center justify-center gap-2\">\n                          {canEdit && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => handleEditExpense(expense)}\n                              className=\"h-9 w-9 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200\"\n                              title=\"تعديل المصروف\"\n                            >\n                              <Edit className=\"w-4 h-4\" />\n                            </Button>\n                          )}\n                          {canDelete && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => handleDeleteExpense(expense.id)}\n                              className=\"h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200\"\n                              title=\"حذف المصروف\"\n                            >\n                              <Trash2 className=\"w-4 h-4\" />\n                            </Button>\n                          )}\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* التصفح */}\n      {pagination.pages > 1 && (\n        <div className=\"flex justify-center space-x-2 space-x-reverse\">\n          <Button\n            variant=\"outline\"\n            disabled={pagination.page === 1}\n            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}\n          >\n            السابق\n          </Button>\n          <span className=\"flex items-center px-4\">\n            صفحة {pagination.page} من {pagination.pages}\n          </span>\n          <Button\n            variant=\"outline\"\n            disabled={pagination.page === pagination.pages}\n            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}\n          >\n            التالي\n          </Button>\n        </div>\n      )}\n\n      {/* إحصائيات حسب الفئة */}\n      {stats.byCategory.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>المصروفات حسب الفئة</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5\">\n              {stats.byCategory.map((categoryStats) => (\n                <div key={categoryStats.category} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"text-sm font-medium text-gray-500\">\n                    {getExpenseCategoryText(categoryStats.category)}\n                  </div>\n                  <div className=\"mt-1 text-lg font-semibold text-gray-900\">\n                    {formatCurrency(categoryStats._sum.amount || 0)}\n                  </div>\n                  <div className=\"text-xs text-gray-400\">\n                    {categoryStats._count} مصروف\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* حوار إضافة/تعديل المصروف */}\n      <ExpenseDialog\n        open={isExpenseDialogOpen}\n        onOpenChange={handleCloseDialog}\n        expense={editingExpense}\n        onSuccess={() => {\n          fetchExpenses()\n          fetchStats()\n        }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAOA;AACA;AACA;AACA;AAtCA;;;;;;;;;;;;;;;AA+De,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,aAAa;QACb,YAAY;QACZ,YAAY,EAAE;IAChB;IACA,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,gBAAgB;IAChB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC;gBACA;gBACA,MAAM,WAAW,IAAI,CAAC,QAAQ;gBAC9B,OAAO,WAAW,KAAK,CAAC,QAAQ;YAClC;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ;YACtD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAyB,MAAM,SAAS,IAAI;YAClD,YAAY,KAAK,QAAQ;YACzB,cAAc,KAAK,UAAU;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAQ;QAAU,WAAW,IAAI;QAAE,WAAW,KAAK;KAAC;IAExD,iBAAiB;IACjB,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAElB,MAAM,OAAyB,MAAM,SAAS,IAAI;YAClD,MAAM,cAAc,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;YACjF,MAAM,aAAa,KAAK,QAAQ,CAAC,MAAM;YAEvC,4BAA4B;YAC5B,MAAM,aAAa,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,KAA4E;gBACnH,MAAM,WAAW,IAAI,IAAI,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,QAAQ,QAAQ;gBACtE,IAAI,UAAU;oBACZ,SAAS,IAAI,CAAC,MAAM,IAAI,QAAQ,MAAM;oBACtC,SAAS,MAAM,IAAI;gBACrB,OAAO;oBACL,IAAI,IAAI,CAAC;wBACP,UAAU,QAAQ,QAAQ;wBAC1B,MAAM;4BAAE,QAAQ,QAAQ,MAAM;wBAAC;wBAC/B,QAAQ;oBACV;gBACF;gBACA,OAAO;YACT,GAAG,EAAE;YAEL,SAAS;gBAAE;gBAAa;gBAAY;YAAW;QACjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,cAAc;IACd,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,uBAAuB;IACzB;IAEA,YAAY;IACZ,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,QAAQ,yEAAyE;QAEtF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAClD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,KAAK,IAAI;gBACrB;YACF;YAEA,MAAM;YACN;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,uBAAuB;IACzB;IAEA,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,uBAAuB;QACvB,kBAAkB;IACpB;IAIA,oCAAoC;IACpC,MAAM,0BAA0B;QAC9B,+BAA+B;QAC/B,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,KAAK,CAAC,OAAO,GAAG,CAAC;;;;;;;;;;;;IAY9B,CAAC;QAED,+BAA+B;QAC/B,aAAa,SAAS,GAAG,CAAC;;;;+EAIiD,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;;;;;;;6EAO3C,EAAE,MAAM,WAAW,CAAC,OAAO,CAAC,GAAG;;;;6EAI/B,EAAE,MAAM,UAAU,CAAC;;;;6EAInB,EAAE,CAAC,MAAM,UAAU,GAAG,IAAI,MAAM,WAAW,GAAG,MAAM,UAAU,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG;;;;;;;;;;;;;;;;;;;YAmBhJ,EAAE,SAAS,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;qCACX,EAAE,QAAQ,MAAM,IAAI,YAAY,UAAU;;kEAEb,EAAE,QAAQ,WAAW,CAAC;kBACtE,EAAE,QAAQ,KAAK,GAAG,CAAC,sEAAsE,EAAE,QAAQ,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG;;iIAEP,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,GAAG;;;oBAGzI,EAAE,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ,QAAQ,EAAE;;;8GAG+C,EAAE,QAAQ,SAAS,IAAI,WAAW;+FACjD,EAAE,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,IAAI,EAAE;;YAE9G,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;+DAOuC,EAAE,IAAI,OAAO,cAAc,CAAC,SAAS;;IAEhG,CAAC;QAED,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,2BAA2B;QAC3B,WAAW;YACT,CAAA,GAAA,yJAAA,CAAA,UAAW,AAAD,EAAE,cAAc;gBACxB,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;YACX,GAAG,IAAI,CAAC,CAAA;gBACN,MAAM,UAAU,OAAO,SAAS,CAAC;gBACjC,MAAM,MAAM,IAAI,mJAAA,CAAA,UAAK,CAAC,KAAK,MAAM;gBAEjC,MAAM,WAAW;gBACjB,MAAM,aAAa;gBACnB,MAAM,YAAY,AAAC,OAAO,MAAM,GAAG,WAAY,OAAO,KAAK;gBAE3D,IAAI,aAAa,YAAY;oBAC3B,aAAa;oBACb,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,GAAG,UAAU;gBAC/C,OAAO;oBACL,eAAe;oBACf,IAAI,aAAa;oBACjB,IAAI,WAAW;oBAEf,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;oBACpD,cAAc;oBAEd,MAAO,cAAc,EAAG;wBACtB,WAAW,aAAa;wBACxB,IAAI,OAAO;wBACX,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;wBACpD,cAAc;oBAChB;gBACF;gBAEA,YAAY;gBACZ,MAAM,WAAW,CAAC,sBAAsB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACtF,IAAI,IAAI,CAAC;gBAET,eAAe;gBACf,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,GAAG,KAAK,CAAC,CAAA;gBACP,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,MAAM;YACR;QACF,GAAG;IACL;IAEA,MAAM,UAAU,SAAS,KAAK,SAAS;IACvC,MAAM,YAAY,SAAS,KAAK,SAAS;IAEzC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAG1B,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAIpD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;8CAIvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAGtC,yBACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;;;;;;kDAEnC,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI5B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,MAAM,UAAU;;;;;;kDAEnB,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;wCAA4B,OAAO;4CAAE,iBAAiB;wCAAU;kDAC7E,cAAA,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIvB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAU;kDACjE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,UAAU,GAAG,IAAI,MAAM,WAAW,GAAG,MAAM,UAAU,GAAG;;;;;;kDAEhF,8OAAC;wCAAE,WAAU;wCAAwB,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAQxE,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;;;;;;4CAEX,wBACC,8OAAC;gDACC,SAAS,IAAM,UAAU;gDACzB,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAU,eAAe;;8DACtC,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;;sEACvB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;4DAAM,WAAU;sEAAa;;;;;;sEAC/C,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;4DAAW,WAAU;sEAAa;;;;;;sEACpD,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;4DAAS,WAAU;sEAAa;;;;;;sEAClD,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;4DAAc,WAAU;sEAAa;;;;;;sEACvD,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;4DAAS,WAAU;sEAAa;;;;;;sEAClD,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;4DAAU,WAAU;sEAAa;;;;;;;;;;;;;;;;;;sDAGvD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAIvB,CAAC,UAAU,aAAa,KAAK,mBAC5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAK;wCAAK,SAAS,MAAM;wCAAC;wCAAS,MAAM,UAAU;wCAAC;;;;;;;gCACpD,wBACC,8OAAC;oCAAK,WAAU;;wCAA+C;wCAC/C;wCAAO;;;;;;;gCAGxB,aAAa,uBACZ,8OAAC;oCAAK,WAAU;;wCAAqD;wCAC3D,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAY;8CAE/B,8OAAC;oCAAK,WAAU;;wCAAe;wCAAE,SAAS,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAGrD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CAA4B;;;;;;;;;;;mCAE3C,SAAS,MAAM,KAAK,kBACtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;oCAAI,WAAU;8CAAiC;;;;;;8CAChD,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;iDAGzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kDACJ,8OAAC,iIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC,iIAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA0C;;;;;;8DAC/D,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA2C;;;;;;8DAChE,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA2C;;;;;;8DAChE,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA2C;;;;;;8DAChE,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA2C;;;;;;8DAChE,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA2C;;;;;;;;;;;;;;;;;kDAGpE,8OAAC,iIAAA,CAAA,YAAS;kDACP,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,iIAAA,CAAA,WAAQ;gDAEP,WAAW,CAAC,wEAAwE,EAClF,QAAQ,MAAM,IAAI,aAAa,iBAC/B;;kEAEF,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA+B,QAAQ,WAAW;;;;;;gEAChE,QAAQ,KAAK,kBACZ,8OAAC;oEAAI,WAAU;;wEAAsE;wEAC/E,QAAQ,KAAK;;;;;;;;;;;;;;;;;;kEAKzB,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;kEAGlC,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAW,CAAC,sBAAsB,EAChC,QAAQ,QAAQ,KAAK,aAAa,8BAClC,QAAQ,QAAQ,KAAK,WAAW,kCAChC,QAAQ,QAAQ,KAAK,gBAAgB,kCACrC,QAAQ,QAAQ,KAAK,WAAW,gCAChC,6BACA;sEAED,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ,QAAQ;;;;;;;;;;;kEAG5C,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,SAAS,kBAChB,8OAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;;;;;;kEAI7C,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAe,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,IAAI;;;;;;;;;;;;;;;;;kEAG1D,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC;4DAAI,WAAU;;gEACZ,yBACC,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,kBAAkB;oEACjC,WAAU;oEACV,OAAM;8EAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;gEAGnB,2BACC,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,oBAAoB,QAAQ,EAAE;oEAC7C,WAAU;oEACV,OAAM;8EAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CApErB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAmF9B,WAAW,KAAK,GAAG,mBAClB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,UAAU,WAAW,IAAI,KAAK;wBAC9B,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM,KAAK,IAAI,GAAG;gCAAE,CAAC;kCACvE;;;;;;kCAGD,8OAAC;wBAAK,WAAU;;4BAAyB;4BACjC,WAAW,IAAI;4BAAC;4BAAK,WAAW,KAAK;;;;;;;kCAE7C,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,UAAU,WAAW,IAAI,KAAK,WAAW,KAAK;wBAC9C,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM,KAAK,IAAI,GAAG;gCAAE,CAAC;kCACvE;;;;;;;;;;;;YAOJ,MAAM,UAAU,CAAC,MAAM,GAAG,mBACzB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,8BACrB,8OAAC;oCAAiC,WAAU;;sDAC1C,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD,EAAE,cAAc,QAAQ;;;;;;sDAEhD,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,IAAI,CAAC,MAAM,IAAI;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,MAAM;gDAAC;;;;;;;;mCARhB,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;0BAkB1C,8OAAC,mJAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,cAAc;gBACd,SAAS;gBACT,WAAW;oBACT;oBACA;gBACF;;;;;;;;;;;;AAIR", "debugId": null}}]}