{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/settings/notification-settings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\n// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Switch } from '@/components/ui/switch'\n// import { Button } from '@/components/ui/button'\n// import { Badge } from '@/components/ui/badge'\n// import { Textarea } from '@/components/ui/textarea'\nimport {\n  Bell,\n  Clock,\n  Users,\n  DollarSign,\n  AlertTriangle,\n  Settings\n} from 'lucide-react'\n\ninterface NotificationSettingsProps {\n  settings: any\n  onChange: (settings: any) => void\n  canEdit: boolean\n}\n\ninterface NotificationSettingsData {\n  // إعدادات عامة للإشعارات\n  enableNotifications: boolean\n  enableSounds: boolean\n  enableDesktopNotifications: boolean\n  enableEmailNotifications: boolean\n  enableSMSNotifications: boolean\n  \n  // إعدادات الإشعارات حسب النوع\n  memberNotifications: {\n    newMember: boolean\n    memberUpdate: boolean\n    memberStatusChange: boolean\n    memberPayment: boolean\n  }\n  \n  incomeNotifications: {\n    newIncome: boolean\n    incomeUpdate: boolean\n    paymentReminder: boolean\n    paymentOverdue: boolean\n  }\n  \n  expenseNotifications: {\n    newExpense: boolean\n    expenseUpdate: boolean\n    budgetAlert: boolean\n    expenseApproval: boolean\n  }\n  \n  systemNotifications: {\n    systemUpdate: boolean\n    securityAlert: boolean\n    backupComplete: boolean\n    errorAlert: boolean\n  }\n  \n  // إعدادات التوقيت\n  quietHours: {\n    enabled: boolean\n    startTime: string\n    endTime: string\n  }\n  \n  // إعدادات البريد الإلكتروني\n  emailSettings: {\n    smtpServer: string\n    smtpPort: number\n    smtpUsername: string\n    smtpPassword: string\n    fromEmail: string\n    fromName: string\n    enableSSL: boolean\n  }\n  \n  // إعدادات الرسائل النصية\n  smsSettings: {\n    provider: string\n    apiKey: string\n    senderName: string\n  }\n  \n  // قوالب الإشعارات\n  templates: {\n    welcomeMessage: string\n    paymentReminder: string\n    paymentConfirmation: string\n    systemAlert: string\n  }\n}\n\nconst defaultSettings: NotificationSettingsData = {\n  enableNotifications: true,\n  enableSounds: true,\n  enableDesktopNotifications: true,\n  enableEmailNotifications: false,\n  enableSMSNotifications: false,\n  \n  memberNotifications: {\n    newMember: true,\n    memberUpdate: true,\n    memberStatusChange: true,\n    memberPayment: true\n  },\n  \n  incomeNotifications: {\n    newIncome: true,\n    incomeUpdate: true,\n    paymentReminder: true,\n    paymentOverdue: true\n  },\n  \n  expenseNotifications: {\n    newExpense: true,\n    expenseUpdate: true,\n    budgetAlert: true,\n    expenseApproval: true\n  },\n  \n  systemNotifications: {\n    systemUpdate: true,\n    securityAlert: true,\n    backupComplete: true,\n    errorAlert: true\n  },\n  \n  quietHours: {\n    enabled: false,\n    startTime: '22:00',\n    endTime: '08:00'\n  },\n  \n  emailSettings: {\n    smtpServer: '',\n    smtpPort: 587,\n    smtpUsername: '',\n    smtpPassword: '',\n    fromEmail: '',\n    fromName: 'ديوان آل أبو علوش',\n    enableSSL: true\n  },\n  \n  smsSettings: {\n    provider: '',\n    apiKey: '',\n    senderName: 'ديوان آل أبو علوش'\n  },\n  \n  templates: {\n    welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',\n    paymentReminder: 'تذكير: يرجى دفع الاشتراك الشهري',\n    paymentConfirmation: 'تم استلام دفعتك بنجاح',\n    systemAlert: 'تنبيه من النظام'\n  }\n}\n\nexport default function NotificationSettings({ settings, onChange, canEdit }: NotificationSettingsProps) {\n  const [localSettings, setLocalSettings] = useState<NotificationSettingsData>(defaultSettings)\n  // const [testingEmail, setTestingEmail] = useState(false)\n  // const [testingSMS, setTestingSMS] = useState(false)\n\n  useEffect(() => {\n    if (settings) {\n      setLocalSettings({ ...defaultSettings, ...settings })\n    }\n  }, [settings])\n\n  const handleChange = (key: string, value: any) => {\n    const keys = key.split('.')\n    let newSettings = { ...localSettings }\n    \n    if (keys.length === 1) {\n      newSettings = { ...newSettings, [keys[0]]: value }\n    } else if (keys.length === 2) {\n      newSettings = {\n        ...newSettings,\n        [keys[0]]: {\n          ...newSettings[keys[0] as keyof NotificationSettingsData],\n          [keys[1]]: value\n        }\n      }\n    }\n    \n    setLocalSettings(newSettings)\n    onChange(newSettings)\n  }\n\n  // const testEmailSettings = async () => {\n  //   setTestingEmail(true)\n  //   try {\n  //     // هنا يمكن إضافة API call لاختبار إعدادات البريد الإلكتروني\n  //     await new Promise(resolve => setTimeout(resolve, 2000)) // محاكاة\n  //     alert('تم إرسال رسالة اختبار بنجاح!')\n  //   } catch {\n  //     alert('فشل في إرسال رسالة الاختبار')\n  //   } finally {\n  //     setTestingEmail(false)\n  //   }\n  // }\n\n  // const testSMSSettings = async () => {\n  //   setTestingSMS(true)\n  //   try {\n  //     // هنا يمكن إضافة API call لاختبار إعدادات الرسائل النصية\n  //     await new Promise(resolve => setTimeout(resolve, 2000)) // محاكاة\n  //     alert('تم إرسال رسالة نصية اختبارية بنجاح!')\n  //   } catch {\n  //     alert('فشل في إرسال الرسالة النصية الاختبارية')\n  //   } finally {\n  //     setTestingSMS(false)\n  //   }\n  // }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* General settings */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Bell className=\"w-5 h-5 text-blue-600\" />\n            الإعدادات العامة للإشعارات\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تفعيل الإشعارات</Label>\n                <p className=\"text-sm text-gray-600\">تفعيل أو إلغاء جميع الإشعارات</p>\n              </div>\n              <Switch\n                checked={localSettings.enableNotifications}\n                onCheckedChange={(checked) => handleChange('enableNotifications', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>الأصوات</Label>\n                <p className=\"text-sm text-gray-600\">تشغيل أصوات الإشعارات</p>\n              </div>\n              <Switch\n                checked={localSettings.enableSounds}\n                onCheckedChange={(checked) => handleChange('enableSounds', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>إشعارات سطح المكتب</Label>\n                <p className=\"text-sm text-gray-600\">عرض إشعارات في المتصفح</p>\n              </div>\n              <Switch\n                checked={localSettings.enableDesktopNotifications}\n                onCheckedChange={(checked) => handleChange('enableDesktopNotifications', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>إشعارات البريد الإلكتروني</Label>\n                <p className=\"text-sm text-gray-600\">إرسال إشعارات عبر البريد الإلكتروني</p>\n              </div>\n              <Switch\n                checked={localSettings.enableEmailNotifications}\n                onCheckedChange={(checked) => handleChange('enableEmailNotifications', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>الرسائل النصية</Label>\n                <p className=\"text-sm text-gray-600\">إرسال إشعارات عبر الرسائل النصية</p>\n              </div>\n              <Switch\n                checked={localSettings.enableSMSNotifications}\n                onCheckedChange={(checked) => handleChange('enableSMSNotifications', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Member notifications */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Users className=\"w-5 h-5 text-green-600\" />\n            إشعارات الأعضاء\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>عضو جديد</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند إضافة عضو جديد</p>\n              </div>\n              <Switch\n                checked={localSettings.memberNotifications.newMember}\n                onCheckedChange={(checked) => handleChange('memberNotifications.newMember', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تحديث بيانات العضو</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند تحديث بيانات عضو</p>\n              </div>\n              <Switch\n                checked={localSettings.memberNotifications.memberUpdate}\n                onCheckedChange={(checked) => handleChange('memberNotifications.memberUpdate', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تغيير حالة العضو</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند تغيير حالة العضو</p>\n              </div>\n              <Switch\n                checked={localSettings.memberNotifications.memberStatusChange}\n                onCheckedChange={(checked) => handleChange('memberNotifications.memberStatusChange', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>دفعات الأعضاء</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند دفع الاشتراكات</p>\n              </div>\n              <Switch\n                checked={localSettings.memberNotifications.memberPayment}\n                onCheckedChange={(checked) => handleChange('memberNotifications.memberPayment', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Revenue notifications */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <DollarSign className=\"w-5 h-5 text-emerald-600\" />\n            إشعارات الإيرادات\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>إيراد جديد</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند إضافة إيراد جديد</p>\n              </div>\n              <Switch\n                checked={localSettings.incomeNotifications.newIncome}\n                onCheckedChange={(checked) => handleChange('incomeNotifications.newIncome', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تحديث الإيراد</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند تحديث إيراد</p>\n              </div>\n              <Switch\n                checked={localSettings.incomeNotifications.incomeUpdate}\n                onCheckedChange={(checked) => handleChange('incomeNotifications.incomeUpdate', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تذكير الدفع</Label>\n                <p className=\"text-sm text-gray-600\">تذكير الأعضاء بموعد الدفع</p>\n              </div>\n              <Switch\n                checked={localSettings.incomeNotifications.paymentReminder}\n                onCheckedChange={(checked) => handleChange('incomeNotifications.paymentReminder', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تأخير الدفع</Label>\n                <p className=\"text-sm text-gray-600\">تنبيه عند تأخر الدفع</p>\n              </div>\n              <Switch\n                checked={localSettings.incomeNotifications.paymentOverdue}\n                onCheckedChange={(checked) => handleChange('incomeNotifications.paymentOverdue', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Expense notifications */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <AlertTriangle className=\"w-5 h-5 text-red-600\" />\n            إشعارات المصروفات\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>مصروف جديد</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند إضافة مصروف جديد</p>\n              </div>\n              <Switch\n                checked={localSettings.expenseNotifications.newExpense}\n                onCheckedChange={(checked) => handleChange('expenseNotifications.newExpense', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تحديث المصروف</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند تحديث مصروف</p>\n              </div>\n              <Switch\n                checked={localSettings.expenseNotifications.expenseUpdate}\n                onCheckedChange={(checked) => handleChange('expenseNotifications.expenseUpdate', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تنبيه الميزانية</Label>\n                <p className=\"text-sm text-gray-600\">تنبيه عند تجاوز حد الميزانية</p>\n              </div>\n              <Switch\n                checked={localSettings.expenseNotifications.budgetAlert}\n                onCheckedChange={(checked) => handleChange('expenseNotifications.budgetAlert', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>موافقة المصروف</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند الحاجة لموافقة على مصروف</p>\n              </div>\n              <Switch\n                checked={localSettings.expenseNotifications.expenseApproval}\n                onCheckedChange={(checked) => handleChange('expenseNotifications.expenseApproval', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* System notifications */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Settings className=\"w-5 h-5 text-purple-600\" />\n            إشعارات النظام\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تحديثات النظام</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند توفر تحديثات</p>\n              </div>\n              <Switch\n                checked={localSettings.systemNotifications.systemUpdate}\n                onCheckedChange={(checked) => handleChange('systemNotifications.systemUpdate', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تنبيهات الأمان</Label>\n                <p className=\"text-sm text-gray-600\">تنبيهات أمنية مهمة</p>\n              </div>\n              <Switch\n                checked={localSettings.systemNotifications.securityAlert}\n                onCheckedChange={(checked) => handleChange('systemNotifications.securityAlert', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>اكتمال النسخ الاحتياطي</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند اكتمال النسخ الاحتياطي</p>\n              </div>\n              <Switch\n                checked={localSettings.systemNotifications.backupComplete}\n                onCheckedChange={(checked) => handleChange('systemNotifications.backupComplete', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>تنبيهات الأخطاء</Label>\n                <p className=\"text-sm text-gray-600\">إشعار عند حدوث أخطاء في النظام</p>\n              </div>\n              <Switch\n                checked={localSettings.systemNotifications.errorAlert}\n                onCheckedChange={(checked) => handleChange('systemNotifications.errorAlert', checked)}\n                disabled={!canEdit || !localSettings.enableNotifications}\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Quiet hours */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Clock className=\"w-5 h-5 text-orange-600\" />\n            ساعات الهدوء\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <Label>تفعيل ساعات الهدوء</Label>\n              <p className=\"text-sm text-gray-600\">إيقاف الإشعارات في أوقات محددة</p>\n            </div>\n            <Switch\n              checked={localSettings.quietHours.enabled}\n              onCheckedChange={(checked) => handleChange('quietHours.enabled', checked)}\n              disabled={!canEdit || !localSettings.enableNotifications}\n            />\n          </div>\n\n          {localSettings.quietHours.enabled && (\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"startTime\">وقت البداية</Label>\n                <Input\n                  id=\"startTime\"\n                  type=\"time\"\n                  value={localSettings.quietHours.startTime}\n                  onChange={(e) => handleChange('quietHours.startTime', e.target.value)}\n                  disabled={!canEdit}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"endTime\">وقت النهاية</Label>\n                <Input\n                  id=\"endTime\"\n                  type=\"time\"\n                  value={localSettings.quietHours.endTime}\n                  onChange={(e) => handleChange('quietHours.endTime', e.target.value)}\n                  disabled={!canEdit}\n                />\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA,yGAAyG;AACzG;AACA,kDAAkD;AAClD,gDAAgD;AAChD,sDAAsD;AACtD;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAXA;;;;;;;AAiGA,MAAM,kBAA4C;IAChD,qBAAqB;IACrB,cAAc;IACd,4BAA4B;IAC5B,0BAA0B;IAC1B,wBAAwB;IAExB,qBAAqB;QACnB,WAAW;QACX,cAAc;QACd,oBAAoB;QACpB,eAAe;IACjB;IAEA,qBAAqB;QACnB,WAAW;QACX,cAAc;QACd,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,sBAAsB;QACpB,YAAY;QACZ,eAAe;QACf,aAAa;QACb,iBAAiB;IACnB;IAEA,qBAAqB;QACnB,cAAc;QACd,eAAe;QACf,gBAAgB;QAChB,YAAY;IACd;IAEA,YAAY;QACV,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,eAAe;QACb,YAAY;QACZ,UAAU;QACV,cAAc;QACd,cAAc;QACd,WAAW;QACX,UAAU;QACV,WAAW;IACb;IAEA,aAAa;QACX,UAAU;QACV,QAAQ;QACR,YAAY;IACd;IAEA,WAAW;QACT,gBAAgB;QAChB,iBAAiB;QACjB,qBAAqB;QACrB,aAAa;IACf;AACF;AAEe,SAAS,qBAAqB,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAA6B;;IACrG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC7E,0DAA0D;IAC1D,sDAAsD;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,UAAU;gBACZ,iBAAiB;oBAAE,GAAG,eAAe;oBAAE,GAAG,QAAQ;gBAAC;YACrD;QACF;yCAAG;QAAC;KAAS;IAEb,MAAM,eAAe,CAAC,KAAa;QACjC,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,cAAc;YAAE,GAAG,aAAa;QAAC;QAErC,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,cAAc;gBAAE,GAAG,WAAW;gBAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAAM;QACnD,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;YAC5B,cAAc;gBACZ,GAAG,WAAW;gBACd,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;oBACT,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,CAAmC;oBACzD,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBACb;YACF;QACF;QAEA,iBAAiB;QACjB,SAAS;IACX;IAEA,0CAA0C;IAC1C,0BAA0B;IAC1B,UAAU;IACV,mEAAmE;IACnE,wEAAwE;IACxE,4CAA4C;IAC5C,cAAc;IACd,2CAA2C;IAC3C,gBAAgB;IAChB,6BAA6B;IAC7B,MAAM;IACN,IAAI;IAEJ,wCAAwC;IACxC,wBAAwB;IACxB,UAAU;IACV,gEAAgE;IAChE,wEAAwE;IACxE,mDAAmD;IACnD,cAAc;IACd,sDAAsD;IACtD,gBAAgB;IAChB,2BAA2B;IAC3B,MAAM;IACN,IAAI;IAEJ,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAI9C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB;4CAC1C,iBAAiB,CAAC,UAAY,aAAa,uBAAuB;4CAClE,UAAU,CAAC;;;;;;;;;;;;8CAIf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,YAAY;4CACnC,iBAAiB,CAAC,UAAY,aAAa,gBAAgB;4CAC3D,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,0BAA0B;4CACjD,iBAAiB,CAAC,UAAY,aAAa,8BAA8B;4CACzE,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,wBAAwB;4CAC/C,iBAAiB,CAAC,UAAY,aAAa,4BAA4B;4CACvE,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,sBAAsB;4CAC7C,iBAAiB,CAAC,UAAY,aAAa,0BAA0B;4CACrE,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlE,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA2B;;;;;;;;;;;;kCAIhD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,SAAS;4CACpD,iBAAiB,CAAC,UAAY,aAAa,iCAAiC;4CAC5E,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,YAAY;4CACvD,iBAAiB,CAAC,UAAY,aAAa,oCAAoC;4CAC/E,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,kBAAkB;4CAC7D,iBAAiB,CAAC,UAAY,aAAa,0CAA0C;4CACrF,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,aAAa;4CACxD,iBAAiB,CAAC,UAAY,aAAa,qCAAqC;4CAChF,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlE,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAA6B;;;;;;;;;;;;kCAIvD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,SAAS;4CACpD,iBAAiB,CAAC,UAAY,aAAa,iCAAiC;4CAC5E,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,YAAY;4CACvD,iBAAiB,CAAC,UAAY,aAAa,oCAAoC;4CAC/E,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,eAAe;4CAC1D,iBAAiB,CAAC,UAAY,aAAa,uCAAuC;4CAClF,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,cAAc;4CACzD,iBAAiB,CAAC,UAAY,aAAa,sCAAsC;4CACjF,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlE,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;;;;;;kCAItD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,oBAAoB,CAAC,UAAU;4CACtD,iBAAiB,CAAC,UAAY,aAAa,mCAAmC;4CAC9E,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,oBAAoB,CAAC,aAAa;4CACzD,iBAAiB,CAAC,UAAY,aAAa,sCAAsC;4CACjF,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,oBAAoB,CAAC,WAAW;4CACvD,iBAAiB,CAAC,UAAY,aAAa,oCAAoC;4CAC/E,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,oBAAoB,CAAC,eAAe;4CAC3D,iBAAiB,CAAC,UAAY,aAAa,wCAAwC;4CACnF,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlE,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAIpD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,YAAY;4CACvD,iBAAiB,CAAC,UAAY,aAAa,oCAAoC;4CAC/E,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,aAAa;4CACxD,iBAAiB,CAAC,UAAY,aAAa,qCAAqC;4CAChF,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,cAAc;4CACzD,iBAAiB,CAAC,UAAY,aAAa,sCAAsC;4CACjF,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,cAAc,mBAAmB,CAAC,UAAU;4CACrD,iBAAiB,CAAC,UAAY,aAAa,kCAAkC;4CAC7E,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlE,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAIjD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,cAAc,UAAU,CAAC,OAAO;wCACzC,iBAAiB,CAAC,UAAY,aAAa,sBAAsB;wCACjE,UAAU,CAAC,WAAW,CAAC,cAAc,mBAAmB;;;;;;;;;;;;4BAI3D,cAAc,UAAU,CAAC,OAAO,kBAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,cAAc,UAAU,CAAC,SAAS;gDACzC,UAAU,CAAC,IAAM,aAAa,wBAAwB,EAAE,MAAM,CAAC,KAAK;gDACpE,UAAU,CAAC;;;;;;;;;;;;kDAGf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;0DACzB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,cAAc,UAAU,CAAC,OAAO;gDACvC,UAAU,CAAC,IAAM,aAAa,sBAAsB,EAAE,MAAM,CAAC,KAAK;gDAClE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7B;GAzawB;KAAA", "debugId": null}}]}