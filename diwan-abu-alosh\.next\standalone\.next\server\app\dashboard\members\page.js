(()=>{var e={};e.id=6253,e.ids=[6253],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6211:(e,s,a)=>{"use strict";a.d(s,{A0:()=>n,BF:()=>d,Hj:()=>o,XI:()=>i,nA:()=>m,nd:()=>c});var t=a(60687),r=a(43210),l=a(4780);let i=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",e),...s})}));i.displayName="Table";let n=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",e),...s}));n.displayName="TableHeader";let d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("tfoot",{ref:a,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));o.displayName="TableRow";let c=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));c.displayName="TableHead";let m=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));m.displayName="TableCell"},8086:e=>{"use strict";e.exports=require("module")},8819:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},18207:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eg});var t=a(60687),r=a(43210),l=a(82136),i=a(29523),n=a(89667),d=a(44493),o=a(6211),c=a(41312),m=a(31158),x=a(62688);let h=(0,x.A)("file-down",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);var p=a(96474),g=a(93508);let u=(0,x.A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var b=a(99270),j=a(80462),f=a(10022),v=a(97992),y=a(48340),N=a(13861),w=a(23928),A=a(63143);let C=(0,x.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var k=a(88233),z=a(27605),E=a(57335),S=a(9275);S.z.object({name:S.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),email:S.z.string().email("البريد الإلكتروني غير صحيح"),password:S.z.string().min(6,"كلمة المرور يجب أن تكون على الأقل 6 أحرف"),role:S.z.enum(["ADMIN","DATA_ENTRY","VIEWER","MEMBER_VIEWER","GALLERY_VIEWER","MEMBER"]).default("VIEWER")});let T=S.z.object({name:S.z.string().min(2,"الاسم يجب أن يكون على الأقل حرفين"),phone:S.z.string().optional().or(S.z.literal("")).or(S.z.literal(null)),email:S.z.union([S.z.string().email("البريد الإلكتروني غير صحيح"),S.z.literal(""),S.z.literal(null),S.z.undefined()]).optional(),address:S.z.string().optional().or(S.z.literal("")).or(S.z.literal(null)),notes:S.z.string().optional().or(S.z.literal("")).or(S.z.literal(null)),photo:S.z.string().optional().or(S.z.literal("")).or(S.z.literal(null)),status:S.z.enum(["ACTIVE","LATE","INACTIVE","SUSPENDED","ARCHIVED"]).default("ACTIVE")});S.z.object({amount:S.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:S.z.date(),source:S.z.string().min(1,"مصدر الإيراد مطلوب"),type:S.z.enum(["SUBSCRIPTION","DONATION","EVENT","OTHER"]).default("SUBSCRIPTION"),description:S.z.string().optional().nullable(),notes:S.z.string().optional().nullable(),memberId:S.z.string().optional().nullable()}),S.z.object({amount:S.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:S.z.date(),description:S.z.string().min(1,"وصف المصروف مطلوب"),category:S.z.enum(["MEETINGS","EVENTS","MAINTENANCE","SOCIAL","GENERAL"]).default("GENERAL"),recipient:S.z.string().optional().nullable(),notes:S.z.string().optional().nullable()}),S.z.object({title:S.z.string().min(1,"عنوان النشاط مطلوب"),description:S.z.string().optional(),startDate:S.z.date(),endDate:S.z.date().optional(),location:S.z.string().optional(),organizers:S.z.string().optional(),participantIds:S.z.array(S.z.string()).optional()}),S.z.object({email:S.z.string().email("البريد الإلكتروني غير صحيح"),password:S.z.string().min(1,"كلمة المرور مطلوبة")});var D=a(63503),I=a(80013),$=a(34729),R=a(96834),O=a(5336),P=a(48730),V=a(93613),q=a(35071),W=a(77026),F=a(58869),M=a(51361),B=a(11860),Z=a(41862),L=a(9005),H=a(41550),U=a(8819);function J({open:e,onOpenChange:s,member:a,onSuccess:l}){let[o,c]=(0,r.useState)(!1),[m,x]=(0,r.useState)(!1),[h,p]=(0,r.useState)(null),u=!!a,{register:b,handleSubmit:j,reset:N,setValue:w,watch:A,formState:{errors:C}}=(0,z.mN)({resolver:(0,E.u)(T),defaultValues:{name:"",phone:"",email:"",address:"",photo:"",notes:"",status:"ACTIVE"}}),k=A("status"),S=async e=>{if(!e)return null;if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type))return p("نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)"),null;if(e.size>5242880)return p("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت"),null;p(null),x(!0);try{let s=new FormData;s.append("file",e);let a=await fetch("/api/upload",{method:"POST",body:s});if(!a.ok){let e=await a.json();throw Error(e.error||"فشل في رفع الصورة")}let t=await a.json();return w("photo",t.filePath),t.filePath}catch(e){return console.error("خطأ في رفع الصورة:",e),p(e.message||"حدث خطأ في رفع الصورة"),null}finally{x(!1)}},J=async()=>{let e=A("photo");if(e)try{await fetch(`/api/upload?path=${encodeURIComponent(e)}`,{method:"DELETE"})}catch(e){console.error("خطأ في حذف الصورة:",e)}w("photo",""),p(null)},_=async e=>{try{c(!0);let t={...e,phone:e.phone?.trim()||null,email:e.email?.trim()||null,address:e.address?.trim()||null,notes:e.notes?.trim()||null},r=u?`/api/members/${a.id}`:"/api/members",i=await fetch(r,{method:u?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!i.ok){let e=await i.json();throw Error(e.error||"حدث خطأ")}alert(u?"تم تحديث بيانات العضو بنجاح":"تم إضافة العضو الجديد بنجاح"),l(),s(!1),N()}catch(e){console.error("خطأ في حفظ العضو:",e),alert(e.message||"حدث خطأ في حفظ العضو")}finally{c(!1)}};return(0,t.jsx)(D.lG,{open:e,onOpenChange:s,children:(0,t.jsxs)(D.Cf,{className:"w-[50vw] h-[85vh] max-w-none max-h-none overflow-y-auto fixed top-[1vh] left-1/2 -translate-x-1/2 translate-y-0",children:[(0,t.jsx)(D.c7,{className:"p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-3 bg-white rounded-xl shadow-sm border border-diwan-200",children:(0,t.jsx)(F.A,{className:"w-6 h-6 text-diwan-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(D.L3,{className:"text-xl font-bold text-gray-900 mb-1",children:u?"تعديل بيانات العضو":"إضافة عضو جديد"}),(0,t.jsx)(D.rr,{className:"text-gray-600",children:u?"قم بتعديل بيانات العضو في النموذج أدناه":"أدخل بيانات العضو الجديد في النموذج أدناه"}),!u&&(0,t.jsx)("div",{className:"mt-2 text-xs text-diwan-600 bg-diwan-50 px-2 py-1 rounded-md inline-block",children:"\uD83D\uDCA1 الحقول المطلوبة مميزة بعلامة *"})]})]})}),(0,t.jsxs)("form",{onSubmit:j(_),className:"p-8 pt-0 space-y-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,t.jsxs)(d.Wu,{className:"p-5",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,t.jsx)("div",{className:"p-1 bg-indigo-100 rounded-lg",children:(0,t.jsx)(M.A,{className:"w-4 h-4 text-indigo-600"})}),"صورة العضو",(0,t.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"اختياري"})]}),(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsxs)("div",{className:"space-y-3 w-full",children:[(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];s&&S(s)},className:"hidden",id:"photo-upload",disabled:o||m}),A("photo")?(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("div",{className:"relative w-32 h-32 mx-auto rounded-xl overflow-hidden border-2 border-gray-200 shadow-lg",children:[(0,t.jsx)("img",{src:A("photo")||void 0,alt:"صورة العضو",className:"w-full h-full object-cover"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center",children:(0,t.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",onClick:J,disabled:o||m,className:"opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,t.jsx)(B.A,{className:"w-4 h-4"})})})]}),(0,t.jsx)("div",{className:"text-center mt-2",children:(0,t.jsxs)(i.$,{type:"button",variant:"outline",size:"sm",onClick:()=>document.getElementById("photo-upload")?.click(),disabled:o||m,className:"text-xs",children:[(0,t.jsx)(M.A,{className:"w-3 h-3 ml-1"}),"تغيير الصورة"]})})]}):(0,t.jsx)("div",{onClick:()=>document.getElementById("photo-upload")?.click(),className:`
                          relative w-full h-32 border-2 border-dashed rounded-xl cursor-pointer
                          transition-all duration-200 flex flex-col items-center justify-center
                          border-gray-300 hover:border-diwan-400 hover:bg-gray-50
                          ${o||m?"opacity-50 cursor-not-allowed":""}
                        `,children:m?(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2 text-diwan-600",children:[(0,t.jsx)(Z.A,{className:"w-6 h-6 animate-spin"}),(0,t.jsx)("span",{className:"text-xs font-medium",children:"جاري الرفع..."})]}):(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2 text-gray-500",children:[(0,t.jsx)("div",{className:"p-2 bg-gray-100 rounded-full",children:(0,t.jsx)(L.A,{className:"w-6 h-6"})}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-xs font-medium text-gray-700",children:"اضغط لاختيار صورة"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"JPG, PNG, WebP"})]})]})}),h&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-red-600",children:[(0,t.jsx)(V.A,{className:"w-3 h-3 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-xs",children:h})]})}),A("photo")&&!h&&(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-green-600",children:[(0,t.jsx)(O.A,{className:"w-3 h-3 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-xs",children:"تم رفع الصورة بنجاح"})]})})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-3 text-center",children:"صورة شخصية للعضو"})]})]})}),(0,t.jsx)(d.Zp,{className:"lg:col-span-2 border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,t.jsxs)(d.Wu,{className:"p-5",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,t.jsx)("div",{className:"p-1 bg-diwan-100 rounded-lg",children:(0,t.jsx)(F.A,{className:"w-4 h-4 text-diwan-600"})}),"المعلومات الأساسية",(0,t.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"مطلوب"})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(I.J,{htmlFor:"name",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"w-4 h-4"}),"الاسم الكامل *"]}),(0,t.jsx)(n.p,{id:"name",...b("name"),placeholder:"أدخل الاسم الكامل للعضو",className:`h-12 text-base transition-all duration-200 ${C.name?"border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50":"border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"}`}),C.name&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mt-1",children:(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"w-4 h-4 flex-shrink-0"}),C.name.message]})})]})})]})})]}),(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,t.jsxs)(d.Wu,{className:"p-5",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,t.jsx)("div",{className:"p-1 bg-blue-100 rounded-lg",children:(0,t.jsx)(y.A,{className:"w-4 h-4 text-blue-600"})}),"معلومات الاتصال",(0,t.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"اختياري"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(I.J,{htmlFor:"phone",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"w-4 h-4"}),"رقم الهاتف"]}),(0,t.jsx)(n.p,{id:"phone",...b("phone"),placeholder:"07xxxxxxxx",dir:"ltr",className:"h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"}),C.phone&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(V.A,{className:"w-4 h-4"}),C.phone.message]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(I.J,{htmlFor:"email",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,t.jsx)(H.A,{className:"w-4 h-4"}),"البريد الإلكتروني"]}),(0,t.jsx)(n.p,{id:"email",type:"email",...b("email"),placeholder:"<EMAIL>",dir:"ltr",className:"h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"}),C.email&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(V.A,{className:"w-4 h-4"}),C.email.message]})]})]})]})}),(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,t.jsxs)(d.Wu,{className:"p-5",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,t.jsx)("div",{className:"p-1 bg-green-100 rounded-lg",children:(0,t.jsx)(v.A,{className:"w-4 h-4 text-green-600"})}),"العنوان والملاحظات",(0,t.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"اختياري"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(I.J,{htmlFor:"address",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"w-4 h-4"}),"العنوان"]}),(0,t.jsx)(n.p,{id:"address",...b("address"),placeholder:"أدخل عنوان السكن",className:"h-12 text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500"}),C.address&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(V.A,{className:"w-4 h-4"}),C.address.message]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(I.J,{htmlFor:"notes",className:"text-sm font-medium text-gray-700 flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"w-4 h-4"}),"ملاحظات إضافية"]}),(0,t.jsx)($.T,{id:"notes",...b("notes"),placeholder:"أدخل أي ملاحظات أو معلومات إضافية عن العضو",rows:4,className:"text-base border-gray-300 focus:border-diwan-500 focus:ring-diwan-500 resize-none"}),C.notes&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(V.A,{className:"w-4 h-4"}),C.notes.message]})]})]})]})}),(0,t.jsx)(d.Zp,{className:"border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200",children:(0,t.jsxs)(d.Wu,{className:"p-5",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,t.jsx)("div",{className:"p-1 bg-purple-100 rounded-lg",children:(0,t.jsx)(g.A,{className:"w-4 h-4 text-purple-600"})}),"حالة العضو",(0,t.jsx)("span",{className:"text-xs text-gray-500 font-normal mr-auto",children:"مطلوب"})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(I.J,{htmlFor:"status",className:"text-sm font-medium text-gray-700",children:"اختر حالة العضو"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"الحالة الحالية:"}),(0,t.jsx)(R.E,{className:`${(e=>{switch(e){case"ACTIVE":return"bg-green-50 text-green-700 border-green-200";case"LATE":return"bg-yellow-50 text-yellow-700 border-yellow-200";case"INACTIVE":return"bg-orange-50 text-orange-700 border-orange-200";case"SUSPENDED":return"bg-red-50 text-red-700 border-red-200";case"ARCHIVED":return"bg-gray-50 text-gray-700 border-gray-200";default:return"bg-blue-50 text-blue-700 border-blue-200"}})(k)} border`,children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(e=>{switch(e){case"ACTIVE":return(0,t.jsx)(O.A,{className:"w-4 h-4 text-green-500"});case"LATE":return(0,t.jsx)(P.A,{className:"w-4 h-4 text-yellow-500"});case"INACTIVE":return(0,t.jsx)(V.A,{className:"w-4 h-4 text-orange-500"});case"SUSPENDED":return(0,t.jsx)(q.A,{className:"w-4 h-4 text-red-500"});case"ARCHIVED":return(0,t.jsx)(W.A,{className:"w-4 h-4 text-gray-500"});default:return(0,t.jsx)(g.A,{className:"w-4 h-4 text-blue-500"})}})(k),(0,t.jsxs)("span",{children:["ACTIVE"===k&&"نشط","LATE"===k&&"متأخر","INACTIVE"===k&&"غير ملتزم","SUSPENDED"===k&&"موقوف مؤقتاً","ARCHIVED"===k&&"مؤرشف"]})]})})]}),(0,t.jsxs)("select",{id:"status",...b("status"),className:"w-full h-12 px-4 py-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-diwan-500 focus:border-diwan-500 bg-white",children:[(0,t.jsx)("option",{value:"ACTIVE",children:"✅ نشط - عضو فعال ومنتظم"}),(0,t.jsx)("option",{value:"LATE",children:"⏰ متأخر - متأخر في الدفع"}),(0,t.jsx)("option",{value:"INACTIVE",children:"⚠️ غير ملتزم - غير منتظم في الحضور"}),(0,t.jsx)("option",{value:"SUSPENDED",children:"❌ موقوف مؤقتاً - موقوف لفترة محددة"}),(0,t.jsx)("option",{value:"ARCHIVED",children:"\uD83D\uDCC1 مؤرشف - عضو سابق"})]}),C.status&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(V.A,{className:"w-4 h-4"}),C.status.message]})]})})]})}),(0,t.jsxs)("div",{className:"flex justify-between items-center gap-4 pt-8 border-t-2 border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 -mx-6 px-8 py-6 rounded-b-xl",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:u?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"تعديل بيانات العضو"]}):(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"إضافة عضو جديد"]})}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:o,className:"px-8 py-3 h-12 border-2 border-gray-300 text-gray-700 hover:bg-white hover:border-gray-400 hover:shadow-md transition-all duration-200 flex items-center gap-2 font-medium",children:[(0,t.jsx)(B.A,{className:"w-4 h-4"}),"إلغاء"]}),(0,t.jsx)(i.$,{type:"submit",disabled:o,className:`px-8 py-3 h-12 font-bold text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2 ${u?"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 border-2 border-blue-500":"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 border-2 border-green-500"}`,children:o?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,t.jsx)("span",{className:"text-base",children:"جاري الحفظ..."})]}):(0,t.jsx)("div",{className:"flex items-center gap-2",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(O.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{className:"text-base",children:"تحديث البيانات"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(U.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{className:"text-base",children:"حفظ العضو الجديد"})]})})})]})]})]})]})})}var _=a(4780),G=a(40228),Y=a(58559);function K({open:e,onOpenChange:s,member:a}){let[l,i]=(0,r.useState)(null),[n,d]=(0,r.useState)(!1);return a?(0,t.jsx)(D.lG,{open:e,onOpenChange:s,children:(0,t.jsxs)(D.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(D.HM,{onOpenChange:s}),(0,t.jsx)(D.c7,{className:"p-6 pb-0",children:(0,t.jsxs)(D.L3,{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(F.A,{className:"w-5 h-5 text-diwan-600"}),(0,t.jsxs)("span",{children:["تفاصيل العضو: ",a.name]})]})}),(0,t.jsx)("div",{className:"p-6 pt-0 space-y-6",children:n?(0,t.jsx)("div",{className:"flex justify-center items-center h-32",children:(0,t.jsx)("div",{className:"text-gray-500",children:"جاري التحميل..."})}):l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"المعلومات الأساسية"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(F.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"الاسم:"}),(0,t.jsx)("span",{className:"font-medium",children:l.member.name})]}),l.member.phone&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"الهاتف:"}),(0,t.jsx)("span",{className:"font-medium",children:l.member.phone})]}),l.member.address&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"العنوان:"}),(0,t.jsx)("span",{className:"font-medium",children:l.member.address})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(G.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"تاريخ الانضمام:"}),(0,t.jsx)("span",{className:"font-medium",children:(0,_.Yq)(l.member.createdAt)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(Y.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"الحالة:"}),(0,t.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${(0,_.OR)(l.member.status)}`,children:(0,_.WK)(l.member.status)})]})]}),l.member.notes&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse mb-2",children:[(0,t.jsx)(f.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"ملاحظات:"})]}),(0,t.jsx)("p",{className:"text-sm bg-white p-3 rounded border",children:l.member.notes})]})]}),(0,t.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"الإحصائيات المالية"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(0,_.vv)(l.totalContributions)}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"إجمالي المساهمات"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:l.incomes.length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"عدد الإيرادات"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:l.incomes.length>0?(0,_.vv)(l.totalContributions/l.incomes.length):(0,_.vv)(0)}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"متوسط المساهمة"})]})]})]}),l.incomes.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"آخر الإيرادات"}),(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:l.incomes.slice(0,10).map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.source}),e.description&&(0,t.jsx)("div",{className:"text-sm text-gray-600",children:e.description}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:(0,_.Yq)(e.date)})]}),(0,t.jsx)("div",{className:"text-green-600 font-semibold",children:(0,_.vv)(e.amount)})]},e.id))})]}),l.activities.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"الأنشطة المشارك فيها"}),(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:l.activities.slice(0,10).map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.title}),e.location&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["\uD83D\uDCCD ",e.location]})]}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:(0,_.Yq)(e.startDate)})]},e.id))})]})]}):(0,t.jsx)("div",{className:"text-center text-gray-500",children:"لا توجد تفاصيل متاحة"})})]})}):null}var X=a(15079);let Q=(0,x.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var ee=a(25541),es=a(53411);function ea({open:e,onOpenChange:s,memberId:a}){let[l,n]=(0,r.useState)(!1),[c,x]=(0,r.useState)(null),[h,p]=(0,r.useState)(new Date().getFullYear().toString()),g=new Date().getFullYear(),u=Array.from({length:5},(e,s)=>g-s);return(0,t.jsx)(D.lG,{open:e,onOpenChange:s,children:(0,t.jsxs)(D.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(D.c7,{className:"p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-3 bg-white rounded-xl shadow-sm border border-diwan-200",children:(0,t.jsx)(f.A,{className:"w-6 h-6 text-diwan-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(D.L3,{className:"text-xl font-bold text-gray-900",children:"كشف حساب العضو"}),c&&(0,t.jsxs)("p",{className:"text-gray-600 mt-1",children:[c.member.name," - عام ",h]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(X.l6,{value:h,onValueChange:p,children:[(0,t.jsx)(X.bq,{className:"w-32",children:(0,t.jsx)(X.yv,{})}),(0,t.jsx)(X.gC,{children:u.map(e=>(0,t.jsx)(X.eb,{value:e.toString(),children:e},e))})]}),c&&(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{if(!c)return;let e=window.open("","_blank");if(!e)return void alert("يرجى السماح بفتح النوافذ المنبثقة لتصدير التقرير");let s=`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>كشف حساب العضو - ${c.member.name}</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');

            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Noto Sans Arabic', Arial, sans-serif;
              direction: rtl;
              text-align: right;
              background: white;
              color: #333;
              line-height: 1.6;
              padding: 20px;
            }

            .header {
              text-align: center;
              margin-bottom: 20px;
              border-bottom: 2px solid #2563eb;
              padding-bottom: 15px;
            }

            .header h1 {
              font-size: 24px;
              font-weight: 700;
              color: #1e40af;
              margin-bottom: 8px;
            }

            .header .member-name {
              font-size: 18px;
              font-weight: 600;
              color: #374151;
              margin-bottom: 4px;
            }

            .header .year {
              font-size: 14px;
              color: #6b7280;
              margin-bottom: 4px;
            }

            .header .date {
              font-size: 12px;
              color: #9ca3af;
            }

            .section {
              margin-bottom: 15px;
              background: #f9fafb;
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              padding: 15px;
            }

            .section-title {
              font-size: 16px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 10px;
              border-bottom: 1px solid #e5e7eb;
              padding-bottom: 5px;
            }

            .info-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 10px;
              margin-bottom: 10px;
            }

            .info-item {
              background: white;
              padding: 8px;
              border-radius: 4px;
              border: 1px solid #e5e7eb;
              font-size: 14px;
            }

            .info-label {
              font-weight: 600;
              color: #374151;
              margin-left: 8px;
            }

            .info-value {
              color: #6b7280;
            }

            .summary-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 15px;
              margin-bottom: 20px;
            }

            .summary-item {
              background: white;
              padding: 15px;
              border-radius: 8px;
              border: 1px solid #e5e7eb;
              text-align: center;
            }

            .summary-label {
              font-size: 14px;
              color: #6b7280;
              margin-bottom: 8px;
            }

            .summary-value {
              font-size: 18px;
              font-weight: 600;
              color: #059669;
            }

            .monthly-grid {
              display: grid;
              grid-template-columns: repeat(4, 1fr);
              gap: 8px;
            }

            .month-item {
              background: white;
              padding: 8px;
              border-radius: 4px;
              border: 1px solid #e5e7eb;
              text-align: center;
            }

            .month-item.has-amount {
              background: #f0f9ff;
              border-color: #0ea5e9;
            }

            .month-name {
              font-weight: 600;
              color: #374151;
              margin-bottom: 3px;
              font-size: 12px;
            }

            .month-amount {
              font-size: 14px;
              font-weight: 600;
              margin-bottom: 2px;
            }

            .month-amount.positive {
              color: #059669;
            }

            .month-amount.zero {
              color: #9ca3af;
            }

            .month-count {
              font-size: 10px;
              color: #6b7280;
            }

            .type-item {
              background: white;
              padding: 12px;
              border-radius: 6px;
              border: 1px solid #e5e7eb;
              margin-bottom: 8px;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }

            .type-name {
              font-weight: 600;
              color: #374151;
            }

            .type-details {
              color: #059669;
              font-weight: 600;
            }

            @media print {
              body {
                padding: 10px;
                font-size: 11px;
              }
              .header {
                margin-bottom: 15px;
                padding-bottom: 10px;
              }
              .section {
                break-inside: avoid;
                margin-bottom: 10px;
                padding: 10px;
              }
              .monthly-grid {
                grid-template-columns: repeat(6, 1fr);
                gap: 4px;
              }
              .month-item {
                padding: 4px;
              }
              .month-name {
                font-size: 10px;
              }
              .month-amount {
                font-size: 12px;
              }
              .month-count {
                font-size: 8px;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>كشف حساب العضو</h1>
            <div class="member-name">${c.member.name}</div>
            <div class="year">عام ${h}</div>
            <div class="date">تاريخ التقرير: ${new Date().toLocaleDateString("en-GB")}</div>
          </div>

          <div class="section">
            <div class="section-title">معلومات العضو</div>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">الاسم:</span>
                <span class="info-value">${c.member.name}</span>
              </div>
              ${c.member.phone?`
              <div class="info-item">
                <span class="info-label">الهاتف:</span>
                <span class="info-value">${c.member.phone}</span>
              </div>
              `:""}
              ${c.member.email?`
              <div class="info-item">
                <span class="info-label">البريد الإلكتروني:</span>
                <span class="info-value">${c.member.email}</span>
              </div>
              `:""}
              <div class="info-item">
                <span class="info-label">عضو منذ:</span>
                <span class="info-value">${(0,_.Yq)(c.member.createdAt)}</span>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">إجمالي المساهمات</div>
            <div style="text-align: center; padding: 15px;">
              <div class="summary-value" style="font-size: 28px; color: #059669; font-weight: bold;">
                ${(0,_.vv)(c.summary.totalAmount)}
              </div>
            </div>
          </div>



          <div class="section">
            <div class="section-title">المساهمات الشهرية - ${h}</div>
            <div class="monthly-grid">
              ${c.monthlyData.map(e=>`
                <div class="month-item ${e.count>0?"has-amount":""}">
                  <div class="month-name">${e.monthName}</div>
                  <div class="month-amount ${e.count>0?"positive":"zero"}">
                    ${(0,_.vv)(e.amount)}
                  </div>
                  <div class="month-count">${e.count} معاملة</div>
                </div>
              `).join("")}
            </div>
          </div>

          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 12px;">
            تم إنشاء هذا التقرير بواسطة نظام إدارة ديوان أبو علوش
          </div>
        </body>
        </html>
      `;e.document.write(s),e.document.close(),e.onload=()=>{e.focus(),setTimeout(()=>{e.print(),e.onafterprint=()=>{e.close()}},1e3)}},className:"text-green-600 border-green-600 hover:bg-green-50",children:[(0,t.jsx)(m.A,{className:"w-4 h-4 ml-1"}),"تصدير"]}),(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>s(!1),className:"text-gray-600 border-gray-300 hover:bg-gray-50",children:[(0,t.jsx)(B.A,{className:"w-4 h-4 ml-1"}),"خروج"]})]})]})}),(0,t.jsx)("div",{className:"p-6 space-y-6",children:l?(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"text-gray-500",children:"جاري تحميل كشف الحساب..."})}):c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(d.Zp,{className:"border-gray-200",children:[(0,t.jsx)(d.aR,{className:"pb-3",children:(0,t.jsxs)(d.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(F.A,{className:"w-5 h-5 text-diwan-600"}),"معلومات العضو"]})}),(0,t.jsx)(d.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"font-medium",children:c.member.name})]}),c.member.phone&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{children:c.member.phone})]}),c.member.email&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(H.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{children:c.member.email})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(G.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsxs)("span",{children:["عضو منذ ",(0,_.Yq)(c.member.createdAt)]})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)(d.Zp,{className:"border-green-200 bg-green-50",children:[(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-medium text-green-800",children:"إجمالي المساهمات"}),(0,t.jsx)(w.A,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-700",children:(0,_.vv)(c.summary.totalAmount)})})]}),(0,t.jsxs)(d.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-medium text-blue-800",children:"عدد المعاملات"}),(0,t.jsx)(Q,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-700",children:c.summary.transactionCount})})]}),(0,t.jsxs)(d.Zp,{className:"border-purple-200 bg-purple-50",children:[(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-medium text-purple-800",children:"متوسط شهري"}),(0,t.jsx)(ee.A,{className:"h-4 w-4 text-purple-600"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-700",children:(0,_.vv)(c.summary.averageMonthlyContribution)})})]}),(0,t.jsxs)(d.Zp,{className:"border-orange-200 bg-orange-50",children:[(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-medium text-orange-800",children:"آخر مساهمة"}),(0,t.jsx)(P.A,{className:"h-4 w-4 text-orange-600"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("div",{className:"text-sm font-bold text-orange-700",children:c.summary.lastTransactionDate?(0,_.Yq)(c.summary.lastTransactionDate):"لا توجد مساهمات"})})]})]}),(0,t.jsxs)(d.Zp,{className:"border-gray-200",children:[(0,t.jsx)(d.aR,{children:(0,t.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(es.A,{className:"w-5 h-5 text-diwan-600"}),"التوزيع حسب نوع المساهمة"]})}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(c.byType).map(([e,s])=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-600",children:(0,_.Tk)(e)}),(0,t.jsx)(R.E,{variant:"secondary",children:s.count})]}),(0,t.jsx)("div",{className:"text-lg font-bold text-diwan-600",children:(0,_.vv)(s.amount)}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[(s.amount/c.summary.totalAmount*100).toFixed(1),"% من الإجمالي"]})]},e))})})]}),(0,t.jsxs)(d.Zp,{className:"border-gray-200",children:[(0,t.jsx)(d.aR,{children:(0,t.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(G.A,{className:"w-5 h-5 text-diwan-600"}),"المساهمات الشهرية - ",h]})}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3",children:c.monthlyData.map(e=>(0,t.jsxs)("div",{className:`p-3 border rounded-lg text-center ${e.count>0?"bg-green-50 border-green-200":"bg-gray-50 border-gray-200"}`,children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-1",children:e.monthName}),(0,t.jsx)("div",{className:`text-lg font-bold ${e.count>0?"text-green-600":"text-gray-400"}`,children:(0,_.vv)(e.amount)}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[e.count," معاملة"]})]},e.month))})})]}),(0,t.jsxs)(d.Zp,{className:"border-gray-200",children:[(0,t.jsx)(d.aR,{children:(0,t.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"w-5 h-5 text-diwan-600"}),"آخر المعاملات"]})}),(0,t.jsx)(d.Wu,{children:c.recentTransactions.length>0?(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{children:(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nd,{children:"التاريخ"}),(0,t.jsx)(o.nd,{children:"المبلغ"}),(0,t.jsx)(o.nd,{children:"المصدر"}),(0,t.jsx)(o.nd,{children:"النوع"}),(0,t.jsx)(o.nd,{children:"الوصف"}),(0,t.jsx)(o.nd,{children:"المُدخِل"})]})}),(0,t.jsx)(o.BF,{children:c.recentTransactions.map(e=>(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nA,{children:(0,_.Yq)(e.date)}),(0,t.jsx)(o.nA,{children:(0,t.jsx)("span",{className:"font-medium text-green-600",children:(0,_.vv)(e.amount)})}),(0,t.jsx)(o.nA,{children:e.source}),(0,t.jsx)(o.nA,{children:(0,t.jsx)(R.E,{variant:"outline",children:(0,_.Tk)(e.type)})}),(0,t.jsx)(o.nA,{children:e.description||"-"}),(0,t.jsx)(o.nA,{className:"text-sm text-gray-600",children:e.createdBy.name})]},e.id))})]}):(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"لا توجد معاملات في هذه الفترة"})})]}),c.allTransactions.length>5&&(0,t.jsxs)(d.Zp,{className:"border-gray-200",children:[(0,t.jsx)(d.aR,{children:(0,t.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"w-5 h-5 text-diwan-600"}),"جميع المعاملات (",c.allTransactions.length,")"]})}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("div",{className:"max-h-96 overflow-y-auto",children:(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{children:(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nd,{children:"التاريخ"}),(0,t.jsx)(o.nd,{children:"المبلغ"}),(0,t.jsx)(o.nd,{children:"المصدر"}),(0,t.jsx)(o.nd,{children:"النوع"}),(0,t.jsx)(o.nd,{children:"الوصف"}),(0,t.jsx)(o.nd,{children:"المُدخِل"})]})}),(0,t.jsx)(o.BF,{children:c.allTransactions.map(e=>(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nA,{children:(0,_.Yq)(e.date)}),(0,t.jsx)(o.nA,{children:(0,t.jsx)("span",{className:"font-medium text-green-600",children:(0,_.vv)(e.amount)})}),(0,t.jsx)(o.nA,{children:e.source}),(0,t.jsx)(o.nA,{children:(0,t.jsx)(R.E,{variant:"outline",children:(0,_.Tk)(e.type)})}),(0,t.jsx)(o.nA,{children:e.description||"-"}),(0,t.jsx)(o.nA,{className:"text-sm text-gray-600",children:e.createdBy.name})]},e.id))})]})})})]})]}):(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(f.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد بيانات لعرضها"}),(0,t.jsxs)("p",{className:"text-gray-500 mb-4",children:["لم يتم العثور على معاملات مالية لهذا العضو في عام ",h]}),(0,t.jsx)("p",{className:"text-sm text-blue-600",children:"\uD83D\uDCA1 جرب تغيير السنة من القائمة المنسدلة أعلاه لعرض بيانات سنوات أخرى"})]})})]})})}let et=S.z.object({amount:S.z.number().positive("المبلغ يجب أن يكون أكبر من صفر"),date:S.z.string().min(1,"التاريخ مطلوب"),source:S.z.string().min(1,"مصدر الإيراد مطلوب"),type:S.z.enum(["SUBSCRIPTION","DONATION","EVENT","OTHER"]).default("SUBSCRIPTION"),description:S.z.string().optional(),notes:S.z.string().optional()});function er({open:e,onOpenChange:s,member:a,onSuccess:l}){let[d,o]=(0,r.useState)(!1),{register:c,handleSubmit:m,reset:x,setValue:h,watch:p,formState:{errors:g}}=(0,z.mN)({resolver:(0,E.u)(et),defaultValues:{amount:0,date:new Date().toISOString().split("T")[0],source:"",type:"SUBSCRIPTION",description:"",notes:""}}),u=p("type"),b=async e=>{if(a)try{o(!0);let t={...e,amount:Number(e.amount),date:new Date(e.date),memberId:a.id,description:e.description?.trim()||null,notes:e.notes?.trim()||null},r=await fetch("/api/incomes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok){let e=await r.json();throw Error(e.error||"حدث خطأ")}alert(`تم إضافة إيراد للعضو ${a.name} بنجاح`),s(!1),l?.()}catch(e){console.error("خطأ في إضافة الإيراد:",e),alert(e.message||"حدث خطأ في إضافة الإيراد")}finally{o(!1)}};return a?(0,t.jsx)(D.lG,{open:e,onOpenChange:s,children:(0,t.jsxs)(D.Cf,{className:"max-w-[50vw] max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(D.c7,{className:"p-6 pb-4 border-b border-gray-100 bg-gradient-to-r from-diwan-50 to-blue-50",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-3 bg-white rounded-xl shadow-sm border border-diwan-200",children:(0,t.jsx)(w.A,{className:"w-6 h-6 text-diwan-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(D.L3,{className:"text-xl font-bold text-gray-900",children:"إضافة إيراد للعضو"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,t.jsx)(F.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("p",{className:"text-gray-600 font-medium",children:a.name})]})]})]})}),(0,t.jsxs)("form",{onSubmit:m(b),className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"amount",children:"المبلغ (دينار أردني) *"}),(0,t.jsx)(n.p,{id:"amount",type:"number",step:"0.01",min:"0",...c("amount",{valueAsNumber:!0}),className:g.amount?"border-red-500":""}),g.amount&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:g.amount.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"date",children:"التاريخ *"}),(0,t.jsx)(n.p,{id:"date",type:"date",...c("date"),className:g.date?"border-red-500":""}),g.date&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:g.date.message})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"source",children:"مصدر الإيراد *"}),(0,t.jsx)(n.p,{id:"source",...c("source"),placeholder:"مثال: اشتراك شهري، تبرع، رسوم فعالية",className:g.source?"border-red-500":""}),g.source&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:g.source.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"type",children:"نوع الإيراد"}),(0,t.jsxs)(X.l6,{value:u,onValueChange:e=>h("type",e),children:[(0,t.jsx)(X.bq,{children:(0,t.jsx)(X.yv,{})}),(0,t.jsxs)(X.gC,{children:[(0,t.jsx)(X.eb,{value:"SUBSCRIPTION",children:"اشتراكات"}),(0,t.jsx)(X.eb,{value:"DONATION",children:"تبرعات"}),(0,t.jsx)(X.eb,{value:"EVENT",children:"فعاليات"}),(0,t.jsx)(X.eb,{value:"OTHER",children:"أخرى"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"description",children:"الوصف (اختياري)"}),(0,t.jsx)(n.p,{id:"description",...c("description"),placeholder:"وصف إضافي للإيراد"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"notes",children:"ملاحظات (اختياري)"}),(0,t.jsx)($.T,{id:"notes",...c("notes"),placeholder:"أي ملاحظات إضافية",rows:3})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 space-x-reverse pt-4 border-t",children:[(0,t.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:d,children:"إلغاء"}),(0,t.jsx)(i.$,{type:"submit",disabled:d,className:"bg-diwan-600 hover:bg-diwan-700",children:d?"جاري الحفظ...":"حفظ الإيراد"})]})]})]})}):null}var el=a(64021),ei=a(12597);function en({open:e,onOpenChange:s,member:a,onSuccess:l}){let[d,o]=(0,r.useState)(""),[c,m]=(0,r.useState)(""),[x,h]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!1),[u,b]=(0,r.useState)(!1),[j,f]=(0,r.useState)(""),v=async e=>{if(e.preventDefault(),f(""),!d)return void f("كلمة المرور مطلوبة");if(d.length<6)return void f("كلمة المرور يجب أن تكون على الأقل 6 أحرف");if(d!==c)return void f("كلمة المرور وتأكيد كلمة المرور غير متطابقين");if(!a)return void f("لم يتم تحديد العضو");try{b(!0);let e=await fetch(`/api/members/${a.id}/password`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:d})});if(!e.ok){let s=await e.json();throw Error(s.message||"فشل في تحديث كلمة المرور")}alert("تم تحديث كلمة مرور العضو بنجاح"),o(""),m(""),f(""),s(!1),l()}catch(e){console.error("خطأ في تحديث كلمة المرور:",e),f(e.message||"حدث خطأ أثناء تحديث كلمة المرور")}finally{b(!1)}},y=()=>{o(""),m(""),f(""),h(!1),g(!1),s(!1)};return(0,t.jsx)(D.lG,{open:e,onOpenChange:y,children:(0,t.jsxs)(D.Cf,{className:"max-w-[50vw]",children:[(0,t.jsx)(D.c7,{children:(0,t.jsxs)(D.L3,{className:"flex items-center gap-2 text-xl",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)(C,{className:"w-5 h-5 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold",children:"إدارة كلمة المرور"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 font-normal",children:"تعيين كلمة مرور للعضو"})]})]})}),a&&(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(F.A,{className:"w-5 h-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-900",children:a.name}),a.email&&(0,t.jsx)("p",{className:"text-sm text-blue-700",children:a.email}),a.phone&&(0,t.jsx)("p",{className:"text-sm text-blue-700",children:a.phone})]})]})}),(0,t.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[j&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:j}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"password",className:"text-gray-700 font-medium",children:"كلمة المرور الجديدة"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(el.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)(n.p,{id:"password",type:x?"text":"password",value:d,onChange:e=>o(e.target.value),placeholder:"أدخل كلمة المرور الجديدة",required:!0,disabled:u,className:"pr-10 pl-10"}),(0,t.jsx)("button",{type:"button",onClick:()=>h(!x),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:u,children:x?(0,t.jsx)(ei.A,{className:"w-5 h-5"}):(0,t.jsx)(N.A,{className:"w-5 h-5"})})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"كلمة المرور يجب أن تكون على الأقل 6 أحرف"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"confirmPassword",className:"text-gray-700 font-medium",children:"تأكيد كلمة المرور"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(el.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)(n.p,{id:"confirmPassword",type:p?"text":"password",value:c,onChange:e=>m(e.target.value),placeholder:"أعد إدخال كلمة المرور",required:!0,disabled:u,className:"pr-10 pl-10"}),(0,t.jsx)("button",{type:"button",onClick:()=>g(!p),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:u,children:p?(0,t.jsx)(ei.A,{className:"w-5 h-5"}):(0,t.jsx)(N.A,{className:"w-5 h-5"})})]})]}),(0,t.jsxs)(D.Es,{className:"gap-3 pt-4",children:[(0,t.jsxs)(i.$,{type:"button",variant:"outline",onClick:y,disabled:u,className:"flex-1",children:[(0,t.jsx)(B.A,{className:"w-4 h-4 ml-2"}),"إلغاء"]}),(0,t.jsx)(i.$,{type:"submit",disabled:u||!d||!c,className:"flex-1 bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white",children:u?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"جاري الحفظ..."]}):(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"w-4 h-4"}),"حفظ كلمة المرور"]})})]})]}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4",children:(0,t.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,t.jsx)("strong",{children:"ملاحظة:"})," بعد تعيين كلمة المرور، سيتمكن العضو من تسجيل الدخول باستخدام بريده الإلكتروني وكلمة المرور الجديدة لعرض كشف حسابه الشخصي ومشاهدة معرض الصور."]})})]})})}function ed({open:e,onOpenChange:s,onSearch:a,onReset:l}){let[d,o]=(0,r.useState)({name:"",phone:"",address:"",status:"all",minContributions:"",maxContributions:"",joinedAfter:"",joinedBefore:""}),c=(e,s)=>{o(a=>({...a,[e]:s}))};return(0,t.jsx)(D.lG,{open:e,onOpenChange:s,children:(0,t.jsxs)(D.Cf,{className:"max-w-[50vw]",children:[(0,t.jsx)(D.HM,{onOpenChange:s}),(0,t.jsx)(D.c7,{className:"p-6 pb-0",children:(0,t.jsxs)(D.L3,{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(j.A,{className:"w-5 h-5 text-diwan-600"}),(0,t.jsx)("span",{children:"البحث المتقدم"})]})}),(0,t.jsxs)("div",{className:"p-6 pt-0 space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"name",children:"الاسم"}),(0,t.jsx)(n.p,{id:"name",value:d.name,onChange:e=>c("name",e.target.value),placeholder:"البحث في الاسم..."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"phone",children:"رقم الهاتف"}),(0,t.jsx)(n.p,{id:"phone",value:d.phone,onChange:e=>c("phone",e.target.value),placeholder:"البحث في رقم الهاتف...",dir:"ltr"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"address",children:"العنوان"}),(0,t.jsx)(n.p,{id:"address",value:d.address,onChange:e=>c("address",e.target.value),placeholder:"البحث في العنوان..."})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"status",children:"حالة العضو"}),(0,t.jsxs)("select",{id:"status",value:d.status,onChange:e=>c("status",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-diwan-500",children:[(0,t.jsx)("option",{value:"all",children:"جميع الأعضاء"}),(0,t.jsx)("option",{value:"ACTIVE",children:"نشط"}),(0,t.jsx)("option",{value:"LATE",children:"متأخر"}),(0,t.jsx)("option",{value:"INACTIVE",children:"غير ملتزم"}),(0,t.jsx)("option",{value:"SUSPENDED",children:"موقوف مؤقتاً"}),(0,t.jsx)("option",{value:"ARCHIVED",children:"مؤرشف"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{children:"نطاق المساهمات (بالدينار الأردني)"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsx)(n.p,{value:d.minContributions,onChange:e=>c("minContributions",e.target.value),placeholder:"الحد الأدنى",type:"number",min:"0",step:"0.01",dir:"ltr"}),(0,t.jsx)(n.p,{value:d.maxContributions,onChange:e=>c("maxContributions",e.target.value),placeholder:"الحد الأعلى",type:"number",min:"0",step:"0.01",dir:"ltr"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{children:"نطاق تاريخ الانضمام"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(I.J,{htmlFor:"joinedAfter",className:"text-xs text-gray-500",children:"من تاريخ"}),(0,t.jsx)(n.p,{id:"joinedAfter",value:d.joinedAfter,onChange:e=>c("joinedAfter",e.target.value),type:"date",dir:"ltr"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(I.J,{htmlFor:"joinedBefore",className:"text-xs text-gray-500",children:"إلى تاريخ"}),(0,t.jsx)(n.p,{id:"joinedBefore",value:d.joinedBefore,onChange:e=>c("joinedBefore",e.target.value),type:"date",dir:"ltr"})]})]})]})]}),(0,t.jsxs)(D.Es,{className:"p-6 pt-0",children:[(0,t.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{o({name:"",phone:"",address:"",status:"all",minContributions:"",maxContributions:"",joinedAfter:"",joinedBefore:""}),l(),s(!1)},className:"text-red-600 border-red-600 hover:bg-red-50",children:[(0,t.jsx)(B.A,{className:"w-4 h-4 ml-2"}),"إعادة تعيين"]}),(0,t.jsxs)(i.$,{type:"button",onClick:()=>{a(d),s(!1)},className:"bg-diwan-600 hover:bg-diwan-700",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 ml-2"}),"بحث"]})]})]})})}var eo=a(74484),ec=a(78272);let em=r.forwardRef(({className:e,options:s,children:a,placeholder:r,size:l="md",error:i=!1,helperText:n,...d},o)=>(0,t.jsxs)("div",{className:"relative w-full",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("select",{ref:o,className:(0,_.cn)("w-full appearance-none rounded-xl border-2 bg-white font-medium transition-all duration-200 focus:outline-none focus:ring-4 disabled:cursor-not-allowed disabled:opacity-50",{sm:"h-9 px-3 text-sm",md:"h-11 px-4 text-base",lg:"h-13 px-5 text-lg"}[l],i?"border-danger-300 text-danger-700 focus:border-danger-500 focus:ring-danger-500/20":"border-secondary-200 text-secondary-700 hover:border-secondary-300 focus:border-primary-500 focus:ring-primary-500/20",e),...d,children:[r&&(0,t.jsx)("option",{value:"",disabled:!0,children:r}),s?s.map(e=>(0,t.jsx)("option",{value:e.value,disabled:e.disabled,children:e.label},e.value)):a]}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,t.jsx)(ec.A,{className:"h-5 w-5 text-secondary-400"})})]}),n&&(0,t.jsx)("p",{className:(0,_.cn)("mt-1 text-sm",i?"text-danger-600":"text-secondary-500"),children:n})]}));function ex({includeAll:e=!0,allLabel:s="جميع الحالات",...a}){let r=[...e?[{value:"all",label:s}]:[],{value:"ACTIVE",label:"نشط"},{value:"LATE",label:"متأخر"},{value:"INACTIVE",label:"غير ملتزم"},{value:"SUSPENDED",label:"موقوف مؤقتاً"},{value:"ARCHIVED",label:"مؤرشف"}];return(0,t.jsx)(em,{...a,options:r})}em.displayName="NativeSelect";var eh=a(4403),ep=a(94424);function eg(){let{data:e}=(0,l.useSession)(),[s,a]=(0,r.useState)([]),[x,z]=(0,r.useState)(!0),[E,S]=(0,r.useState)(""),[T,D]=(0,r.useState)("all"),[I,$]=(0,r.useState)({page:1,limit:10,total:0,pages:0}),[R,O]=(0,r.useState)(!1),[P,V]=(0,r.useState)(null),[q,W]=(0,r.useState)(!1),[F,M]=(0,r.useState)(null),[B,Z]=(0,r.useState)(!1),[L,H]=(0,r.useState)(!1),[U,G]=(0,r.useState)(null),[Y,X]=(0,r.useState)(!1),[Q,ee]=(0,r.useState)(!1),[es,et]=(0,r.useState)(null),[el,ei]=(0,r.useState)(!1),[ec,em]=(0,r.useState)(null),[eg,eu]=(0,r.useState)({total:0,active:0,late:0,inactive:0,suspended:0,archived:0}),eb=async()=>{try{z(!0);let e=new URLSearchParams({search:E,status:T,page:I.page.toString(),limit:I.limit.toString()}),s=await fetch(`/api/members?${e}`);if(!s.ok)throw Error("فشل في جلب الأعضاء");let t=await s.json();a(t.members),$(t.pagination)}catch(e){console.error("خطأ في جلب الأعضاء:",e)}finally{z(!1)}},ej=async()=>{try{let e=await fetch("/api/members?limit=1000");if(!e.ok)return;let s=await e.json(),a=s.members.length,t=s.members.filter(e=>"ACTIVE"===e.status).length,r=s.members.filter(e=>"LATE"===e.status).length,l=s.members.filter(e=>"INACTIVE"===e.status).length,i=s.members.filter(e=>"SUSPENDED"===e.status).length,n=s.members.filter(e=>"ARCHIVED"===e.status).length;eu({total:a,active:t,late:r,inactive:l,suspended:i,archived:n})}catch(e){console.error("خطأ في جلب الإحصائيات:",e)}},ef=e=>{V(e),O(!0)},ev=e=>{M(e),W(!0)},ey=e=>{G(e),H(!0)},eN=e=>{et(e),ei(!0)},ew=e=>{em(e),ee(!0)},eA=async e=>{if(confirm("هل أنت متأكد من حذف هذا العضو؟"))try{let s=await fetch(`/api/members/${e}`,{method:"DELETE"});if(!s.ok){let e=await s.json();alert(e.error||"فشل في حذف العضو");return}eb(),ej()}catch(e){console.error("خطأ في حذف العضو:",e),alert("حدث خطأ في حذف العضو")}},eC=()=>{O(!1),eb(),ej()},ek=async()=>{let e=document.createElement("div");e.style.position="absolute",e.style.left="-9999px",e.style.top="0",e.style.width="210mm",e.style.padding="20mm",e.style.fontFamily="Arial, sans-serif",e.style.fontSize="12px",e.style.lineHeight="1.4",e.style.color="#000",e.style.backgroundColor="#fff",e.style.direction="rtl";let a=new Date().toLocaleDateString("en-GB",{year:"numeric",month:"2-digit",day:"2-digit"});e.innerHTML=`
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="font-size: 24px; margin: 0; color: #2c3e50;">ديوان أبو علوش</h1>
        <h2 style="font-size: 18px; margin: 10px 0; color: #34495e;">قائمة الأعضاء</h2>
        <hr style="border: 1px solid #bdc3c7; margin: 20px 0;">
      </div>

      <div style="margin-bottom: 25px;">
        <p style="font-size: 14px; margin: 5px 0;"><strong>تاريخ التقرير:</strong> ${a}</p>
      </div>

      <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 25px; border: 1px solid #dee2e6;">
        <h3 style="margin: 0 0 10px 0; color: #495057;">إحصائيات الأعضاء:</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
          <div>• إجمالي الأعضاء: <strong>${eg.total}</strong> عضو</div>
          <div>• الأعضاء النشطون: <strong>${eg.active}</strong> عضو</div>
          <div>• الأعضاء المتأخرون: <strong>${eg.late}</strong> عضو</div>
          <div>• الأعضاء غير الملتزمون: <strong>${eg.inactive}</strong> عضو</div>
          <div>• الأعضاء الموقوفون: <strong>${eg.suspended}</strong> عضو</div>
          <div>• الأعضاء المؤرشفون: <strong>${eg.archived}</strong> عضو</div>
        </div>
      </div>

      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <thead>
          <tr style="background: #4682b4; color: white;">
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">اسم العضو</th>
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">رقم الهاتف</th>
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">حالة العضو</th>
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">إجمالي الإيرادات</th>
            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">تاريخ الانضمام</th>
          </tr>
        </thead>
        <tbody>
          ${s.map((e,s)=>`
            <tr style="background: ${s%2==0?"#f8f9fa":"#ffffff"};">
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${e.name}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${e.phone||"غير محدد"}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${(0,_.WK)(e.status)}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${(0,_.vv)(e.incomes?.reduce((e,s)=>e+s.amount,0)||0)}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${(0,_.Yq)(e.createdAt)}</td>
            </tr>
          `).join("")}
        </tbody>
      </table>

      <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #6c757d;">
        <hr style="border: 1px solid #dee2e6; margin: 20px 0;">
        <p>تم إنشاؤه بواسطة نظام ديوان أبو علوش - ${a}</p>
      </div>
    `,document.body.appendChild(e);try{let s=await (0,ep.default)(e,{scale:2,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff"}),a=s.toDataURL("image/png"),t=new eh.default("p","mm","a4"),r=210*s.height/s.width,l=r,i=0;for(t.addImage(a,"PNG",0,i,210,r),l-=295;l>=0;)i=l-r,t.addPage(),t.addImage(a,"PNG",0,i,210,r),l-=295;let n=`قائمة_اعضاء_الديوان_${new Date().toISOString().split("T")[0]}.pdf`;t.save(n)}catch(e){console.error("خطأ في تصدير PDF:",e),alert("حدث خطأ في تصدير PDF")}finally{document.body.removeChild(e)}},ez=async(e,s)=>{try{let a=await fetch(`/api/members/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,status:s})});if(!a.ok){let e=await a.json();alert(e.error||"فشل في تحديث حالة العضو");return}eb(),ej()}catch(e){console.error("خطأ في تحديث حالة العضو:",e),alert("حدث خطأ في تحديث حالة العضو")}},eE=e?.user.role!=="VIEWER",eS=e?.user.role==="ADMIN";return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"text-center mb-8",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl shadow-2xl p-10 text-white relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-30 animate-pulse"}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 bg-white bg-opacity-20 backdrop-blur-sm",children:(0,t.jsx)(c.A,{className:"w-10 h-10 text-white"})}),(0,t.jsx)("h1",{className:"text-5xl font-black mb-4 text-white",children:"إدارة الأعضاء"}),(0,t.jsx)("p",{className:"text-xl font-semibold mb-6 text-blue-100",children:"إدارة شاملة لأعضاء ديوان أبو علوش وبياناتهم"}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,t.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"}),(0,t.jsx)("div",{className:"h-1 w-8 rounded-full bg-white bg-opacity-40"}),(0,t.jsx)("div",{className:"h-1 w-16 rounded-full bg-white bg-opacity-60"})]})]})]})}),(0,t.jsx)("div",{className:"flex justify-center mb-8",children:(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-6 border border-gray-100",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,t.jsxs)(i.$,{onClick:()=>{let e=new Blob(["\uFEFF"+[["الاسم","الهاتف","العنوان","الحالة","إجمالي الإيرادات","تاريخ الإضافة"],...s.map(e=>[e.name,e.phone||"",e.address||"",(0,_.WK)(e.status),(e.incomes?.reduce((e,s)=>e+s.amount,0)||0).toString(),(0,_.Yq)(e.createdAt)])].map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a");a.href=URL.createObjectURL(e),a.download=`اعضاء_الديوان_${new Date().toISOString().split("T")[0]}.csv`,a.click()},className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 ml-2"}),"تصدير CSV"]}),(0,t.jsxs)(i.$,{onClick:ek,className:"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,t.jsx)(h,{className:"w-5 h-5 ml-2"}),"تصدير PDF"]}),eE&&(0,t.jsxs)(i.$,{onClick:()=>{V(null),O(!0)},className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-6 py-3 rounded-xl font-semibold",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 ml-2"}),"إضافة عضو جديد"]})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6 sm:grid-cols-3 lg:grid-cols-6",children:[(0,t.jsxs)(d.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 p-1 rounded-t-xl"}),(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"إجمالي الأعضاء"}),(0,t.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#007bff"},children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-white"})})]}),(0,t.jsx)(d.Wu,{className:"relative z-10",children:(0,t.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.total})})]}),(0,t.jsxs)(d.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-green-600 p-1 rounded-t-xl"}),(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"نشط"}),(0,t.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#28a745"},children:(0,t.jsx)(g.A,{className:"h-5 w-5 text-white"})})]}),(0,t.jsx)(d.Wu,{className:"relative z-10",children:(0,t.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.active})})]}),(0,t.jsxs)(d.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-yellow-500 to-yellow-600 p-1 rounded-t-xl"}),(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"متأخر"}),(0,t.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#ffc107"},children:(0,t.jsx)("span",{className:"text-white text-sm font-bold",children:"⏰"})})]}),(0,t.jsx)(d.Wu,{className:"relative z-10",children:(0,t.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.late})})]}),(0,t.jsxs)(d.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-red-500 to-red-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 p-1 rounded-t-xl"}),(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"غير ملتزم"}),(0,t.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#dc3545"},children:(0,t.jsx)(u,{className:"h-5 w-5 text-white"})})]}),(0,t.jsx)(d.Wu,{className:"relative z-10",children:(0,t.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.inactive})})]}),(0,t.jsxs)(d.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 p-1 rounded-t-xl"}),(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"موقوف"}),(0,t.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#800020"},children:(0,t.jsx)("span",{className:"text-white text-sm font-bold",children:"⏸️"})})]}),(0,t.jsx)(d.Wu,{className:"relative z-10",children:(0,t.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.suspended})})]}),(0,t.jsxs)(d.Zp,{className:"group border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-white overflow-hidden relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-500 to-gray-600 opacity-0 group-hover:opacity-10 transition-opacity duration-500"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-gray-500 to-gray-600 p-1 rounded-t-xl"}),(0,t.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3 relative z-10",children:[(0,t.jsx)(d.ZB,{className:"text-sm font-bold",style:{color:"#333333"},children:"مؤرشف"}),(0,t.jsx)("div",{className:"p-3 rounded-2xl shadow-lg",style:{backgroundColor:"#6c757d"},children:(0,t.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCC1"})})]}),(0,t.jsx)(d.Wu,{className:"relative z-10",children:(0,t.jsx)("div",{className:"text-3xl font-black mb-2",style:{color:"#191970"},children:eg.archived})})]})]}),(0,t.jsx)(d.Zp,{className:"border-0 shadow-xl bg-white/80 backdrop-blur-sm",children:(0,t.jsx)(d.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(b.A,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5"}),(0,t.jsx)(n.p,{placeholder:"البحث في الأعضاء بالاسم أو الهاتف...",value:E,onChange:e=>S(e.target.value),className:"pr-12 h-12 text-base shadow-md"})]}),(0,t.jsx)(ex,{value:T,onChange:e=>D(e.target.value),allLabel:"جميع الأعضاء",className:"shadow-md h-12"}),(0,t.jsxs)(i.$,{variant:"info",onClick:()=>Z(!0),className:"shadow-lg h-12",children:[(0,t.jsx)(j.A,{className:"w-4 h-4 ml-2"}),"بحث متقدم"]}),(0,t.jsxs)(i.$,{variant:"accent",onClick:()=>{X(!0)},className:"shadow-lg h-12",children:[(0,t.jsx)(f.A,{className:"w-4 h-4 ml-2"}),"كشف حساب"]})]})})}),(0,t.jsx)(d.Zp,{className:"border-0 shadow-xl bg-white/90 backdrop-blur-sm overflow-hidden",children:(0,t.jsx)(d.Wu,{className:"p-0",children:x?(0,t.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-slate-50 to-slate-100",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500 mb-4"}),(0,t.jsx)("div",{className:"text-slate-600 font-medium",children:"جاري تحميل بيانات الأعضاء..."})]}):0===s.length?(0,t.jsxs)("div",{className:"flex flex-col justify-center items-center h-64 bg-gradient-to-br from-slate-50 to-slate-100",children:[(0,t.jsx)(c.A,{className:"h-16 w-16 text-slate-300 mb-4"}),(0,t.jsx)("div",{className:"text-slate-600 font-medium",children:"لا توجد أعضاء مطابقة للبحث"})]}):(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{children:(0,t.jsxs)(o.Hj,{style:{backgroundColor:"#191970"},children:[(0,t.jsx)(o.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"الاسم"}),(0,t.jsx)(o.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"الهاتف"}),(0,t.jsx)(o.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"الحالة"}),(0,t.jsx)(o.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"إجمالي الإيرادات"}),(0,t.jsx)(o.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"تاريخ الإضافة"}),(0,t.jsx)(o.nd,{style:{backgroundColor:"#191970",color:"white",fontWeight:"600"},children:"الإجراءات"})]})}),(0,t.jsx)(o.BF,{children:s.map(e=>(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),e.address&&(0,t.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,t.jsx)(v.A,{className:"w-3 h-3 ml-1"}),e.address]})]})}),(0,t.jsx)(o.nA,{children:e.phone&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"w-3 h-3 ml-1"}),e.phone]})}),(0,t.jsx)(o.nA,{children:eE?(0,t.jsxs)("select",{value:e.status,onChange:s=>ez(e,s.target.value),className:`px-2 py-1 text-xs font-semibold rounded-full border-0 cursor-pointer ${(0,_.OR)(e.status)}`,children:[(0,t.jsx)("option",{value:"ACTIVE",children:"✅ نشط"}),(0,t.jsx)("option",{value:"LATE",children:"⏰ متأخر"}),(0,t.jsx)("option",{value:"INACTIVE",children:"❌ غير ملتزم"}),(0,t.jsx)("option",{value:"SUSPENDED",children:"⏸️ موقوف مؤقتاً"}),(0,t.jsx)("option",{value:"ARCHIVED",children:"\uD83D\uDCC1 مؤرشف"})]}):(0,t.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${(0,_.OR)(e.status)}`,children:(0,_.WK)(e.status)})}),(0,t.jsx)(o.nA,{children:(0,_.vv)(e.incomes?.reduce((e,s)=>e+s.amount,0)||0)}),(0,t.jsx)(o.nA,{children:(0,_.Yq)(e.createdAt)}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1 space-x-reverse",children:[(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>ev(e),className:"hover:bg-blue-50 border",style:{color:"#0056cc",backgroundColor:"rgba(0, 86, 204, 0.1)",borderColor:"rgba(0, 86, 204, 0.2)",fontWeight:"600"},title:"عرض التفاصيل",children:(0,t.jsx)(N.A,{className:"w-4 h-4"})}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>ey(e.id),className:"hover:bg-purple-50 border",style:{color:"#800020",backgroundColor:"rgba(128, 0, 32, 0.1)",borderColor:"rgba(128, 0, 32, 0.2)",fontWeight:"600"},title:"كشف الحساب",children:(0,t.jsx)(f.A,{className:"w-4 h-4"})}),eE&&(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>ew(e),className:"hover:bg-yellow-50 border",style:{color:"#b8860b",backgroundColor:"rgba(184, 134, 11, 0.1)",borderColor:"rgba(184, 134, 11, 0.2)",fontWeight:"600"},title:"إضافة إيراد",children:(0,t.jsx)(w.A,{className:"w-4 h-4"})}),eE&&(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>ef(e),className:"hover:bg-green-50 border",style:{color:"#1e7e34",backgroundColor:"rgba(30, 126, 52, 0.1)",borderColor:"rgba(30, 126, 52, 0.2)",fontWeight:"600"},title:"تعديل",children:(0,t.jsx)(A.A,{className:"w-4 h-4"})}),eE&&(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>eN(e),className:"hover:bg-purple-50 border",style:{color:"#6f42c1",backgroundColor:"rgba(111, 66, 193, 0.1)",borderColor:"rgba(111, 66, 193, 0.2)",fontWeight:"600"},title:"إدارة كلمة المرور",children:(0,t.jsx)(C,{className:"w-4 h-4"})}),eS&&(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>eA(e.id),className:"hover:bg-red-50 border",style:{color:"#c82333",backgroundColor:"rgba(200, 35, 51, 0.1)",borderColor:"rgba(200, 35, 51, 0.2)",fontWeight:"600"},title:"حذف",children:(0,t.jsx)(k.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})}),I.pages>1&&(0,t.jsxs)("div",{className:"flex justify-center space-x-2 space-x-reverse",children:[(0,t.jsx)(i.$,{variant:"outline",disabled:1===I.page,onClick:()=>$(e=>({...e,page:e.page-1})),className:"border-2",style:{borderColor:"#007bff",color:1===I.page?"#6c757d":"#007bff",backgroundColor:"white"},children:"السابق"}),(0,t.jsxs)("span",{className:"flex items-center px-4",style:{color:"#333333"},children:["صفحة ",I.page," من ",I.pages]}),(0,t.jsx)(i.$,{variant:"outline",disabled:I.page===I.pages,onClick:()=>$(e=>({...e,page:e.page+1})),className:"border-2",style:{borderColor:"#007bff",color:I.page===I.pages?"#6c757d":"#007bff",backgroundColor:"white"},children:"التالي"})]}),(0,t.jsx)(J,{open:R,onOpenChange:O,member:P,onSuccess:eC}),(0,t.jsx)(K,{open:q,onOpenChange:W,member:F}),(0,t.jsx)(ed,{open:B,onOpenChange:Z,onSearch:e=>{let s="";e.name&&(s+=e.name+" "),e.phone&&(s+=e.phone+" "),e.address&&(s+=e.address+" "),S(s.trim()),D(e.status),$(e=>({...e,page:1}))},onReset:()=>{S(""),D("all"),$(e=>({...e,page:1}))}}),(0,t.jsx)(ea,{open:L,onOpenChange:H,memberId:U}),(0,t.jsx)(er,{open:Q,onOpenChange:ee,member:ec,onSuccess:eC}),(0,t.jsx)(eo.A,{open:Y,onOpenChange:X,onSelectMember:e=>{G(e),X(!1),H(!0)},title:"اختيار عضو لكشف الحساب",description:"ابحث عن العضو المطلوب لعرض كشف حسابه"}),(0,t.jsx)(en,{open:el,onOpenChange:ei,member:es,onSuccess:eC})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28094:(e,s,a)=>{Promise.resolve().then(a.bind(a,18207))},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},32822:(e,s,a)=>{Promise.resolve().then(a.bind(a,69895))},33873:e=>{"use strict";e.exports=require("path")},34729:(e,s,a)=>{"use strict";a.d(s,{T:()=>i});var t=a(60687),r=a(43210),l=a(4780);let i=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));i.displayName="Textarea"},35071:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},41550:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},48340:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},48730:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51361:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},53411:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58559:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},69895:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\ابوعلوش\\\\diwan-abu-alosh\\\\src\\\\app\\\\dashboard\\\\members\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},74484:(e,s,a)=>{"use strict";a.d(s,{A:()=>u});var t=a(60687),r=a(43210),l=a(29523),i=a(89667),n=a(63503),d=a(99270),o=a(11860),c=a(58869),m=a(48340),x=a(97992),h=a(10022),p=a(96834),g=a(4780);function u({open:e,onOpenChange:s,onSelectMember:a,title:u="اختيار عضو",description:b="ابحث عن العضو المطلوب"}){let[j,f]=(0,r.useState)(""),[v,y]=(0,r.useState)([]),[N,w]=(0,r.useState)(!1),[A,C]=(0,r.useState)(null),k=e=>{C(e)};return(0,t.jsx)(n.lG,{open:e,onOpenChange:s,children:(0,t.jsxs)(n.Cf,{className:"max-w-[50vw] max-h-[80vh] overflow-hidden flex flex-col",children:[(0,t.jsx)(n.c7,{className:"pb-4 border-b",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,t.jsx)(d.A,{className:"w-5 h-5 text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.L3,{className:"text-lg font-bold text-gray-900",children:u}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:b})]})]}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>s(!1),className:"text-gray-500 hover:text-gray-700",children:(0,t.jsx)(o.A,{className:"w-4 h-4"})})]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden flex flex-col",children:[(0,t.jsx)("div",{className:"p-4 border-b bg-gray-50",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)(i.p,{placeholder:"ابحث بالاسم أو الهاتف أو العنوان...",value:j,onChange:e=>f(e.target.value),className:"pr-10",autoFocus:!0})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:N?(0,t.jsx)("div",{className:"flex justify-center items-center h-32",children:(0,t.jsx)("div",{className:"text-gray-500",children:"جاري البحث..."})}):0===v.length?(0,t.jsxs)("div",{className:"flex flex-col justify-center items-center h-32 text-gray-500",children:[(0,t.jsx)(c.A,{className:"w-12 h-12 mb-2 text-gray-300"}),(0,t.jsx)("p",{children:j?"لا توجد نتائج للبحث":"ابدأ بكتابة اسم العضو للبحث"})]}):(0,t.jsx)("div",{className:"space-y-2",children:v.map(e=>(0,t.jsx)("div",{onClick:()=>k(e),className:`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${A?.id===e.id?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,t.jsx)(p.E,{className:(0,g.OR)(e.status),children:(0,g.WK)(e.status)})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[e.phone&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(m.A,{className:"w-3 h-3"}),e.phone]}),e.address&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(x.A,{className:"w-3 h-3"}),e.address]})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[e._count.incomes," مساهمة"]})]})},e.id))})}),(0,t.jsxs)("div",{className:"p-4 border-t bg-gray-50 flex justify-end gap-2",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:()=>s(!1),children:"إلغاء"}),(0,t.jsxs)(l.$,{onClick:()=>{A&&(a(A.id),s(!1))},disabled:!A,className:"bg-purple-600 hover:bg-purple-700 text-white",children:[(0,t.jsx)(h.A,{className:"w-4 h-4 ml-2"}),"عرض كشف الحساب"]})]})]})]})})}},77026:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,a)=>{"use strict";a.d(s,{J:()=>i});var t=a(60687),r=a(43210),l=a(4780);let i=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("label",{ref:a,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...s}));i.displayName="Label"},81630:e=>{"use strict";e.exports=require("http")},91054:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=a(65239),r=a(48088),l=a(88170),i=a.n(l),n=a(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let o={children:["",{children:["dashboard",{children:["members",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,69895)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,63144)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\ابوعلوش\\diwan-abu-alosh\\src\\app\\dashboard\\members\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/members/page",pathname:"/dashboard/members",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},93508:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},93613:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},97992:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4243,5663,4999,3412,5442,7934,5498,1726,2131,5662,2635,4403,6329,5977,6154],()=>a(91054));module.exports=t})();