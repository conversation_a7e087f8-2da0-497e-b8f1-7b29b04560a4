/* إصلاحات ألوان التاجات والشارات - النص الأبيض */

/* إصلاح ألوان Badge العامة */
.badge,
[class*="badge"],
[data-badge],
.tag,
[class*="tag"],
[data-tag] {
  color: white !important;
}

/* إصلاح ألوان Badge المحددة */
.badge-default,
.badge-primary {
  background-color: #3b82f6 !important; /* blue-500 */
  color: white !important;
  border-color: #2563eb !important; /* blue-600 */
}

.badge-secondary {
  background-color: #6b7280 !important; /* gray-500 */
  color: white !important;
  border-color: #4b5563 !important; /* gray-600 */
}

.badge-success {
  background-color: #10b981 !important; /* emerald-500 */
  color: white !important;
  border-color: #059669 !important; /* emerald-600 */
}

.badge-warning {
  background-color: #f59e0b !important; /* amber-500 */
  color: white !important;
  border-color: #d97706 !important; /* amber-600 */
}

.badge-error,
.badge-danger,
.badge-destructive {
  background-color: #ef4444 !important; /* red-500 */
  color: white !important;
  border-color: #dc2626 !important; /* red-600 */
}

.badge-info {
  background-color: #3b82f6 !important; /* blue-500 */
  color: white !important;
  border-color: #2563eb !important; /* blue-600 */
}

.badge-outline {
  background-color: transparent !important;
  color: white !important;
  border-color: white !important;
  border-width: 1px !important;
}

/* إصلاح ألوان حالات الأعضاء */
.status-active,
.badge-active {
  background-color: #10b981 !important; /* emerald-500 */
  color: white !important;
  border-color: #059669 !important; /* emerald-600 */
}

.status-late,
.badge-late {
  background-color: #f59e0b !important; /* amber-500 */
  color: white !important;
  border-color: #d97706 !important; /* amber-600 */
}

.status-inactive,
.badge-inactive {
  background-color: #ef4444 !important; /* red-500 */
  color: white !important;
  border-color: #dc2626 !important; /* red-600 */
}

.status-suspended,
.badge-suspended {
  background-color: #8b5cf6 !important; /* violet-500 */
  color: white !important;
  border-color: #7c3aed !important; /* violet-600 */
}

.status-archived,
.badge-archived {
  background-color: #6b7280 !important; /* gray-500 */
  color: white !important;
  border-color: #4b5563 !important; /* gray-600 */
}

/* إصلاح ألوان فئات المصروفات */
.category-general,
.badge-general {
  background-color: #3b82f6 !important; /* blue-500 */
  color: white !important;
  border-color: #2563eb !important; /* blue-600 */
}

.category-maintenance,
.badge-maintenance {
  background-color: #f59e0b !important; /* amber-500 */
  color: white !important;
  border-color: #d97706 !important; /* amber-600 */
}

.category-events,
.badge-events {
  background-color: #8b5cf6 !important; /* violet-500 */
  color: white !important;
  border-color: #7c3aed !important; /* violet-600 */
}

.category-utilities,
.badge-utilities {
  background-color: #06b6d4 !important; /* cyan-500 */
  color: white !important;
  border-color: #0891b2 !important; /* cyan-600 */
}

/* إصلاح ألوان أنواع الإيرادات */
.type-monthly,
.badge-monthly {
  background-color: #10b981 !important; /* emerald-500 */
  color: white !important;
  border-color: #059669 !important; /* emerald-600 */
}

.type-donation,
.badge-donation {
  background-color: #f59e0b !important; /* amber-500 */
  color: white !important;
  border-color: #d97706 !important; /* amber-600 */
}

.type-special,
.badge-special {
  background-color: #8b5cf6 !important; /* violet-500 */
  color: white !important;
  border-color: #7c3aed !important; /* violet-600 */
}

/* إصلاح ألوان التاجات في الجداول */
table .badge,
table [class*="badge"],
.table .badge,
.table [class*="badge"],
td .badge,
td [class*="badge"] {
  color: white !important;
}

/* إصلاح ألوان التاجات في البطاقات */
.card .badge,
.card [class*="badge"],
[class*="card"] .badge,
[class*="card"] [class*="badge"] {
  color: white !important;
}

/* إصلاح ألوان التاجات في الحوارات */
.dialog .badge,
.dialog [class*="badge"],
[role="dialog"] .badge,
[role="dialog"] [class*="badge"] {
  color: white !important;
}

/* إصلاح ألوان التاجات في النماذج */
form .badge,
form [class*="badge"],
.form-group .badge,
.form-group [class*="badge"] {
  color: white !important;
}

/* إصلاح ألوان التاجات مع فئات Tailwind محددة */
.bg-green-100,
.bg-green-200 {
  background-color: #10b981 !important; /* emerald-500 */
  color: white !important;
}

.bg-amber-100,
.bg-amber-200,
.bg-yellow-100,
.bg-yellow-200 {
  background-color: #f59e0b !important; /* amber-500 */
  color: white !important;
}

.bg-red-100,
.bg-red-200 {
  background-color: #ef4444 !important; /* red-500 */
  color: white !important;
}

.bg-blue-100,
.bg-blue-200 {
  background-color: #3b82f6 !important; /* blue-500 */
  color: white !important;
}

.bg-purple-100,
.bg-purple-200,
.bg-violet-100,
.bg-violet-200 {
  background-color: #8b5cf6 !important; /* violet-500 */
  color: white !important;
}

.bg-gray-100,
.bg-gray-200,
.bg-slate-100,
.bg-slate-200 {
  background-color: #6b7280 !important; /* gray-500 */
  color: white !important;
}

.bg-orange-100,
.bg-orange-200 {
  background-color: #f97316 !important; /* orange-500 */
  color: white !important;
}

.bg-cyan-100,
.bg-cyan-200 {
  background-color: #06b6d4 !important; /* cyan-500 */
  color: white !important;
}

.bg-pink-100,
.bg-pink-200 {
  background-color: #ec4899 !important; /* pink-500 */
  color: white !important;
}

.bg-indigo-100,
.bg-indigo-200 {
  background-color: #6366f1 !important; /* indigo-500 */
  color: white !important;
}

/* إصلاح ألوان النصوص الملونة في التاجات */
.text-green-800,
.text-green-700,
.text-green-600 {
  color: white !important;
}

.text-amber-800,
.text-amber-700,
.text-amber-600,
.text-yellow-800,
.text-yellow-700,
.text-yellow-600 {
  color: white !important;
}

.text-red-800,
.text-red-700,
.text-red-600 {
  color: white !important;
}

.text-blue-800,
.text-blue-700,
.text-blue-600 {
  color: white !important;
}

.text-purple-800,
.text-purple-700,
.text-purple-600,
.text-violet-800,
.text-violet-700,
.text-violet-600 {
  color: white !important;
}

.text-gray-800,
.text-gray-700,
.text-gray-600,
.text-slate-800,
.text-slate-700,
.text-slate-600 {
  color: white !important;
}

/* إصلاح ألوان التاجات في صفحات محددة */
.members-page .badge,
.incomes-page .badge,
.expenses-page .badge,
.gallery-page .badge,
.reports-page .badge,
.settings-page .badge {
  color: white !important;
}

/* إصلاح ألوان التاجات مع حالات خاصة */
.badge:hover,
[class*="badge"]:hover {
  color: white !important;
  opacity: 0.9 !important;
}

.badge:focus,
[class*="badge"]:focus {
  color: white !important;
  outline: 2px solid rgba(255, 255, 255, 0.5) !important;
}

.badge:active,
[class*="badge"]:active {
  color: white !important;
  opacity: 0.8 !important;
}

/* إصلاح ألوان التاجات المعطلة */
.badge:disabled,
.badge[disabled],
[class*="badge"]:disabled,
[class*="badge"][disabled] {
  background-color: #9ca3af !important; /* gray-400 */
  color: white !important;
  opacity: 0.6 !important;
}

/* إصلاح ألوان التاجات الصغيرة والكبيرة */
.badge-sm,
.badge-small,
.badge-lg,
.badge-large {
  color: white !important;
}

/* إصلاح ألوان التاجات مع أيقونات */
.badge .icon,
.badge svg,
[class*="badge"] .icon,
[class*="badge"] svg {
  color: white !important;
}

/* إصلاح ألوان التاجات في الوضع الداكن */
@media (prefers-color-scheme: dark) {
  .badge,
  [class*="badge"],
  .tag,
  [class*="tag"] {
    color: white !important;
  }
}

/* إصلاح ألوان التاجات مع فئات Bootstrap */
.badge-primary,
.badge-secondary,
.badge-success,
.badge-danger,
.badge-warning,
.badge-info,
.badge-light,
.badge-dark {
  color: white !important;
}

/* إصلاح ألوان التاجات مع فئات Material UI */
.MuiBadge-badge,
.MuiChip-root {
  color: white !important;
}

/* إصلاح ألوان التاجات المخصصة */
.custom-badge,
.status-badge,
.category-badge,
.type-badge {
  color: white !important;
}

/* إصلاح ألوان التاجات في المكونات المحددة */
.member-status-badge,
.income-type-badge,
.expense-category-badge,
.activity-badge {
  color: white !important;
}

/* إصلاح ألوان التاجات مع تأثيرات الانتقال */
.badge,
[class*="badge"] {
  transition: all 0.2s ease-in-out !important;
}

/* إصلاح ألوان التاجات مع تأثيرات الظل */
.badge:hover,
[class*="badge"]:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}
