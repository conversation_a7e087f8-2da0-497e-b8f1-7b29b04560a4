/* [project]/src/styles/button-colors.css [app-client] (css) */
.btn-primary, .bg-primary, button[class*="bg-primary"], .button-primary {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-primary:hover, .bg-primary:hover, button[class*="bg-primary"]:hover, .button-primary:hover {
  color: #fff !important;
  background-color: #0056cc !important;
  border-color: #0056cc !important;
}

.btn-primary:focus, .bg-primary:focus, button[class*="bg-primary"]:focus, .button-primary:focus {
  background-color: #0056cc !important;
  border-color: #0056cc !important;
  box-shadow: 0 0 0 .2rem #007bff40 !important;
}

.btn-success, .bg-success, button[class*="bg-success"], .button-success {
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.btn-success:hover, .bg-success:hover, button[class*="bg-success"]:hover, .button-success:hover {
  color: #fff !important;
  background-color: #228b3c !important;
  border-color: #228b3c !important;
}

.btn-success:focus, .bg-success:focus, button[class*="bg-success"]:focus, .button-success:focus {
  background-color: #228b3c !important;
  border-color: #228b3c !important;
  box-shadow: 0 0 0 .2rem #28a74540 !important;
}

.btn-danger, .bg-destructive, button[class*="bg-destructive"], .button-danger {
  color: #fff !important;
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}

.btn-danger:hover, .bg-destructive:hover, button[class*="bg-destructive"]:hover, .button-danger:hover {
  color: #fff !important;
  background-color: #c82333 !important;
  border-color: #c82333 !important;
}

.btn-warning, .bg-warning, button[class*="bg-warning"], .button-warning {
  color: #333 !important;
  background-color: #ffc107 !important;
  border-color: #ffc107 !important;
}

.btn-warning:hover, .bg-warning:hover, button[class*="bg-warning"]:hover, .button-warning:hover {
  color: #333 !important;
  background-color: #e0a800 !important;
  border-color: #e0a800 !important;
}

.btn-info, .bg-info, button[class*="bg-info"], .button-info {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-secondary, .bg-secondary, button[class*="bg-secondary"], .button-secondary {
  color: #fff !important;
  background-color: #6c757d !important;
  border-color: #6c757d !important;
}

.btn-secondary:hover, .bg-secondary:hover, button[class*="bg-secondary"]:hover, .button-secondary:hover {
  color: #fff !important;
  background-color: #5a6268 !important;
  border-color: #5a6268 !important;
}

.btn-outline-primary {
  color: #007bff !important;
  background-color: #0000 !important;
  border-color: #007bff !important;
}

.btn-outline-primary:hover {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-outline-success {
  color: #28a745 !important;
  background-color: #0000 !important;
  border-color: #28a745 !important;
}

.btn-outline-success:hover {
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.bg-blue-500, .bg-blue-600 {
  background-color: #007bff !important;
}

.bg-blue-500:hover, .bg-blue-600:hover, .hover\:bg-blue-600:hover, .hover\:bg-blue-700:hover {
  background-color: #0056cc !important;
}

.bg-green-500, .bg-green-600 {
  background-color: #28a745 !important;
}

.bg-green-500:hover, .bg-green-600:hover, .hover\:bg-green-600:hover, .hover\:bg-green-700:hover {
  background-color: #228b3c !important;
}

.diwan-btn-primary {
  border-radius: .375rem;
  padding: .5rem 1rem;
  font-weight: 500;
  transition: all .2s;
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.diwan-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #007bff4d;
  background-color: #0056cc !important;
  border-color: #0056cc !important;
}

.diwan-btn-success {
  border-radius: .375rem;
  padding: .5rem 1rem;
  font-weight: 500;
  transition: all .2s;
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.diwan-btn-success:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #28a7454d;
  background-color: #228b3c !important;
  border-color: #228b3c !important;
}

.save-button, button[type="submit"] {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.save-button:hover, button[type="submit"]:hover {
  background-color: #0056cc !important;
  border-color: #0056cc !important;
}

.cancel-button, button[type="button"][class*="cancel"] {
  color: #fff !important;
  background-color: #6c757d !important;
  border-color: #6c757d !important;
}

.cancel-button:hover, button[type="button"][class*="cancel"]:hover {
  background-color: #5a6268 !important;
  border-color: #5a6268 !important;
}

.action-button-edit {
  color: #fff !important;
  background-color: #007bff !important;
}

.action-button-delete {
  color: #fff !important;
  background-color: #dc3545 !important;
}

.action-button-view {
  color: #fff !important;
  background-color: #28a745 !important;
}

button:focus, .btn:focus {
  outline: none !important;
  box-shadow: 0 0 0 .2rem #007bff40 !important;
}

.btn-sm {
  padding: .25rem .5rem !important;
  font-size: .875rem !important;
}

.btn-lg {
  padding: .75rem 1.5rem !important;
  font-size: 1.125rem !important;
}

button, .btn {
  transition: all .2s !important;
}

button:hover, .btn:hover {
  transform: translateY(-1px) !important;
}

button:active, .btn:active {
  transform: translateY(0) !important;
}

button:disabled, .btn:disabled {
  opacity: .6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

button:disabled:hover, .btn:disabled:hover {
  background-color: inherit !important;
  transform: none !important;
}

.members-page .btn-primary, .members-page button[style*="background-color: rgb(0, 123, 255)"] {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.members-page .btn-success, .members-page button[style*="background-color: rgb(40, 167, 69)"] {
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.members-page .btn-danger, .members-page button[style*="background-color: rgb(220, 53, 69)"] {
  color: #fff !important;
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}

button[class*="bg-blue"], .bg-blue-500, .bg-blue-600 {
  color: #fff !important;
  background-color: #007bff !important;
}

button[class*="bg-green"], .bg-green-500, .bg-green-600 {
  color: #fff !important;
  background-color: #28a745 !important;
}

button[class*="bg-red"], .bg-red-500, .bg-red-600 {
  color: #fff !important;
  background-color: #dc3545 !important;
}

button[title*="تصدير"], button[aria-label*="تصدير"], .export-button {
  color: #fff !important;
  background-color: #28a745 !important;
}

button[title*="PDF"], button[aria-label*="PDF"], .pdf-button {
  color: #fff !important;
  background-color: #dc3545 !important;
}

button[title*="إضافة"], button[aria-label*="إضافة"], button[title*="جديد"], button[aria-label*="جديد"], .add-button {
  color: #fff !important;
  background-color: #007bff !important;
}

/*# sourceMappingURL=src_styles_button-colors_css_f9ee138c._.single.css.map*/