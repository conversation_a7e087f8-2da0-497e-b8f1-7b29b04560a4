{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/badge-fixes.css"], "sourcesContent": ["/* إصلاحات ألوان التاجات والشارات - النص الأبيض */\n\n/* إصلاح ألوان Badge العامة */\n.badge,\n[class*=\"badge\"],\n[data-badge],\n.tag,\n[class*=\"tag\"],\n[data-tag] {\n  color: white !important;\n}\n\n/* إصلاح ألوان Badge المحددة */\n.badge-default,\n.badge-primary {\n  background-color: #3b82f6 !important; /* blue-500 */\n  color: white !important;\n  border-color: #2563eb !important; /* blue-600 */\n}\n\n.badge-secondary {\n  background-color: #6b7280 !important; /* gray-500 */\n  color: white !important;\n  border-color: #4b5563 !important; /* gray-600 */\n}\n\n.badge-success {\n  background-color: #10b981 !important; /* emerald-500 */\n  color: white !important;\n  border-color: #059669 !important; /* emerald-600 */\n}\n\n.badge-warning {\n  background-color: #f59e0b !important; /* amber-500 */\n  color: white !important;\n  border-color: #d97706 !important; /* amber-600 */\n}\n\n.badge-error,\n.badge-danger,\n.badge-destructive {\n  background-color: #ef4444 !important; /* red-500 */\n  color: white !important;\n  border-color: #dc2626 !important; /* red-600 */\n}\n\n.badge-info {\n  background-color: #3b82f6 !important; /* blue-500 */\n  color: white !important;\n  border-color: #2563eb !important; /* blue-600 */\n}\n\n.badge-outline {\n  background-color: transparent !important;\n  color: white !important;\n  border-color: white !important;\n  border-width: 1px !important;\n}\n\n/* إصلاح ألوان حالات الأعضاء */\n.status-active,\n.badge-active {\n  background-color: #10b981 !important; /* emerald-500 */\n  color: white !important;\n  border-color: #059669 !important; /* emerald-600 */\n}\n\n.status-late,\n.badge-late {\n  background-color: #f59e0b !important; /* amber-500 */\n  color: white !important;\n  border-color: #d97706 !important; /* amber-600 */\n}\n\n.status-inactive,\n.badge-inactive {\n  background-color: #ef4444 !important; /* red-500 */\n  color: white !important;\n  border-color: #dc2626 !important; /* red-600 */\n}\n\n.status-suspended,\n.badge-suspended {\n  background-color: #8b5cf6 !important; /* violet-500 */\n  color: white !important;\n  border-color: #7c3aed !important; /* violet-600 */\n}\n\n.status-archived,\n.badge-archived {\n  background-color: #6b7280 !important; /* gray-500 */\n  color: white !important;\n  border-color: #4b5563 !important; /* gray-600 */\n}\n\n/* إصلاح ألوان فئات المصروفات */\n.category-general,\n.badge-general {\n  background-color: #3b82f6 !important; /* blue-500 */\n  color: white !important;\n  border-color: #2563eb !important; /* blue-600 */\n}\n\n.category-maintenance,\n.badge-maintenance {\n  background-color: #f59e0b !important; /* amber-500 */\n  color: white !important;\n  border-color: #d97706 !important; /* amber-600 */\n}\n\n.category-events,\n.badge-events {\n  background-color: #8b5cf6 !important; /* violet-500 */\n  color: white !important;\n  border-color: #7c3aed !important; /* violet-600 */\n}\n\n.category-utilities,\n.badge-utilities {\n  background-color: #06b6d4 !important; /* cyan-500 */\n  color: white !important;\n  border-color: #0891b2 !important; /* cyan-600 */\n}\n\n/* إصلاح ألوان أنواع الإيرادات */\n.type-monthly,\n.badge-monthly {\n  background-color: #10b981 !important; /* emerald-500 */\n  color: white !important;\n  border-color: #059669 !important; /* emerald-600 */\n}\n\n.type-donation,\n.badge-donation {\n  background-color: #f59e0b !important; /* amber-500 */\n  color: white !important;\n  border-color: #d97706 !important; /* amber-600 */\n}\n\n.type-special,\n.badge-special {\n  background-color: #8b5cf6 !important; /* violet-500 */\n  color: white !important;\n  border-color: #7c3aed !important; /* violet-600 */\n}\n\n/* إصلاح ألوان التاجات في الجداول */\ntable .badge,\ntable [class*=\"badge\"],\n.table .badge,\n.table [class*=\"badge\"],\ntd .badge,\ntd [class*=\"badge\"] {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات في البطاقات */\n.card .badge,\n.card [class*=\"badge\"],\n[class*=\"card\"] .badge,\n[class*=\"card\"] [class*=\"badge\"] {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات في الحوارات */\n.dialog .badge,\n.dialog [class*=\"badge\"],\n[role=\"dialog\"] .badge,\n[role=\"dialog\"] [class*=\"badge\"] {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات في النماذج */\nform .badge,\nform [class*=\"badge\"],\n.form-group .badge,\n.form-group [class*=\"badge\"] {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات مع فئات Tailwind محددة */\n.bg-green-100,\n.bg-green-200 {\n  background-color: #10b981 !important; /* emerald-500 */\n  color: white !important;\n}\n\n.bg-amber-100,\n.bg-amber-200,\n.bg-yellow-100,\n.bg-yellow-200 {\n  background-color: #f59e0b !important; /* amber-500 */\n  color: white !important;\n}\n\n.bg-red-100,\n.bg-red-200 {\n  background-color: #ef4444 !important; /* red-500 */\n  color: white !important;\n}\n\n.bg-blue-100,\n.bg-blue-200 {\n  background-color: #3b82f6 !important; /* blue-500 */\n  color: white !important;\n}\n\n.bg-purple-100,\n.bg-purple-200,\n.bg-violet-100,\n.bg-violet-200 {\n  background-color: #8b5cf6 !important; /* violet-500 */\n  color: white !important;\n}\n\n.bg-gray-100,\n.bg-gray-200,\n.bg-slate-100,\n.bg-slate-200 {\n  background-color: #6b7280 !important; /* gray-500 */\n  color: white !important;\n}\n\n.bg-orange-100,\n.bg-orange-200 {\n  background-color: #f97316 !important; /* orange-500 */\n  color: white !important;\n}\n\n.bg-cyan-100,\n.bg-cyan-200 {\n  background-color: #06b6d4 !important; /* cyan-500 */\n  color: white !important;\n}\n\n.bg-pink-100,\n.bg-pink-200 {\n  background-color: #ec4899 !important; /* pink-500 */\n  color: white !important;\n}\n\n.bg-indigo-100,\n.bg-indigo-200 {\n  background-color: #6366f1 !important; /* indigo-500 */\n  color: white !important;\n}\n\n/* إصلاح ألوان النصوص الملونة في التاجات */\n.text-green-800,\n.text-green-700,\n.text-green-600 {\n  color: white !important;\n}\n\n.text-amber-800,\n.text-amber-700,\n.text-amber-600,\n.text-yellow-800,\n.text-yellow-700,\n.text-yellow-600 {\n  color: white !important;\n}\n\n.text-red-800,\n.text-red-700,\n.text-red-600 {\n  color: white !important;\n}\n\n.text-blue-800,\n.text-blue-700,\n.text-blue-600 {\n  color: white !important;\n}\n\n.text-purple-800,\n.text-purple-700,\n.text-purple-600,\n.text-violet-800,\n.text-violet-700,\n.text-violet-600 {\n  color: white !important;\n}\n\n.text-gray-800,\n.text-gray-700,\n.text-gray-600,\n.text-slate-800,\n.text-slate-700,\n.text-slate-600 {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات في صفحات محددة */\n.members-page .badge,\n.incomes-page .badge,\n.expenses-page .badge,\n.gallery-page .badge,\n.reports-page .badge,\n.settings-page .badge {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات مع حالات خاصة */\n.badge:hover,\n[class*=\"badge\"]:hover {\n  color: white !important;\n  opacity: 0.9 !important;\n}\n\n.badge:focus,\n[class*=\"badge\"]:focus {\n  color: white !important;\n  outline: 2px solid rgba(255, 255, 255, 0.5) !important;\n}\n\n.badge:active,\n[class*=\"badge\"]:active {\n  color: white !important;\n  opacity: 0.8 !important;\n}\n\n/* إصلاح ألوان التاجات المعطلة */\n.badge:disabled,\n.badge[disabled],\n[class*=\"badge\"]:disabled,\n[class*=\"badge\"][disabled] {\n  background-color: #9ca3af !important; /* gray-400 */\n  color: white !important;\n  opacity: 0.6 !important;\n}\n\n/* إصلاح ألوان التاجات الصغيرة والكبيرة */\n.badge-sm,\n.badge-small,\n.badge-lg,\n.badge-large {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات مع أيقونات */\n.badge .icon,\n.badge svg,\n[class*=\"badge\"] .icon,\n[class*=\"badge\"] svg {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات في الوضع الداكن */\n@media (prefers-color-scheme: dark) {\n  .badge,\n  [class*=\"badge\"],\n  .tag,\n  [class*=\"tag\"] {\n    color: white !important;\n  }\n}\n\n/* إصلاح ألوان التاجات مع فئات Bootstrap */\n.badge-primary,\n.badge-secondary,\n.badge-success,\n.badge-danger,\n.badge-warning,\n.badge-info,\n.badge-light,\n.badge-dark {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات مع فئات Material UI */\n.MuiBadge-badge,\n.MuiChip-root {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات المخصصة */\n.custom-badge,\n.status-badge,\n.category-badge,\n.type-badge {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات في المكونات المحددة */\n.member-status-badge,\n.income-type-badge,\n.expense-category-badge,\n.activity-badge {\n  color: white !important;\n}\n\n/* إصلاح ألوان التاجات مع تأثيرات الانتقال */\n.badge,\n[class*=\"badge\"] {\n  transition: all 0.2s ease-in-out !important;\n}\n\n/* إصلاح ألوان التاجات مع تأثيرات الظل */\n.badge:hover,\n[class*=\"badge\"]:hover {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;\n}\n"], "names": [], "mappings": "AAGA;;;;AAUA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAQA;;;;;;AAMA;;;;;;;AAQA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAQA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAQA;;;;;;AAOA;;;;;;AAOA;;;;;;AAQA;;;;AAkCA;;;;;AAMA;;;;;AAQA;;;;;AAMA;;;;;AAMA;;;;;AAQA;;;;;AAQA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAOA;;;;AAwDA;;;;;AAMA;;;;;AAMA;;;;;AAOA;;;;;;AAUA;;;;AAgBA;EACE;;;;;AASF;;;;AAkCA;;;;AAMA"}}]}