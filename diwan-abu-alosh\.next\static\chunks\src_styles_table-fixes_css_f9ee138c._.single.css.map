{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/table-fixes.css"], "sourcesContent": ["/* إصلاحات ألوان الجداول - اللون الكحلي */\n\n/* تعريف اللون الكحلي */\n:root {\n  --navy-50: #f8fafc;\n  --navy-100: #f1f5f9;\n  --navy-200: #e2e8f0;\n  --navy-300: #cbd5e1;\n  --navy-400: #94a3b8;\n  --navy-500: #64748b;\n  --navy-600: #475569;\n  --navy-700: #334155;\n  --navy-800: #1e293b;\n  --navy-900: #0f172a;\n}\n\n/* إصلاح ألوان الجداول العامة */\ntable,\n.table,\n[role=\"table\"] {\n  color: var(--navy-800) !important;\n}\n\n/* إصلاح ألوان رؤوس الجداول */\nth,\nthead th,\n.table-head,\n.table-header,\n[role=\"columnheader\"] {\n  color: var(--navy-700) !important;\n  font-weight: 600 !important;\n}\n\n/* إصلاح ألوان خلايا الجداول */\ntd,\ntbody td,\n.table-cell,\n.table-data,\n[role=\"cell\"] {\n  color: var(--navy-800) !important;\n}\n\n/* إصلاح ألوان صفوف الجداول */\ntr,\n.table-row,\n[role=\"row\"] {\n  color: var(--navy-800) !important;\n}\n\n/* إصلاح ألوان الجداول المحسنة */\n.enhanced-table,\n.enhanced-table th,\n.enhanced-table td {\n  color: var(--navy-800) !important;\n}\n\n.enhanced-table thead th {\n  color: var(--navy-700) !important;\n  font-weight: 600 !important;\n}\n\n/* إصلاح ألوان جداول الأعضاء */\n.members-table,\n.members-table th,\n.members-table td {\n  color: var(--navy-800) !important;\n}\n\n.members-table thead th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان جداول الإيرادات */\n.incomes-table,\n.incomes-table th,\n.incomes-table td {\n  color: var(--navy-800) !important;\n}\n\n.incomes-table thead th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان جداول المصروفات */\n.expenses-table,\n.expenses-table th,\n.expenses-table td {\n  color: var(--navy-800) !important;\n}\n\n.expenses-table thead th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان جداول التقارير */\n.reports-table,\n.reports-table th,\n.reports-table td {\n  color: var(--navy-800) !important;\n}\n\n.reports-table thead th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول في الحوارات */\n.dialog table,\n.dialog th,\n.dialog td,\n[role=\"dialog\"] table,\n[role=\"dialog\"] th,\n[role=\"dialog\"] td {\n  color: var(--navy-800) !important;\n}\n\n.dialog thead th,\n[role=\"dialog\"] thead th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول في البطاقات */\n.card table,\n.card th,\n.card td,\n[class*=\"card\"] table,\n[class*=\"card\"] th,\n[class*=\"card\"] td {\n  color: var(--navy-800) !important;\n}\n\n.card thead th,\n[class*=\"card\"] thead th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان فئات Tailwind المحددة */\n.text-slate-700,\n.text-slate-800,\n.text-gray-700,\n.text-gray-800 {\n  color: var(--navy-800) !important;\n}\n\n.text-slate-600,\n.text-gray-600 {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول مع فئات مخصصة */\n.table-navy,\n.navy-table {\n  color: var(--navy-800) !important;\n}\n\n.table-navy th,\n.navy-table th {\n  color: var(--navy-700) !important;\n  font-weight: 600 !important;\n}\n\n/* إصلاح ألوان الجداول الاستجابية */\n.table-responsive,\n.responsive-table {\n  color: var(--navy-800) !important;\n}\n\n.table-responsive th,\n.responsive-table th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول المرتبة */\n.sortable-table,\n.table-sortable {\n  color: var(--navy-800) !important;\n}\n\n.sortable-table th,\n.table-sortable th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول القابلة للتصفية */\n.filterable-table,\n.table-filterable {\n  color: var(--navy-800) !important;\n}\n\n.filterable-table th,\n.table-filterable th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول مع البحث */\n.searchable-table,\n.table-searchable {\n  color: var(--navy-800) !important;\n}\n\n.searchable-table th,\n.table-searchable th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول مع التصفح */\n.paginated-table,\n.table-paginated {\n  color: var(--navy-800) !important;\n}\n\n.paginated-table th,\n.table-paginated th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول في صفحات محددة */\n.members-page table,\n.incomes-page table,\n.expenses-page table,\n.reports-page table,\n.gallery-page table {\n  color: var(--navy-800) !important;\n}\n\n.members-page thead th,\n.incomes-page thead th,\n.expenses-page thead th,\n.reports-page thead th,\n.gallery-page thead th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول مع حالات خاصة */\n.table-loading,\n.loading-table {\n  color: var(--navy-800) !important;\n}\n\n.table-empty,\n.empty-table {\n  color: var(--navy-600) !important;\n}\n\n/* إصلاح ألوان الجداول مع التمرير */\n.table-hover tr:hover,\n.hover-table tr:hover {\n  color: var(--navy-900) !important;\n}\n\n/* إصلاح ألوان الجداول المحددة */\n.table-selected,\n.selected-table,\ntr.selected,\ntr[data-selected=\"true\"] {\n  color: var(--navy-900) !important;\n  background-color: var(--navy-50) !important;\n}\n\n/* إصلاح ألوان الجداول المعطلة */\n.table-disabled,\n.disabled-table,\ntr.disabled,\ntr[disabled] {\n  color: var(--navy-400) !important;\n  opacity: 0.6 !important;\n}\n\n/* إصلاح ألوان أيقونات الترتيب */\n.sort-icon,\n.table-sort-icon,\n.sortable-icon {\n  color: var(--navy-600) !important;\n}\n\n.sort-icon.active,\n.table-sort-icon.active,\n.sortable-icon.active {\n  color: var(--navy-800) !important;\n}\n\n/* إصلاح ألوان الروابط في الجداول */\ntable a,\n.table a,\ntd a,\nth a {\n  color: var(--navy-700) !important;\n  text-decoration: underline;\n}\n\ntable a:hover,\n.table a:hover,\ntd a:hover,\nth a:hover {\n  color: var(--navy-900) !important;\n}\n\n/* إصلاح ألوان الأزرار في الجداول */\ntable button,\n.table button,\ntd button,\nth button {\n  color: inherit !important;\n}\n\n/* إصلاح ألوان النصوص المساعدة في الجداول */\n.table-caption,\n.table-description,\n.table-helper-text {\n  color: var(--navy-600) !important;\n}\n\n/* إصلاح ألوان الجداول في الوضع الداكن */\n@media (prefers-color-scheme: dark) {\n  table,\n  .table,\n  [role=\"table\"],\n  th,\n  td,\n  tr {\n    color: var(--navy-800) !important;\n  }\n  \n  thead th {\n    color: var(--navy-700) !important;\n  }\n}\n\n/* إصلاح ألوان الجداول مع فئات Bootstrap */\n.table-primary,\n.table-secondary,\n.table-success,\n.table-info,\n.table-warning,\n.table-danger {\n  color: var(--navy-800) !important;\n}\n\n.table-primary th,\n.table-secondary th,\n.table-success th,\n.table-info th,\n.table-warning th,\n.table-danger th {\n  color: var(--navy-700) !important;\n}\n\n/* إصلاح ألوان الجداول مع فئات Material UI */\n.MuiTable-root,\n.MuiTableHead-root,\n.MuiTableBody-root,\n.MuiTableRow-root,\n.MuiTableCell-root {\n  color: var(--navy-800) !important;\n}\n\n.MuiTableHead-root .MuiTableCell-root {\n  color: var(--navy-700) !important;\n  font-weight: 600 !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;;;;;;;;;AAcA;;;;AAOA;;;;;AAUA;;;;AAsBA;;;;;AAMA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAKA;;;;AASA;;;;AAMA;;;;AASA;;;;AAMA;;;;AAOA;;;;AAMA;;;;AAKA;;;;;AAOA;;;;AAKA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAQA;;;;AASA;;;;AAKA;;;;AAMA;;;;AAMA;;;;;AASA;;;;;AASA;;;;AAMA;;;;AAOA;;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAOA;EACE;;;;EASA;;;;;AAMF;;;;AASA;;;;AAUA;;;;AAQA"}}]}