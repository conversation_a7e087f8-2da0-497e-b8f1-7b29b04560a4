{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "sEyjV_7UlcEr37M76FI7a", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WsUnalvfLC8q6thQbM7XpflHInutTa69tutWo9d2/Nc=", "__NEXT_PREVIEW_MODE_ID": "5c571a911255dbd80bfda3c7eaa7387c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cb163cf50e9d8fb50ed389b5e4bae31bccbac1391ddf408846ef3bd7bd5337cf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "14260dfaf9720454ed64eaebcaccec8b69fe5c122f329712e123705e7ebfae87"}}}, "functions": {}, "sortedMiddleware": ["/"]}