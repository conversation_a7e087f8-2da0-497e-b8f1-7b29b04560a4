{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WsUnalvfLC8q6thQbM7XpflHInutTa69tutWo9d2/Nc=", "__NEXT_PREVIEW_MODE_ID": "9557a760e172c81e649df15cb939fd02", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "01f07b2cb66e3ab2385cd60c51d0e13c7b3c3d8b0e798e000a9487eec5c254f4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a657f04d56823885b412a44641a17a5ea9fc2eac537294d5f8692813eb18f053"}}}, "sortedMiddleware": ["/"], "functions": {}}