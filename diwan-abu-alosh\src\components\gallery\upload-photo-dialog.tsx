'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Upload, X, Image as ImageIcon, Folder, Calendar } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface UploadPhotoDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
  defaultFolderId?: string
  defaultActivityId?: string
}

interface Activity {
  id: string
  title: string
}

interface GalleryFolder {
  id: string
  title: string
}

export default function UploadPhotoDialog({
  open,
  onOpenChange,
  onSuccess,
  defaultFolderId,
  defaultActivityId,
}: UploadPhotoDialogProps) {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [activityId, setActivityId] = useState('none')
  const [folderId, setFolderId] = useState('none')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activities, setActivities] = useState<Activity[]>([])
  const [folders, setFolders] = useState<GalleryFolder[]>([])
  const [loadingActivities, setLoadingActivities] = useState(false)
  const [loadingFolders, setLoadingFolders] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // تعيين القيم الافتراضية
  useEffect(() => {
    if (open) {
      if (defaultFolderId) {
        setFolderId(defaultFolderId)
        setActivityId('none')
      } else if (defaultActivityId) {
        setActivityId(defaultActivityId)
        setFolderId('none')
      }
    }
  }, [open, defaultFolderId, defaultActivityId])

  // جلب الأنشطة والمجلدات من API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoadingActivities(true)
        setLoadingFolders(true)

        // جلب الأنشطة
        const activitiesResponse = await fetch('/api/activities')
        if (activitiesResponse.ok) {
          const activitiesData = await activitiesResponse.json()
          setActivities(activitiesData.activities || [])
        }

        // جلب المجلدات
        const foldersResponse = await fetch('/api/gallery-folders')
        if (foldersResponse.ok) {
          const foldersData = await foldersResponse.json()
          setFolders(foldersData.folders || [])
        }
      } catch (error) {
        console.error('خطأ في جلب البيانات:', error)
      } finally {
        setLoadingActivities(false)
        setLoadingFolders(false)
      }
    }

    if (open) {
      fetchData()
    }
  }, [open])

  const handleFileSelect = (file: File) => {
    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      setError('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, WebP)')
      return
    }

    // التحقق من حجم الملف (5MB كحد أقصى)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      setError('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت')
      return
    }

    setSelectedFile(file)
    setError(null)

    // إنشاء معاينة للصورة
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  const removeFile = () => {
    setSelectedFile(null)
    setPreviewUrl(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedFile) {
      setError('يرجى اختيار صورة')
      return
    }

    if (!title.trim()) {
      setError('يرجى إدخال عنوان للصورة')
      return
    }

    setUploading(true)
    setError(null)

    try {
      // رفع الصورة أولاً
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('type', 'gallery')

      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json()
        throw new Error(errorData.error || 'فشل في رفع الصورة')
      }

      const uploadData = await uploadResponse.json()

      // إضافة الصورة إلى معرض الصور
      const galleryResponse = await fetch('/api/gallery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: title.trim(),
          description: description.trim() || undefined,
          imagePath: uploadData.filePath,
          activityId: activityId && activityId !== 'none' ? activityId : undefined,
          folderId: folderId && folderId !== 'none' ? folderId : undefined,
        }),
      })

      if (!galleryResponse.ok) {
        const errorData = await galleryResponse.json()
        throw new Error(errorData.error || 'فشل في إضافة الصورة إلى المعرض')
      }

      // إعادة تعيين النموذج
      setTitle('')
      setDescription('')
      setActivityId('none')
      setFolderId('none')
      setSelectedFile(null)
      setPreviewUrl(null)
      
      onSuccess()
      onOpenChange(false)
    } catch (error: any) {
      console.error('خطأ في رفع الصورة:', error)
      setError(error.message || 'حدث خطأ في رفع الصورة')
    } finally {
      setUploading(false)
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      enableScrollInteraction={true}
      maxHeight="95vh"
    >
      <DialogContent className="max-w-[50vw] bg-gradient-to-br from-white to-gray-50">
        <DialogHeader className="pb-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="bg-diwan-100 rounded-full p-3">
              <Upload className="w-6 h-6 text-diwan-600" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold text-diwan-700">رفع صورة جديدة</DialogTitle>
              <p className="text-sm text-gray-600 mt-1">أضف صورة جديدة إلى معرض الصور مع إمكانية ربطها بمجلد أو نشاط</p>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-8 pt-6">
          {/* منطقة رفع الصورة المحسنة */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center gap-2 mb-4">
              <ImageIcon className="w-5 h-5 text-diwan-600" />
              <Label className="text-lg font-semibold text-gray-800">اختيار الصورة *</Label>
            </div>

            {!selectedFile ? (
              <div
                className="drop-zone border-2 border-dashed border-diwan-300 rounded-2xl p-12 text-center cursor-pointer hover:border-diwan-500 hover:bg-gradient-to-br hover:from-diwan-50 hover:to-blue-50 transition-all duration-300 bg-gradient-to-br from-gray-50 via-white to-gray-50 group"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={openFileDialog}
              >
                <div className="bg-gradient-to-br from-diwan-100 to-diwan-200 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <ImageIcon className="w-10 h-10 text-diwan-700" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">اختر صورة للرفع</h3>
                <p className="text-gray-600 mb-6 text-lg">اسحب الصورة هنا أو انقر للاختيار من جهازك</p>

                <div className="bg-gray-50 rounded-xl p-4 mb-4">
                  <div className="flex items-center justify-center gap-3 text-sm text-gray-600 mb-3">
                    <span className="font-medium">الأنواع المدعومة:</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Badge variant="outline" className="text-sm font-medium bg-green-50 text-green-700 border-green-200">JPG</Badge>
                    <Badge variant="outline" className="text-sm font-medium bg-blue-50 text-blue-700 border-blue-200">PNG</Badge>
                    <Badge variant="outline" className="text-sm font-medium bg-purple-50 text-purple-700 border-purple-200">WebP</Badge>
                  </div>
                </div>

                <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <span>حد أقصى: 5 ميجابايت</span>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="relative group">
                  <div className="relative w-full h-80 rounded-2xl overflow-hidden border-2 border-diwan-200 bg-gray-100 shadow-lg">
                    <img
                      src={previewUrl!}
                      alt="معاينة الصورة"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-6">
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={removeFile}
                        className="bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-3 h-12 shadow-xl hover:shadow-2xl transition-all duration-200 rounded-xl"
                        disabled={uploading}
                      >
                        <X className="w-5 h-5 ml-2" />
                        إزالة الصورة
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="bg-green-100 rounded-full p-2">
                        <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-sm font-semibold text-gray-900">{selectedFile.name}</p>
                        <p className="text-xs text-gray-600">
                          {(selectedFile.size / (1024 * 1024)).toFixed(2)} ميجابايت • تم اختيار الصورة بنجاح
                        </p>
                      </div>
                    </div>
                    <Badge className="bg-green-100 text-green-800 border-green-200 font-medium">
                      جاهز للرفع
                    </Badge>
                  </div>
                </div>
              </div>
            )}

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="hidden"
              disabled={uploading}
            />
          </div>

          {/* معلومات الصورة */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center gap-2 mb-6">
              <svg className="w-5 h-5 text-diwan-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clipRule="evenodd" />
              </svg>
              <Label className="text-lg font-semibold text-gray-800">معلومات الصورة</Label>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* عنوان الصورة */}
              <div className="space-y-3">
                <Label htmlFor="title" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                  العنوان
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="أدخل عنوان واضح ومميز للصورة"
                  disabled={uploading}
                  required
                  className="h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base"
                />
              </div>

              {/* ربط بمجلد */}
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <Folder className="w-4 h-4 text-purple-600" />
                  ربط بمجلد (اختياري)
                </Label>
                <Select value={folderId} onValueChange={(value) => {
                  setFolderId(value)
                  if (value !== 'none') setActivityId('none') // إلغاء اختيار النشاط
                }} disabled={uploading || loadingFolders}>
                  <SelectTrigger className="h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base">
                    <SelectValue placeholder={loadingFolders ? "جاري التحميل..." : "اختر مجلد لتنظيم الصورة"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      <div className="flex items-center">
                        <ImageIcon className="w-4 h-4 ml-2 text-gray-400" />
                        بدون ربط (صور عامة)
                      </div>
                    </SelectItem>
                    {folders.map((folder) => (
                      <SelectItem key={folder.id} value={folder.id}>
                        <div className="flex items-center">
                          <Folder className="w-4 h-4 ml-2 text-purple-600" />
                          {folder.title}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* ربط بنشاط */}
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-green-600" />
                  ربط بنشاط (اختياري)
                </Label>
                <Select value={activityId} onValueChange={(value) => {
                  setActivityId(value)
                  if (value !== 'none') setFolderId('none') // إلغاء اختيار المجلد
                }} disabled={uploading || loadingActivities || folderId !== 'none'}>
                  <SelectTrigger className="h-12 border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 text-base">
                    <SelectValue placeholder={loadingActivities ? "جاري التحميل..." : folderId !== 'none' ? "تم اختيار مجلد" : "اختر نشاط مرتبط"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      <div className="flex items-center">
                        <ImageIcon className="w-4 h-4 ml-2 text-gray-400" />
                        بدون ربط
                      </div>
                    </SelectItem>
                    {activities.map((activity) => (
                      <SelectItem key={activity.id} value={activity.id}>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 ml-2 text-green-600" />
                          {activity.title}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* وصف الصورة */}
              <div className="space-y-3">
                <Label htmlFor="description" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clipRule="evenodd" />
                  </svg>
                  الوصف (اختياري)
                </Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="أضف وصفاً تفصيلياً للصورة، المناسبة، أو الحدث المصور..."
                  disabled={uploading}
                  rows={4}
                  className="border-gray-200 focus:border-diwan-500 focus:ring-diwan-500 resize-none text-base"
                />
              </div>
            </div>
          </div>

          {/* رسالة الخطأ */}
          {error && (
            <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-5 shadow-sm">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 bg-red-100 rounded-full p-2">
                  <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-red-800 mb-1">حدث خطأ</h4>
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="gap-4 pt-8 border-t border-gray-100">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={uploading}
              className="flex-1 h-14 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-semibold text-base rounded-xl transition-all duration-200"
            >
              <X className="w-5 h-5 ml-2" />
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={uploading || !selectedFile || !title.trim()}
              className="flex-1 h-14 bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 text-white font-bold text-base shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl border-0 transform hover:scale-105 active:scale-95"
            >
              {uploading ? (
                <>
                  <div className="w-6 h-6 ml-2 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span className="font-bold">جاري الرفع...</span>
                </>
              ) : (
                <>
                  <div className="bg-white/20 rounded-full p-1 ml-2">
                    <Upload className="w-5 h-5 text-white" />
                  </div>
                  <span className="font-bold">رفع الصورة</span>
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
