/* [project]/src/styles/dropdown-fixes.css [app-client] (css) */
[data-radix-select-content] {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

[data-radix-select-item] {
  color: #000 !important;
  background-color: #0000 !important;
}

[data-radix-select-item]:hover, [data-radix-select-item][data-highlighted], [data-radix-select-item][data-state="checked"] {
  color: #1e40af !important;
  background-color: #dbeafe !important;
}

[data-radix-select-trigger] {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

[data-radix-select-trigger]:hover {
  border-color: #9ca3af !important;
}

[data-radix-select-trigger]:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px #3b82f633 !important;
}

[data-radix-select-trigger] [data-radix-select-icon] {
  color: #6b7280 !important;
}

[data-radix-dropdown-menu-content] {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

[data-radix-dropdown-menu-item] {
  color: #000 !important;
  background-color: #0000 !important;
}

[data-radix-dropdown-menu-item]:hover, [data-radix-dropdown-menu-item][data-highlighted] {
  color: #1e40af !important;
  background-color: #dbeafe !important;
}

[data-radix-dropdown-menu-label] {
  color: #374151 !important;
}

[role="combobox"], [role="listbox"] {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

[role="option"] {
  color: #000 !important;
  background-color: #0000 !important;
}

[role="option"]:hover, [role="option"][aria-selected="true"] {
  color: #1e40af !important;
  background-color: #dbeafe !important;
}

.enhanced-select-content {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

.enhanced-select-item {
  color: #000 !important;
  background-color: #0000 !important;
}

.enhanced-select-item:hover, .enhanced-select-item[data-selected="true"] {
  color: #1e40af !important;
  background-color: #dbeafe !important;
}

select {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

select:hover {
  border-color: #9ca3af !important;
}

select:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px #3b82f633 !important;
}

select option {
  color: #000 !important;
  background-color: #fff !important;
}

select option:hover, select option:checked {
  color: #1e40af !important;
  background-color: #dbeafe !important;
}

[role="textbox"][aria-autocomplete], [data-radix-popover-content] {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

[cmdk-root] {
  color: #000 !important;
  background-color: #fff !important;
}

[cmdk-input] {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

[cmdk-item] {
  color: #000 !important;
  background-color: #0000 !important;
}

[cmdk-item]:hover, [cmdk-item][data-selected="true"] {
  color: #1e40af !important;
  background-color: #dbeafe !important;
}

.dropdown-content, .select-content, .combobox-content, .popover-content {
  color: #000 !important;
  background-color: #fff !important;
  border-color: #d1d5db !important;
}

.dropdown-item, .select-item, .combobox-item, .popover-item {
  color: #000 !important;
  background-color: #0000 !important;
}

.dropdown-item:hover, .select-item:hover, .combobox-item:hover, .popover-item:hover, .dropdown-item[data-highlighted], .select-item[data-highlighted], .combobox-item[data-highlighted], .popover-item[data-highlighted] {
  color: #1e40af !important;
  background-color: #dbeafe !important;
}

.dropdown-label, .select-label, .combobox-label {
  color: #374151 !important;
}

.dropdown-placeholder, .select-placeholder, .combobox-placeholder, .dropdown-icon, .select-icon, .combobox-icon {
  color: #6b7280 !important;
}

.dropdown-trigger:focus, .select-trigger:focus, .combobox-trigger:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px #3b82f633 !important;
}

.dropdown-trigger:disabled, .select-trigger:disabled, .combobox-trigger:disabled {
  color: #9ca3af !important;
  background-color: #f9fafb !important;
  border-color: #e5e7eb !important;
}

.dropdown-trigger[data-error="true"], .select-trigger[data-error="true"], .combobox-trigger[data-error="true"] {
  color: #dc2626 !important;
  border-color: #ef4444 !important;
}

.dropdown-trigger[data-error="true"]:focus, .select-trigger[data-error="true"]:focus, .combobox-trigger[data-error="true"]:focus {
  box-shadow: 0 0 0 4px #ef444433 !important;
}

.dropdown-content::-webkit-scrollbar, .select-content::-webkit-scrollbar, .combobox-content::-webkit-scrollbar {
  width: 8px;
}

.dropdown-content::-webkit-scrollbar-track, .select-content::-webkit-scrollbar-track, .combobox-content::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 4px;
}

.dropdown-content::-webkit-scrollbar-thumb, .select-content::-webkit-scrollbar-thumb, .combobox-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover, .select-content::-webkit-scrollbar-thumb:hover, .combobox-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

@media (prefers-color-scheme: dark) {
  [data-radix-select-content], [data-radix-dropdown-menu-content], [role="listbox"], .dropdown-content, .select-content, .combobox-content {
    color: #000 !important;
    background-color: #fff !important;
    border-color: #d1d5db !important;
  }
}

/*# sourceMappingURL=src_styles_dropdown-fixes_css_f9ee138c._.single.css.map*/