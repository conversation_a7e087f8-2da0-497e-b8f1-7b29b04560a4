{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'البريد الإلكتروني', type: 'email' },\n        password: { label: 'كلمة المرور', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAqB,MAAM;gBAAQ;gBACnD,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/api/members/%5Bid%5D/account-statement/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    if (!session) {\n      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })\n    }\n\n    const { id } = await params\n    const { searchParams } = new URL(request.url)\n    const startDate = searchParams.get('startDate')\n    const endDate = searchParams.get('endDate')\n    const year = searchParams.get('year')\n\n    // التحقق من وجود العضو\n    const member = await prisma.member.findUnique({\n      where: { id },\n      select: {\n        id: true,\n        name: true,\n        phone: true,\n        email: true,\n        address: true,\n        photo: true,\n        status: true,\n        createdAt: true,\n      },\n    })\n\n    if (!member) {\n      return NextResponse.json({ error: 'العضو غير موجود' }, { status: 404 })\n    }\n\n    // تحديد فترة البحث\n    let dateFilter: Record<string, unknown> = {}\n    if (startDate && endDate) {\n      dateFilter = {\n        date: {\n          gte: new Date(startDate),\n          lte: new Date(endDate),\n        },\n      }\n    } else if (year) {\n      const yearNum = parseInt(year)\n      dateFilter = {\n        date: {\n          gte: new Date(yearNum, 0, 1),\n          lte: new Date(yearNum, 11, 31),\n        },\n      }\n    }\n\n    // جلب جميع الإيرادات للعضو\n    const incomes = await prisma.income.findMany({\n      where: {\n        memberId: id,\n        ...dateFilter,\n      },\n      orderBy: { date: 'desc' },\n      include: {\n        createdBy: {\n          select: {\n            name: true,\n          },\n        },\n      },\n    })\n\n    // حساب الإحصائيات\n    const totalAmount = incomes.reduce((sum, income) => sum + income.amount, 0)\n    const transactionCount = incomes.length\n\n    // تجميع البيانات حسب النوع\n    const byType = incomes.reduce((acc, income) => {\n      if (!acc[income.type]) {\n        acc[income.type] = { count: 0, amount: 0 }\n      }\n      acc[income.type].count++\n      acc[income.type].amount += income.amount\n      return acc\n    }, {} as Record<string, { count: number; amount: number }>)\n\n    // تجميع البيانات حسب الشهر (للسنة الحالية أو المحددة)\n    const currentYear = year ? parseInt(year) : new Date().getFullYear()\n    const monthlyData = Array.from({ length: 12 }, (_, index) => {\n      const month = index + 1\n      const monthIncomes = incomes.filter(income => {\n        const incomeDate = new Date(income.date)\n        return incomeDate.getFullYear() === currentYear && incomeDate.getMonth() === index\n      })\n      \n      return {\n        month,\n        monthName: getMonthName(month),\n        count: monthIncomes.length,\n        amount: monthIncomes.reduce((sum, income) => sum + income.amount, 0),\n      }\n    })\n\n    // آخر 5 معاملات\n    const recentTransactions = incomes.slice(0, 5)\n\n    // إحصائيات إضافية\n    const firstTransaction = incomes.length > 0 ? incomes[incomes.length - 1] : null\n    const lastTransaction = incomes.length > 0 ? incomes[0] : null\n    \n    // متوسط المساهمة الشهرية\n    const monthsWithTransactions = monthlyData.filter(month => month.count > 0).length\n    const averageMonthlyContribution = monthsWithTransactions > 0 ? totalAmount / monthsWithTransactions : 0\n\n    const accountStatement = {\n      member,\n      summary: {\n        totalAmount,\n        transactionCount,\n        averageMonthlyContribution,\n        firstTransactionDate: firstTransaction?.date || null,\n        lastTransactionDate: lastTransaction?.date || null,\n      },\n      byType,\n      monthlyData,\n      recentTransactions,\n      allTransactions: incomes,\n      period: {\n        startDate: startDate || `${currentYear}-01-01`,\n        endDate: endDate || `${currentYear}-12-31`,\n        year: currentYear,\n      },\n    }\n\n    // console.log('إرسال كشف الحساب للعضو:', id, 'البيانات:', {\n    //   memberName: member.name,\n    //   totalAmount,\n    //   transactionCount,\n    //   incomesLength: incomes.length\n    // })\n\n    return NextResponse.json(accountStatement)\n  } catch (error) {\n    console.error('خطأ في جلب كشف الحساب:', error)\n    return NextResponse.json(\n      { error: 'حدث خطأ في جلب كشف الحساب' },\n      { status: 500 }\n    )\n  }\n}\n\n// دالة مساعدة للحصول على اسم الشهر\nfunction getMonthName(month: number): string {\n  return `شهر${month}`\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,uBAAuB;QACvB,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YAC5C,OAAO;gBAAE;YAAG;YACZ,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;QACF;QAEA,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,mBAAmB;QACnB,IAAI,aAAsC,CAAC;QAC3C,IAAI,aAAa,SAAS;YACxB,aAAa;gBACX,MAAM;oBACJ,KAAK,IAAI,KAAK;oBACd,KAAK,IAAI,KAAK;gBAChB;YACF;QACF,OAAO,IAAI,MAAM;YACf,MAAM,UAAU,SAAS;YACzB,aAAa;gBACX,MAAM;oBACJ,KAAK,IAAI,KAAK,SAAS,GAAG;oBAC1B,KAAK,IAAI,KAAK,SAAS,IAAI;gBAC7B;YACF;QACF;QAEA,2BAA2B;QAC3B,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,OAAO;gBACL,UAAU;gBACV,GAAG,UAAU;YACf;YACA,SAAS;gBAAE,MAAM;YAAO;YACxB,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,MAAM;oBACR;gBACF;YACF;QACF;QAEA,kBAAkB;QAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;QACzE,MAAM,mBAAmB,QAAQ,MAAM;QAEvC,2BAA2B;QAC3B,MAAM,SAAS,QAAQ,MAAM,CAAC,CAAC,KAAK;YAClC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE;gBACrB,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG;oBAAE,OAAO;oBAAG,QAAQ;gBAAE;YAC3C;YACA,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,KAAK;YACtB,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,MAAM;YACxC,OAAO;QACT,GAAG,CAAC;QAEJ,sDAAsD;QACtD,MAAM,cAAc,OAAO,SAAS,QAAQ,IAAI,OAAO,WAAW;QAClE,MAAM,cAAc,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAG,GAAG,CAAC,GAAG;YACjD,MAAM,QAAQ,QAAQ;YACtB,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA;gBAClC,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;gBACvC,OAAO,WAAW,WAAW,OAAO,eAAe,WAAW,QAAQ,OAAO;YAC/E;YAEA,OAAO;gBACL;gBACA,WAAW,aAAa;gBACxB,OAAO,aAAa,MAAM;gBAC1B,QAAQ,aAAa,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;YACpE;QACF;QAEA,gBAAgB;QAChB,MAAM,qBAAqB,QAAQ,KAAK,CAAC,GAAG;QAE5C,kBAAkB;QAClB,MAAM,mBAAmB,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,GAAG;QAC5E,MAAM,kBAAkB,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,GAAG;QAE1D,yBAAyB;QACzB,MAAM,yBAAyB,YAAY,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG,GAAG,MAAM;QAClF,MAAM,6BAA6B,yBAAyB,IAAI,cAAc,yBAAyB;QAEvG,MAAM,mBAAmB;YACvB;YACA,SAAS;gBACP;gBACA;gBACA;gBACA,sBAAsB,kBAAkB,QAAQ;gBAChD,qBAAqB,iBAAiB,QAAQ;YAChD;YACA;YACA;YACA;YACA,iBAAiB;YACjB,QAAQ;gBACN,WAAW,aAAa,GAAG,YAAY,MAAM,CAAC;gBAC9C,SAAS,WAAW,GAAG,YAAY,MAAM,CAAC;gBAC1C,MAAM;YACR;QACF;QAEA,4DAA4D;QAC5D,6BAA6B;QAC7B,iBAAiB;QACjB,sBAAsB;QACtB,kCAAkC;QAClC,KAAK;QAEL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,mCAAmC;AACnC,SAAS,aAAa,KAAa;IACjC,OAAO,CAAC,GAAG,EAAE,OAAO;AACtB", "debugId": null}}]}