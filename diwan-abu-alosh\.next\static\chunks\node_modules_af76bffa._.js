(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/canvg/lib/index.es.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_63278475._.js",
  "static/chunks/node_modules_canvg_lib_index_es_c2faeb8d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/canvg/lib/index.es.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/html2canvas/dist/html2canvas.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/html2canvas/dist/html2canvas.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/dompurify/dist/purify.es.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_dompurify_dist_purify_es_mjs_39224456._.js",
  "static/chunks/node_modules_dompurify_dist_purify_es_mjs_c2faeb8d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/dompurify/dist/purify.es.mjs [app-client] (ecmascript)");
    });
});
}}),
}]);