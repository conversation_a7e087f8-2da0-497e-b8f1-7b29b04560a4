/* تحسينات CSS للشاشة المنبثقة التفاعلية */

/* تأثيرات الانتقال المحسنة */
.dialog-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dialog-content {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, filter;
}

/* تأثيرات الظل المتقدمة */
.dialog-shadow-enhanced {
  filter: drop-shadow(0 20px 25px rgb(0 0 0 / 0.1)) drop-shadow(0 8px 10px rgb(0 0 0 / 0.04));
}

.dialog-shadow-hover {
  filter: drop-shadow(0 25px 25px rgb(0 0 0 / 0.15)) drop-shadow(0 10px 10px rgb(0 0 0 / 0.04));
}

/* تأثيرات التمرير السلس المحسنة */
.smooth-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) rgba(243, 244, 246, 0.5);
  overflow-y: auto;
  overflow-x: hidden;
}

.smooth-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.smooth-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.5);
  border-radius: 4px;
  margin: 4px;
}

.smooth-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.6), rgba(37, 99, 235, 0.8));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.smooth-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 1));
  transform: scale(1.1);
}

.smooth-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, rgba(37, 99, 235, 0.9), rgba(29, 78, 216, 1));
}

/* تحسين التمرير على الأجهزة اللمسية */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* تأثيرات التمرير التفاعلية */
.dialog-scroll-indicator {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.smooth-scroll:hover .dialog-scroll-indicator {
  opacity: 1;
}

.dialog-scroll-thumb {
  position: absolute;
  right: 0;
  width: 4px;
  background: rgba(59, 130, 246, 0.6);
  border-radius: 2px;
  transition: all 0.2s ease;
}

/* تأثيرات الخلفية المحسنة */
.dialog-backdrop {
  backdrop-filter: blur(8px) saturate(180%);
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-out;
}

.dialog-backdrop.enhanced {
  backdrop-filter: blur(12px) saturate(200%);
  background: rgba(0, 0, 0, 0.7);
}

/* تأثيرات الحركة المتقدمة */
@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
}

@keyframes dialogSlideOut {
  from {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
  to {
    opacity: 0;
    transform: translate3d(0, -20px, 0) scale(0.95);
  }
}

.dialog-animate-in {
  animation: dialogSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.dialog-animate-out {
  animation: dialogSlideOut 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* تأثيرات التفاعل مع الماوس */
.dialog-mouse-follow {
  cursor: move;
  user-select: none;
}

.dialog-mouse-follow:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease-out;
}

/* تأثيرات الحدود المحسنة */
.dialog-border-glow {
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 
    0 0 0 1px rgba(59, 130, 246, 0.1),
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dialog-border-glow:hover {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 
    0 0 0 1px rgba(59, 130, 246, 0.2),
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* تأثيرات الشفافية المتقدمة */
.dialog-glass-effect {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تأثيرات الإضاءة */
.dialog-highlight {
  position: relative;
  overflow: hidden;
}

.dialog-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease-in-out;
}

.dialog-highlight:hover::before {
  left: 100%;
}

/* تأثيرات الاستجابة للشاشات المختلفة */
@media (max-width: 768px) {
  .dialog-content {
    max-width: 95vw !important;
    margin: 10px;
  }
  
  .dialog-mouse-follow {
    cursor: default;
  }
  
  .dialog-mouse-follow:hover {
    transform: none;
  }
}

@media (max-width: 480px) {
  .dialog-content {
    max-width: 100vw !important;
    margin: 0;
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }
}

/* تأثيرات الأداء المحسن */
.dialog-performance-optimized {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* تأثيرات التركيز المحسنة */
.dialog-focus-trap {
  outline: none;
}

.dialog-focus-trap:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.6);
  outline-offset: 2px;
}

/* تأثيرات الحالة المختلفة */
.dialog-loading {
  pointer-events: none;
  opacity: 0.7;
}

.dialog-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-top-color: rgba(59, 130, 246, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* تأثيرات الألوان المتدرجة */
.dialog-gradient-border {
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4) border-box;
  border: 2px solid transparent;
}

/* تأثيرات النبضة */
.dialog-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* تأثيرات الاهتزاز الخفيف */
.dialog-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* تأثيرات التكبير التدريجي */
.dialog-zoom-in {
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* تأثيرات الانزلاق من الأعلى */
.dialog-slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات الطباعة */
@media print {
  .dialog-container {
    position: static !important;
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
  
  .dialog-backdrop {
    display: none !important;
  }
}
