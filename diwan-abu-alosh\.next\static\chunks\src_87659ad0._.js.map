{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-white\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-white hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-white hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-white hover:bg-destructive/80\",\n        outline: \"text-white border-white\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-amber-500 text-white hover:bg-amber-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qLACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/dashboard/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'\nimport {\n  Download,\n  FileText,\n  TrendingUp,\n  TrendingDown,\n  Users,\n  DollarSign,\n  Activity,\n  Calendar,\n  BarChart3,\n  PieChart,\n  Target,\n  Award,\n  RefreshCw\n} from 'lucide-react'\nimport { formatCurrency, formatDate } from '@/lib/utils'\n\ninterface ReportsData {\n  summary: {\n    totalMembers: number\n    activeMembers: number\n    totalIncomes: number\n    totalExpenses: number\n    balance: number\n    totalActivities: number\n  }\n  topContributors: Array<{\n    id: string\n    name: string\n    totalContributions: number\n    incomes: Array<{ amount: number }>\n  }>\n  recentTransactions: Array<{\n    type: string\n    id: string\n    amount: number\n    date: string\n    description: string\n    memberName?: string\n  }>\n  monthlyData: Array<{\n    month: string\n    incomes: number\n    expenses: number\n    balance: number\n  }>\n}\n\nexport default function ReportsPage() {\n  const { data: session } = useSession()\n  const [data, setData] = useState<ReportsData | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [selectedPeriod, setSelectedPeriod] = useState('current-year')\n  const [refreshing, setRefreshing] = useState(false)\n\n  // جلب بيانات التقارير\n  const fetchReportsData = async (showRefreshing = false) => {\n    try {\n      if (showRefreshing) {\n        setRefreshing(true)\n      } else {\n        setLoading(true)\n      }\n\n      const response = await fetch(`/api/reports?period=${selectedPeriod}`)\n      if (!response.ok) throw new Error('فشل في جلب بيانات التقارير')\n\n      const reportsData = await response.json()\n      setData(reportsData)\n    } catch (error) {\n      console.error('خطأ في جلب بيانات التقارير:', error)\n      alert('حدث خطأ في جلب بيانات التقارير')\n    } finally {\n      setLoading(false)\n      setRefreshing(false)\n    }\n  }\n\n  // تحديث البيانات\n  const handleRefresh = () => {\n    fetchReportsData(true)\n  }\n\n  useEffect(() => {\n    if (session) {\n      fetchReportsData()\n    }\n  }, [selectedPeriod, session])\n\n  // تصدير التقرير الشامل بصيغة PDF\n  const handleExportPDF = async () => {\n    if (!data) return\n\n    try {\n      // إنشاء محتوى HTML للطباعة\n      const periodNames: { [key: string]: string } = {\n        'current-month': 'الشهر الحالي',\n        'last-month': 'الشهر الماضي',\n        'current-year': 'السنة الحالية',\n        'last-year': 'السنة الماضية',\n        'all-time': 'جميع الفترات'\n      }\n\n      const currentDate = new Date().toLocaleDateString('ar-JO', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      })\n\n      const htmlContent = `\n<!DOCTYPE html>\n<html dir=\"rtl\" lang=\"ar\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>تقرير شامل - ديوان أبو علوش</title>\n    <style>\n        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');\n\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n\n        body {\n            font-family: 'Cairo', Arial, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            background: white;\n            padding: 20px;\n            direction: rtl;\n        }\n\n        .header {\n            text-align: center;\n            margin-bottom: 30px;\n            border-bottom: 3px solid #2563eb;\n            padding-bottom: 20px;\n        }\n\n        .header h1 {\n            color: #2563eb;\n            font-size: 28px;\n            font-weight: 700;\n            margin-bottom: 10px;\n        }\n\n        .header .subtitle {\n            color: #666;\n            font-size: 16px;\n            margin-bottom: 5px;\n        }\n\n        .summary-grid {\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 20px;\n            margin-bottom: 30px;\n        }\n\n        .summary-card {\n            background: #f8fafc;\n            border: 1px solid #e2e8f0;\n            border-radius: 8px;\n            padding: 15px;\n            text-align: center;\n        }\n\n        .summary-card h3 {\n            color: #475569;\n            font-size: 14px;\n            margin-bottom: 8px;\n            font-weight: 600;\n        }\n\n        .summary-card .value {\n            font-size: 20px;\n            font-weight: 700;\n            color: #1e293b;\n        }\n\n        .section {\n            margin-bottom: 30px;\n            page-break-inside: avoid;\n        }\n\n        .section h2 {\n            color: #2563eb;\n            font-size: 18px;\n            font-weight: 600;\n            margin-bottom: 15px;\n            border-bottom: 2px solid #e2e8f0;\n            padding-bottom: 5px;\n        }\n\n        .table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n        }\n\n        .table th,\n        .table td {\n            padding: 8px 12px;\n            text-align: right;\n            border-bottom: 1px solid #e2e8f0;\n            font-size: 12px;\n        }\n\n        .table th {\n            background: #f1f5f9;\n            font-weight: 600;\n            color: #475569;\n        }\n\n        .income {\n            color: #059669;\n            font-weight: 600;\n        }\n\n        .expense {\n            color: #dc2626;\n            font-weight: 600;\n        }\n\n        @media print {\n            body { padding: 0; }\n            .section { page-break-inside: avoid; }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <h1>تقرير شامل - ديوان أبو علوش</h1>\n        <div class=\"subtitle\">فترة التقرير: ${periodNames[selectedPeriod] || selectedPeriod}</div>\n        <div class=\"subtitle\">تاريخ الإنشاء: ${currentDate}</div>\n    </div>\n\n    <div class=\"summary-grid\">\n        <div class=\"summary-card\">\n            <h3>إجمالي الأعضاء</h3>\n            <div class=\"value\">${data.summary.totalMembers}</div>\n        </div>\n        <div class=\"summary-card\">\n            <h3>الأعضاء النشطون</h3>\n            <div class=\"value\">${data.summary.activeMembers}</div>\n        </div>\n        <div class=\"summary-card\">\n            <h3>إجمالي الإيرادات</h3>\n            <div class=\"value income\">${formatCurrency(data.summary.totalIncomes)}</div>\n        </div>\n        <div class=\"summary-card\">\n            <h3>إجمالي المصروفات</h3>\n            <div class=\"value expense\">${formatCurrency(data.summary.totalExpenses)}</div>\n        </div>\n        <div class=\"summary-card\">\n            <h3>الرصيد الحالي</h3>\n            <div class=\"value ${data.summary.balance >= 0 ? 'income' : 'expense'}\">${formatCurrency(data.summary.balance)}</div>\n        </div>\n        <div class=\"summary-card\">\n            <h3>إجمالي الأنشطة</h3>\n            <div class=\"value\">${data.summary.totalActivities}</div>\n        </div>\n    </div>\n\n    <div class=\"section\">\n        <h2>أكثر الأعضاء مساهمة</h2>\n        <table class=\"table\">\n            <thead>\n                <tr>\n                    <th>الترتيب</th>\n                    <th>اسم العضو</th>\n                    <th>عدد المساهمات</th>\n                    <th>إجمالي المساهمات</th>\n                </tr>\n            </thead>\n            <tbody>\n                ${data.topContributors.slice(0, 10).map((member, index) => `\n                    <tr>\n                        <td>${index + 1}</td>\n                        <td>${member.name}</td>\n                        <td>${member.incomes.length}</td>\n                        <td class=\"income\">${formatCurrency(member.totalContributions)}</td>\n                    </tr>\n                `).join('')}\n            </tbody>\n        </table>\n    </div>\n\n    <div class=\"section\">\n        <h2>آخر المعاملات</h2>\n        <table class=\"table\">\n            <thead>\n                <tr>\n                    <th>النوع</th>\n                    <th>الوصف</th>\n                    <th>المبلغ</th>\n                    <th>التاريخ</th>\n                    <th>العضو/المستفيد</th>\n                </tr>\n            </thead>\n            <tbody>\n                ${data.recentTransactions.slice(0, 15).map((transaction) => `\n                    <tr>\n                        <td>${transaction.type === 'income' ? 'إيراد' : 'مصروف'}</td>\n                        <td>${transaction.description}</td>\n                        <td class=\"${transaction.type === 'income' ? 'income' : 'expense'}\">\n                            ${formatCurrency(transaction.amount)}\n                        </td>\n                        <td>${formatDate(transaction.date)}</td>\n                        <td>${transaction.memberName || 'غير محدد'}</td>\n                    </tr>\n                `).join('')}\n            </tbody>\n        </table>\n    </div>\n</body>\n</html>`\n\n      // إنشاء نافذة جديدة للطباعة\n      const printWindow = window.open('', '_blank')\n      if (printWindow) {\n        printWindow.document.write(htmlContent)\n        printWindow.document.close()\n\n        // انتظار تحميل الخطوط ثم الطباعة\n        setTimeout(() => {\n          printWindow.print()\n        }, 1000)\n      } else {\n        alert('يرجى السماح بفتح النوافذ المنبثقة لتصدير التقرير')\n      }\n\n    } catch (error) {\n      console.error('خطأ في تصدير التقرير:', error)\n      alert('حدث خطأ في تصدير التقرير')\n    }\n  }\n\n  // تصدير PDF للتحميل المباشر\n  const handleExportPDFDownload = async () => {\n    if (!data) return\n\n    try {\n      // استيراد مكتبة html2pdf\n      const html2pdf = (await import('html2pdf.js')).default as { set: (opt: unknown) => { from: (element: HTMLElement) => { save: () => Promise<void> } } }\n\n      const periodNames: { [key: string]: string } = {\n        'current-month': 'الشهر الحالي',\n        'last-month': 'الشهر الماضي',\n        'current-year': 'السنة الحالية',\n        'last-year': 'السنة الماضية',\n        'all-time': 'جميع الفترات'\n      }\n\n      const currentDate = new Date().toLocaleDateString('ar-JO', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      })\n\n      // إنشاء عنصر HTML مؤقت\n      const element = document.createElement('div')\n      element.innerHTML = `\n        <div style=\"font-family: 'Cairo', Arial, sans-serif; direction: rtl; padding: 20px; color: #333;\">\n          <div style=\"text-align: center; margin-bottom: 30px; border-bottom: 3px solid #2563eb; padding-bottom: 20px;\">\n            <h1 style=\"color: #2563eb; font-size: 24px; font-weight: 700; margin-bottom: 10px;\">\n              تقرير شامل - ديوان أبو علوش\n            </h1>\n            <div style=\"color: #666; font-size: 14px; margin-bottom: 5px;\">\n              فترة التقرير: ${periodNames[selectedPeriod] || selectedPeriod}\n            </div>\n            <div style=\"color: #666; font-size: 14px;\">\n              تاريخ الإنشاء: ${currentDate}\n            </div>\n          </div>\n\n          <div style=\"display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 30px;\">\n            <div style=\"background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;\">\n              <h3 style=\"color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;\">إجمالي الأعضاء</h3>\n              <div style=\"font-size: 18px; font-weight: 700; color: #1e293b;\">${data.summary.totalMembers}</div>\n            </div>\n            <div style=\"background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;\">\n              <h3 style=\"color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;\">الأعضاء النشطون</h3>\n              <div style=\"font-size: 18px; font-weight: 700; color: #1e293b;\">${data.summary.activeMembers}</div>\n            </div>\n            <div style=\"background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;\">\n              <h3 style=\"color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;\">إجمالي الإيرادات</h3>\n              <div style=\"font-size: 18px; font-weight: 700; color: #059669;\">${formatCurrency(data.summary.totalIncomes)}</div>\n            </div>\n            <div style=\"background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;\">\n              <h3 style=\"color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;\">إجمالي المصروفات</h3>\n              <div style=\"font-size: 18px; font-weight: 700; color: #dc2626;\">${formatCurrency(data.summary.totalExpenses)}</div>\n            </div>\n            <div style=\"background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;\">\n              <h3 style=\"color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;\">الرصيد الحالي</h3>\n              <div style=\"font-size: 18px; font-weight: 700; color: ${data.summary.balance >= 0 ? '#059669' : '#dc2626'};\">${formatCurrency(data.summary.balance)}</div>\n            </div>\n            <div style=\"background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; text-align: center;\">\n              <h3 style=\"color: #475569; font-size: 12px; margin-bottom: 8px; font-weight: 600;\">إجمالي الأنشطة</h3>\n              <div style=\"font-size: 18px; font-weight: 700; color: #1e293b;\">${data.summary.totalActivities}</div>\n            </div>\n          </div>\n\n          <div style=\"margin-bottom: 30px;\">\n            <h2 style=\"color: #2563eb; font-size: 16px; font-weight: 600; margin-bottom: 15px; border-bottom: 2px solid #e2e8f0; padding-bottom: 5px;\">\n              أكثر الأعضاء مساهمة\n            </h2>\n            <table style=\"width: 100%; border-collapse: collapse;\">\n              <thead>\n                <tr style=\"background: #f1f5f9;\">\n                  <th style=\"padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;\">الترتيب</th>\n                  <th style=\"padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;\">اسم العضو</th>\n                  <th style=\"padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;\">عدد المساهمات</th>\n                  <th style=\"padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;\">إجمالي المساهمات</th>\n                </tr>\n              </thead>\n              <tbody>\n                ${data.topContributors.slice(0, 10).map((member, index) => `\n                  <tr>\n                    <td style=\"padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;\">${index + 1}</td>\n                    <td style=\"padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;\">${member.name}</td>\n                    <td style=\"padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;\">${member.incomes.length}</td>\n                    <td style=\"padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px; color: #059669; font-weight: 600;\">${formatCurrency(member.totalContributions)}</td>\n                  </tr>\n                `).join('')}\n              </tbody>\n            </table>\n          </div>\n\n          <div style=\"margin-bottom: 30px;\">\n            <h2 style=\"color: #2563eb; font-size: 16px; font-weight: 600; margin-bottom: 15px; border-bottom: 2px solid #e2e8f0; padding-bottom: 5px;\">\n              آخر المعاملات\n            </h2>\n            <table style=\"width: 100%; border-collapse: collapse;\">\n              <thead>\n                <tr style=\"background: #f1f5f9;\">\n                  <th style=\"padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;\">النوع</th>\n                  <th style=\"padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;\">الوصف</th>\n                  <th style=\"padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;\">المبلغ</th>\n                  <th style=\"padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;\">التاريخ</th>\n                  <th style=\"padding: 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 11px; font-weight: 600; color: #475569;\">العضو/المستفيد</th>\n                </tr>\n              </thead>\n              <tbody>\n                ${data.recentTransactions.slice(0, 15).map((transaction) => `\n                  <tr>\n                    <td style=\"padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;\">${transaction.type === 'income' ? 'إيراد' : 'مصروف'}</td>\n                    <td style=\"padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;\">${transaction.description}</td>\n                    <td style=\"padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px; color: ${transaction.type === 'income' ? '#059669' : '#dc2626'}; font-weight: 600;\">${formatCurrency(transaction.amount)}</td>\n                    <td style=\"padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;\">${formatDate(transaction.date)}</td>\n                    <td style=\"padding: 6px 8px; text-align: right; border-bottom: 1px solid #e2e8f0; font-size: 10px;\">${transaction.memberName || 'غير محدد'}</td>\n                  </tr>\n                `).join('')}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      `\n\n      // إعدادات PDF\n      const opt = {\n        margin: 1,\n        filename: `تقرير_شامل_${selectedPeriod}_${new Date().toISOString().split('T')[0]}.pdf`,\n        image: { type: 'jpeg', quality: 0.98 },\n        html2canvas: { scale: 2, useCORS: true },\n        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }\n      }\n\n      // تحويل إلى PDF وتحميل\n      await html2pdf().set(opt).from(element).save()\n\n    } catch (error) {\n      console.error('خطأ في تصدير التقرير:', error)\n      alert('حدث خطأ في تصدير التقرير')\n    }\n  }\n\n  // تصدير التقرير الشامل بصيغة CSV\n  const handleExportCSV = () => {\n    if (!data) return\n\n    const reportContent = [\n      ['نوع البيان', 'القيمة'],\n      ['إجمالي الأعضاء', data.summary.totalMembers.toString()],\n      ['الأعضاء النشطون', data.summary.activeMembers.toString()],\n      ['إجمالي الإيرادات', formatCurrency(data.summary.totalIncomes)],\n      ['إجمالي المصروفات', formatCurrency(data.summary.totalExpenses)],\n      ['الرصيد الحالي', formatCurrency(data.summary.balance)],\n      ['إجمالي الأنشطة', data.summary.totalActivities.toString()],\n      [],\n      ['أكثر الأعضاء مساهمة'],\n      ['الترتيب', 'الاسم', 'إجمالي المساهمات'],\n      ...data.topContributors.slice(0, 10).map((member, index) => [\n        (index + 1).toString(),\n        member.name,\n        formatCurrency(member.totalContributions)\n      ]),\n      [],\n      ['آخر المعاملات'],\n      ['النوع', 'الوصف', 'المبلغ', 'التاريخ', 'العضو/المستفيد'],\n      ...data.recentTransactions.slice(0, 20).map(transaction => [\n        transaction.type === 'income' ? 'إيراد' : 'مصروف',\n        transaction.description,\n        formatCurrency(transaction.amount),\n        formatDate(transaction.date),\n        transaction.memberName || 'غير محدد'\n      ])\n    ].map(row => Array.isArray(row) ? row.join(',') : row).join('\\n')\n\n    const blob = new Blob(['\\ufeff' + reportContent], { type: 'text/csv;charset=utf-8;' })\n    const link = document.createElement('a')\n    link.href = URL.createObjectURL(blob)\n    link.download = `تقرير_شامل_${selectedPeriod}_${new Date().toISOString().split('T')[0]}.csv`\n    link.click()\n  }\n\n  // تصدير البيانات الشهرية\n  const handleExportMonthlyData = () => {\n    if (!data || !data.monthlyData) return\n\n    const monthlyContent = [\n      ['الشهر', 'الإيرادات', 'المصروفات', 'الرصيد'],\n      ...data.monthlyData.map(month => [\n        new Date(month.month + '-01').toLocaleDateString('ar-JO', {\n          year: 'numeric',\n          month: 'long'\n        }),\n        formatCurrency(month.incomes),\n        formatCurrency(month.expenses),\n        formatCurrency(month.balance)\n      ])\n    ].map(row => row.join(',')).join('\\n')\n\n    const blob = new Blob(['\\ufeff' + monthlyContent], { type: 'text/csv;charset=utf-8;' })\n    const link = document.createElement('a')\n    link.href = URL.createObjectURL(blob)\n    link.download = `البيانات_الشهرية_${selectedPeriod}_${new Date().toISOString().split('T')[0]}.csv`\n    link.click()\n  }\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6 arabic-text\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            التقارير والإحصائيات\n          </h1>\n          <p className=\"text-gray-600\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            تقارير شاملة عن أنشطة الديوان المالية والإدارية\n          </p>\n        </div>\n        <div className=\"flex justify-center items-center h-64\">\n          <div className=\"text-gray-500\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            جاري تحميل التقارير...\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!data) {\n    return (\n      <div className=\"space-y-6 arabic-text\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            التقارير والإحصائيات\n          </h1>\n          <p className=\"text-gray-600\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            تقارير شاملة عن أنشطة الديوان المالية والإدارية\n          </p>\n        </div>\n        <div className=\"flex justify-center items-center h-64\">\n          <div className=\"text-gray-500\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            لا توجد بيانات متاحة\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6 arabic-text\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            التقارير والإحصائيات\n          </h1>\n          <p className=\"text-gray-600\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            تقارير شاملة عن أنشطة الديوان المالية والإدارية\n          </p>\n        </div>\n        <div className=\"flex space-x-2 space-x-reverse\">\n          <select\n            value={selectedPeriod}\n            onChange={(e) => setSelectedPeriod(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-diwan-500\"\n          >\n            <option value=\"current-month\">الشهر الحالي</option>\n            <option value=\"last-month\">الشهر الماضي</option>\n            <option value=\"current-year\">السنة الحالية</option>\n            <option value=\"last-year\">السنة الماضية</option>\n            <option value=\"all-time\">جميع الفترات</option>\n          </select>\n          <Button\n            variant=\"outline\"\n            onClick={handleRefresh}\n            disabled={refreshing}\n            className=\"text-gray-600 border-gray-300 hover:bg-gray-50\"\n          >\n            <RefreshCw className={`w-4 h-4 ml-2 ${refreshing ? 'animate-spin' : ''}`} />\n            {refreshing ? 'جاري التحديث...' : 'تحديث'}\n          </Button>\n          <Button\n            variant=\"outline\"\n            onClick={handleExportMonthlyData}\n            className=\"text-blue-600 border-blue-600 hover:bg-blue-50\"\n          >\n            <BarChart3 className=\"w-4 h-4 ml-2\" />\n            البيانات الشهرية\n          </Button>\n          <Button\n            onClick={handleExportPDF}\n            className=\"bg-red-600 hover:bg-red-700 text-white\"\n          >\n            <FileText className=\"w-4 h-4 ml-2\" />\n            طباعة PDF\n          </Button>\n          <Button\n            onClick={handleExportPDFDownload}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >\n            <Download className=\"w-4 h-4 ml-2\" />\n            تحميل PDF\n          </Button>\n          <Button\n            onClick={handleExportCSV}\n            className=\"bg-green-600 hover:bg-green-700 text-white\"\n          >\n            <Download className=\"w-4 h-4 ml-2\" />\n            تصدير CSV\n          </Button>\n        </div>\n      </div>\n\n      {/* الملخص المالي */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-5\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">إجمالي الأعضاء</CardTitle>\n            <Users className=\"h-4 w-4 text-blue-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-blue-600\">\n              {data.summary.totalMembers}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">\n              {data.summary.activeMembers} عضو نشط\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">إجمالي الإيرادات</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-green-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-green-600\">\n              {formatCurrency(data.summary.totalIncomes)}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">إجمالي المصروفات</CardTitle>\n            <TrendingDown className=\"h-4 w-4 text-red-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-red-600\">\n              {formatCurrency(data.summary.totalExpenses)}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">الرصيد الحالي</CardTitle>\n            <DollarSign className=\"h-4 w-4 text-blue-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className={`text-2xl font-bold ${\n              data.summary.balance >= 0 ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {formatCurrency(data.summary.balance)}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">إجمالي الأنشطة</CardTitle>\n            <Activity className=\"h-4 w-4 text-purple-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-purple-600\">\n              {data.summary.totalActivities}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* التقارير التفصيلية */}\n      <Tabs defaultValue=\"overview\" className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-5\">\n          <TabsTrigger value=\"overview\">نظرة عامة</TabsTrigger>\n          <TabsTrigger value=\"contributors\">أكثر المساهمين</TabsTrigger>\n          <TabsTrigger value=\"transactions\">المعاملات</TabsTrigger>\n          <TabsTrigger value=\"analytics\">التحليلات</TabsTrigger>\n          <TabsTrigger value=\"insights\">رؤى ذكية</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"overview\" className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n            {/* أكثر الأعضاء مساهمة */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Award className=\"w-5 h-5 ml-2\" />\n                  أكثر الأعضاء مساهمة\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                {data.topContributors.length === 0 ? (\n                  <p className=\"text-gray-500 text-center py-4\">لا توجد مساهمات</p>\n                ) : (\n                  <div className=\"space-y-3\">\n                    {data.topContributors.slice(0, 10).map((member, index) => (\n                      <div key={member.id} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${\n                            index === 0 ? 'bg-yellow-500' :\n                            index === 1 ? 'bg-gray-400' :\n                            index === 2 ? 'bg-orange-600' :\n                            'bg-gray-300'\n                          }`}>\n                            {index + 1}\n                          </div>\n                          <div className=\"mr-3\">\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {member.name}\n                            </div>\n                            <div className=\"text-xs text-gray-500\">\n                              {member.incomes.length} إيراد\n                            </div>\n                          </div>\n                        </div>\n                        <div className=\"text-sm font-medium text-green-600\">\n                          {formatCurrency(member.totalContributions)}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* آخر المعاملات */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <FileText className=\"w-5 h-5 ml-2\" />\n                  آخر المعاملات\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                {data.recentTransactions.length === 0 ? (\n                  <p className=\"text-gray-500 text-center py-4\">لا توجد معاملات</p>\n                ) : (\n                  <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                    {data.recentTransactions.slice(0, 15).map((transaction, index) => (\n                      <div key={`${transaction.type}-${transaction.id}-${index}`} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <div className={`w-3 h-3 rounded-full ${\n                            transaction.type === 'income' ? 'bg-green-500' : 'bg-red-500'\n                          }`}></div>\n                          <div className=\"mr-3\">\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {transaction.description}\n                            </div>\n                            <div className=\"text-xs text-gray-500\">\n                              {transaction.memberName || 'غير محدد'} • {formatDate(transaction.date)}\n                            </div>\n                          </div>\n                        </div>\n                        <div className={`text-sm font-medium ${\n                          transaction.type === 'income' ? 'text-green-600' : 'text-red-600'\n                        }`}>\n                          {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </div>\n        </TabsContent>\n\n        <TabsContent value=\"contributors\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>تفاصيل المساهمين</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {data.topContributors.map((member, index) => (\n                  <div key={member.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <Badge variant={index < 3 ? 'success' : 'secondary'} className=\"ml-3\">\n                        #{index + 1}\n                      </Badge>\n                      <div>\n                        <div className=\"font-medium\">{member.name}</div>\n                        <div className=\"text-sm text-gray-500\">\n                          {member.incomes.length} مساهمة\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"font-medium text-green-600\">\n                        {formatCurrency(member.totalContributions)}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        متوسط: {formatCurrency(member.totalContributions / member.incomes.length)}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"transactions\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>جميع المعاملات الحديثة</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {data.recentTransactions.map((transaction, index) => (\n                  <div key={`${transaction.type}-${transaction.id}-${index}`}\n                       className={`p-4 rounded-lg border-l-4 ${\n                         transaction.type === 'income'\n                           ? 'border-green-500 bg-green-50'\n                           : 'border-red-500 bg-red-50'\n                       }`}>\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <div className=\"font-medium\">{transaction.description}</div>\n                        <div className=\"text-sm text-gray-600 mt-1\">\n                          <Calendar className=\"w-3 h-3 inline ml-1\" />\n                          {formatDate(transaction.date)}\n                        </div>\n                        {transaction.memberName && (\n                          <div className=\"text-sm text-gray-600\">\n                            <Users className=\"w-3 h-3 inline ml-1\" />\n                            {transaction.memberName}\n                          </div>\n                        )}\n                      </div>\n                      <div className={`text-lg font-bold ${\n                        transaction.type === 'income' ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"analytics\" className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n            {/* إحصائيات إضافية */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Target className=\"w-5 h-5 ml-2\" />\n                  مؤشرات الأداء الرئيسية\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                  <div className=\"text-center p-4 border border-gray-200 rounded-lg\">\n                    <div className=\"text-lg font-semibold text-gray-900\">\n                      {data.summary.totalIncomes > 0\n                        ? formatCurrency(data.summary.totalIncomes / data.summary.totalMembers)\n                        : formatCurrency(0)\n                      }\n                    </div>\n                    <div className=\"text-sm text-gray-500\">متوسط المساهمة لكل عضو</div>\n                  </div>\n\n                  <div className=\"text-center p-4 border border-gray-200 rounded-lg\">\n                    <div className=\"text-lg font-semibold text-gray-900\">\n                      {data.summary.totalExpenses > 0 && data.summary.totalActivities > 0\n                        ? formatCurrency(data.summary.totalExpenses / data.summary.totalActivities)\n                        : formatCurrency(0)\n                      }\n                    </div>\n                    <div className=\"text-sm text-gray-500\">متوسط تكلفة النشاط</div>\n                  </div>\n\n                  <div className=\"text-center p-4 border border-gray-200 rounded-lg\">\n                    <div className=\"text-lg font-semibold text-gray-900\">\n                      {data.summary.totalMembers > 0\n                        ? Math.round((data.summary.activeMembers / data.summary.totalMembers) * 100)\n                        : 0\n                      }%\n                    </div>\n                    <div className=\"text-sm text-gray-500\">نسبة الأعضاء النشطين</div>\n                  </div>\n\n                  <div className=\"text-center p-4 border border-gray-200 rounded-lg\">\n                    <div className=\"text-lg font-semibold text-gray-900\">\n                      {data.summary.totalIncomes > 0\n                        ? Math.round((data.summary.totalExpenses / data.summary.totalIncomes) * 100)\n                        : 0\n                      }%\n                    </div>\n                    <div className=\"text-sm text-gray-500\">نسبة المصروفات للإيرادات</div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* البيانات الشهرية */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <PieChart className=\"w-5 h-5 ml-2\" />\n                  الاتجاهات الشهرية\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                {data.monthlyData.length > 0 ? (\n                  <div className=\"space-y-3\">\n                    {data.monthlyData.slice(-6).map((month) => (\n                      <div key={month.month} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                        <div>\n                          <div className=\"font-medium\">\n                            {new Date(month.month + '-01').toLocaleDateString('ar-JO', {\n                              year: 'numeric',\n                              month: 'long'\n                            })}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            إيرادات: {formatCurrency(month.incomes)} |\n                            مصروفات: {formatCurrency(month.expenses)}\n                          </div>\n                        </div>\n                        <div className={`font-medium ${\n                          month.balance >= 0 ? 'text-green-600' : 'text-red-600'\n                        }`}>\n                          {formatCurrency(month.balance)}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <p className=\"text-gray-500 text-center py-4\">لا توجد بيانات شهرية</p>\n                )}\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* تحليل الأداء */}\n          <Card>\n            <CardHeader>\n              <CardTitle>تحليل الأداء المالي</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 gap-6 md:grid-cols-3\">\n                <div className=\"text-center\">\n                  <div className={`text-3xl font-bold ${\n                    data.summary.balance >= 0 ? 'text-green-600' : 'text-red-600'\n                  }`}>\n                    {data.summary.balance >= 0 ? '✓' : '⚠'}\n                  </div>\n                  <div className=\"text-sm text-gray-600 mt-2\">\n                    {data.summary.balance >= 0 ? 'الوضع المالي مستقر' : 'يحتاج مراجعة مالية'}\n                  </div>\n                </div>\n\n                <div className=\"text-center\">\n                  <div className={`text-3xl font-bold ${\n                    (data.summary.activeMembers / data.summary.totalMembers) >= 0.7 ? 'text-green-600' : 'text-yellow-600'\n                  }`}>\n                    {(data.summary.activeMembers / data.summary.totalMembers) >= 0.7 ? '📈' : '📊'}\n                  </div>\n                  <div className=\"text-sm text-gray-600 mt-2\">\n                    {(data.summary.activeMembers / data.summary.totalMembers) >= 0.7\n                      ? 'مشاركة عالية للأعضاء'\n                      : 'يمكن تحسين مشاركة الأعضاء'\n                    }\n                  </div>\n                </div>\n\n                <div className=\"text-center\">\n                  <div className={`text-3xl font-bold ${\n                    data.summary.totalActivities >= 5 ? 'text-green-600' : 'text-blue-600'\n                  }`}>\n                    {data.summary.totalActivities >= 5 ? '🎯' : '📅'}\n                  </div>\n                  <div className=\"text-sm text-gray-600 mt-2\">\n                    {data.summary.totalActivities >= 5\n                      ? 'نشاط جيد للديوان'\n                      : 'يمكن زيادة الأنشطة'\n                    }\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"insights\" className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n            {/* توصيات ذكية */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Target className=\"w-5 h-5 ml-2\" />\n                  توصيات ذكية\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {/* توصية الوضع المالي */}\n                  <div className={`p-4 rounded-lg border-l-4 ${\n                    data.summary.balance >= 0 ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'\n                  }`}>\n                    <div className=\"font-medium mb-2\">\n                      {data.summary.balance >= 0 ? '✅ الوضع المالي مستقر' : '⚠️ تحذير مالي'}\n                    </div>\n                    <div className=\"text-sm text-gray-600\">\n                      {data.summary.balance >= 0\n                        ? 'الديوان يحقق فائضاً مالياً جيداً. يُنصح بالاستثمار في أنشطة إضافية.'\n                        : 'الديوان يواجه عجزاً مالياً. يُنصح بمراجعة المصروفات وزيادة الإيرادات.'\n                      }\n                    </div>\n                  </div>\n\n                  {/* توصية مشاركة الأعضاء */}\n                  <div className={`p-4 rounded-lg border-l-4 ${\n                    (data.summary.activeMembers / data.summary.totalMembers) >= 0.7\n                      ? 'border-green-500 bg-green-50'\n                      : 'border-yellow-500 bg-yellow-50'\n                  }`}>\n                    <div className=\"font-medium mb-2\">\n                      {(data.summary.activeMembers / data.summary.totalMembers) >= 0.7\n                        ? '👥 مشاركة ممتازة للأعضاء'\n                        : '📢 تحسين مشاركة الأعضاء'\n                      }\n                    </div>\n                    <div className=\"text-sm text-gray-600\">\n                      {(data.summary.activeMembers / data.summary.totalMembers) >= 0.7\n                        ? 'نسبة عالية من الأعضاء النشطين. استمر في تقديم الأنشطة المفيدة.'\n                        : 'يمكن تحسين مشاركة الأعضاء من خلال المزيد من الأنشطة التفاعلية.'\n                      }\n                    </div>\n                  </div>\n\n                  {/* توصية الأنشطة */}\n                  <div className={`p-4 rounded-lg border-l-4 ${\n                    data.summary.totalActivities >= 5 ? 'border-green-500 bg-green-50' : 'border-blue-500 bg-blue-50'\n                  }`}>\n                    <div className=\"font-medium mb-2\">\n                      {data.summary.totalActivities >= 5 ? '🎯 نشاط ممتاز' : '📅 زيادة الأنشطة'}\n                    </div>\n                    <div className=\"text-sm text-gray-600\">\n                      {data.summary.totalActivities >= 5\n                        ? 'مستوى جيد من الأنشطة. حافظ على هذا المعدل.'\n                        : 'يُنصح بزيادة عدد الأنشطة لتعزيز التفاعل بين الأعضاء.'\n                      }\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* إحصائيات متقدمة */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <BarChart3 className=\"w-5 h-5 ml-2\" />\n                  إحصائيات متقدمة\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                    <span className=\"text-sm font-medium\">معدل المساهمة الشهرية</span>\n                    <span className=\"font-bold text-blue-600\">\n                      {data.summary.totalIncomes > 0 && data.monthlyData.length > 0\n                        ? formatCurrency(data.summary.totalIncomes / Math.max(data.monthlyData.length, 1))\n                        : formatCurrency(0)\n                      }\n                    </span>\n                  </div>\n\n                  <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                    <span className=\"text-sm font-medium\">معدل الإنفاق الشهري</span>\n                    <span className=\"font-bold text-red-600\">\n                      {data.summary.totalExpenses > 0 && data.monthlyData.length > 0\n                        ? formatCurrency(data.summary.totalExpenses / Math.max(data.monthlyData.length, 1))\n                        : formatCurrency(0)\n                      }\n                    </span>\n                  </div>\n\n                  <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                    <span className=\"text-sm font-medium\">أعلى مساهمة فردية</span>\n                    <span className=\"font-bold text-green-600\">\n                      {data.topContributors.length > 0\n                        ? formatCurrency(data.topContributors[0].totalContributions)\n                        : formatCurrency(0)\n                      }\n                    </span>\n                  </div>\n\n                  <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                    <span className=\"text-sm font-medium\">نسبة الادخار</span>\n                    <span className={`font-bold ${\n                      data.summary.totalIncomes > 0\n                        ? (data.summary.balance / data.summary.totalIncomes) >= 0.2\n                          ? 'text-green-600'\n                          : 'text-yellow-600'\n                        : 'text-gray-600'\n                    }`}>\n                      {data.summary.totalIncomes > 0\n                        ? `${Math.round((data.summary.balance / data.summary.totalIncomes) * 100)}%`\n                        : '0%'\n                      }\n                    </span>\n                  </div>\n\n                  <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                    <span className=\"text-sm font-medium\">متوسط المعاملات اليومية</span>\n                    <span className=\"font-bold text-purple-600\">\n                      {data.recentTransactions.length > 0\n                        ? Math.round(data.recentTransactions.length / 30)\n                        : 0\n                      } معاملة/يوم\n                    </span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* مقارنة الأداء */}\n          <Card>\n            <CardHeader>\n              <CardTitle>مقارنة الأداء</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 gap-6 md:grid-cols-3\">\n                <div className=\"text-center p-6 border border-gray-200 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-blue-600 mb-2\">\n                    {Math.round((data.summary.activeMembers / data.summary.totalMembers) * 100)}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-2\">معدل المشاركة</div>\n                  <div className={`text-xs px-2 py-1 rounded-full ${\n                    (data.summary.activeMembers / data.summary.totalMembers) >= 0.8\n                      ? 'bg-green-100 text-green-800'\n                      : (data.summary.activeMembers / data.summary.totalMembers) >= 0.6\n                        ? 'bg-yellow-100 text-yellow-800'\n                        : 'bg-red-100 text-red-800'\n                  }`}>\n                    {(data.summary.activeMembers / data.summary.totalMembers) >= 0.8\n                      ? 'ممتاز'\n                      : (data.summary.activeMembers / data.summary.totalMembers) >= 0.6\n                        ? 'جيد'\n                        : 'يحتاج تحسين'\n                    }\n                  </div>\n                </div>\n\n                <div className=\"text-center p-6 border border-gray-200 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-green-600 mb-2\">\n                    {data.summary.totalIncomes > 0\n                      ? Math.round((data.summary.balance / data.summary.totalIncomes) * 100)\n                      : 0\n                    }%\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-2\">معدل الادخار</div>\n                  <div className={`text-xs px-2 py-1 rounded-full ${\n                    data.summary.totalIncomes > 0 && (data.summary.balance / data.summary.totalIncomes) >= 0.3\n                      ? 'bg-green-100 text-green-800'\n                      : data.summary.totalIncomes > 0 && (data.summary.balance / data.summary.totalIncomes) >= 0.1\n                        ? 'bg-yellow-100 text-yellow-800'\n                        : 'bg-red-100 text-red-800'\n                  }`}>\n                    {data.summary.totalIncomes > 0 && (data.summary.balance / data.summary.totalIncomes) >= 0.3\n                      ? 'ممتاز'\n                      : data.summary.totalIncomes > 0 && (data.summary.balance / data.summary.totalIncomes) >= 0.1\n                        ? 'جيد'\n                        : 'يحتاج تحسين'\n                    }\n                  </div>\n                </div>\n\n                <div className=\"text-center p-6 border border-gray-200 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-purple-600 mb-2\">\n                    {data.summary.totalActivities}\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-2\">عدد الأنشطة</div>\n                  <div className={`text-xs px-2 py-1 rounded-full ${\n                    data.summary.totalActivities >= 10\n                      ? 'bg-green-100 text-green-800'\n                      : data.summary.totalActivities >= 5\n                        ? 'bg-yellow-100 text-yellow-800'\n                        : 'bg-red-100 text-red-800'\n                  }`}>\n                    {data.summary.totalActivities >= 10\n                      ? 'نشط جداً'\n                      : data.summary.totalActivities >= 5\n                        ? 'نشط'\n                        : 'يحتاج تفعيل'\n                    }\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;;;AAvBA;;;;;;;;;AAwDe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,sBAAsB;IACtB,MAAM,mBAAmB,OAAO,iBAAiB,KAAK;QACpD,IAAI;YACF,IAAI,gBAAgB;gBAClB,cAAc;YAChB,OAAO;gBACL,WAAW;YACb;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,gBAAgB;YACpE,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,cAAc,MAAM,SAAS,IAAI;YACvC,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR,SAAU;YACR,WAAW;YACX,cAAc;QAChB;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,iBAAiB;IACnB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,SAAS;gBACX;YACF;QACF;gCAAG;QAAC;QAAgB;KAAQ;IAE5B,iCAAiC;IACjC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,2BAA2B;YAC3B,MAAM,cAAyC;gBAC7C,iBAAiB;gBACjB,cAAc;gBACd,gBAAgB;gBAChB,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBACzD,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;YAEA,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CA6HiB,EAAE,WAAW,CAAC,eAAe,IAAI,eAAe;6CAC/C,EAAE,YAAY;;;;;;+BAM5B,EAAE,KAAK,OAAO,CAAC,YAAY,CAAC;;;;+BAI5B,EAAE,KAAK,OAAO,CAAC,aAAa,CAAC;;;;sCAItB,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,YAAY,EAAE;;;;uCAI3C,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa,EAAE;;;;8BAItD,EAAE,KAAK,OAAO,CAAC,OAAO,IAAI,IAAI,WAAW,UAAU,EAAE,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,OAAO,EAAE;;;;+BAI3F,EAAE,KAAK,OAAO,CAAC,eAAe,CAAC;;;;;;;;;;;;;;;;gBAgB9C,EAAE,KAAK,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;;4BAEhD,EAAE,QAAQ,EAAE;4BACZ,EAAE,OAAO,IAAI,CAAC;4BACd,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC;2CACT,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,kBAAkB,EAAE;;gBAEvE,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;gBAkBZ,EAAE,KAAK,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,cAAgB,CAAC;;4BAEjD,EAAE,YAAY,IAAI,KAAK,WAAW,UAAU,QAAQ;4BACpD,EAAE,YAAY,WAAW,CAAC;mCACnB,EAAE,YAAY,IAAI,KAAK,WAAW,WAAW,UAAU;4BAC9D,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,EAAE;;4BAErC,EAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,IAAI,EAAE;4BAC/B,EAAE,YAAY,UAAU,IAAI,WAAW;;gBAEnD,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;OAKrB,CAAC;YAEF,4BAA4B;YAC5B,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;YACpC,IAAI,aAAa;gBACf,YAAY,QAAQ,CAAC,KAAK,CAAC;gBAC3B,YAAY,QAAQ,CAAC,KAAK;gBAE1B,iCAAiC;gBACjC,WAAW;oBACT,YAAY,KAAK;gBACnB,GAAG;YACL,OAAO;gBACL,MAAM;YACR;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,0BAA0B;QAC9B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,yBAAyB;YACzB,MAAM,WAAW,CAAC,qJAA2B,EAAE,OAAO;YAEtD,MAAM,cAAyC;gBAC7C,iBAAiB;gBACjB,cAAc;gBACd,gBAAgB;gBAChB,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBACzD,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;YAEA,uBAAuB;YACvB,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,SAAS,GAAG,CAAC;;;;;;;4BAOC,EAAE,WAAW,CAAC,eAAe,IAAI,eAAe;;;6BAG/C,EAAE,YAAY;;;;;;;8EAOmC,EAAE,KAAK,OAAO,CAAC,YAAY,CAAC;;;;8EAI5B,EAAE,KAAK,OAAO,CAAC,aAAa,CAAC;;;;8EAI7B,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,YAAY,EAAE;;;;8EAI5C,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa,EAAE;;;;oEAIvD,EAAE,KAAK,OAAO,CAAC,OAAO,IAAI,IAAI,YAAY,UAAU,GAAG,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,OAAO,EAAE;;;;8EAIpF,EAAE,KAAK,OAAO,CAAC,eAAe,CAAC;;;;;;;;;;;;;;;;;;gBAkB7F,EAAE,KAAK,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;;wHAE4C,EAAE,QAAQ,EAAE;wHACZ,EAAE,OAAO,IAAI,CAAC;wHACd,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC;0JACU,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,kBAAkB,EAAE;;gBAEtL,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;gBAoBZ,EAAE,KAAK,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,cAAgB,CAAC;;wHAE2C,EAAE,YAAY,IAAI,KAAK,WAAW,UAAU,QAAQ;wHACpD,EAAE,YAAY,WAAW,CAAC;8HACpB,EAAE,YAAY,IAAI,KAAK,WAAW,YAAY,UAAU,qBAAqB,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,EAAE;wHACxH,EAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,IAAI,EAAE;wHAC/B,EAAE,YAAY,UAAU,IAAI,WAAW;;gBAE/I,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;MAKtB,CAAC;YAED,cAAc;YACd,MAAM,MAAM;gBACV,QAAQ;gBACR,UAAU,CAAC,WAAW,EAAE,eAAe,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACtF,OAAO;oBAAE,MAAM;oBAAQ,SAAS;gBAAK;gBACrC,aAAa;oBAAE,OAAO;oBAAG,SAAS;gBAAK;gBACvC,OAAO;oBAAE,MAAM;oBAAM,QAAQ;oBAAM,aAAa;gBAAW;YAC7D;YAEA,uBAAuB;YACvB,MAAM,WAAW,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS,IAAI;QAE9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,iCAAiC;IACjC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;QAEX,MAAM,gBAAgB;YACpB;gBAAC;gBAAc;aAAS;YACxB;gBAAC;gBAAkB,KAAK,OAAO,CAAC,YAAY,CAAC,QAAQ;aAAG;YACxD;gBAAC;gBAAmB,KAAK,OAAO,CAAC,aAAa,CAAC,QAAQ;aAAG;YAC1D;gBAAC;gBAAoB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,YAAY;aAAE;YAC/D;gBAAC;gBAAoB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa;aAAE;YAChE;gBAAC;gBAAiB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,OAAO;aAAE;YACvD;gBAAC;gBAAkB,KAAK,OAAO,CAAC,eAAe,CAAC,QAAQ;aAAG;YAC3D,EAAE;YACF;gBAAC;aAAsB;YACvB;gBAAC;gBAAW;gBAAS;aAAmB;eACrC,KAAK,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,QAAU;oBAC1D,CAAC,QAAQ,CAAC,EAAE,QAAQ;oBACpB,OAAO,IAAI;oBACX,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,kBAAkB;iBACzC;YACD,EAAE;YACF;gBAAC;aAAgB;YACjB;gBAAC;gBAAS;gBAAS;gBAAU;gBAAW;aAAiB;eACtD,KAAK,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,cAAe;oBACzD,YAAY,IAAI,KAAK,WAAW,UAAU;oBAC1C,YAAY,WAAW;oBACvB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM;oBACjC,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,IAAI;oBAC3B,YAAY,UAAU,IAAI;iBAC3B;SACF,CAAC,GAAG,CAAC,CAAA,MAAO,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;QAE5D,MAAM,OAAO,IAAI,KAAK;YAAC,WAAW;SAAc,EAAE;YAAE,MAAM;QAA0B;QACpF,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,IAAI,eAAe,CAAC;QAChC,KAAK,QAAQ,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAC5F,KAAK,KAAK;IACZ;IAEA,yBAAyB;IACzB,MAAM,0BAA0B;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,EAAE;QAEhC,MAAM,iBAAiB;YACrB;gBAAC;gBAAS;gBAAa;gBAAa;aAAS;eAC1C,KAAK,WAAW,CAAC,GAAG,CAAC,CAAA,QAAS;oBAC/B,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,kBAAkB,CAAC,SAAS;wBACxD,MAAM;wBACN,OAAO;oBACT;oBACA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,OAAO;oBAC5B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;oBAC7B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,OAAO;iBAC7B;SACF,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;QAEjC,MAAM,OAAO,IAAI,KAAK;YAAC,WAAW;SAAe,EAAE;YAAE,MAAM;QAA0B;QACrF,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,IAAI,eAAe,CAAC;QAChC,KAAK,QAAQ,GAAG,CAAC,iBAAiB,EAAE,eAAe,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAClG,KAAK,KAAK;IACZ;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;4BAAmC,OAAO;gCAAE,YAAY;4BAA6B;sCAAG;;;;;;sCAGtG,6LAAC;4BAAE,WAAU;4BAAgB,OAAO;gCAAE,YAAY;4BAA6B;sCAAG;;;;;;;;;;;;8BAIpF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAgB,OAAO;4BAAE,YAAY;wBAA6B;kCAAG;;;;;;;;;;;;;;;;;IAM5F;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;4BAAmC,OAAO;gCAAE,YAAY;4BAA6B;sCAAG;;;;;;sCAGtG,6LAAC;4BAAE,WAAU;4BAAgB,OAAO;gCAAE,YAAY;4BAA6B;sCAAG;;;;;;;;;;;;8BAIpF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAgB,OAAO;4BAAE,YAAY;wBAA6B;kCAAG;;;;;;;;;;;;;;;;;IAM5F;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;gCAAmC,OAAO;oCAAE,YAAY;gCAA6B;0CAAG;;;;;;0CAGtG,6LAAC;gCAAE,WAAU;gCAAgB,OAAO;oCAAE,YAAY;gCAA6B;0CAAG;;;;;;;;;;;;kCAIpF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAgB;;;;;;kDAC9B,6LAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,6LAAC;wCAAO,OAAM;kDAAe;;;;;;kDAC7B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,aAAa,EAAE,aAAa,iBAAiB,IAAI;;;;;;oCACvE,aAAa,oBAAoB;;;;;;;0CAEpC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO,CAAC,YAAY;;;;;;kDAE5B,6LAAC;wCAAE,WAAU;;4CACV,KAAK,OAAO,CAAC,aAAa;4CAAC;;;;;;;;;;;;;;;;;;;kCAKlC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;kCAK/C,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;0CAE1B,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa;;;;;;;;;;;;;;;;;kCAKhD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAW,CAAC,mBAAmB,EAClC,KAAK,OAAO,CAAC,OAAO,IAAI,IAAI,mBAAmB,gBAC/C;8CACC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;kCAK1C,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,KAAK,OAAO,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAW,WAAU;;kCACtC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAe;;;;;;0CAClC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAe;;;;;;0CAClC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;0CAC/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;;;;;;;kCAGhC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAItC,6LAAC,mIAAA,CAAA,cAAW;sDACT,KAAK,eAAe,CAAC,MAAM,KAAK,kBAC/B,6LAAC;gDAAE,WAAU;0DAAiC;;;;;qEAE9C,6LAAC;gDAAI,WAAU;0DACZ,KAAK,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,sBAC9C,6LAAC;wDAAoB,WAAU;;0EAC7B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAW,CAAC,mFAAmF,EAClG,UAAU,IAAI,kBACd,UAAU,IAAI,gBACd,UAAU,IAAI,kBACd,eACA;kFACC,QAAQ;;;;;;kFAEX,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,OAAO,IAAI;;;;;;0FAEd,6LAAC;gFAAI,WAAU;;oFACZ,OAAO,OAAO,CAAC,MAAM;oFAAC;;;;;;;;;;;;;;;;;;;0EAI7B,6LAAC;gEAAI,WAAU;0EACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,kBAAkB;;;;;;;uDApBnC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;8CA8B7B,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAIzC,6LAAC,mIAAA,CAAA,cAAW;sDACT,KAAK,kBAAkB,CAAC,MAAM,KAAK,kBAClC,6LAAC;gDAAE,WAAU;0DAAiC;;;;;qEAE9C,6LAAC;gDAAI,WAAU;0DACZ,KAAK,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,aAAa,sBACtD,6LAAC;wDAA2D,WAAU;;0EACpE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAW,CAAC,qBAAqB,EACpC,YAAY,IAAI,KAAK,WAAW,iBAAiB,cACjD;;;;;;kFACF,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,YAAY,WAAW;;;;;;0FAE1B,6LAAC;gFAAI,WAAU;;oFACZ,YAAY,UAAU,IAAI;oFAAW;oFAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,IAAI;;;;;;;;;;;;;;;;;;;0EAI3E,6LAAC;gEAAI,WAAW,CAAC,oBAAoB,EACnC,YAAY,IAAI,KAAK,WAAW,mBAAmB,gBACnD;;oEACC,YAAY,IAAI,KAAK,WAAW,MAAM;oEAAK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM;;;;;;;;uDAjBvE,GAAG,YAAY,IAAI,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA4BxE,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAe,WAAU;kCAC1C,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,KAAK,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACjC,6LAAC;gDAAoB,WAAU;;kEAC7B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,QAAQ,IAAI,YAAY;gEAAa,WAAU;;oEAAO;oEAClE,QAAQ;;;;;;;0EAEZ,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAe,OAAO,IAAI;;;;;;kFACzC,6LAAC;wEAAI,WAAU;;4EACZ,OAAO,OAAO,CAAC,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;kEAI7B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,kBAAkB;;;;;;0EAE3C,6LAAC;gEAAI,WAAU;;oEAAwB;oEAC7B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,kBAAkB,GAAG,OAAO,OAAO,CAAC,MAAM;;;;;;;;;;;;;;+CAjBpE,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCA2B7B,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAe,WAAU;kCAC1C,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,KAAK,kBAAkB,CAAC,GAAG,CAAC,CAAC,aAAa,sBACzC,6LAAC;gDACI,WAAW,CAAC,0BAA0B,EACpC,YAAY,IAAI,KAAK,WACjB,iCACA,4BACJ;0DACL,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAe,YAAY,WAAW;;;;;;8EACrD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEACnB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,IAAI;;;;;;;gEAE7B,YAAY,UAAU,kBACrB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,YAAY,UAAU;;;;;;;;;;;;;sEAI7B,6LAAC;4DAAI,WAAW,CAAC,kBAAkB,EACjC,YAAY,IAAI,KAAK,WAAW,mBAAmB,gBACnD;;gEACC,YAAY,IAAI,KAAK,WAAW,MAAM;gEAAK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM;;;;;;;;;;;;;+CAvBzE,GAAG,YAAY,IAAI,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiCpE,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;;0CACvC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIvC,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,KAAK,OAAO,CAAC,YAAY,GAAG,IACzB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,YAAY,GAAG,KAAK,OAAO,CAAC,YAAY,IACpE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;8EAGrB,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAGzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,KAAK,OAAO,CAAC,eAAe,GAAG,IAC9D,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,eAAe,IACxE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;8EAGrB,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAGzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEACZ,KAAK,OAAO,CAAC,YAAY,GAAG,IACzB,KAAK,KAAK,CAAC,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,GAAI,OACtE;wEACH;;;;;;;8EAEH,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAGzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEACZ,KAAK,OAAO,CAAC,YAAY,GAAG,IACzB,KAAK,KAAK,CAAC,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,GAAI,OACtE;wEACH;;;;;;;8EAEH,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO/C,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIzC,6LAAC,mIAAA,CAAA,cAAW;0DACT,KAAK,WAAW,CAAC,MAAM,GAAG,kBACzB,6LAAC;oDAAI,WAAU;8DACZ,KAAK,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,sBAC/B,6LAAC;4DAAsB,WAAU;;8EAC/B,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFACZ,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,kBAAkB,CAAC,SAAS;gFACzD,MAAM;gFACN,OAAO;4EACT;;;;;;sFAEF,6LAAC;4EAAI,WAAU;;gFAAwB;gFAC3B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,OAAO;gFAAE;gFAC9B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;;;;;;;;;;;;;8EAG3C,6LAAC;oEAAI,WAAW,CAAC,YAAY,EAC3B,MAAM,OAAO,IAAI,IAAI,mBAAmB,gBACxC;8EACC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,OAAO;;;;;;;2DAhBvB,MAAM,KAAK;;;;;;;;;yEAsBzB,6LAAC;oDAAE,WAAU;8DAAiC;;;;;;;;;;;;;;;;;;;;;;;0CAOtD,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,mBAAmB,EAClC,KAAK,OAAO,CAAC,OAAO,IAAI,IAAI,mBAAmB,gBAC/C;sEACC,KAAK,OAAO,CAAC,OAAO,IAAI,IAAI,MAAM;;;;;;sEAErC,6LAAC;4DAAI,WAAU;sEACZ,KAAK,OAAO,CAAC,OAAO,IAAI,IAAI,uBAAuB;;;;;;;;;;;;8DAIxD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,mBAAmB,EAClC,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MAAM,mBAAmB,mBACrF;sEACC,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MAAM,OAAO;;;;;;sEAE5E,6LAAC;4DAAI,WAAU;sEACZ,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACzD,yBACA;;;;;;;;;;;;8DAKR,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,mBAAmB,EAClC,KAAK,OAAO,CAAC,eAAe,IAAI,IAAI,mBAAmB,iBACvD;sEACC,KAAK,OAAO,CAAC,eAAe,IAAI,IAAI,OAAO;;;;;;sEAE9C,6LAAC;4DAAI,WAAU;sEACZ,KAAK,OAAO,CAAC,eAAe,IAAI,IAC7B,qBACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIvC,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAW,CAAC,0BAA0B,EACzC,KAAK,OAAO,CAAC,OAAO,IAAI,IAAI,iCAAiC,4BAC7D;;8EACA,6LAAC;oEAAI,WAAU;8EACZ,KAAK,OAAO,CAAC,OAAO,IAAI,IAAI,yBAAyB;;;;;;8EAExD,6LAAC;oEAAI,WAAU;8EACZ,KAAK,OAAO,CAAC,OAAO,IAAI,IACrB,wEACA;;;;;;;;;;;;sEAMR,6LAAC;4DAAI,WAAW,CAAC,0BAA0B,EACzC,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACxD,iCACA,kCACJ;;8EACA,6LAAC;oEAAI,WAAU;8EACZ,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACzD,6BACA;;;;;;8EAGN,6LAAC;oEAAI,WAAU;8EACZ,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACzD,mEACA;;;;;;;;;;;;sEAMR,6LAAC;4DAAI,WAAW,CAAC,0BAA0B,EACzC,KAAK,OAAO,CAAC,eAAe,IAAI,IAAI,iCAAiC,8BACrE;;8EACA,6LAAC;oEAAI,WAAU;8EACZ,KAAK,OAAO,CAAC,eAAe,IAAI,IAAI,kBAAkB;;;;;;8EAEzD,6LAAC;oEAAI,WAAU;8EACZ,KAAK,OAAO,CAAC,eAAe,IAAI,IAC7B,+CACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASd,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAI1C,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAU;8EACb,KAAK,OAAO,CAAC,YAAY,GAAG,KAAK,KAAK,WAAW,CAAC,MAAM,GAAG,IACxD,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,KAAK,WAAW,CAAC,MAAM,EAAE,MAC7E,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;sEAKvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAU;8EACb,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,KAAK,WAAW,CAAC,MAAM,GAAG,IACzD,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,GAAG,CAAC,KAAK,WAAW,CAAC,MAAM,EAAE,MAC9E,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;sEAKvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAU;8EACb,KAAK,eAAe,CAAC,MAAM,GAAG,IAC3B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,eAAe,CAAC,EAAE,CAAC,kBAAkB,IACzD,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;sEAKvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAW,CAAC,UAAU,EAC1B,KAAK,OAAO,CAAC,YAAY,GAAG,IACxB,AAAC,KAAK,OAAO,CAAC,OAAO,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACpD,mBACA,oBACF,iBACJ;8EACC,KAAK,OAAO,CAAC,YAAY,GAAG,IACzB,GAAG,KAAK,KAAK,CAAC,AAAC,KAAK,OAAO,CAAC,OAAO,GAAG,KAAK,OAAO,CAAC,YAAY,GAAI,KAAK,CAAC,CAAC,GAC1E;;;;;;;;;;;;sEAKR,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAU;;wEACb,KAAK,kBAAkB,CAAC,MAAM,GAAG,IAC9B,KAAK,KAAK,CAAC,KAAK,kBAAkB,CAAC,MAAM,GAAG,MAC5C;wEACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,KAAK,CAAC,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,GAAI;gEAAK;;;;;;;sEAE9E,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;sEAC5C,6LAAC;4DAAI,WAAW,CAAC,+BAA+B,EAC9C,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACxD,gCACA,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MAC1D,kCACA,2BACN;sEACC,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACzD,UACA,AAAC,KAAK,OAAO,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MAC1D,QACA;;;;;;;;;;;;8DAKV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,OAAO,CAAC,YAAY,GAAG,IACzB,KAAK,KAAK,CAAC,AAAC,KAAK,OAAO,CAAC,OAAO,GAAG,KAAK,OAAO,CAAC,YAAY,GAAI,OAChE;gEACH;;;;;;;sEAEH,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;sEAC5C,6LAAC;4DAAI,WAAW,CAAC,+BAA+B,EAC9C,KAAK,OAAO,CAAC,YAAY,GAAG,KAAK,AAAC,KAAK,OAAO,CAAC,OAAO,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACnF,gCACA,KAAK,OAAO,CAAC,YAAY,GAAG,KAAK,AAAC,KAAK,OAAO,CAAC,OAAO,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACrF,kCACA,2BACN;sEACC,KAAK,OAAO,CAAC,YAAY,GAAG,KAAK,AAAC,KAAK,OAAO,CAAC,OAAO,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACpF,UACA,KAAK,OAAO,CAAC,YAAY,GAAG,KAAK,AAAC,KAAK,OAAO,CAAC,OAAO,GAAG,KAAK,OAAO,CAAC,YAAY,IAAK,MACrF,QACA;;;;;;;;;;;;8DAKV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,KAAK,OAAO,CAAC,eAAe;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;sEAC5C,6LAAC;4DAAI,WAAW,CAAC,+BAA+B,EAC9C,KAAK,OAAO,CAAC,eAAe,IAAI,KAC5B,gCACA,KAAK,OAAO,CAAC,eAAe,IAAI,IAC9B,kCACA,2BACN;sEACC,KAAK,OAAO,CAAC,eAAe,IAAI,KAC7B,aACA,KAAK,OAAO,CAAC,eAAe,IAAI,IAC9B,QACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B;GA7qCwB;;QACI,iJAAA,CAAA,aAAU;;;KADd", "debugId": null}}]}