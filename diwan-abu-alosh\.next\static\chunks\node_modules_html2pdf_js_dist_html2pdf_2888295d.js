(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/html2pdf.js/dist/html2pdf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_9081b531._.js",
  "static/chunks/node_modules_jspdf_dist_jspdf_es_min_c277e70f.js",
  "static/chunks/node_modules_html2canvas_dist_html2canvas_3938cdad.js",
  "static/chunks/node_modules_html2pdf_js_dist_html2pdf_6c43fb94.js",
  "static/chunks/node_modules_1b674287._.js",
  "static/chunks/node_modules_html2pdf_js_dist_html2pdf_31d1da67.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/html2pdf.js/dist/html2pdf.js [app-client] (ecmascript)");
    });
});
}}),
}]);