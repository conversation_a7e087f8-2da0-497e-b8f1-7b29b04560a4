{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { X } from \"lucide-react\"\n\ninterface DialogProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n  enableScrollInteraction?: boolean // خيار لتفعيل التفاعل مع التمرير\n  maxHeight?: string // الحد الأقصى للارتفاع\n}\n\ninterface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Dialog = ({\n  open,\n  onOpenChange,\n  children,\n  enableScrollInteraction = true,\n  maxHeight = \"90vh\"\n}: DialogProps) => {\n  const dialogRef = React.useRef<HTMLDivElement>(null)\n  const containerRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onOpenChange?.(false)\n      }\n    }\n\n    const handleWheel = (e: WheelEvent) => {\n      if (!open || !enableScrollInteraction || !dialogRef.current) return\n\n      // السماح بالتمرير داخل الحوار فقط\n      const dialogElement = dialogRef.current\n      const isScrollable = dialogElement.scrollHeight > dialogElement.clientHeight\n\n      if (isScrollable) {\n        // التحقق من أن الماوس داخل منطقة الحوار\n        const rect = dialogElement.getBoundingClientRect()\n        const isInsideDialog = e.clientX >= rect.left && e.clientX <= rect.right &&\n                              e.clientY >= rect.top && e.clientY <= rect.bottom\n\n        if (isInsideDialog) {\n          // السماح بالتمرير الطبيعي داخل الحوار\n          return\n        }\n      }\n\n      // منع التمرير خارج الحوار\n      e.preventDefault()\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEscape)\n      if (enableScrollInteraction) {\n        document.addEventListener('wheel', handleWheel, { passive: false })\n      }\n      // عدم منع التمرير في الخلفية للسماح بالتمرير داخل الحوار\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.removeEventListener('wheel', handleWheel)\n    }\n  }, [open, onOpenChange, enableScrollInteraction])\n\n  if (!open) return null\n\n  return (\n    <div\n      ref={containerRef}\n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n    >\n      <div\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300\"\n        onClick={() => onOpenChange?.(false)}\n      />\n      <div\n        ref={dialogRef}\n        className={cn(\n          \"relative z-50 w-full overflow-hidden animate-in fade-in-0 zoom-in-95 duration-300\",\n          \"smooth-scroll\", // فئة CSS للتمرير السلس\n          enableScrollInteraction ? \"overflow-y-auto\" : \"overflow-hidden\"\n        )}\n        style={{\n          maxHeight: maxHeight,\n          transform: 'translate3d(0, 0, 0)', // تحسين الأداء\n          willChange: 'transform', // تحسين الأداء\n        }}\n      >\n        {children}\n      </div>\n    </div>\n  )\n}\n\nconst DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200\",\n        \"backdrop-blur-sm bg-white/95\", // تحسين الشفافية\n        \"hover:shadow-3xl transition-shadow duration-300\", // تأثير الظل عند التمرير\n        \"max-h-full overflow-y-auto smooth-scroll\", // تمكين التمرير السلس\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogContent.displayName = \"DialogContent\"\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-bold leading-none tracking-tight text-slate-800\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = \"DialogTitle\"\n\nconst DialogDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-slate-600 font-medium\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = \"DialogDescription\"\n\nconst DialogTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement>\n>(({ className, ...props }, ref) => (\n  <button\n    ref={ref}\n    className={className}\n    {...props}\n  />\n))\nDialogTrigger.displayName = \"DialogTrigger\"\n\nconst DialogClose = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & { onOpenChange?: (open: boolean) => void }\n>(({ className, onOpenChange, ...props }, ref) => (\n  <button\n    ref={ref}\n    type=\"button\"\n    onClick={() => onOpenChange?.(false)}\n    className={cn(\n      \"absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10\",\n      className\n    )}\n    {...props}\n  >\n    <X className=\"h-4 w-4 text-slate-600\" />\n    <span className=\"sr-only\">إغلاق</span>\n  </button>\n))\nDialogClose.displayName = \"DialogClose\"\n\nexport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n  DialogClose,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAkBA,MAAM,SAAS,CAAC,EACd,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,0BAA0B,IAAI,EAC9B,YAAY,MAAM,EACN;;IACZ,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAkB;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAkB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd,MAAM;iDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB,eAAe;oBACjB;gBACF;;YAEA,MAAM;gDAAc,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,UAAU,OAAO,EAAE;oBAE7D,kCAAkC;oBAClC,MAAM,gBAAgB,UAAU,OAAO;oBACvC,MAAM,eAAe,cAAc,YAAY,GAAG,cAAc,YAAY;oBAE5E,IAAI,cAAc;wBAChB,wCAAwC;wBACxC,MAAM,OAAO,cAAc,qBAAqB;wBAChD,MAAM,iBAAiB,EAAE,OAAO,IAAI,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,KAAK,KAAK,IAClD,EAAE,OAAO,IAAI,KAAK,GAAG,IAAI,EAAE,OAAO,IAAI,KAAK,MAAM;wBAEvE,IAAI,gBAAgB;4BAClB,sCAAsC;4BACtC;wBACF;oBACF;oBAEA,0BAA0B;oBAC1B,EAAE,cAAc;gBAClB;;YAEA,IAAI,MAAM;gBACR,SAAS,gBAAgB,CAAC,WAAW;gBACrC,IAAI,yBAAyB;oBAC3B,SAAS,gBAAgB,CAAC,SAAS,aAAa;wBAAE,SAAS;oBAAM;gBACnE;YACA,yDAAyD;YAC3D;YAEA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,mBAAmB,CAAC,SAAS;gBACxC;;QACF;2BAAG;QAAC;QAAM;QAAc;KAAwB;IAEhD,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAEV,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAEhC,6LAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA,iBACA,0BAA0B,oBAAoB;gBAEhD,OAAO;oBACL,WAAW;oBACX,WAAW;oBACX,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAIT;GAlFM;KAAA;AAoFN,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QACnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA,gCACA,mDACA,4CACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+HACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGjC,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE,oBACxC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,SAAS,IAAM,eAAe;QAC9B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wRACA;QAED,GAAG,KAAK;;0BAET,6LAAC,+LAAA,CAAA,IAAC;gBAAC,WAAU;;;;;;0BACb,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;;AAG9B,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog-demo.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON><PERSON>, DialogFooter } from './dialog'\nimport { Button } from './button'\nimport { <PERSON><PERSON><PERSON>, MousePointer, Move, Eye, Info } from 'lucide-react'\nimport MouseTracker, { MouseInfoPanel } from './mouse-tracker'\n\ninterface DialogDemoProps {\n  className?: string\n}\n\nexport default function DialogDemo({ className }: DialogDemoProps) {\n  const [basicDialogOpen, setBasicDialogOpen] = useState(false)\n  const [mouseFollowDialogOpen, setMouseFollowDialogOpen] = useState(false)\n  const [staticDialogOpen, setStaticDialogOpen] = useState(false)\n  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false)\n  const [showMouseTracker, setShowMouseTracker] = useState(false)\n  const [showMouseInfo, setShowMouseInfo] = useState(false)\n\n  return (\n    <div className={`space-y-4 p-6 ${className}`}>\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">تجربة الشاشات المنبثقة مع التمرير المحسن</h2>\n        <p className=\"text-gray-600\">اختبر التمرير بعجلة الماوس داخل الشاشات المنبثقة لرؤية المحتوى كاملاً</p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {/* حوار قصير */}\n        <Button\n          onClick={() => setBasicDialogOpen(true)}\n          className=\"flex items-center gap-2 h-20 flex-col justify-center\"\n          variant=\"outline\"\n        >\n          <Eye className=\"w-6 h-6\" />\n          <span>حوار قصير</span>\n        </Button>\n\n        {/* حوار طويل مع تمرير */}\n        <Button\n          onClick={() => setMouseFollowDialogOpen(true)}\n          className=\"flex items-center gap-2 h-20 flex-col justify-center\"\n          variant=\"outline\"\n        >\n          <Move className=\"w-6 h-6\" />\n          <span>حوار طويل مع تمرير</span>\n        </Button>\n\n        {/* حوار الإعدادات */}\n        <Button\n          onClick={() => setSettingsDialogOpen(true)}\n          className=\"flex items-center gap-2 h-20 flex-col justify-center\"\n          variant=\"outline\"\n        >\n          <Settings className=\"w-6 h-6\" />\n          <span>إعدادات متقدمة</span>\n        </Button>\n      </div>\n\n      {/* حوار قصير */}\n      <Dialog\n        open={basicDialogOpen}\n        onOpenChange={setBasicDialogOpen}\n        enableScrollInteraction={true}\n        maxHeight=\"80vh\"\n      >\n        <DialogContent className=\"max-w-md\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Eye className=\"w-5 h-5 text-blue-600\" />\n              حوار قصير\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6\">\n            <p className=\"text-gray-600 mb-4\">\n              هذا حوار قصير لا يحتاج تمرير. المحتوى يظهر كاملاً بدون الحاجة لعجلة الماوس.\n            </p>\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <h4 className=\"font-semibold text-blue-900 mb-2\">الميزات:</h4>\n              <ul className=\"text-sm text-blue-800 space-y-1\">\n                <li>• محتوى قصير ومباشر</li>\n                <li>• لا يحتاج تمرير</li>\n                <li>• سهل القراءة</li>\n                <li>• تفاعل سريع</li>\n              </ul>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setBasicDialogOpen(false)}>\n              إغلاق\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n\n\n      {/* حوار طويل مع تمرير */}\n      <Dialog\n        open={mouseFollowDialogOpen}\n        onOpenChange={setMouseFollowDialogOpen}\n        enableScrollInteraction={true}\n        maxHeight=\"70vh\"\n      >\n        <DialogContent className=\"max-w-lg\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Move className=\"w-5 h-5 text-purple-600\" />\n              حوار طويل مع تمرير محسن\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6 space-y-4\">\n            <p className=\"text-gray-600\">\n              هذا حوار طويل يحتوي على محتوى كثير. استخدم عجلة الماوس للتمرير لأعلى وأسفل لرؤية المحتوى كاملاً.\n            </p>\n\n            {/* محتوى طويل للاختبار */}\n            {[1, 2, 3, 4, 5, 6, 7, 8].map((section) => (\n              <div key={section} className=\"bg-purple-50 p-4 rounded-lg\">\n                <h4 className=\"font-semibold text-purple-900 mb-2\">القسم {section}</h4>\n                <p className=\"text-sm text-purple-800 mb-2\">\n                  هذا نص تجريبي للقسم رقم {section}. يحتوي على معلومات مهمة تحتاج للتمرير لرؤيتها.\n                </p>\n                <ul className=\"text-sm text-purple-700 space-y-1\">\n                  <li>• نقطة مهمة رقم 1 في القسم {section}</li>\n                  <li>• نقطة مهمة رقم 2 في القسم {section}</li>\n                  <li>• نقطة مهمة رقم 3 في القسم {section}</li>\n                </ul>\n              </div>\n            ))}\n\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <h4 className=\"font-semibold text-blue-900 mb-2\">💡 تعليمات التمرير:</h4>\n              <ol className=\"text-sm text-blue-700 space-y-1\">\n                <li>1. استخدم عجلة الماوس للتمرير لأعلى وأسفل</li>\n                <li>2. يمكنك أيضاً استخدام شريط التمرير على الجانب</li>\n                <li>3. التمرير سلس ومحسن للأداء</li>\n                <li>4. المحتوى محمي من التمرير خارج الحوار</li>\n              </ol>\n            </div>\n\n            <div className=\"bg-green-50 p-4 rounded-lg\">\n              <h4 className=\"font-semibold text-green-900 mb-2\">✨ الميزات المحسنة:</h4>\n              <ul className=\"text-sm text-green-700 space-y-1\">\n                <li>• شريط تمرير مخصص وجميل</li>\n                <li>• تمرير سلس ومرن</li>\n                <li>• حماية من التمرير خارج الحوار</li>\n                <li>• تحسين للأجهزة اللمسية</li>\n                <li>• دعم جميع المتصفحات</li>\n              </ul>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setMouseFollowDialogOpen(false)}\n            >\n              إلغاء\n            </Button>\n            <Button onClick={() => setMouseFollowDialogOpen(false)}>\n              موافق\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار الإعدادات */}\n      <Dialog\n        open={settingsDialogOpen}\n        onOpenChange={setSettingsDialogOpen}\n        enableScrollInteraction={true}\n        maxHeight=\"85vh\"\n      >\n        <DialogContent className=\"max-w-2xl\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Settings className=\"w-5 h-5 text-indigo-600\" />\n              إعدادات التمرير المحسن\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6 space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-gray-900\">الخصائص المتاحة</h4>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                    <span className=\"text-sm font-medium\">enableScrollInteraction</span>\n                    <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">true</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                    <span className=\"text-sm font-medium\">maxHeight</span>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">90vh</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                    <span className=\"text-sm font-medium\">smooth-scroll</span>\n                    <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">CSS</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-gray-900\">التحسينات</h4>\n                <div className=\"space-y-2 text-sm text-gray-600\">\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span>تمرير سلس بعجلة الماوس</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span>شريط تمرير مخصص وجميل</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                    <span>حماية من التمرير خارج الحوار</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                    <span>دعم الأجهزة اللمسية</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-indigo-50 p-4 rounded-lg\">\n              <h4 className=\"font-semibold text-indigo-900 mb-2\">💡 كيفية الاستخدام</h4>\n              <ul className=\"text-sm text-indigo-800 space-y-1\">\n                <li>• استخدم عجلة الماوس للتمرير داخل الحوار</li>\n                <li>• يمكنك سحب شريط التمرير للتنقل السريع</li>\n                <li>• التمرير محمي من التأثير على الصفحة الخلفية</li>\n                <li>• يعمل على جميع الأجهزة والمتصفحات</li>\n              </ul>\n            </div>\n\n            {/* محتوى إضافي للاختبار */}\n            <div className=\"space-y-4\">\n              <h4 className=\"font-semibold text-gray-900\">أمثلة إضافية للتمرير</h4>\n              {[1, 2, 3, 4, 5].map((item) => (\n                <div key={item} className=\"bg-gray-50 p-4 rounded-lg\">\n                  <h5 className=\"font-medium text-gray-800 mb-2\">مثال {item}</h5>\n                  <p className=\"text-sm text-gray-600\">\n                    هذا مثال رقم {item} لاختبار التمرير. يحتوي على نص كافي لإظهار كيفية عمل التمرير السلس داخل الحوار.\n                    يمكنك استخدام عجلة الماوس أو شريط التمرير للتنقل بين هذه الأمثلة بسهولة.\n                  </p>\n                </div>\n              ))}\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setSettingsDialogOpen(false)}>\n              فهمت\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAYe,SAAS,WAAW,EAAE,SAAS,EAAmB;;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,6LAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;;0BAC1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAG/B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,mBAAmB;wBAClC,WAAU;wBACV,SAAQ;;0CAER,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;kCAIR,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,yBAAyB;wBACxC,WAAU;wBACV,SAAQ;;0CAER,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;kCAIR,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,sBAAsB;wBACrC,WAAU;wBACV,SAAQ;;0CAER,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,yBAAyB;gBACzB,WAAU;0BAEV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;;;;;;sCAI7C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAIV,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,mBAAmB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;0BAUxD,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,yBAAyB;gBACzB,WAAU;0BAEV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;;;;;;sCAIhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;gCAK5B;oCAAC;oCAAG;oCAAG;oCAAG;oCAAG;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,wBAC7B,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDAAG,WAAU;;oDAAqC;oDAAO;;;;;;;0DAC1D,6LAAC;gDAAE,WAAU;;oDAA+B;oDACjB;oDAAQ;;;;;;;0DAEnC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;;4DAAG;4DAA4B;;;;;;;kEAChC,6LAAC;;4DAAG;4DAA4B;;;;;;;kEAChC,6LAAC;;4DAAG;4DAA4B;;;;;;;;;;;;;;uCAR1B;;;;;8CAaZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAIV,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,yBAAyB;8CACzC;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,yBAAyB;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,yBAAyB;gBACzB,WAAU;0BAEV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAU;8EAAwD;;;;;;;;;;;;sEAE1E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAU;8EAAsD;;;;;;;;;;;;sEAExE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAU;8EAA0D;;;;;;;;;;;;;;;;;;;;;;;;sDAKhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAKR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;wCAC3C;4CAAC;4CAAG;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;gDAAe,WAAU;;kEACxB,6LAAC;wDAAG,WAAU;;4DAAiC;4DAAM;;;;;;;kEACrD,6LAAC;wDAAE,WAAU;;4DAAwB;4DACrB;4DAAK;;;;;;;;+CAHb;;;;;;;;;;;;;;;;;sCAUhB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,sBAAsB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjE;GApPwB;KAAA", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // This interface extends the base input props\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-11 w-full rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-sm font-medium text-secondary-700 transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-400 focus-visible:outline-none focus-visible:border-primary-500 focus-visible:ring-4 focus-visible:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 hover:border-secondary-300\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gbACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {\n  // This interface extends the base label props\n}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = \"Label\"\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  // This interface extends the base textarea props\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog-examples.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>T<PERSON>le, DialogFooter } from './dialog'\nimport { Button } from './button'\nimport { Card, CardContent, CardHeader, CardTitle } from './card'\nimport { Input } from './input'\nimport { Label } from './label'\nimport { Textarea } from './textarea'\nimport { \n  User, \n  Mail, \n  Phone, \n  MapPin, \n  Calendar, \n  CreditCard, \n  Settings, \n  Bell,\n  Shield,\n  Palette,\n  Database,\n  FileText,\n  Image,\n  Video,\n  Music,\n  Download,\n  Upload,\n  Share,\n  Heart,\n  Star,\n  MessageCircle\n} from 'lucide-react'\n\ninterface DialogExamplesProps {\n  className?: string\n}\n\nexport default function DialogExamples({ className }: DialogExamplesProps) {\n  const [profileDialogOpen, setProfileDialogOpen] = useState(false)\n  const [formDialogOpen, setFormDialogOpen] = useState(false)\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)\n  const [mediaD<PERSON><PERSON><PERSON><PERSON>, setMediaDialogOpen] = useState(false)\n  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false)\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      <div className=\"text-center\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">أمثلة متنوعة للشاشات المنبثقة</h3>\n        <p className=\"text-gray-600\">تجربة أنواع مختلفة من المحتوى مع تتبع الماوس</p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {/* حوار الملف الشخصي */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setProfileDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-blue-900\">\n              <User className=\"w-5 h-5\" />\n              الملف الشخصي\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">عرض وتحرير معلومات المستخدم</p>\n          </CardContent>\n        </Card>\n\n        {/* حوار النموذج */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setFormDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-green-900\">\n              <FileText className=\"w-5 h-5\" />\n              نموذج تفاعلي\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">نموذج مع حقول متعددة</p>\n          </CardContent>\n        </Card>\n\n        {/* حوار التأكيد */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setConfirmDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-red-900\">\n              <Shield className=\"w-5 h-5\" />\n              تأكيد العملية\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">حوار تأكيد بسيط</p>\n          </CardContent>\n        </Card>\n\n        {/* حوار الوسائط */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setMediaDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-purple-900\">\n              <Image className=\"w-5 h-5\" />\n              معرض الوسائط\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">عرض الصور والفيديوهات</p>\n          </CardContent>\n        </Card>\n\n        {/* حوار الإعدادات */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setSettingsDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-indigo-900\">\n              <Settings className=\"w-5 h-5\" />\n              الإعدادات\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">إعدادات التطبيق المتقدمة</p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* حوار الملف الشخصي */}\n      <Dialog\n        open={profileDialogOpen}\n        onOpenChange={setProfileDialogOpen}\n        enableScrollInteraction={true}\n        maxHeight=\"80vh\"\n      >\n        <DialogContent className=\"max-w-md\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <User className=\"w-5 h-5 text-blue-600\" />\n              الملف الشخصي\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6 space-y-4\">\n            <div className=\"flex items-center gap-4\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center\">\n                <User className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">أحمد محمد</h3>\n                <p className=\"text-sm text-gray-600\">مطور واجهات أمامية</p>\n              </div>\n            </div>\n            \n            <div className=\"space-y-3\">\n              <div className=\"flex items-center gap-3\">\n                <Mail className=\"w-4 h-4 text-gray-500\" />\n                <span className=\"text-sm\"><EMAIL></span>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <Phone className=\"w-4 h-4 text-gray-500\" />\n                <span className=\"text-sm\">+962 79 123 4567</span>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <MapPin className=\"w-4 h-4 text-gray-500\" />\n                <span className=\"text-sm\">عمان، الأردن</span>\n              </div>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setProfileDialogOpen(false)}>\n              إغلاق\n            </Button>\n            <Button>تحرير</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار النموذج */}\n      <Dialog\n        open={formDialogOpen}\n        onOpenChange={setFormDialogOpen}\n        enableScrollInteraction={true}\n        maxHeight=\"85vh\"\n      >\n        <DialogContent className=\"max-w-lg\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <FileText className=\"w-5 h-5 text-green-600\" />\n              إضافة عضو جديد\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6 space-y-4\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"firstName\">الاسم الأول</Label>\n                <Input id=\"firstName\" placeholder=\"أدخل الاسم الأول\" />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"lastName\">الاسم الأخير</Label>\n                <Input id=\"lastName\" placeholder=\"أدخل الاسم الأخير\" />\n              </div>\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">البريد الإلكتروني</Label>\n              <Input id=\"email\" type=\"email\" placeholder=\"<EMAIL>\" />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"phone\">رقم الهاتف</Label>\n              <Input id=\"phone\" placeholder=\"+962 79 123 4567\" />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"notes\">ملاحظات</Label>\n              <Textarea id=\"notes\" placeholder=\"أضف أي ملاحظات إضافية...\" rows={3} />\n            </div>\n          </div>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setFormDialogOpen(false)}>\n              إلغاء\n            </Button>\n            <Button>حفظ</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار التأكيد */}\n      <Dialog\n        open={confirmDialogOpen}\n        onOpenChange={setConfirmDialogOpen}\n        enableScrollInteraction={false}\n        maxHeight=\"60vh\"\n      >\n        <DialogContent className=\"max-w-sm\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Shield className=\"w-5 h-5 text-red-600\" />\n              تأكيد الحذف\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6\">\n            <p className=\"text-gray-600\">\n              هل أنت متأكد من أنك تريد حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.\n            </p>\n          </div>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setConfirmDialogOpen(false)}>\n              إلغاء\n            </Button>\n            <Button variant=\"destructive\">حذف</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار الوسائط */}\n      <Dialog\n        open={mediaDialogOpen}\n        onOpenChange={setMediaDialogOpen}\n        enableScrollInteraction={true}\n        maxHeight=\"90vh\"\n      >\n        <DialogContent className=\"max-w-2xl\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Image className=\"w-5 h-5 text-purple-600\" />\n              معرض الوسائط\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-3 gap-4\">\n              {[1, 2, 3, 4, 5, 6].map((item) => (\n                <div key={item} className=\"aspect-square bg-gray-100 rounded-lg flex items-center justify-center\">\n                  <Image className=\"w-8 h-8 text-gray-400\" />\n                </div>\n              ))}\n            </div>\n            \n            <div className=\"flex justify-center gap-4 mt-6\">\n              <Button variant=\"outline\" size=\"sm\">\n                <Upload className=\"w-4 h-4 mr-2\" />\n                رفع\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <Download className=\"w-4 h-4 mr-2\" />\n                تحميل\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <Share className=\"w-4 h-4 mr-2\" />\n                مشاركة\n              </Button>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setMediaDialogOpen(false)}>\n              إغلاق\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار الإعدادات */}\n      <Dialog\n        open={settingsDialogOpen}\n        onOpenChange={setSettingsDialogOpen}\n        enableScrollInteraction={true}\n        maxHeight=\"85vh\"\n      >\n        <DialogContent className=\"max-w-3xl\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Settings className=\"w-5 h-5 text-indigo-600\" />\n              إعدادات التطبيق\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-gray-900 flex items-center gap-2\">\n                  <Bell className=\"w-4 h-4\" />\n                  الإشعارات\n                </h4>\n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center gap-2\">\n                    <input type=\"checkbox\" className=\"rounded\" />\n                    <span className=\"text-sm\">إشعارات البريد الإلكتروني</span>\n                  </label>\n                  <label className=\"flex items-center gap-2\">\n                    <input type=\"checkbox\" className=\"rounded\" />\n                    <span className=\"text-sm\">إشعارات الهاتف</span>\n                  </label>\n                </div>\n              </div>\n              \n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-gray-900 flex items-center gap-2\">\n                  <Palette className=\"w-4 h-4\" />\n                  المظهر\n                </h4>\n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center gap-2\">\n                    <input type=\"radio\" name=\"theme\" className=\"rounded\" />\n                    <span className=\"text-sm\">فاتح</span>\n                  </label>\n                  <label className=\"flex items-center gap-2\">\n                    <input type=\"radio\" name=\"theme\" className=\"rounded\" />\n                    <span className=\"text-sm\">داكن</span>\n                  </label>\n                </div>\n              </div>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setSettingsDialogOpen(false)}>\n              إلغاء\n            </Button>\n            <Button>حفظ الإعدادات</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AAqCe,SAAS,eAAe,EAAE,SAAS,EAAuB;;IACvE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAG/B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,qBAAqB;;0CACrG,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIhC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAKzC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,kBAAkB;;0CAClG,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIpC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAKzC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,qBAAqB;;0CACrG,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIlC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAKzC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,mBAAmB;;0CACnG,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIjC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAKzC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,sBAAsB;;0CACtG,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIpC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,yBAAyB;gBACzB,WAAU;0BAEV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;;;;;;sCAI9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAIzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAIhC,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,qBAAqB;8CAAQ;;;;;;8CAGtE,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAMd,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,yBAAyB;gBACzB,WAAU;0BAEV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;;;;;;sCAInD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAY,aAAY;;;;;;;;;;;;sDAEpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAW,aAAY;;;;;;;;;;;;;;;;;;8CAIrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CAAC,IAAG;4CAAQ,MAAK;4CAAQ,aAAY;;;;;;;;;;;;8CAG7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CAAC,IAAG;4CAAQ,aAAY;;;;;;;;;;;;8CAGhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,IAAG;4CAAQ,aAAY;4CAA2B,MAAM;;;;;;;;;;;;;;;;;;sCAGtE,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,kBAAkB;8CAAQ;;;;;;8CAGnE,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAMd,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,yBAAyB;gBACzB,WAAU;0BAEV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;;;;;;sCAI/C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;sCAI/B,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,qBAAqB;8CAAQ;;;;;;8CAGtE,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAMpC,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,yBAAyB;gBACzB,WAAU;0BAEV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;;;;;;sCAIjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAC,qBACvB,6LAAC;4CAAe,WAAU;sDACxB,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;2CADT;;;;;;;;;;8CAMd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGrC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAKxC,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,mBAAmB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;0BAQxD,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,yBAAyB;gBACzB,WAAU;0BAEV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEAAM,MAAK;gEAAW,WAAU;;;;;;0EACjC,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEAAM,MAAK;gEAAW,WAAU;;;;;;0EACjC,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAKhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEAAM,MAAK;gEAAQ,MAAK;gEAAQ,WAAU;;;;;;0EAC3C,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEAAM,MAAK;gEAAQ,MAAK;gEAAQ,WAAU;;;;;;0EAC3C,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMpC,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,sBAAsB;8CAAQ;;;;;;8CAGvE,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB;GA3TwB;KAAA", "debugId": null}}, {"offset": {"line": 2549, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/dialog-test/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from 'react'\nimport DialogDemo from '@/components/ui/dialog-demo'\nimport DialogExamples from '@/components/ui/dialog-examples'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Eye } from 'lucide-react'\n\nexport default function DialogTestPage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6\">\n      <div className=\"max-w-6xl mx-auto space-y-8\">\n        {/* العنوان الرئيسي */}\n        <div className=\"text-center space-y-4\">\n          <div className=\"flex items-center justify-center gap-3\">\n            <div className=\"p-3 bg-blue-100 rounded-full\">\n              <MousePointer className=\"w-8 h-8 text-blue-600\" />\n            </div>\n            <h1 className=\"text-4xl font-bold text-gray-900\">\n              اختبار الشاشات المنبثقة مع التمرير المحسن\n            </h1>\n          </div>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            تجربة شاملة للشاشات المنبثقة المحسنة مع تمرير سلس بعجلة الماوس لرؤية المحتوى كاملاً\n          </p>\n        </div>\n\n        {/* بطاقات الميزات */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card className=\"border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"flex items-center gap-2 text-blue-900\">\n                <MousePointer className=\"w-5 h-5\" />\n                تمرير بعجلة الماوس\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-blue-800\">\n                استخدم عجلة الماوس للتمرير داخل الحوار لرؤية المحتوى كاملاً\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-green-200 bg-gradient-to-br from-green-50 to-green-100\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"flex items-center gap-2 text-green-900\">\n                <Sparkles className=\"w-5 h-5\" />\n                شريط تمرير مخصص\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-green-800\">\n                شريط تمرير جميل ومخصص مع تأثيرات بصرية محسنة\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"flex items-center gap-2 text-purple-900\">\n                <Zap className=\"w-5 h-5\" />\n                تمرير محمي\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-purple-800\">\n                حماية من التمرير خارج الحوار والتأثير على الصفحة الخلفية\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"flex items-center gap-2 text-orange-900\">\n                <Eye className=\"w-5 h-5\" />\n                دعم الأجهزة اللمسية\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-orange-800\">\n                يعمل بسلاسة على الهواتف والأجهزة اللوحية\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* منطقة التجربة */}\n        <Card className=\"border-gray-200 shadow-lg\">\n          <CardHeader>\n            <CardTitle className=\"text-center text-2xl text-gray-900\">\n              منطقة التجربة التفاعلية\n            </CardTitle>\n            <p className=\"text-center text-gray-600\">\n              اختبر الأنواع المختلفة من الشاشات المنبثقة وشاهد كيف تتفاعل مع حركة الماوس\n            </p>\n          </CardHeader>\n          <CardContent>\n            <DialogDemo />\n          </CardContent>\n        </Card>\n\n        {/* أمثلة متنوعة */}\n        <Card className=\"border-gray-200 shadow-lg\">\n          <CardHeader>\n            <CardTitle className=\"text-center text-2xl text-gray-900\">\n              أمثلة متنوعة للاستخدام\n            </CardTitle>\n            <p className=\"text-center text-gray-600\">\n              تجربة أنواع مختلفة من المحتوى والتفاعلات مع الشاشات المنبثقة\n            </p>\n          </CardHeader>\n          <CardContent>\n            <DialogExamples />\n          </CardContent>\n        </Card>\n\n        {/* معلومات تقنية */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg text-gray-900\">الخصائص المتاحة</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <code className=\"text-sm font-mono\">followMouse</code>\n                  <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">boolean</span>\n                </div>\n                <p className=\"text-xs text-gray-600 pr-4\">تفعيل/إلغاء تتبع حركة الماوس</p>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <code className=\"text-sm font-mono\">centerOnOpen</code>\n                  <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">boolean</span>\n                </div>\n                <p className=\"text-xs text-gray-600 pr-4\">توسيط الحوار عند الفتح</p>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <code className=\"text-sm font-mono\">smoothTransition</code>\n                  <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">boolean</span>\n                </div>\n                <p className=\"text-xs text-gray-600 pr-4\">تفعيل الانتقالات السلسة</p>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg text-gray-900\">التحسينات التقنية</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">requestAnimationFrame</h4>\n                  <p className=\"text-sm text-gray-600\">لضمان انتقالات سلسة وأداء محسن</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Boundary Detection</h4>\n                  <p className=\"text-sm text-gray-600\">حماية من الخروج عن حدود الشاشة</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-2 h-2 bg-purple-500 rounded-full mt-2\"></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Event Optimization</h4>\n                  <p className=\"text-sm text-gray-600\">تحسين معالجة أحداث الماوس</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2\"></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Responsive Design</h4>\n                  <p className=\"text-sm text-gray-600\">يتكيف مع جميع أحجام الشاشات</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* تعليمات الاستخدام */}\n        <Card className=\"bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200\">\n          <CardHeader>\n            <CardTitle className=\"text-xl text-indigo-900\">كيفية الاستخدام</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-3\">\n                <h4 className=\"font-semibold text-indigo-900\">للمطورين:</h4>\n                <div className=\"bg-white p-4 rounded-lg border border-indigo-100\">\n                  <pre className=\"text-sm text-gray-800 overflow-x-auto\">\n{`<Dialog \n  open={isOpen}\n  onOpenChange={setIsOpen}\n  followMouse={true}\n  centerOnOpen={true}\n  smoothTransition={true}\n>\n  <DialogContent>\n    {/* المحتوى */}\n  </DialogContent>\n</Dialog>`}\n                  </pre>\n                </div>\n              </div>\n              \n              <div className=\"space-y-3\">\n                <h4 className=\"font-semibold text-indigo-900\">للمستخدمين:</h4>\n                <ol className=\"space-y-2 text-sm text-indigo-800\">\n                  <li className=\"flex items-start gap-2\">\n                    <span className=\"bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold\">1</span>\n                    <span>اضغط على أي زر لفتح الحوار</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <span className=\"bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold\">2</span>\n                    <span>حرك الماوس لترى الحوار يتبعه</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <span className=\"bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold\">3</span>\n                    <span>جرب الأنواع المختلفة من الحوارات</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <span className=\"bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold\">4</span>\n                    <span>لاحظ كيف يبقى الحوار داخل الشاشة</span>\n                  </li>\n                </ol>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;AAQe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;sCAInD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIxC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAMzC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIpC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;sCAM1C,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAI/B,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAM3C,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAI/B,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;8BAQ7C,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAqC;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;;;;;;;sCAI3C,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,6IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;8BAKf,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAqC;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;;;;;;;sCAI3C,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,iJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAwB;;;;;;;;;;;8CAE/C,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,6LAAC;4DAAK,WAAU;sEAAsD;;;;;;;;;;;;8DAExE,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAG5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,6LAAC;4DAAK,WAAU;sEAAwD;;;;;;;;;;;;8DAE1E,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAG5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,6LAAC;4DAAK,WAAU;sEAA0D;;;;;;;;;;;;8DAE5E,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;sCAKhD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAwB;;;;;;;;;;;8CAE/C,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA0B;;;;;;;;;;;sCAEjD,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAChC,CAAC;;;;;;;;;;SAUO,CAAC;;;;;;;;;;;;;;;;;kDAKI,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAAwG;;;;;;0EACxH,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAAwG;;;;;;0EACxH,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAAwG;;;;;;0EACxH,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAAwG;;;;;;0EACxH,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B;KA1OwB", "debugId": null}}]}