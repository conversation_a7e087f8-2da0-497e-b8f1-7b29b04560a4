{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/settings/appearance-settings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Switch } from '@/components/ui/switch'\nimport { Button } from '@/components/ui/button'\n// import { Badge } from '@/components/ui/badge'\n// import { useTheme } from '@/components/theme-provider'\nimport {\n  Palette,\n  Type,\n  Image,\n  Monitor,\n  Sun,\n  Moon,\n  Smartphone,\n  Eye,\n  RotateCcw,\n  Upload,\n  Sparkles\n} from 'lucide-react'\n\ninterface AppearanceSettingsProps {\n  settings: any\n  onChange: (settings: any) => void\n  canEdit: boolean\n}\n\ninterface AppearanceSettingsData {\n  // الثيم والألوان\n  theme: 'light' | 'dark' | 'system'\n  primaryColor: string\n  secondaryColor: string\n  accentColor: string\n  backgroundColor: string\n  textColor: string\n  \n  // الخطوط\n  fontFamily: string\n  fontSize: string\n  fontWeight: string\n  lineHeight: string\n  \n  // الشعار والعلامة التجارية\n  logo: string\n  favicon: string\n  brandName: string\n  brandColors: {\n    primary: string\n    secondary: string\n  }\n  \n  // تخصيص الواجهة\n  sidebarStyle: 'default' | 'compact' | 'minimal'\n  headerStyle: 'default' | 'compact' | 'transparent'\n  cardStyle: 'default' | 'bordered' | 'shadow' | 'flat'\n  buttonStyle: 'default' | 'rounded' | 'square'\n  \n  // إعدادات الاستجابة\n  enableAnimations: boolean\n  enableTransitions: boolean\n  enableShadows: boolean\n  enableGradients: boolean\n  \n  // إعدادات إضافية\n  customCSS: string\n  enableCustomCSS: boolean\n}\n\nconst defaultSettings: AppearanceSettingsData = {\n  theme: 'light',\n  primaryColor: '#3b82f6',\n  secondaryColor: '#64748b',\n  accentColor: '#f59e0b',\n  backgroundColor: '#ffffff',\n  textColor: '#1f2937',\n  fontFamily: 'Cairo',\n  fontSize: '14px',\n  fontWeight: 'normal',\n  lineHeight: '1.5',\n  logo: '',\n  favicon: '',\n  brandName: 'ديوان آل أبو علوش',\n  brandColors: {\n    primary: '#3b82f6',\n    secondary: '#64748b'\n  },\n  sidebarStyle: 'default',\n  headerStyle: 'default',\n  cardStyle: 'default',\n  buttonStyle: 'default',\n  enableAnimations: true,\n  enableTransitions: true,\n  enableShadows: true,\n  enableGradients: false,\n  customCSS: '',\n  enableCustomCSS: false\n}\n\nconst colorPresets = [\n  { name: 'الأزرق الافتراضي', primary: '#3b82f6', secondary: '#64748b' },\n  { name: 'الأخضر الطبيعي', primary: '#10b981', secondary: '#6b7280' },\n  { name: 'البنفسجي الملكي', primary: '#8b5cf6', secondary: '#6b7280' },\n  { name: 'الأحمر الكلاسيكي', primary: '#ef4444', secondary: '#6b7280' },\n  { name: 'البرتقالي الدافئ', primary: '#f97316', secondary: '#6b7280' },\n  { name: 'الوردي الناعم', primary: '#ec4899', secondary: '#6b7280' },\n  { name: 'الذهبي الفاخر', primary: '#f59e0b', secondary: '#78716c' },\n  { name: 'الأزرق الداكن', primary: '#1e40af', secondary: '#4b5563' }\n]\n\nconst fontFamilies = [\n  { value: 'Cairo', label: 'Cairo (الافتراضي)' },\n  { value: 'Almarai', label: 'Almarai' },\n  { value: 'Tajawal', label: 'Tajawal' },\n  { value: 'Amiri', label: 'Amiri' },\n  { value: 'Scheherazade', label: 'Scheherazade' },\n  { value: 'Noto Sans Arabic', label: 'Noto Sans Arabic' },\n  { value: 'IBM Plex Sans Arabic', label: 'IBM Plex Sans Arabic' }\n]\n\nconst fontSizes = [\n  { value: '12px', label: 'صغير (12px)' },\n  { value: '14px', label: 'متوسط (14px)' },\n  { value: '16px', label: 'كبير (16px)' },\n  { value: '18px', label: 'كبير جداً (18px)' }\n]\n\nexport default function AppearanceSettings({ settings, onChange, canEdit }: AppearanceSettingsProps) {\n  const [localSettings, setLocalSettings] = useState<AppearanceSettingsData>(defaultSettings)\n  const [previewMode, setPreviewMode] = useState(false)\n  const [uploading, setUploading] = useState<{ logo: boolean; favicon: boolean }>({ logo: false, favicon: false })\n  // const { updateSettings, applyTheme } = useTheme()\n\n  useEffect(() => {\n    if (settings) {\n      setLocalSettings({ ...defaultSettings, ...settings })\n      // تطبيق الإعدادات المحفوظة عند التحميل\n      Object.entries({ ...defaultSettings, ...settings }).forEach(([key, value]) => {\n        applyThemeChanges(key, value)\n      })\n    }\n  }, [settings])\n\n  // تطبيق الإعدادات الافتراضية عند التحميل الأول\n  useEffect(() => {\n    // تحميل الإعدادات المحفوظة أولاً\n    const savedSettings = loadFromLocalStorage()\n    const finalSettings = { ...defaultSettings, ...savedSettings }\n\n    // تطبيق الإعدادات\n    Object.entries(finalSettings).forEach(([key, value]) => {\n      applyThemeChanges(key, value)\n    })\n\n    // تحديث الحالة المحلية\n    setLocalSettings(finalSettings)\n  }, [])\n\n  const handleChange = (key: keyof AppearanceSettingsData, value: any) => {\n    const newSettings = { ...localSettings, [key]: value }\n    setLocalSettings(newSettings)\n    onChange(newSettings)\n\n    // تطبيق التغييرات فوراً في الواجهة\n    applyThemeChanges(key, value)\n\n    // حفظ في localStorage للاحتفاظ بالتغييرات\n    saveToLocalStorage(key, value)\n  }\n\n  // دالة لحفظ الإعدادات في localStorage\n  const saveToLocalStorage = (key: string, value: any) => {\n    try {\n      const savedSettings = JSON.parse(localStorage.getItem('diwan-appearance-settings') || '{}')\n      savedSettings[key] = value\n      localStorage.setItem('diwan-appearance-settings', JSON.stringify(savedSettings))\n    } catch (error) {\n      console.error('خطأ في حفظ الإعدادات:', error)\n    }\n  }\n\n  // دالة لتحميل الإعدادات من localStorage\n  const loadFromLocalStorage = () => {\n    try {\n      const savedSettings = JSON.parse(localStorage.getItem('diwan-appearance-settings') || '{}')\n      return savedSettings\n    } catch (error) {\n      console.error('خطأ في تحميل الإعدادات:', error)\n      return {}\n    }\n  }\n\n  // دالة لتطبيق التغييرات فوراً\n  const applyThemeChanges = (key: string, value: any) => {\n    const root = document.documentElement\n\n    switch (key) {\n      case 'primaryColor':\n        root.style.setProperty('--theme-primary-color', value)\n        root.style.setProperty('--primary', value)\n        break\n      case 'secondaryColor':\n        root.style.setProperty('--theme-secondary-color', value)\n        root.style.setProperty('--secondary', value)\n        break\n      case 'accentColor':\n        root.style.setProperty('--theme-accent-color', value)\n        root.style.setProperty('--accent', value)\n        break\n      case 'backgroundColor':\n        root.style.setProperty('--theme-background-color', value)\n        root.style.setProperty('--background', value)\n        break\n      case 'textColor':\n        root.style.setProperty('--theme-text-color', value)\n        root.style.setProperty('--foreground', value)\n        break\n      case 'fontFamily':\n        root.style.setProperty('--theme-font-family', value)\n        document.body.style.fontFamily = value\n        break\n      case 'fontSize':\n        root.style.setProperty('--theme-font-size', value)\n        document.body.style.fontSize = value\n        break\n      case 'fontWeight':\n        root.style.setProperty('--theme-font-weight', value)\n        document.body.style.fontWeight = value\n        break\n      case 'lineHeight':\n        root.style.setProperty('--theme-line-height', value)\n        document.body.style.lineHeight = value\n        break\n      case 'theme':\n        if (value === 'dark') {\n          document.documentElement.classList.add('dark')\n          document.documentElement.classList.add('dark-theme')\n          document.documentElement.classList.remove('light')\n        } else {\n          document.documentElement.classList.add('light')\n          document.documentElement.classList.remove('dark')\n          document.documentElement.classList.remove('dark-theme')\n        }\n        break\n      case 'brandName':\n        document.title = value || 'ديوان آل أبو علوش'\n        break\n    }\n\n    // إضافة كلاس theme-applied للتطبيق\n    document.body.classList.add('theme-applied')\n  }\n\n  const applyColorPreset = (preset: typeof colorPresets[0]) => {\n    const newSettings = {\n      ...localSettings,\n      primaryColor: preset.primary,\n      secondaryColor: preset.secondary,\n      brandColors: {\n        primary: preset.primary,\n        secondary: preset.secondary\n      }\n    }\n    setLocalSettings(newSettings)\n    onChange(newSettings)\n\n    // تطبيق التغييرات فوراً\n    applyThemeChanges('primaryColor', preset.primary)\n    applyThemeChanges('secondaryColor', preset.secondary)\n  }\n\n  const resetToDefaults = () => {\n    setLocalSettings(defaultSettings)\n    onChange(defaultSettings)\n\n    // تطبيق الإعدادات الافتراضية\n    Object.entries(defaultSettings).forEach(([key, value]) => {\n      applyThemeChanges(key, value)\n    })\n  }\n\n  // رفع الملفات\n  const handleFileUpload = async (file: File, type: 'logo' | 'favicon') => {\n    if (!canEdit) return\n\n    // التحقق من نوع الملف\n    if (!file.type.startsWith('image/')) {\n      alert('يرجى اختيار ملف صورة صحيح')\n      return\n    }\n\n    // التحقق من حجم الملف (5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت')\n      return\n    }\n\n    setUploading(prev => ({ ...prev, [type]: true }))\n\n    const formData = new FormData()\n    formData.append('file', file)\n    formData.append('type', type)\n\n    try {\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        handleChange(type, data.url)\n\n        // إشعار نجاح\n        const event = new CustomEvent('showToast', {\n          detail: { message: 'تم رفع الملف بنجاح', type: 'success' }\n        })\n        window.dispatchEvent(event)\n      } else {\n        const errorData = await response.json()\n        alert(errorData.error || 'فشل في رفع الملف')\n      }\n    } catch (error) {\n      console.error('خطأ في رفع الملف:', error)\n      alert('حدث خطأ في رفع الملف')\n    } finally {\n      setUploading(prev => ({ ...prev, [type]: false }))\n    }\n  }\n\n  // معالج اختيار الملف\n  const handleFileSelect = (type: 'logo' | 'favicon') => {\n    const input = document.createElement('input')\n    input.type = 'file'\n    input.accept = 'image/*'\n    input.onchange = (e) => {\n      const file = (e.target as HTMLInputElement).files?.[0]\n      if (file) {\n        handleFileUpload(file, type)\n      }\n    }\n    input.click()\n  }\n\n  // معالج السحب والإفلات\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n  }\n\n  const handleDrop = (e: React.DragEvent, type: 'logo' | 'favicon') => {\n    e.preventDefault()\n    e.stopPropagation()\n\n    const files = e.dataTransfer.files\n    if (files.length > 0) {\n      const file = files[0]\n      if (file.type.startsWith('image/')) {\n        handleFileUpload(file, type)\n      }\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* مؤشر المعاينة المباشرة */}\n      <Card className=\"border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 animate-pulse\">\n        <CardContent className=\"p-4\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-2 bg-green-100 rounded-lg animate-bounce\">\n              <Sparkles className=\"w-5 h-5 text-green-600\" />\n            </div>\n            <div>\n              <p className=\"text-green-800 font-semibold\">🎨 المعاينة المباشرة مفعلة</p>\n              <p className=\"text-green-700 text-sm\">\n                جميع التغييرات ستظهر فوراً في الواجهة! جرب تغيير الألوان أو الخطوط لترى النتيجة مباشرة.\n              </p>\n            </div>\n            <div className=\"mr-auto\">\n              <div className=\"flex gap-1\">\n                <div className=\"w-3 h-3 bg-blue-500 rounded-full animate-ping\"></div>\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-ping\" style={{animationDelay: '0.2s'}}></div>\n                <div className=\"w-3 h-3 bg-yellow-500 rounded-full animate-ping\" style={{animationDelay: '0.4s'}}></div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* الثيم والألوان الأساسية */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Palette className=\"w-5 h-5 text-blue-600\" />\n            الثيم والألوان\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium text-gray-700\">نمط الثيم</Label>\n            <div className=\"grid grid-cols-3 gap-3\">\n              <Button\n                variant={localSettings.theme === 'light' ? 'default' : 'outline'}\n                onClick={() => handleChange('theme', 'light')}\n                disabled={!canEdit}\n                className={`flex items-center gap-2 py-3 ${\n                  localSettings.theme === 'light'\n                    ? 'bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg'\n                    : 'hover:bg-sky-50 hover:border-sky-300'\n                }`}\n              >\n                <Sun className=\"w-4 h-4\" />\n                فاتح\n              </Button>\n              <Button\n                variant={localSettings.theme === 'dark' ? 'default' : 'outline'}\n                onClick={() => handleChange('theme', 'dark')}\n                disabled={!canEdit}\n                className={`flex items-center gap-2 py-3 ${\n                  localSettings.theme === 'dark'\n                    ? 'bg-gradient-to-r from-gray-700 to-gray-800 text-white shadow-lg'\n                    : 'hover:bg-gray-50 hover:border-gray-300'\n                }`}\n              >\n                <Moon className=\"w-4 h-4\" />\n                داكن\n              </Button>\n              <Button\n                variant={localSettings.theme === 'system' ? 'default' : 'outline'}\n                onClick={() => handleChange('theme', 'system')}\n                disabled={!canEdit}\n                className={`flex items-center gap-2 py-3 ${\n                  localSettings.theme === 'system'\n                    ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg'\n                    : 'hover:bg-purple-50 hover:border-purple-300'\n                }`}\n              >\n                <Monitor className=\"w-4 h-4\" />\n                النظام\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"space-y-3\">\n            <Label>الألوان المحددة مسبقاً</Label>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2\">\n              {colorPresets.map((preset, index) => (\n                <Button\n                  key={index}\n                  variant=\"outline\"\n                  onClick={() => applyColorPreset(preset)}\n                  disabled={!canEdit}\n                  className=\"h-auto p-3 flex flex-col items-center gap-2\"\n                >\n                  <div className=\"flex gap-1\">\n                    <div\n                      className=\"w-4 h-4 rounded-full border\"\n                      style={{ backgroundColor: preset.primary }}\n                    />\n                    <div\n                      className=\"w-4 h-4 rounded-full border\"\n                      style={{ backgroundColor: preset.secondary }}\n                    />\n                  </div>\n                  <span className=\"text-xs text-center\">{preset.name}</span>\n                </Button>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"primaryColor\">اللون الأساسي</Label>\n              <div className=\"flex gap-2\">\n                <Input\n                  id=\"primaryColor\"\n                  type=\"color\"\n                  value={localSettings.primaryColor}\n                  onChange={(e) => handleChange('primaryColor', e.target.value)}\n                  disabled={!canEdit}\n                  className=\"w-12 h-10 p-1 border rounded\"\n                />\n                <Input\n                  value={localSettings.primaryColor}\n                  onChange={(e) => handleChange('primaryColor', e.target.value)}\n                  disabled={!canEdit}\n                  placeholder=\"#3b82f6\"\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"secondaryColor\">اللون الثانوي</Label>\n              <div className=\"flex gap-2\">\n                <Input\n                  id=\"secondaryColor\"\n                  type=\"color\"\n                  value={localSettings.secondaryColor}\n                  onChange={(e) => handleChange('secondaryColor', e.target.value)}\n                  disabled={!canEdit}\n                  className=\"w-12 h-10 p-1 border rounded\"\n                />\n                <Input\n                  value={localSettings.secondaryColor}\n                  onChange={(e) => handleChange('secondaryColor', e.target.value)}\n                  disabled={!canEdit}\n                  placeholder=\"#64748b\"\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"accentColor\">لون التمييز</Label>\n              <div className=\"flex gap-2\">\n                <Input\n                  id=\"accentColor\"\n                  type=\"color\"\n                  value={localSettings.accentColor}\n                  onChange={(e) => handleChange('accentColor', e.target.value)}\n                  disabled={!canEdit}\n                  className=\"w-12 h-10 p-1 border rounded\"\n                />\n                <Input\n                  value={localSettings.accentColor}\n                  onChange={(e) => handleChange('accentColor', e.target.value)}\n                  disabled={!canEdit}\n                  placeholder=\"#f59e0b\"\n                />\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* الخطوط والنصوص */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Type className=\"w-5 h-5 text-green-600\" />\n            الخطوط والنصوص\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>نوع الخط</Label>\n              <Select\n                value={localSettings.fontFamily}\n                onValueChange={(value) => handleChange('fontFamily', value)}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"اختر نوع الخط\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {fontFamilies.map((font) => (\n                    <SelectItem key={font.value} value={font.value}>\n                      <span style={{ fontFamily: font.value }}>{font.label}</span>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>حجم الخط</Label>\n              <Select\n                value={localSettings.fontSize}\n                onValueChange={(value) => handleChange('fontSize', value)}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"اختر حجم الخط\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {fontSizes.map((size) => (\n                    <SelectItem key={size.value} value={size.value}>\n                      {size.label}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label>معاينة الخط</Label>\n            <div\n              className=\"p-4 border rounded-lg bg-gray-50\"\n              style={{\n                fontFamily: localSettings.fontFamily,\n                fontSize: localSettings.fontSize,\n                lineHeight: localSettings.lineHeight\n              }}\n            >\n              <p className=\"text-lg font-bold mb-2\">ديوان آل أبو علوش</p>\n              <p className=\"mb-2\">هذا نص تجريبي لمعاينة الخط المختار. يمكنك رؤية كيف سيظهر النص في النظام.</p>\n              <p className=\"text-sm text-gray-600\">الأرقام: 1234567890</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* الشعار والعلامة التجارية */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Image className=\"w-5 h-5 text-purple-600\" />\n            الشعار والعلامة التجارية\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"brandName\">اسم العلامة التجارية</Label>\n            <Input\n              id=\"brandName\"\n              value={localSettings.brandName}\n              onChange={(e) => handleChange('brandName', e.target.value)}\n              disabled={!canEdit}\n              placeholder=\"ديوان آل أبو علوش\"\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>الشعار الرئيسي</Label>\n              <div\n                className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer\"\n                onDragOver={handleDragOver}\n                onDrop={(e) => handleDrop(e, 'logo')}\n                onClick={() => handleFileSelect('logo')}\n              >\n                {localSettings.logo ? (\n                  <div className=\"space-y-2\">\n                    <img\n                      src={localSettings.logo}\n                      alt=\"الشعار\"\n                      className=\"max-h-16 mx-auto\"\n                    />\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleChange('logo', '')}\n                      disabled={!canEdit}\n                    >\n                      إزالة الشعار\n                    </Button>\n                  </div>\n                ) : (\n                  <div className=\"space-y-2\">\n                    {uploading.logo ? (\n                      <>\n                        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n                        <p className=\"text-sm text-blue-600\">جاري رفع الشعار...</p>\n                      </>\n                    ) : (\n                      <>\n                        <Upload className=\"w-8 h-8 text-gray-400 mx-auto\" />\n                        <p className=\"text-sm text-gray-600\">اسحب الشعار هنا أو انقر للرفع</p>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          disabled={!canEdit || uploading.logo}\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            handleFileSelect('logo')\n                          }}\n                        >\n                          رفع شعار\n                        </Button>\n                      </>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>أيقونة الموقع (Favicon)</Label>\n              <div\n                className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer\"\n                onDragOver={handleDragOver}\n                onDrop={(e) => handleDrop(e, 'favicon')}\n                onClick={() => handleFileSelect('favicon')}\n              >\n                {localSettings.favicon ? (\n                  <div className=\"space-y-2\">\n                    <img\n                      src={localSettings.favicon}\n                      alt=\"الأيقونة\"\n                      className=\"w-8 h-8 mx-auto\"\n                    />\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleChange('favicon', '')}\n                      disabled={!canEdit}\n                    >\n                      إزالة الأيقونة\n                    </Button>\n                  </div>\n                ) : (\n                  <div className=\"space-y-2\">\n                    {uploading.favicon ? (\n                      <>\n                        <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto\"></div>\n                        <p className=\"text-xs text-blue-600\">جاري رفع الأيقونة...</p>\n                      </>\n                    ) : (\n                      <>\n                        <Upload className=\"w-6 h-6 text-gray-400 mx-auto\" />\n                        <p className=\"text-xs text-gray-600\">32x32 px</p>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          disabled={!canEdit || uploading.favicon}\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            handleFileSelect('favicon')\n                          }}\n                        >\n                          رفع أيقونة\n                        </Button>\n                      </>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* تخصيص الواجهة */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Smartphone className=\"w-5 h-5 text-orange-600\" />\n            تخصيص الواجهة\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>نمط الشريط الجانبي</Label>\n              <Select\n                value={localSettings.sidebarStyle}\n                onValueChange={(value) => handleChange('sidebarStyle', value)}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"default\">افتراضي</SelectItem>\n                  <SelectItem value=\"compact\">مضغوط</SelectItem>\n                  <SelectItem value=\"minimal\">بسيط</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>نمط الرأس</Label>\n              <Select\n                value={localSettings.headerStyle}\n                onValueChange={(value) => handleChange('headerStyle', value)}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"default\">افتراضي</SelectItem>\n                  <SelectItem value=\"compact\">مضغوط</SelectItem>\n                  <SelectItem value=\"transparent\">شفاف</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>نمط البطاقات</Label>\n              <Select\n                value={localSettings.cardStyle}\n                onValueChange={(value) => handleChange('cardStyle', value)}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"default\">افتراضي</SelectItem>\n                  <SelectItem value=\"bordered\">محدد</SelectItem>\n                  <SelectItem value=\"shadow\">ظلال</SelectItem>\n                  <SelectItem value=\"flat\">مسطح</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>نمط الأزرار</Label>\n              <Select\n                value={localSettings.buttonStyle}\n                onValueChange={(value) => handleChange('buttonStyle', value)}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"default\">افتراضي</SelectItem>\n                  <SelectItem value=\"rounded\">دائري</SelectItem>\n                  <SelectItem value=\"square\">مربع</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>الحركات والانتقالات</Label>\n                <p className=\"text-sm text-gray-600\">تفعيل الحركات المتحركة</p>\n              </div>\n              <Switch\n                checked={localSettings.enableAnimations}\n                onCheckedChange={(checked) => handleChange('enableAnimations', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>الظلال</Label>\n                <p className=\"text-sm text-gray-600\">إضافة ظلال للعناصر</p>\n              </div>\n              <Switch\n                checked={localSettings.enableShadows}\n                onCheckedChange={(checked) => handleChange('enableShadows', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>التدرجات اللونية</Label>\n                <p className=\"text-sm text-gray-600\">استخدام التدرجات في الخلفيات</p>\n              </div>\n              <Switch\n                checked={localSettings.enableGradients}\n                onCheckedChange={(checked) => handleChange('enableGradients', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* CSS مخصص */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Eye className=\"w-5 h-5 text-red-600\" />\n            CSS مخصص\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <Label>تفعيل CSS مخصص</Label>\n              <p className=\"text-sm text-gray-600\">إضافة أنماط CSS مخصصة</p>\n            </div>\n            <Switch\n              checked={localSettings.enableCustomCSS}\n              onCheckedChange={(checked) => handleChange('enableCustomCSS', checked)}\n              disabled={!canEdit}\n            />\n          </div>\n\n          {localSettings.enableCustomCSS && (\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"customCSS\">كود CSS المخصص</Label>\n              <textarea\n                id=\"customCSS\"\n                value={localSettings.customCSS}\n                onChange={(e) => handleChange('customCSS', e.target.value)}\n                disabled={!canEdit}\n                placeholder=\"/* أضف كود CSS المخصص هنا */\"\n                className=\"w-full h-32 p-3 border rounded-lg font-mono text-sm\"\n              />\n              <p className=\"text-xs text-gray-500\">\n                تحذير: استخدم CSS مخصص بحذر. قد يؤثر على مظهر النظام.\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* أزرار الإجراءات */}\n      {canEdit && (\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex justify-between\">\n              <Button\n                variant=\"outline\"\n                onClick={resetToDefaults}\n                className=\"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300\"\n              >\n                <RotateCcw className=\"w-4 h-4 ml-2\" />\n                إعادة تعيين الافتراضي\n              </Button>\n\n              <Button\n                onClick={() => setPreviewMode(!previewMode)}\n                className=\"bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white\"\n              >\n                <Eye className=\"w-4 h-4 ml-2\" />\n                {previewMode ? 'إخفاء المعاينة' : 'معاينة التغييرات'}\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;AAChD,yDAAyD;AACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAXA;;;;;;;;;AAwEA,MAAM,kBAA0C;IAC9C,OAAO;IACP,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,SAAS;IACT,WAAW;IACX,aAAa;QACX,SAAS;QACT,WAAW;IACb;IACA,cAAc;IACd,aAAa;IACb,WAAW;IACX,aAAa;IACb,kBAAkB;IAClB,mBAAmB;IACnB,eAAe;IACf,iBAAiB;IACjB,WAAW;IACX,iBAAiB;AACnB;AAEA,MAAM,eAAe;IACnB;QAAE,MAAM;QAAoB,SAAS;QAAW,WAAW;IAAU;IACrE;QAAE,MAAM;QAAkB,SAAS;QAAW,WAAW;IAAU;IACnE;QAAE,MAAM;QAAmB,SAAS;QAAW,WAAW;IAAU;IACpE;QAAE,MAAM;QAAoB,SAAS;QAAW,WAAW;IAAU;IACrE;QAAE,MAAM;QAAoB,SAAS;QAAW,WAAW;IAAU;IACrE;QAAE,MAAM;QAAiB,SAAS;QAAW,WAAW;IAAU;IAClE;QAAE,MAAM;QAAiB,SAAS;QAAW,WAAW;IAAU;IAClE;QAAE,MAAM;QAAiB,SAAS;QAAW,WAAW;IAAU;CACnE;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAS,OAAO;IAAoB;IAC7C;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAgB,OAAO;IAAe;IAC/C;QAAE,OAAO;QAAoB,OAAO;IAAmB;IACvD;QAAE,OAAO;QAAwB,OAAO;IAAuB;CAChE;AAED,MAAM,YAAY;IAChB;QAAE,OAAO;QAAQ,OAAO;IAAc;IACtC;QAAE,OAAO;QAAQ,OAAO;IAAe;IACvC;QAAE,OAAO;QAAQ,OAAO;IAAc;IACtC;QAAE,OAAO;QAAQ,OAAO;IAAmB;CAC5C;AAEc,SAAS,mBAAmB,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAA2B;;IACjG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;QAAE,MAAM;QAAO,SAAS;IAAM;IAC9G,oDAAoD;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,UAAU;gBACZ,iBAAiB;oBAAE,GAAG,eAAe;oBAAE,GAAG,QAAQ;gBAAC;gBACnD,uCAAuC;gBACvC,OAAO,OAAO,CAAC;oBAAE,GAAG,eAAe;oBAAE,GAAG,QAAQ;gBAAC,GAAG,OAAO;oDAAC,CAAC,CAAC,KAAK,MAAM;wBACvE,kBAAkB,KAAK;oBACzB;;YACF;QACF;uCAAG;QAAC;KAAS;IAEb,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,iCAAiC;YACjC,MAAM,gBAAgB;YACtB,MAAM,gBAAgB;gBAAE,GAAG,eAAe;gBAAE,GAAG,aAAa;YAAC;YAE7D,kBAAkB;YAClB,OAAO,OAAO,CAAC,eAAe,OAAO;gDAAC,CAAC,CAAC,KAAK,MAAM;oBACjD,kBAAkB,KAAK;gBACzB;;YAEA,uBAAuB;YACvB,iBAAiB;QACnB;uCAAG,EAAE;IAEL,MAAM,eAAe,CAAC,KAAmC;QACvD,MAAM,cAAc;YAAE,GAAG,aAAa;YAAE,CAAC,IAAI,EAAE;QAAM;QACrD,iBAAiB;QACjB,SAAS;QAET,mCAAmC;QACnC,kBAAkB,KAAK;QAEvB,0CAA0C;QAC1C,mBAAmB,KAAK;IAC1B;IAEA,sCAAsC;IACtC,MAAM,qBAAqB,CAAC,KAAa;QACvC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gCAAgC;YACtF,aAAa,CAAC,IAAI,GAAG;YACrB,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;QACnE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,wCAAwC;IACxC,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gCAAgC;YACtF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,CAAC;QACV;IACF;IAEA,8BAA8B;IAC9B,MAAM,oBAAoB,CAAC,KAAa;QACtC,MAAM,OAAO,SAAS,eAAe;QAErC,OAAQ;YACN,KAAK;gBACH,KAAK,KAAK,CAAC,WAAW,CAAC,yBAAyB;gBAChD,KAAK,KAAK,CAAC,WAAW,CAAC,aAAa;gBACpC;YACF,KAAK;gBACH,KAAK,KAAK,CAAC,WAAW,CAAC,2BAA2B;gBAClD,KAAK,KAAK,CAAC,WAAW,CAAC,eAAe;gBACtC;YACF,KAAK;gBACH,KAAK,KAAK,CAAC,WAAW,CAAC,wBAAwB;gBAC/C,KAAK,KAAK,CAAC,WAAW,CAAC,YAAY;gBACnC;YACF,KAAK;gBACH,KAAK,KAAK,CAAC,WAAW,CAAC,4BAA4B;gBACnD,KAAK,KAAK,CAAC,WAAW,CAAC,gBAAgB;gBACvC;YACF,KAAK;gBACH,KAAK,KAAK,CAAC,WAAW,CAAC,sBAAsB;gBAC7C,KAAK,KAAK,CAAC,WAAW,CAAC,gBAAgB;gBACvC;YACF,KAAK;gBACH,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB;gBAC9C,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;gBACjC;YACF,KAAK;gBACH,KAAK,KAAK,CAAC,WAAW,CAAC,qBAAqB;gBAC5C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAC/B;YACF,KAAK;gBACH,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB;gBAC9C,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;gBACjC;YACF,KAAK;gBACH,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB;gBAC9C,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;gBACjC;YACF,KAAK;gBACH,IAAI,UAAU,QAAQ;oBACpB,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;oBACvC,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;oBACvC,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5C,OAAO;oBACL,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;oBACvC,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC1C,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5C;gBACA;YACF,KAAK;gBACH,SAAS,KAAK,GAAG,SAAS;gBAC1B;QACJ;QAEA,mCAAmC;QACnC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;IAC9B;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,cAAc;YAClB,GAAG,aAAa;YAChB,cAAc,OAAO,OAAO;YAC5B,gBAAgB,OAAO,SAAS;YAChC,aAAa;gBACX,SAAS,OAAO,OAAO;gBACvB,WAAW,OAAO,SAAS;YAC7B;QACF;QACA,iBAAiB;QACjB,SAAS;QAET,wBAAwB;QACxB,kBAAkB,gBAAgB,OAAO,OAAO;QAChD,kBAAkB,kBAAkB,OAAO,SAAS;IACtD;IAEA,MAAM,kBAAkB;QACtB,iBAAiB;QACjB,SAAS;QAET,6BAA6B;QAC7B,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACnD,kBAAkB,KAAK;QACzB;IACF;IAEA,cAAc;IACd,MAAM,mBAAmB,OAAO,MAAY;QAC1C,IAAI,CAAC,SAAS;QAEd,sBAAsB;QACtB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,MAAM;YACN;QACF;QAEA,4BAA4B;QAC5B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,MAAM;YACN;QACF;QAEA,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAK,CAAC;QAE/C,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,aAAa,MAAM,KAAK,GAAG;gBAE3B,aAAa;gBACb,MAAM,QAAQ,IAAI,YAAY,aAAa;oBACzC,QAAQ;wBAAE,SAAS;wBAAsB,MAAM;oBAAU;gBAC3D;gBACA,OAAO,aAAa,CAAC;YACvB,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,UAAU,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR,SAAU;YACR,aAAa,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAM,CAAC;QAClD;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,IAAI,GAAG;QACb,MAAM,MAAM,GAAG;QACf,MAAM,QAAQ,GAAG,CAAC;YAChB,MAAM,OAAO,AAAC,EAAE,MAAM,CAAsB,KAAK,EAAE,CAAC,EAAE;YACtD,IAAI,MAAM;gBACR,iBAAiB,MAAM;YACzB;QACF;QACA,MAAM,KAAK;IACb;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;IACnB;IAEA,MAAM,aAAa,CAAC,GAAoB;QACtC,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAClC,iBAAiB,MAAM;YACzB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA+B;;;;;;kDAC5C,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;0CAIxC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;4CAAiD,OAAO;gDAAC,gBAAgB;4CAAM;;;;;;sDAC9F,6LAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAC,gBAAgB;4CAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzG,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAIjD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAoC;;;;;;kDACrD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,KAAK,KAAK,UAAU,YAAY;gDACvD,SAAS,IAAM,aAAa,SAAS;gDACrC,UAAU,CAAC;gDACX,WAAW,CAAC,6BAA6B,EACvC,cAAc,KAAK,KAAK,UACpB,kEACA,wCACJ;;kEAEF,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG7B,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,KAAK,KAAK,SAAS,YAAY;gDACtD,SAAS,IAAM,aAAa,SAAS;gDACrC,UAAU,CAAC;gDACX,WAAW,CAAC,6BAA6B,EACvC,cAAc,KAAK,KAAK,SACpB,oEACA,0CACJ;;kEAEF,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,KAAK,KAAK,WAAW,YAAY;gDACxD,SAAS,IAAM,aAAa,SAAS;gDACrC,UAAU,CAAC;gDACX,WAAW,CAAC,6BAA6B,EACvC,cAAc,KAAK,KAAK,WACpB,wEACA,8CACJ;;kEAEF,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;0CAMrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAQ;gDACR,SAAS,IAAM,iBAAiB;gDAChC,UAAU,CAAC;gDACX,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,OAAO,OAAO;gEAAC;;;;;;0EAE3C,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,OAAO,SAAS;gEAAC;;;;;;;;;;;;kEAG/C,6LAAC;wDAAK,WAAU;kEAAuB,OAAO,IAAI;;;;;;;+CAhB7C;;;;;;;;;;;;;;;;0CAsBb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,YAAY;wDACjC,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC5D,UAAU,CAAC;wDACX,WAAU;;;;;;kEAEZ,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,cAAc,YAAY;wDACjC,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC5D,UAAU,CAAC;wDACX,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,cAAc;wDACnC,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDAC9D,UAAU,CAAC;wDACX,WAAU;;;;;;kEAEZ,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,cAAc,cAAc;wDACnC,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDAC9D,UAAU,CAAC;wDACX,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,WAAW;wDAChC,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC3D,UAAU,CAAC;wDACX,WAAU;;;;;;kEAEZ,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,cAAc,WAAW;wDAChC,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC3D,UAAU,CAAC;wDACX,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA2B;;;;;;;;;;;;kCAI/C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,UAAU;gDAC/B,eAAe,CAAC,QAAU,aAAa,cAAc;gDACrD,UAAU,CAAC;;kEAEX,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;kEACX,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,qIAAA,CAAA,aAAU;gEAAkB,OAAO,KAAK,KAAK;0EAC5C,cAAA,6LAAC;oEAAK,OAAO;wEAAE,YAAY,KAAK,KAAK;oEAAC;8EAAI,KAAK,KAAK;;;;;;+DADrC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;kDAQnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,QAAQ;gDAC7B,eAAe,CAAC,QAAU,aAAa,YAAY;gDACnD,UAAU,CAAC;;kEAEX,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;kEACX,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,qIAAA,CAAA,aAAU;gEAAkB,OAAO,KAAK,KAAK;0EAC3C,KAAK,KAAK;+DADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,YAAY,cAAc,UAAU;4CACpC,UAAU,cAAc,QAAQ;4CAChC,YAAY,cAAc,UAAU;wCACtC;;0DAEA,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAO;;;;;;0DACpB,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAIjD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO,cAAc,SAAS;wCAC9B,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;wCACzD,UAAU,CAAC;wCACX,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDACC,WAAU;gDACV,YAAY;gDACZ,QAAQ,CAAC,IAAM,WAAW,GAAG;gDAC7B,SAAS,IAAM,iBAAiB;0DAE/B,cAAc,IAAI,iBACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,KAAK,cAAc,IAAI;4DACvB,KAAI;4DACJ,WAAU;;;;;;sEAEZ,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,aAAa,QAAQ;4DACpC,UAAU,CAAC;sEACZ;;;;;;;;;;;yEAKH,6LAAC;oDAAI,WAAU;8DACZ,UAAU,IAAI,iBACb;;0EACE,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;qFAGvC;;0EACE,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,UAAU,CAAC,WAAW,UAAU,IAAI;gEACpC,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,iBAAiB;gEACnB;0EACD;;;;;;;;;;;;;;;;;;;;;;;;kDAUb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDACC,WAAU;gDACV,YAAY;gDACZ,QAAQ,CAAC,IAAM,WAAW,GAAG;gDAC7B,SAAS,IAAM,iBAAiB;0DAE/B,cAAc,OAAO,iBACpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,KAAK,cAAc,OAAO;4DAC1B,KAAI;4DACJ,WAAU;;;;;;sEAEZ,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,aAAa,WAAW;4DACvC,UAAU,CAAC;sEACZ;;;;;;;;;;;yEAKH,6LAAC;oDAAI,WAAU;8DACZ,UAAU,OAAO,iBAChB;;0EACE,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;qFAGvC;;0EACE,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,UAAU,CAAC,WAAW,UAAU,OAAO;gEACvC,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,iBAAiB;gEACnB;0EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAcnB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,iNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAItD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,YAAY;gDACjC,eAAe,CAAC,QAAU,aAAa,gBAAgB;gDACvD,UAAU,CAAC;;kEAEX,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAKlC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,WAAW;gDAChC,eAAe,CAAC,QAAU,aAAa,eAAe;gDACtD,UAAU,CAAC;;kEAEX,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,SAAS;gDAC9B,eAAe,CAAC,QAAU,aAAa,aAAa;gDACpD,UAAU,CAAC;;kEAEX,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;;;;;;;;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,WAAW;gDAChC,eAAe,CAAC,QAAU,aAAa,eAAe;gDACtD,UAAU,CAAC;;kEAEX,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,gBAAgB;gDACvC,iBAAiB,CAAC,UAAY,aAAa,oBAAoB;gDAC/D,UAAU,CAAC;;;;;;;;;;;;kDAIf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,aAAa;gDACpC,iBAAiB,CAAC,UAAY,aAAa,iBAAiB;gDAC5D,UAAU,CAAC;;;;;;;;;;;;kDAIf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,eAAe;gDACtC,iBAAiB,CAAC,UAAY,aAAa,mBAAmB;gDAC9D,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;;;;;;kCAI5C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,cAAc,eAAe;wCACtC,iBAAiB,CAAC,UAAY,aAAa,mBAAmB;wCAC9D,UAAU,CAAC;;;;;;;;;;;;4BAId,cAAc,eAAe,kBAC5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,6LAAC;wCACC,IAAG;wCACH,OAAO,cAAc,SAAS;wCAC9B,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;wCACzD,UAAU,CAAC;wCACX,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;YAS5C,yBACC,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIxC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCACd,cAAc,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;GA/xBwB;KAAA", "debugId": null}}]}