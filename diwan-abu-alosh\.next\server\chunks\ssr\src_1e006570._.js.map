{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/reports/ReportExporter.tsx"], "sourcesContent": ["'use client'\n\nimport { formatCurrency } from '@/lib/utils'\n\n// دالة تصدير PDF متقدمة\nexport const exportToPDF = async (data: any, reportType: string) => {\n  try {\n    const html2pdf = (await import('html2pdf.js')).default as any\n\n    let htmlContent = ''\n\n    switch (reportType) {\n      case 'members-list':\n        htmlContent = generateMembersListHTML(data)\n        break\n      case 'member-detail':\n        htmlContent = generateMemberDetailHTML(data)\n        break\n      case 'member-statement':\n        htmlContent = generateMemberStatementHTML(data)\n        break\n      case 'incomes-detailed':\n        htmlContent = generateIncomesDetailedHTML(data)\n        break\n      case 'incomes-summary':\n        htmlContent = generateIncomesSummaryHTML(data)\n        break\n      case 'incomes-by-type':\n        htmlContent = generateIncomesByTypeHTML(data)\n        break\n      case 'incomes-by-member':\n        htmlContent = generateIncomesByMemberHTML(data)\n        break\n      case 'expenses-detailed':\n        htmlContent = generateExpensesDetailedHTML(data)\n        break\n      case 'expenses-summary':\n        htmlContent = generateExpensesSummaryHTML(data)\n        break\n      case 'expenses-by-category':\n        htmlContent = generateExpensesByCategoryHTML(data)\n        break\n      case 'expenses-by-recipient':\n        htmlContent = generateExpensesByRecipientHTML(data)\n        break\n      case 'general-summary':\n        htmlContent = generateGeneralSummaryHTML(data)\n        break\n      case 'comparison-report':\n        htmlContent = generateComparisonReportHTML(data)\n        break\n      case 'late-members':\n        htmlContent = generateLateMembersHTML(data)\n        break\n      case 'emergency-report':\n        htmlContent = generateEmergencyReportHTML(data)\n        break\n      default:\n        throw new Error('نوع التقرير غير مدعوم')\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = htmlContent\n\n    const opt = {\n      margin: 0.5,\n      filename: `${data.title.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`,\n      image: { type: 'jpeg', quality: 0.98 },\n      html2canvas: { scale: 2, useCORS: true },\n      jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }\n    }\n\n    await html2pdf().set(opt).from(element).save()\n\n  } catch (error) {\n    console.error('خطأ في تصدير PDF:', error)\n    alert('حدث خطأ في تصدير التقرير')\n  }\n}\n\n// دالة تصدير CSV\nexport const exportToCSV = (data: any, reportType: string) => {\n  try {\n    let csvContent = ''\n\n    switch (reportType) {\n      case 'members-list':\n        csvContent = generateMembersListCSV(data)\n        break\n      case 'member-detail':\n        csvContent = generateMemberDetailCSV(data)\n        break\n      case 'member-statement':\n        csvContent = generateMemberStatementCSV(data)\n        break\n      case 'incomes-detailed':\n        csvContent = generateIncomesDetailedCSV(data)\n        break\n      case 'incomes-summary':\n        csvContent = generateIncomesSummaryCSV(data)\n        break\n      case 'incomes-by-type':\n        csvContent = generateIncomesByTypeCSV(data)\n        break\n      case 'incomes-by-member':\n        csvContent = generateIncomesByMemberCSV(data)\n        break\n      case 'expenses-detailed':\n        csvContent = generateExpensesDetailedCSV(data)\n        break\n      case 'expenses-summary':\n        csvContent = generateExpensesSummaryCSV(data)\n        break\n      case 'expenses-by-category':\n        csvContent = generateExpensesByCategoryCSV(data)\n        break\n      case 'expenses-by-recipient':\n        csvContent = generateExpensesByRecipientCSV(data)\n        break\n      case 'general-summary':\n        csvContent = generateGeneralSummaryCSV(data)\n        break\n      case 'comparison-report':\n        csvContent = generateComparisonReportCSV(data)\n        break\n      case 'late-members':\n        csvContent = generateLateMembersCSV(data)\n        break\n      case 'emergency-report':\n        csvContent = generateEmergencyReportCSV(data)\n        break\n      default:\n        throw new Error('نوع التقرير غير مدعوم')\n    }\n\n    const blob = new Blob(['\\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })\n    const link = document.createElement('a')\n    link.href = URL.createObjectURL(blob)\n    link.download = `${data.title.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`\n    link.click()\n\n  } catch (error) {\n    console.error('خطأ في تصدير CSV:', error)\n    alert('حدث خطأ في تصدير التقرير')\n  }\n}\n\n// دالة إنشاء HTML أساسية\nconst generateBaseHTML = (title: string, content: string) => {\n  return `\n<!DOCTYPE html>\n<html dir=\"rtl\" lang=\"ar\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>${title}</title>\n    <style>\n        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');\n\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n\n        body {\n            font-family: 'Cairo', Arial, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            background: white;\n            padding: 20px;\n            direction: rtl;\n        }\n\n        .header {\n            text-align: center;\n            margin-bottom: 30px;\n            border-bottom: 3px solid #2563eb;\n            padding-bottom: 20px;\n        }\n\n        .header h1 {\n            color: #2563eb;\n            font-size: 24px;\n            font-weight: 700;\n            margin-bottom: 10px;\n        }\n\n        .header .subtitle {\n            color: #666;\n            font-size: 14px;\n            margin-bottom: 5px;\n        }\n\n        .section {\n            margin-bottom: 30px;\n            page-break-inside: avoid;\n        }\n\n        .section h2 {\n            color: #2563eb;\n            font-size: 18px;\n            font-weight: 600;\n            margin-bottom: 15px;\n            border-bottom: 2px solid #e2e8f0;\n            padding-bottom: 5px;\n        }\n\n        .table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n        }\n\n        .table th,\n        .table td {\n            padding: 8px 12px;\n            text-align: right;\n            border-bottom: 1px solid #e2e8f0;\n            font-size: 12px;\n        }\n\n        .table th {\n            background: #f1f5f9;\n            font-weight: 600;\n            color: #475569;\n        }\n\n        .income {\n            color: #059669;\n            font-weight: 600;\n        }\n\n        .expense {\n            color: #dc2626;\n            font-weight: 600;\n        }\n\n        .summary-grid {\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 15px;\n            margin-bottom: 30px;\n        }\n\n        .summary-card {\n            background: #f8fafc;\n            border: 1px solid #e2e8f0;\n            border-radius: 8px;\n            padding: 15px;\n            text-align: center;\n        }\n\n        .summary-card h3 {\n            color: #475569;\n            font-size: 12px;\n            margin-bottom: 8px;\n            font-weight: 600;\n        }\n\n        .summary-card .value {\n            font-size: 18px;\n            font-weight: 700;\n            color: #1e293b;\n        }\n\n        @media print {\n            body { padding: 0; }\n            .section { page-break-inside: avoid; }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <h1>${title}</h1>\n        <div class=\"subtitle\">ديوان أبو علوش</div>\n        <div class=\"subtitle\">تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-JO')}</div>\n    </div>\n    ${content}\n</body>\n</html>`\n}\n\n// دوال إنشاء HTML للتقارير المختلفة\nconst generateMembersListHTML = (data: any) => {\n  const content = `\n    <div class=\"section\">\n        <h2>قائمة الأعضاء</h2>\n        <table class=\"table\">\n            <thead>\n                <tr>\n                    <th>رقم العضو</th>\n                    <th>الاسم الكامل</th>\n                    <th>رقم الهاتف</th>\n                    <th>تاريخ الانضمام</th>\n                    <th>الحالة</th>\n                    <th>إجمالي المساهمات</th>\n                    <th>عدد المساهمات</th>\n                </tr>\n            </thead>\n            <tbody>\n                ${data.members.map((member: any) => `\n                    <tr>\n                        <td>${member.memberNumber}</td>\n                        <td>${member.name}</td>\n                        <td>${member.phone}</td>\n                        <td>${member.joinDate}</td>\n                        <td>${member.status}</td>\n                        <td class=\"income\">${formatCurrency(member.totalContributions)}</td>\n                        <td>${member.contributionsCount}</td>\n                    </tr>\n                `).join('')}\n            </tbody>\n        </table>\n    </div>\n  `\n  return generateBaseHTML(data.title, content)\n}\n\nconst generateMemberDetailHTML = (data: any) => {\n  const content = `\n    <div class=\"section\">\n        <h2>معلومات العضو</h2>\n        <div class=\"summary-grid\">\n            <div class=\"summary-card\">\n                <h3>الاسم</h3>\n                <div class=\"value\">${data.member.name}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>الهاتف</h3>\n                <div class=\"value\">${data.member.phone}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>إجمالي المساهمات</h3>\n                <div class=\"value income\">${formatCurrency(data.member.totalContributions)}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>عدد المساهمات</h3>\n                <div class=\"value\">${data.member.contributionsCount}</div>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"section\">\n        <h2>تفاصيل المساهمات</h2>\n        <table class=\"table\">\n            <thead>\n                <tr>\n                    <th>التاريخ</th>\n                    <th>المبلغ</th>\n                    <th>المصدر</th>\n                    <th>الوصف</th>\n                </tr>\n            </thead>\n            <tbody>\n                ${data.contributions.map((contribution: any) => `\n                    <tr>\n                        <td>${contribution.date}</td>\n                        <td class=\"income\">${formatCurrency(contribution.amount)}</td>\n                        <td>${contribution.source}</td>\n                        <td>${contribution.description}</td>\n                    </tr>\n                `).join('')}\n            </tbody>\n        </table>\n    </div>\n  `\n  return generateBaseHTML(data.title, content)\n}\n\n// دوال إنشاء CSV للتقارير المختلفة\nconst generateMembersListCSV = (data: any) => {\n  const headers = ['رقم العضو', 'الاسم الكامل', 'رقم الهاتف', 'تاريخ الانضمام', 'الحالة', 'إجمالي المساهمات', 'عدد المساهمات']\n  const rows = data.members.map((member: any) => [\n    member.memberNumber,\n    member.name,\n    member.phone,\n    member.joinDate,\n    member.status,\n    formatCurrency(member.totalContributions),\n    member.contributionsCount\n  ])\n\n  return [headers, ...rows].map(row => row.join(',')).join('\\n')\n}\n\nconst generateMemberDetailCSV = (data: any) => {\n  const memberInfo = [\n    ['معلومات العضو'],\n    ['الاسم', data.member.name],\n    ['الهاتف', data.member.phone],\n    ['إجمالي المساهمات', formatCurrency(data.member.totalContributions)],\n    ['عدد المساهمات', data.member.contributionsCount],\n    [],\n    ['تفاصيل المساهمات'],\n    ['التاريخ', 'المبلغ', 'المصدر', 'الوصف'],\n    ...data.contributions.map((contribution: any) => [\n      contribution.date,\n      formatCurrency(contribution.amount),\n      contribution.source,\n      contribution.description\n    ])\n  ]\n\n  return memberInfo.map(row => Array.isArray(row) ? row.join(',') : row).join('\\n')\n}\n\n// دوال إضافية لتقارير الإيرادات\nconst generateIncomesDetailedHTML = (data: any) => {\n  const content = `\n    <div class=\"section\">\n        <h2>ملخص الإيرادات</h2>\n        <div class=\"summary-grid\">\n            <div class=\"summary-card\">\n                <h3>إجمالي الإيرادات</h3>\n                <div class=\"value income\">${formatCurrency(data.statistics.total)}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>عدد المعاملات</h3>\n                <div class=\"value\">${data.statistics.count}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>متوسط المبلغ</h3>\n                <div class=\"value\">${formatCurrency(data.statistics.average)}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>الفترة</h3>\n                <div class=\"value\">${data.period}</div>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"section\">\n        <h2>تفاصيل الإيرادات</h2>\n        <table class=\"table\">\n            <thead>\n                <tr>\n                    <th>التاريخ</th>\n                    <th>العضو</th>\n                    <th>النوع</th>\n                    <th>المصدر</th>\n                    <th>المبلغ</th>\n                    <th>الوصف</th>\n                </tr>\n            </thead>\n            <tbody>\n                ${data.incomes.map((income: any) => `\n                    <tr>\n                        <td>${income.date}</td>\n                        <td>${income.memberName}</td>\n                        <td>${income.type}</td>\n                        <td>${income.source}</td>\n                        <td class=\"income\">${formatCurrency(income.amount)}</td>\n                        <td>${income.description}</td>\n                    </tr>\n                `).join('')}\n            </tbody>\n        </table>\n    </div>\n  `\n  return generateBaseHTML(data.title, content)\n}\n\nconst generateExpensesDetailedHTML = (data: any) => {\n  const content = `\n    <div class=\"section\">\n        <h2>ملخص المصروفات</h2>\n        <div class=\"summary-grid\">\n            <div class=\"summary-card\">\n                <h3>إجمالي المصروفات</h3>\n                <div class=\"value expense\">${formatCurrency(data.statistics.total)}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>عدد المعاملات</h3>\n                <div class=\"value\">${data.statistics.count}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>متوسط المبلغ</h3>\n                <div class=\"value\">${formatCurrency(data.statistics.average)}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>الفترة</h3>\n                <div class=\"value\">${data.period}</div>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"section\">\n        <h2>تفاصيل المصروفات</h2>\n        <table class=\"table\">\n            <thead>\n                <tr>\n                    <th>التاريخ</th>\n                    <th>الوصف</th>\n                    <th>الفئة</th>\n                    <th>المبلغ</th>\n                    <th>المستفيد</th>\n                    <th>المسؤول</th>\n                </tr>\n            </thead>\n            <tbody>\n                ${data.expenses.map((expense: any) => `\n                    <tr>\n                        <td>${expense.date}</td>\n                        <td>${expense.description}</td>\n                        <td>${expense.category}</td>\n                        <td class=\"expense\">${formatCurrency(expense.amount)}</td>\n                        <td>${expense.recipient}</td>\n                        <td>${expense.createdBy}</td>\n                    </tr>\n                `).join('')}\n            </tbody>\n        </table>\n    </div>\n  `\n  return generateBaseHTML(data.title, content)\n}\n\nconst generateLateMembersHTML = (data: any) => {\n  const content = `\n    <div class=\"section\">\n        <h2>ملخص الأعضاء المتأخرين</h2>\n        <div class=\"summary-grid\">\n            <div class=\"summary-card\">\n                <h3>عدد الأعضاء المتأخرين</h3>\n                <div class=\"value\">${data.summary.totalLateMembers}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>إجمالي المبالغ المستحقة</h3>\n                <div class=\"value expense\">${formatCurrency(data.summary.totalAmountDue)}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>عتبة التأخير</h3>\n                <div class=\"value\">${data.threshold}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>تاريخ التقرير</h3>\n                <div class=\"value\">${data.date}</div>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"section\">\n        <h2>قائمة الأعضاء المتأخرين</h2>\n        <table class=\"table\">\n            <thead>\n                <tr>\n                    <th>الاسم</th>\n                    <th>الهاتف</th>\n                    <th>الحالة</th>\n                    <th>آخر مساهمة</th>\n                    <th>أيام التأخير</th>\n                    <th>الأشهر المتأخرة</th>\n                    <th>المبلغ المستحق</th>\n                </tr>\n            </thead>\n            <tbody>\n                ${data.lateMembers.map((member: any) => `\n                    <tr>\n                        <td>${member.name}</td>\n                        <td>${member.phone}</td>\n                        <td>${member.status}</td>\n                        <td>${member.lastContribution}</td>\n                        <td>${member.daysSinceLastPayment}</td>\n                        <td>${member.monthsLate}</td>\n                        <td class=\"expense\">${formatCurrency(member.amountDue)}</td>\n                    </tr>\n                `).join('')}\n            </tbody>\n        </table>\n    </div>\n  `\n  return generateBaseHTML(data.title, content)\n}\n\n// دوال CSV للتقارير الإضافية\nconst generateIncomesDetailedCSV = (data: any) => {\n  const headers = ['التاريخ', 'العضو', 'النوع', 'المصدر', 'المبلغ', 'الوصف']\n  const rows = data.incomes.map((income: any) => [\n    income.date,\n    income.memberName,\n    income.type,\n    income.source,\n    formatCurrency(income.amount),\n    income.description\n  ])\n\n  return [\n    [`${data.title} - ${data.date}`],\n    [`الفترة: ${data.period}`],\n    [`إجمالي الإيرادات: ${formatCurrency(data.statistics.total)}`],\n    [`عدد المعاملات: ${data.statistics.count}`],\n    [],\n    headers,\n    ...rows\n  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\\n')\n}\n\nconst generateExpensesDetailedCSV = (data: any) => {\n  const headers = ['التاريخ', 'الوصف', 'الفئة', 'المبلغ', 'المستفيد', 'المسؤول']\n  const rows = data.expenses.map((expense: any) => [\n    expense.date,\n    expense.description,\n    expense.category,\n    formatCurrency(expense.amount),\n    expense.recipient,\n    expense.createdBy\n  ])\n\n  return [\n    [`${data.title} - ${data.date}`],\n    [`الفترة: ${data.period}`],\n    [`إجمالي المصروفات: ${formatCurrency(data.statistics.total)}`],\n    [`عدد المعاملات: ${data.statistics.count}`],\n    [],\n    headers,\n    ...rows\n  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\\n')\n}\n\nconst generateLateMembersCSV = (data: any) => {\n  const headers = ['الاسم', 'الهاتف', 'الحالة', 'آخر مساهمة', 'أيام التأخير', 'الأشهر المتأخرة', 'المبلغ المستحق']\n  const rows = data.lateMembers.map((member: any) => [\n    member.name,\n    member.phone,\n    member.status,\n    member.lastContribution,\n    member.daysSinceLastPayment,\n    member.monthsLate,\n    formatCurrency(member.amountDue)\n  ])\n\n  return [\n    [`${data.title} - ${data.date}`],\n    [`عتبة التأخير: ${data.threshold}`],\n    [`عدد الأعضاء المتأخرين: ${data.summary.totalLateMembers}`],\n    [`إجمالي المبالغ المستحقة: ${formatCurrency(data.summary.totalAmountDue)}`],\n    [],\n    headers,\n    ...rows\n  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\\n')\n}\n\n// دوال مساعدة للتقارير الأخرى (يمكن إضافة المزيد حسب الحاجة)\nconst generateMemberStatementHTML = (data: any) => {\n  return generateMemberDetailHTML(data) // نفس تنسيق التقرير المفصل\n}\n\nconst generateMemberStatementCSV = (data: any) => {\n  return generateMemberDetailCSV(data) // نفس تنسيق التقرير المفصل\n}\n\nconst generateIncomesSummaryHTML = (data: any) => {\n  const content = `\n    <div class=\"section\">\n        <h2>ملخص الإيرادات</h2>\n        <div class=\"summary-grid\">\n            <div class=\"summary-card\">\n                <h3>إجمالي المبلغ</h3>\n                <div class=\"value income\">${formatCurrency(data.summary.totalAmount)}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>عدد المعاملات</h3>\n                <div class=\"value\">${data.summary.totalCount}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>متوسط المبلغ</h3>\n                <div class=\"value\">${formatCurrency(data.summary.averageAmount)}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>الفترة</h3>\n                <div class=\"value\">${data.period}</div>\n            </div>\n        </div>\n    </div>\n  `\n  return generateBaseHTML(data.title, content)\n}\n\nconst generateIncomesSummaryCSV = (data: any) => {\n  return [\n    [`${data.title} - ${data.date}`],\n    [`الفترة: ${data.period}`],\n    ['البيان', 'القيمة'],\n    ['إجمالي المبلغ', formatCurrency(data.summary.totalAmount)],\n    ['عدد المعاملات', data.summary.totalCount],\n    ['متوسط المبلغ', formatCurrency(data.summary.averageAmount)]\n  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\\n')\n}\n\n// دوال أساسية للتقارير الأخرى\nconst generateIncomesByTypeHTML = (data: any) => generateIncomesSummaryHTML(data)\nconst generateIncomesByMemberHTML = (data: any) => generateIncomesSummaryHTML(data)\nconst generateExpensesSummaryHTML = (data: any) => generateIncomesSummaryHTML(data)\nconst generateExpensesByCategoryHTML = (data: any) => generateIncomesSummaryHTML(data)\nconst generateExpensesByRecipientHTML = (data: any) => generateIncomesSummaryHTML(data)\nconst generateGeneralSummaryHTML = (data: any) => generateIncomesSummaryHTML(data)\nconst generateComparisonReportHTML = (data: any) => generateIncomesSummaryHTML(data)\nconst generateEmergencyReportHTML = (data: any) => generateIncomesSummaryHTML(data)\n\nconst generateIncomesByTypeCSV = (data: any) => generateIncomesSummaryCSV(data)\nconst generateIncomesByMemberCSV = (data: any) => generateIncomesSummaryCSV(data)\nconst generateExpensesSummaryCSV = (data: any) => generateIncomesSummaryCSV(data)\nconst generateExpensesByCategoryCSV = (data: any) => generateIncomesSummaryCSV(data)\nconst generateExpensesByRecipientCSV = (data: any) => generateIncomesSummaryCSV(data)\nconst generateGeneralSummaryCSV = (data: any) => generateIncomesSummaryCSV(data)\nconst generateComparisonReportCSV = (data: any) => generateIncomesSummaryCSV(data)\nconst generateEmergencyReportCSV = (data: any) => generateIncomesSummaryCSV(data)"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAKO,MAAM,cAAc,OAAO,MAAW;IAC3C,IAAI;QACF,MAAM,WAAW,CAAC,kJAA2B,EAAE,OAAO;QAEtD,IAAI,cAAc;QAElB,OAAQ;YACN,KAAK;gBACH,cAAc,wBAAwB;gBACtC;YACF,KAAK;gBACH,cAAc,yBAAyB;gBACvC;YACF,KAAK;gBACH,cAAc,4BAA4B;gBAC1C;YACF,KAAK;gBACH,cAAc,4BAA4B;gBAC1C;YACF,KAAK;gBACH,cAAc,2BAA2B;gBACzC;YACF,KAAK;gBACH,cAAc,0BAA0B;gBACxC;YACF,KAAK;gBACH,cAAc,4BAA4B;gBAC1C;YACF,KAAK;gBACH,cAAc,6BAA6B;gBAC3C;YACF,KAAK;g<PERSON><PERSON>,cAAc,4BAA4B;gBAC1C;YAC<PERSON>,KAAK;gBACH,cAAc,+BAA+B;gBAC7C;YACF,KAAK;gBACH,cAAc,gCAAgC;gBAC9C;YACF,KAAK;gBACH,cAAc,2BAA2B;gBACzC;YACF,KAAK;gBACH,cAAc,6BAA6B;gBAC3C;YACF,KAAK;gBACH,cAAc,wBAAwB;gBACtC;YACF,KAAK;gBACH,cAAc,4BAA4B;gBAC1C;YACF;gBACE,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,SAAS,GAAG;QAEpB,MAAM,MAAM;YACV,QAAQ;YACR,UAAU,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAC5F,OAAO;gBAAE,MAAM;gBAAQ,SAAS;YAAK;YACrC,aAAa;gBAAE,OAAO;gBAAG,SAAS;YAAK;YACvC,OAAO;gBAAE,MAAM;gBAAM,QAAQ;gBAAM,aAAa;YAAW;QAC7D;QAEA,MAAM,WAAW,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS,IAAI;IAE9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM;IACR;AACF;AAGO,MAAM,cAAc,CAAC,MAAW;IACrC,IAAI;QACF,IAAI,aAAa;QAEjB,OAAQ;YACN,KAAK;gBACH,aAAa,uBAAuB;gBACpC;YACF,KAAK;gBACH,aAAa,wBAAwB;gBACrC;YACF,KAAK;gBACH,aAAa,2BAA2B;gBACxC;YACF,KAAK;gBACH,aAAa,2BAA2B;gBACxC;YACF,KAAK;gBACH,aAAa,0BAA0B;gBACvC;YACF,KAAK;gBACH,aAAa,yBAAyB;gBACtC;YACF,KAAK;gBACH,aAAa,2BAA2B;gBACxC;YACF,KAAK;gBACH,aAAa,4BAA4B;gBACzC;YACF,KAAK;gBACH,aAAa,2BAA2B;gBACxC;YACF,KAAK;gBACH,aAAa,8BAA8B;gBAC3C;YACF,KAAK;gBACH,aAAa,+BAA+B;gBAC5C;YACF,KAAK;gBACH,aAAa,0BAA0B;gBACvC;YACF,KAAK;gBACH,aAAa,4BAA4B;gBACzC;YACF,KAAK;gBACH,aAAa,uBAAuB;gBACpC;YACF,KAAK;gBACH,aAAa,2BAA2B;gBACxC;YACF;gBACE,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC,WAAW;SAAW,EAAE;YAAE,MAAM;QAA0B;QACjF,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,IAAI,eAAe,CAAC;QAChC,KAAK,QAAQ,GAAG,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAClG,KAAK,KAAK;IAEZ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM;IACR;AACF;AAEA,yBAAyB;AACzB,MAAM,mBAAmB,CAAC,OAAe;IACvC,OAAO,CAAC;;;;;WAKC,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAuHP,EAAE,MAAM;;6CAEyB,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;;IAElF,EAAE,QAAQ;;OAEP,CAAC;AACR;AAEA,oCAAoC;AACpC,MAAM,0BAA0B,CAAC;IAC/B,MAAM,UAAU,CAAC;;;;;;;;;;;;;;;;gBAgBH,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;;4BAEzB,EAAE,OAAO,YAAY,CAAC;4BACtB,EAAE,OAAO,IAAI,CAAC;4BACd,EAAE,OAAO,KAAK,CAAC;4BACf,EAAE,OAAO,QAAQ,CAAC;4BAClB,EAAE,OAAO,MAAM,CAAC;2CACD,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,kBAAkB,EAAE;4BAC3D,EAAE,OAAO,kBAAkB,CAAC;;gBAExC,CAAC,EAAE,IAAI,CAAC,IAAI;;;;EAI1B,CAAC;IACD,OAAO,iBAAiB,KAAK,KAAK,EAAE;AACtC;AAEA,MAAM,2BAA2B,CAAC;IAChC,MAAM,UAAU,CAAC;;;;;;mCAMgB,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC;;;;mCAInB,EAAE,KAAK,MAAM,CAAC,KAAK,CAAC;;;;0CAIb,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,CAAC,kBAAkB,EAAE;;;;mCAIxD,EAAE,KAAK,MAAM,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;gBAiBpD,EAAE,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,eAAsB,CAAC;;4BAErC,EAAE,aAAa,IAAI,CAAC;2CACL,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,MAAM,EAAE;4BACrD,EAAE,aAAa,MAAM,CAAC;4BACtB,EAAE,aAAa,WAAW,CAAC;;gBAEvC,CAAC,EAAE,IAAI,CAAC,IAAI;;;;EAI1B,CAAC;IACD,OAAO,iBAAiB,KAAK,KAAK,EAAE;AACtC;AAEA,mCAAmC;AACnC,MAAM,yBAAyB,CAAC;IAC9B,MAAM,UAAU;QAAC;QAAa;QAAgB;QAAc;QAAkB;QAAU;QAAoB;KAAgB;IAC5H,MAAM,OAAO,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB;YAC7C,OAAO,YAAY;YACnB,OAAO,IAAI;YACX,OAAO,KAAK;YACZ,OAAO,QAAQ;YACf,OAAO,MAAM;YACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,kBAAkB;YACxC,OAAO,kBAAkB;SAC1B;IAED,OAAO;QAAC;WAAY;KAAK,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AAC3D;AAEA,MAAM,0BAA0B,CAAC;IAC/B,MAAM,aAAa;QACjB;YAAC;SAAgB;QACjB;YAAC;YAAS,KAAK,MAAM,CAAC,IAAI;SAAC;QAC3B;YAAC;YAAU,KAAK,MAAM,CAAC,KAAK;SAAC;QAC7B;YAAC;YAAoB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,CAAC,kBAAkB;SAAE;QACpE;YAAC;YAAiB,KAAK,MAAM,CAAC,kBAAkB;SAAC;QACjD,EAAE;QACF;YAAC;SAAmB;QACpB;YAAC;YAAW;YAAU;YAAU;SAAQ;WACrC,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,eAAsB;gBAC/C,aAAa,IAAI;gBACjB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,MAAM;gBAClC,aAAa,MAAM;gBACnB,aAAa,WAAW;aACzB;KACF;IAED,OAAO,WAAW,GAAG,CAAC,CAAA,MAAO,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;AAC9E;AAEA,gCAAgC;AAChC,MAAM,8BAA8B,CAAC;IACnC,MAAM,UAAU,CAAC;;;;;;0CAMuB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU,CAAC,KAAK,EAAE;;;;mCAI/C,EAAE,KAAK,UAAU,CAAC,KAAK,CAAC;;;;mCAIxB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU,CAAC,OAAO,EAAE;;;;mCAI1C,EAAE,KAAK,MAAM,CAAC;;;;;;;;;;;;;;;;;;;gBAmBjC,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;;4BAEzB,EAAE,OAAO,IAAI,CAAC;4BACd,EAAE,OAAO,UAAU,CAAC;4BACpB,EAAE,OAAO,IAAI,CAAC;4BACd,EAAE,OAAO,MAAM,CAAC;2CACD,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM,EAAE;4BAC/C,EAAE,OAAO,WAAW,CAAC;;gBAEjC,CAAC,EAAE,IAAI,CAAC,IAAI;;;;EAI1B,CAAC;IACD,OAAO,iBAAiB,KAAK,KAAK,EAAE;AACtC;AAEA,MAAM,+BAA+B,CAAC;IACpC,MAAM,UAAU,CAAC;;;;;;2CAMwB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU,CAAC,KAAK,EAAE;;;;mCAIhD,EAAE,KAAK,UAAU,CAAC,KAAK,CAAC;;;;mCAIxB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU,CAAC,OAAO,EAAE;;;;mCAI1C,EAAE,KAAK,MAAM,CAAC;;;;;;;;;;;;;;;;;;;gBAmBjC,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;;4BAE3B,EAAE,QAAQ,IAAI,CAAC;4BACf,EAAE,QAAQ,WAAW,CAAC;4BACtB,EAAE,QAAQ,QAAQ,CAAC;4CACH,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM,EAAE;4BACjD,EAAE,QAAQ,SAAS,CAAC;4BACpB,EAAE,QAAQ,SAAS,CAAC;;gBAEhC,CAAC,EAAE,IAAI,CAAC,IAAI;;;;EAI1B,CAAC;IACD,OAAO,iBAAiB,KAAK,KAAK,EAAE;AACtC;AAEA,MAAM,0BAA0B,CAAC;IAC/B,MAAM,UAAU,CAAC;;;;;;mCAMgB,EAAE,KAAK,OAAO,CAAC,gBAAgB,CAAC;;;;2CAIxB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,cAAc,EAAE;;;;mCAItD,EAAE,KAAK,SAAS,CAAC;;;;mCAIjB,EAAE,KAAK,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;gBAoB/B,EAAE,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;;4BAE7B,EAAE,OAAO,IAAI,CAAC;4BACd,EAAE,OAAO,KAAK,CAAC;4BACf,EAAE,OAAO,MAAM,CAAC;4BAChB,EAAE,OAAO,gBAAgB,CAAC;4BAC1B,EAAE,OAAO,oBAAoB,CAAC;4BAC9B,EAAE,OAAO,UAAU,CAAC;4CACJ,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS,EAAE;;gBAE/D,CAAC,EAAE,IAAI,CAAC,IAAI;;;;EAI1B,CAAC;IACD,OAAO,iBAAiB,KAAK,KAAK,EAAE;AACtC;AAEA,6BAA6B;AAC7B,MAAM,6BAA6B,CAAC;IAClC,MAAM,UAAU;QAAC;QAAW;QAAS;QAAS;QAAU;QAAU;KAAQ;IAC1E,MAAM,OAAO,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB;YAC7C,OAAO,IAAI;YACX,OAAO,UAAU;YACjB,OAAO,IAAI;YACX,OAAO,MAAM;YACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM;YAC5B,OAAO,WAAW;SACnB;IAED,OAAO;QACL;YAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;SAAC;QAChC;YAAC,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE;SAAC;QAC1B;YAAC,CAAC,kBAAkB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU,CAAC,KAAK,GAAG;SAAC;QAC9D;YAAC,CAAC,eAAe,EAAE,KAAK,UAAU,CAAC,KAAK,EAAE;SAAC;QAC3C,EAAE;QACF;WACG;KACJ,CAAC,GAAG,CAAC,CAAA,MAAO,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;AAC9D;AAEA,MAAM,8BAA8B,CAAC;IACnC,MAAM,UAAU;QAAC;QAAW;QAAS;QAAS;QAAU;QAAY;KAAU;IAC9E,MAAM,OAAO,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,UAAiB;YAC/C,QAAQ,IAAI;YACZ,QAAQ,WAAW;YACnB,QAAQ,QAAQ;YAChB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;YAC7B,QAAQ,SAAS;YACjB,QAAQ,SAAS;SAClB;IAED,OAAO;QACL;YAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;SAAC;QAChC;YAAC,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE;SAAC;QAC1B;YAAC,CAAC,kBAAkB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU,CAAC,KAAK,GAAG;SAAC;QAC9D;YAAC,CAAC,eAAe,EAAE,KAAK,UAAU,CAAC,KAAK,EAAE;SAAC;QAC3C,EAAE;QACF;WACG;KACJ,CAAC,GAAG,CAAC,CAAA,MAAO,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;AAC9D;AAEA,MAAM,yBAAyB,CAAC;IAC9B,MAAM,UAAU;QAAC;QAAS;QAAU;QAAU;QAAc;QAAgB;QAAmB;KAAiB;IAChH,MAAM,OAAO,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,SAAgB;YACjD,OAAO,IAAI;YACX,OAAO,KAAK;YACZ,OAAO,MAAM;YACb,OAAO,gBAAgB;YACvB,OAAO,oBAAoB;YAC3B,OAAO,UAAU;YACjB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS;SAChC;IAED,OAAO;QACL;YAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;SAAC;QAChC;YAAC,CAAC,cAAc,EAAE,KAAK,SAAS,EAAE;SAAC;QACnC;YAAC,CAAC,uBAAuB,EAAE,KAAK,OAAO,CAAC,gBAAgB,EAAE;SAAC;QAC3D;YAAC,CAAC,yBAAyB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,cAAc,GAAG;SAAC;QAC3E,EAAE;QACF;WACG;KACJ,CAAC,GAAG,CAAC,CAAA,MAAO,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;AAC9D;AAEA,6DAA6D;AAC7D,MAAM,8BAA8B,CAAC;IACnC,OAAO,yBAAyB,MAAM,2BAA2B;;AACnE;AAEA,MAAM,6BAA6B,CAAC;IAClC,OAAO,wBAAwB,MAAM,2BAA2B;;AAClE;AAEA,MAAM,6BAA6B,CAAC;IAClC,MAAM,UAAU,CAAC;;;;;;0CAMuB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE;;;;mCAIlD,EAAE,KAAK,OAAO,CAAC,UAAU,CAAC;;;;mCAI1B,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa,EAAE;;;;mCAI7C,EAAE,KAAK,MAAM,CAAC;;;;EAI/C,CAAC;IACD,OAAO,iBAAiB,KAAK,KAAK,EAAE;AACtC;AAEA,MAAM,4BAA4B,CAAC;IACjC,OAAO;QACL;YAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;SAAC;QAChC;YAAC,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE;SAAC;QAC1B;YAAC;YAAU;SAAS;QACpB;YAAC;YAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,WAAW;SAAE;QAC3D;YAAC;YAAiB,KAAK,OAAO,CAAC,UAAU;SAAC;QAC1C;YAAC;YAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa;SAAE;KAC7D,CAAC,GAAG,CAAC,CAAA,MAAO,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;AAC9D;AAEA,8BAA8B;AAC9B,MAAM,4BAA4B,CAAC,OAAc,2BAA2B;AAC5E,MAAM,8BAA8B,CAAC,OAAc,2BAA2B;AAC9E,MAAM,8BAA8B,CAAC,OAAc,2BAA2B;AAC9E,MAAM,iCAAiC,CAAC,OAAc,2BAA2B;AACjF,MAAM,kCAAkC,CAAC,OAAc,2BAA2B;AAClF,MAAM,6BAA6B,CAAC,OAAc,2BAA2B;AAC7E,MAAM,+BAA+B,CAAC,OAAc,2BAA2B;AAC/E,MAAM,8BAA8B,CAAC,OAAc,2BAA2B;AAE9E,MAAM,2BAA2B,CAAC,OAAc,0BAA0B;AAC1E,MAAM,6BAA6B,CAAC,OAAc,0BAA0B;AAC5E,MAAM,6BAA6B,CAAC,OAAc,0BAA0B;AAC5E,MAAM,gCAAgC,CAAC,OAAc,0BAA0B;AAC/E,MAAM,iCAAiC,CAAC,OAAc,0BAA0B;AAChF,MAAM,4BAA4B,CAAC,OAAc,0BAA0B;AAC3E,MAAM,8BAA8B,CAAC,OAAc,0BAA0B;AAC7E,MAAM,6BAA6B,CAAC,OAAc,0BAA0B", "debugId": null}}, {"offset": {"line": 852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // This interface extends the base input props\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-11 w-full rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-sm font-medium text-secondary-700 transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-400 focus-visible:outline-none focus-visible:border-primary-500 focus-visible:ring-4 focus-visible:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 hover:border-secondary-300\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gbACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-11 w-full items-center justify-between rounded-xl border-2 border-gray-300 bg-white px-4 py-3 text-base font-medium text-black transition-all duration-200 hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-4 focus:ring-blue-500/20 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-5 w-5 text-gray-600 transition-transform duration-200 data-[state=open]:rotate-180\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1 text-black\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4 text-gray-600\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1 text-black\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4 text-gray-600\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-xl border border-gray-300 bg-white text-black shadow-xl backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-2\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-2 pl-10 pr-3 text-sm font-bold text-gray-700 bg-gray-50\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-pointer select-none items-center rounded-lg py-3 pl-10 pr-3 text-sm font-medium text-black transition-colors hover:bg-blue-50 focus:bg-blue-100 focus:text-blue-800 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[state=checked]:bg-blue-100 data-[state=checked]:text-blue-800 data-[state=checked]:font-semibold\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-3 flex h-4 w-4 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4 text-blue-600\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8UACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ydACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yWACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-white\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-white hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-white hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-white hover:bg-destructive/80\",\n        outline: \"text-white border-white\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-amber-500 text-white hover:bg-amber-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qLACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/reports/MembersReports.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { formatCurrency, formatDate } from '@/lib/utils'\nimport {\n  Users,\n  FileText,\n  Download,\n  Search,\n  Filter,\n  Calendar,\n  Phone,\n  Mail,\n  MapPin,\n  DollarSign\n} from 'lucide-react'\n\ninterface Member {\n  id: string\n  name: string\n  phone?: string\n  email?: string\n  address?: string\n  status: 'ACTIVE' | 'INACTIVE' | 'LATE' | 'NON_COMPLIANT'\n  createdAt: string\n  incomes: {\n    id: string\n    amount: number\n    date: string\n    source: string\n    description?: string\n  }[]\n  _count: {\n    incomes: number\n  }\n}\n\ninterface MembersReportsProps {\n  onExportPDF: (data: any, type: string) => void\n  onExportCSV: (data: any, type: string) => void\n}\n\nexport default function MembersReports({ onExportPDF, onExportCSV }: MembersReportsProps) {\n  const [members, setMembers] = useState<Member[]>([])\n  const [filteredMembers, setFilteredMembers] = useState<Member[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [statusFilter, setStatusFilter] = useState('all')\n  const [selectedMember, setSelectedMember] = useState<Member | null>(null)\n  const [showDetailedReport, setShowDetailedReport] = useState(false)\n\n  // جلب بيانات الأعضاء\n  const fetchMembers = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/members?limit=1000&includeIncomes=true')\n      if (!response.ok) throw new Error('فشل في جلب بيانات الأعضاء')\n      \n      const data = await response.json()\n      setMembers(data.members || [])\n      setFilteredMembers(data.members || [])\n    } catch (error) {\n      console.error('خطأ في جلب بيانات الأعضاء:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchMembers()\n  }, [])\n\n  // تطبيق الفلاتر\n  useEffect(() => {\n    let filtered = members\n\n    // فلتر البحث\n    if (searchTerm) {\n      filtered = filtered.filter(member =>\n        member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        member.phone?.includes(searchTerm) ||\n        member.email?.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // فلتر الحالة\n    if (statusFilter !== 'all') {\n      filtered = filtered.filter(member => member.status === statusFilter)\n    }\n\n    setFilteredMembers(filtered)\n  }, [members, searchTerm, statusFilter])\n\n  // حساب إحصائيات العضو\n  const getMemberStats = (member: Member) => {\n    const totalContributions = member.incomes.reduce((sum, income) => sum + income.amount, 0)\n    const contributionsCount = member.incomes.length\n\n    // تصفية التواريخ الصحيحة فقط\n    const validDates = member.incomes\n      .map(income => new Date(income.date))\n      .filter(date => !isNaN(date.getTime()))\n\n    const lastContribution = validDates.length > 0\n      ? new Date(Math.max(...validDates.map(date => date.getTime())))\n      : null\n\n    return {\n      totalContributions,\n      contributionsCount,\n      lastContribution\n    }\n  }\n\n  // تصدير تقرير قائمة الأعضاء\n  const handleExportMembersList = (format: 'pdf' | 'csv') => {\n    const reportData = {\n      title: 'تقرير قائمة الأعضاء',\n      date: new Date().toLocaleDateString('ar-JO'),\n      members: filteredMembers.map((member, index) => {\n        const stats = getMemberStats(member)\n        return {\n          memberNumber: index + 1,\n          name: member.name,\n          phone: member.phone || 'غير محدد',\n          email: member.email || 'غير محدد',\n          joinDate: formatDate(member.createdAt),\n          status: getStatusLabel(member.status),\n          totalContributions: stats.totalContributions,\n          contributionsCount: stats.contributionsCount,\n          lastContribution: stats.lastContribution && !isNaN(stats.lastContribution.getTime())\n            ? formatDate(stats.lastContribution.toISOString())\n            : 'لا يوجد'\n        }\n      })\n    }\n\n    if (format === 'pdf') {\n      onExportPDF(reportData, 'members-list')\n    } else {\n      onExportCSV(reportData, 'members-list')\n    }\n  }\n\n  // تصدير تقرير العضو المفصل\n  const handleExportMemberDetail = (member: Member, format: 'pdf' | 'csv') => {\n    const stats = getMemberStats(member)\n    const reportData = {\n      title: `تقرير مفصل للعضو: ${member.name}`,\n      date: new Date().toLocaleDateString('ar-JO'),\n      member: {\n        name: member.name,\n        phone: member.phone || 'غير محدد',\n        email: member.email || 'غير محدد',\n        address: member.address || 'غير محدد',\n        status: getStatusLabel(member.status),\n        joinDate: formatDate(member.createdAt),\n        totalContributions: stats.totalContributions,\n        contributionsCount: stats.contributionsCount\n      },\n      contributions: member.incomes.map(income => ({\n        date: formatDate(income.date),\n        amount: income.amount,\n        source: income.source,\n        description: income.description || 'غير محدد'\n      }))\n    }\n\n    if (format === 'pdf') {\n      onExportPDF(reportData, 'member-detail')\n    } else {\n      onExportCSV(reportData, 'member-detail')\n    }\n  }\n\n  // الحصول على تسمية الحالة\n  const getStatusLabel = (status: string) => {\n    const statusLabels = {\n      'ACTIVE': 'نشط',\n      'INACTIVE': 'غير نشط',\n      'LATE': 'متأخر',\n      'NON_COMPLIANT': 'غير ملتزم'\n    }\n    return statusLabels[status as keyof typeof statusLabels] || status\n  }\n\n  // الحصول على لون الحالة\n  const getStatusColor = (status: string) => {\n    const statusColors = {\n      'ACTIVE': 'bg-green-100 text-green-800',\n      'INACTIVE': 'bg-gray-100 text-gray-800',\n      'LATE': 'bg-yellow-100 text-yellow-800',\n      'NON_COMPLIANT': 'bg-red-100 text-red-800'\n    }\n    return statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"text-gray-500\">جاري تحميل بيانات الأعضاء...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* عنوان القسم */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900 flex items-center\">\n            <Users className=\"w-5 h-5 ml-2\" />\n            تقارير الأعضاء\n          </h2>\n          <p className=\"text-gray-600\">تقارير شاملة عن الأعضاء ومساهماتهم</p>\n        </div>\n        <div className=\"flex space-x-2 space-x-reverse\">\n          <Button\n            onClick={() => handleExportMembersList('pdf')}\n            className=\"bg-red-600 hover:bg-red-700 text-white\"\n          >\n            <FileText className=\"w-4 h-4 ml-2\" />\n            تصدير PDF\n          </Button>\n          <Button\n            onClick={() => handleExportMembersList('csv')}\n            className=\"bg-green-600 hover:bg-green-700 text-white\"\n          >\n            <Download className=\"w-4 h-4 ml-2\" />\n            تصدير CSV\n          </Button>\n        </div>\n      </div>\n\n      {/* أدوات البحث والفلترة */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"relative\">\n              <Search className=\"absolute right-3 top-3 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"البحث بالاسم أو الهاتف أو البريد الإلكتروني...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pr-10\"\n              />\n            </div>\n            <Select value={statusFilter} onValueChange={setStatusFilter}>\n              <SelectTrigger>\n                <SelectValue placeholder=\"فلترة حسب الحالة\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">جميع الحالات</SelectItem>\n                <SelectItem value=\"ACTIVE\">نشط</SelectItem>\n                <SelectItem value=\"INACTIVE\">غير نشط</SelectItem>\n                <SelectItem value=\"LATE\">متأخر</SelectItem>\n                <SelectItem value=\"NON_COMPLIANT\">غير ملتزم</SelectItem>\n              </SelectContent>\n            </Select>\n            <div className=\"text-sm text-gray-600 flex items-center\">\n              <Filter className=\"w-4 h-4 ml-2\" />\n              عدد النتائج: {filteredMembers.length} من {members.length}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* جدول الأعضاء */}\n      <Card>\n        <CardHeader>\n          <CardTitle>قائمة الأعضاء</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full border-collapse\">\n              <thead>\n                <tr className=\"border-b\">\n                  <th className=\"text-right p-3 font-medium text-gray-700\">رقم العضو</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">الاسم الكامل</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">رقم الهاتف</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">تاريخ الانضمام</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">الحالة</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">المساهمات</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">الإجراءات</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredMembers.map((member, index) => {\n                  const stats = getMemberStats(member)\n                  return (\n                    <tr key={member.id} className=\"border-b hover:bg-gray-50\">\n                      <td className=\"p-3\">{index + 1}</td>\n                      <td className=\"p-3 font-medium\">{member.name}</td>\n                      <td className=\"p-3\">{member.phone || 'غير محدد'}</td>\n                      <td className=\"p-3\">{formatDate(member.createdAt)}</td>\n                      <td className=\"p-3\">\n                        <Badge className={getStatusColor(member.status)}>\n                          {getStatusLabel(member.status)}\n                        </Badge>\n                      </td>\n                      <td className=\"p-3\">\n                        <div className=\"text-sm\">\n                          <div className=\"font-medium text-green-600\">\n                            {formatCurrency(stats.totalContributions)}\n                          </div>\n                          <div className=\"text-gray-500\">\n                            {stats.contributionsCount} مساهمة\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"p-3\">\n                        <div className=\"flex space-x-1 space-x-reverse\">\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => {\n                              setSelectedMember(member)\n                              setShowDetailedReport(true)\n                            }}\n                          >\n                            تفاصيل\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => handleExportMemberDetail(member, 'pdf')}\n                          >\n                            PDF\n                          </Button>\n                        </div>\n                      </td>\n                    </tr>\n                  )\n                })}\n              </tbody>\n            </table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* نافذة التقرير المفصل */}\n      {showDetailedReport && selectedMember && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex justify-between items-center\">\n              <span>تقرير مفصل للعضو: {selectedMember.name}</span>\n              <div className=\"flex space-x-2 space-x-reverse\">\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExportMemberDetail(selectedMember, 'pdf')}\n                  className=\"bg-red-600 hover:bg-red-700 text-white\"\n                >\n                  <FileText className=\"w-4 h-4 ml-2\" />\n                  PDF\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExportMemberDetail(selectedMember, 'csv')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                >\n                  <Download className=\"w-4 h-4 ml-2\" />\n                  CSV\n                </Button>\n                <Button\n                  size=\"sm\"\n                  variant=\"outline\"\n                  onClick={() => setShowDetailedReport(false)}\n                >\n                  إغلاق\n                </Button>\n              </div>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* معلومات العضو الأساسية */}\n              <div className=\"space-y-4\">\n                <h3 className=\"font-medium text-gray-900 border-b pb-2\">المعلومات الأساسية</h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center\">\n                    <Users className=\"w-4 h-4 ml-2 text-gray-400\" />\n                    <span className=\"text-sm text-gray-600 ml-2\">الاسم:</span>\n                    <span className=\"font-medium\">{selectedMember.name}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <Phone className=\"w-4 h-4 ml-2 text-gray-400\" />\n                    <span className=\"text-sm text-gray-600 ml-2\">الهاتف:</span>\n                    <span>{selectedMember.phone || 'غير محدد'}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <Mail className=\"w-4 h-4 ml-2 text-gray-400\" />\n                    <span className=\"text-sm text-gray-600 ml-2\">البريد الإلكتروني:</span>\n                    <span>{selectedMember.email || 'غير محدد'}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <MapPin className=\"w-4 h-4 ml-2 text-gray-400\" />\n                    <span className=\"text-sm text-gray-600 ml-2\">العنوان:</span>\n                    <span>{selectedMember.address || 'غير محدد'}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <Calendar className=\"w-4 h-4 ml-2 text-gray-400\" />\n                    <span className=\"text-sm text-gray-600 ml-2\">تاريخ الانضمام:</span>\n                    <span>{formatDate(selectedMember.createdAt)}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"text-sm text-gray-600 ml-2\">الحالة:</span>\n                    <Badge className={getStatusColor(selectedMember.status)}>\n                      {getStatusLabel(selectedMember.status)}\n                    </Badge>\n                  </div>\n                </div>\n              </div>\n\n              {/* ملخص المساهمات */}\n              <div className=\"space-y-4\">\n                <h3 className=\"font-medium text-gray-900 border-b pb-2\">ملخص المساهمات</h3>\n                <div className=\"space-y-3\">\n                  {(() => {\n                    const stats = getMemberStats(selectedMember)\n                    return (\n                      <>\n                        <div className=\"flex items-center\">\n                          <DollarSign className=\"w-4 h-4 ml-2 text-green-500\" />\n                          <span className=\"text-sm text-gray-600 ml-2\">إجمالي المساهمات:</span>\n                          <span className=\"font-bold text-green-600\">\n                            {formatCurrency(stats.totalContributions)}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <span className=\"text-sm text-gray-600 ml-2\">عدد المساهمات:</span>\n                          <span className=\"font-medium\">{stats.contributionsCount}</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <span className=\"text-sm text-gray-600 ml-2\">آخر مساهمة:</span>\n                          <span>\n                            {stats.lastContribution && !isNaN(stats.lastContribution.getTime())\n                              ? formatDate(stats.lastContribution.toISOString())\n                              : 'لا يوجد'\n                            }\n                          </span>\n                        </div>\n                      </>\n                    )\n                  })()}\n                </div>\n              </div>\n            </div>\n\n            {/* جدول المساهمات */}\n            <div className=\"mt-6\">\n              <h3 className=\"font-medium text-gray-900 border-b pb-2 mb-4\">تفاصيل المساهمات</h3>\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full border-collapse\">\n                  <thead>\n                    <tr className=\"border-b bg-gray-50\">\n                      <th className=\"text-right p-3 font-medium text-gray-700\">التاريخ</th>\n                      <th className=\"text-right p-3 font-medium text-gray-700\">المبلغ</th>\n                      <th className=\"text-right p-3 font-medium text-gray-700\">المصدر</th>\n                      <th className=\"text-right p-3 font-medium text-gray-700\">الوصف</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {selectedMember.incomes.map((income) => (\n                      <tr key={income.id} className=\"border-b hover:bg-gray-50\">\n                        <td className=\"p-3\">{formatDate(income.date)}</td>\n                        <td className=\"p-3 font-medium text-green-600\">\n                          {formatCurrency(income.amount)}\n                        </td>\n                        <td className=\"p-3\">{income.source}</td>\n                        <td className=\"p-3\">{income.description || 'غير محدد'}</td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AA+Ce,SAAS,eAAe,EAAE,WAAW,EAAE,WAAW,EAAuB;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,qBAAqB;IACrB,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,WAAW,KAAK,OAAO,IAAI,EAAE;YAC7B,mBAAmB,KAAK,OAAO,IAAI,EAAE;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,aAAa;QACb,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,SACzB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,EAAE,SAAS,eACvB,OAAO,KAAK,EAAE,cAAc,SAAS,WAAW,WAAW;QAE/D;QAEA,cAAc;QACd,IAAI,iBAAiB,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;QACzD;QAEA,mBAAmB;IACrB,GAAG;QAAC;QAAS;QAAY;KAAa;IAEtC,sBAAsB;IACtB,MAAM,iBAAiB,CAAC;QACtB,MAAM,qBAAqB,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;QACvF,MAAM,qBAAqB,OAAO,OAAO,CAAC,MAAM;QAEhD,6BAA6B;QAC7B,MAAM,aAAa,OAAO,OAAO,CAC9B,GAAG,CAAC,CAAA,SAAU,IAAI,KAAK,OAAO,IAAI,GAClC,MAAM,CAAC,CAAA,OAAQ,CAAC,MAAM,KAAK,OAAO;QAErC,MAAM,mBAAmB,WAAW,MAAM,GAAG,IACzC,IAAI,KAAK,KAAK,GAAG,IAAI,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,QACxD;QAEJ,OAAO;YACL;YACA;YACA;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,0BAA0B,CAAC;QAC/B,MAAM,aAAa;YACjB,OAAO;YACP,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,SAAS,gBAAgB,GAAG,CAAC,CAAC,QAAQ;gBACpC,MAAM,QAAQ,eAAe;gBAC7B,OAAO;oBACL,cAAc,QAAQ;oBACtB,MAAM,OAAO,IAAI;oBACjB,OAAO,OAAO,KAAK,IAAI;oBACvB,OAAO,OAAO,KAAK,IAAI;oBACvB,UAAU,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;oBACrC,QAAQ,eAAe,OAAO,MAAM;oBACpC,oBAAoB,MAAM,kBAAkB;oBAC5C,oBAAoB,MAAM,kBAAkB;oBAC5C,kBAAkB,MAAM,gBAAgB,IAAI,CAAC,MAAM,MAAM,gBAAgB,CAAC,OAAO,MAC7E,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,gBAAgB,CAAC,WAAW,MAC7C;gBACN;YACF;QACF;QAEA,IAAI,WAAW,OAAO;YACpB,YAAY,YAAY;QAC1B,OAAO;YACL,YAAY,YAAY;QAC1B;IACF;IAEA,2BAA2B;IAC3B,MAAM,2BAA2B,CAAC,QAAgB;QAChD,MAAM,QAAQ,eAAe;QAC7B,MAAM,aAAa;YACjB,OAAO,CAAC,kBAAkB,EAAE,OAAO,IAAI,EAAE;YACzC,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,QAAQ;gBACN,MAAM,OAAO,IAAI;gBACjB,OAAO,OAAO,KAAK,IAAI;gBACvB,OAAO,OAAO,KAAK,IAAI;gBACvB,SAAS,OAAO,OAAO,IAAI;gBAC3B,QAAQ,eAAe,OAAO,MAAM;gBACpC,UAAU,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;gBACrC,oBAAoB,MAAM,kBAAkB;gBAC5C,oBAAoB,MAAM,kBAAkB;YAC9C;YACA,eAAe,OAAO,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC3C,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI;oBAC5B,QAAQ,OAAO,MAAM;oBACrB,QAAQ,OAAO,MAAM;oBACrB,aAAa,OAAO,WAAW,IAAI;gBACrC,CAAC;QACH;QAEA,IAAI,WAAW,OAAO;YACpB,YAAY,YAAY;QAC1B,OAAO;YACL,YAAY,YAAY;QAC1B;IACF;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,iBAAiB;QACnB;QACA,OAAO,YAAY,CAAC,OAAoC,IAAI;IAC9D;IAEA,wBAAwB;IACxB,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,iBAAiB;QACnB;QACA,OAAO,YAAY,CAAC,OAAoC,IAAI;IAC9D;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGpC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,wBAAwB;gCACvC,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,wBAAwB;gCACvC,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAGd,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAc,eAAe;;kDAC1C,8OAAC,kIAAA,CAAA,gBAAa;kDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;0DAC3B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAO;;;;;;0DACzB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAgB;;;;;;;;;;;;;;;;;;0CAGtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;oCACrB,gBAAgB,MAAM;oCAAC;oCAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAOhE,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;;;;;;;;;;;;kDAG7D,8OAAC;kDACE,gBAAgB,GAAG,CAAC,CAAC,QAAQ;4CAC5B,MAAM,QAAQ,eAAe;4CAC7B,qBACE,8OAAC;gDAAmB,WAAU;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAO,QAAQ;;;;;;kEAC7B,8OAAC;wDAAG,WAAU;kEAAmB,OAAO,IAAI;;;;;;kEAC5C,8OAAC;wDAAG,WAAU;kEAAO,OAAO,KAAK,IAAI;;;;;;kEACrC,8OAAC;wDAAG,WAAU;kEAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;;;;;;kEAChD,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAW,eAAe,OAAO,MAAM;sEAC3C,eAAe,OAAO,MAAM;;;;;;;;;;;kEAGjC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,kBAAkB;;;;;;8EAE1C,8OAAC;oEAAI,WAAU;;wEACZ,MAAM,kBAAkB;wEAAC;;;;;;;;;;;;;;;;;;kEAIhC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS;wEACP,kBAAkB;wEAClB,sBAAsB;oEACxB;8EACD;;;;;;8EAGD,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,IAAM,yBAAyB,QAAQ;8EACjD;;;;;;;;;;;;;;;;;;+CApCE,OAAO,EAAE;;;;;wCA2CtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQT,sBAAsB,gCACrB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;;wCAAK;wCAAmB,eAAe,IAAI;;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,IAAM,yBAAyB,gBAAgB;4CACxD,WAAU;;8DAEV,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,IAAM,yBAAyB,gBAAgB;4CACxD,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,sBAAsB;sDACtC;;;;;;;;;;;;;;;;;;;;;;;kCAMP,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,8OAAC;gEAAK,WAAU;0EAAe,eAAe,IAAI;;;;;;;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,8OAAC;0EAAM,eAAe,KAAK,IAAI;;;;;;;;;;;;kEAEjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,8OAAC;0EAAM,eAAe,KAAK,IAAI;;;;;;;;;;;;kEAEjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,8OAAC;0EAAM,eAAe,OAAO,IAAI;;;;;;;;;;;;kEAEnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,8OAAC;0EAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,eAAe,SAAS;;;;;;;;;;;;kEAE5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAW,eAAe,eAAe,MAAM;0EACnD,eAAe,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kDAO7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DACZ,CAAC;oDACA,MAAM,QAAQ,eAAe;oDAC7B,qBACE;;0EACE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;kFAC7C,8OAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,kBAAkB;;;;;;;;;;;;0EAG5C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;kFAC7C,8OAAC;wEAAK,WAAU;kFAAe,MAAM,kBAAkB;;;;;;;;;;;;0EAEzD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;kFAC7C,8OAAC;kFACE,MAAM,gBAAgB,IAAI,CAAC,MAAM,MAAM,gBAAgB,CAAC,OAAO,MAC5D,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,gBAAgB,CAAC,WAAW,MAC7C;;;;;;;;;;;;;;gDAMd,CAAC;;;;;;;;;;;;;;;;;;0CAMP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+C;;;;;;kDAC7D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;8DACC,cAAA,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;;;;;;;;;;;;8DAG7D,8OAAC;8DACE,eAAe,OAAO,CAAC,GAAG,CAAC,CAAC,uBAC3B,8OAAC;4DAAmB,WAAU;;8EAC5B,8OAAC;oEAAG,WAAU;8EAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI;;;;;;8EAC3C,8OAAC;oEAAG,WAAU;8EACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM;;;;;;8EAE/B,8OAAC;oEAAG,WAAU;8EAAO,OAAO,MAAM;;;;;;8EAClC,8OAAC;oEAAG,WAAU;8EAAO,OAAO,WAAW,IAAI;;;;;;;2DANpC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBxC", "debugId": null}}, {"offset": {"line": 2344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/reports/IncomesReports.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { formatCurrency, formatDate } from '@/lib/utils'\nimport {\n  TrendingUp,\n  FileText,\n  Download,\n  Search,\n  Filter,\n  Calendar,\n  DollarSign,\n  User,\n  Receipt\n} from 'lucide-react'\n\ninterface Income {\n  id: string\n  amount: number\n  date: string\n  source: string\n  type: 'SUBSCRIPTION' | 'DONATION' | 'EVENT' | 'OTHER'\n  description?: string\n  member?: {\n    id: string\n    name: string\n  }\n  createdBy?: {\n    name: string\n  }\n}\n\ninterface IncomesReportsProps {\n  onExportPDF: (data: any, type: string) => void\n  onExportCSV: (data: any, type: string) => void\n}\n\nexport default function IncomesReports({ onExportPDF, onExportCSV }: IncomesReportsProps) {\n  const [incomes, setIncomes] = useState<Income[]>([])\n  const [filteredIncomes, setFilteredIncomes] = useState<Income[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [typeFilter, setTypeFilter] = useState('all')\n  const [dateFilter, setDateFilter] = useState('all')\n  const [startDate, setStartDate] = useState('')\n  const [endDate, setEndDate] = useState('')\n\n  // جلب بيانات الإيرادات\n  const fetchIncomes = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/incomes?limit=1000')\n      if (!response.ok) throw new Error('فشل في جلب بيانات الإيرادات')\n\n      const data = await response.json()\n      setIncomes(data.incomes || [])\n      setFilteredIncomes(data.incomes || [])\n    } catch (error) {\n      console.error('خطأ في جلب بيانات الإيرادات:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchIncomes()\n  }, [])\n\n  // تطبيق الفلاتر\n  useEffect(() => {\n    let filtered = incomes\n\n    // فلتر البحث\n    if (searchTerm) {\n      filtered = filtered.filter(income =>\n        income.source.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        income.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        income.member?.name.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // فلتر النوع\n    if (typeFilter !== 'all') {\n      filtered = filtered.filter(income => income.type === typeFilter)\n    }\n\n    // فلتر التاريخ\n    if (dateFilter !== 'all') {\n      const now = new Date()\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())\n      \n      switch (dateFilter) {\n        case 'today':\n          filtered = filtered.filter(income => {\n            const incomeDate = new Date(income.date)\n            return incomeDate >= today\n          })\n          break\n        case 'week':\n          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)\n          filtered = filtered.filter(income => {\n            const incomeDate = new Date(income.date)\n            return incomeDate >= weekAgo\n          })\n          break\n        case 'month':\n          const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())\n          filtered = filtered.filter(income => {\n            const incomeDate = new Date(income.date)\n            return incomeDate >= monthAgo\n          })\n          break\n        case 'year':\n          const yearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate())\n          filtered = filtered.filter(income => {\n            const incomeDate = new Date(income.date)\n            return incomeDate >= yearAgo\n          })\n          break\n        case 'custom':\n          if (startDate && endDate) {\n            const start = new Date(startDate)\n            const end = new Date(endDate)\n            filtered = filtered.filter(income => {\n              const incomeDate = new Date(income.date)\n              return incomeDate >= start && incomeDate <= end\n            })\n          }\n          break\n      }\n    }\n\n    setFilteredIncomes(filtered)\n  }, [incomes, searchTerm, typeFilter, dateFilter, startDate, endDate])\n\n  // حساب الإحصائيات\n  const getStatistics = () => {\n    const total = filteredIncomes.reduce((sum, income) => sum + income.amount, 0)\n    const count = filteredIncomes.length\n    const average = count > 0 ? total / count : 0\n\n    // إحصائيات حسب النوع\n    const byType = filteredIncomes.reduce((acc, income) => {\n      acc[income.type] = (acc[income.type] || 0) + income.amount\n      return acc\n    }, {} as Record<string, number>)\n\n    // إحصائيات حسب العضو\n    const byMember = filteredIncomes.reduce((acc, income) => {\n      if (income.member) {\n        const memberName = income.member.name\n        acc[memberName] = (acc[memberName] || 0) + income.amount\n      }\n      return acc\n    }, {} as Record<string, number>)\n\n    return {\n      total,\n      count,\n      average,\n      byType,\n      byMember\n    }\n  }\n\n  // تصدير التقرير\n  const handleExport = (format: 'pdf' | 'csv', reportType: 'detailed' | 'summary' | 'by-type' | 'by-member') => {\n    const stats = getStatistics()\n    \n    const reportData: any = {\n      title: '',\n      date: new Date().toLocaleDateString('ar-JO'),\n      period: getPeriodLabel(),\n      statistics: stats\n    }\n\n    switch (reportType) {\n      case 'detailed':\n        reportData.title = 'تقرير الإيرادات المفصل'\n        reportData.incomes = filteredIncomes.map(income => ({\n          date: formatDate(income.date),\n          memberName: income.member?.name || 'غير محدد',\n          type: getTypeLabel(income.type),\n          source: income.source,\n          amount: income.amount,\n          description: income.description || 'غير محدد',\n          createdBy: income.createdBy?.name || 'غير محدد'\n        }))\n        break\n      \n      case 'summary':\n        reportData.title = 'ملخص الإيرادات'\n        reportData.summary = {\n          totalAmount: stats.total,\n          totalCount: stats.count,\n          averageAmount: stats.average\n        }\n        break\n      \n      case 'by-type':\n        reportData.title = 'تقرير الإيرادات حسب النوع'\n        reportData.byType = Object.entries(stats.byType).map(([type, amount]) => ({\n          type: getTypeLabel(type),\n          amount,\n          percentage: ((amount / stats.total) * 100).toFixed(1)\n        }))\n        break\n      \n      case 'by-member':\n        reportData.title = 'تقرير الإيرادات حسب العضو'\n        reportData.byMember = Object.entries(stats.byMember)\n          .sort(([,a], [,b]) => b - a)\n          .map(([memberName, amount]) => ({\n            memberName,\n            amount,\n            percentage: ((amount / stats.total) * 100).toFixed(1)\n          }))\n        break\n    }\n\n    if (format === 'pdf') {\n      onExportPDF(reportData, `incomes-${reportType}`)\n    } else {\n      onExportCSV(reportData, `incomes-${reportType}`)\n    }\n  }\n\n  // الحصول على تسمية النوع\n  const getTypeLabel = (type: string) => {\n    const typeLabels = {\n      'SUBSCRIPTION': 'اشتراك',\n      'DONATION': 'تبرع',\n      'EVENT': 'فعالية',\n      'OTHER': 'أخرى'\n    }\n    return typeLabels[type as keyof typeof typeLabels] || type\n  }\n\n  // الحصول على لون النوع\n  const getTypeColor = (type: string) => {\n    const typeColors = {\n      'SUBSCRIPTION': 'bg-blue-100 text-blue-800',\n      'DONATION': 'bg-green-100 text-green-800',\n      'EVENT': 'bg-purple-100 text-purple-800',\n      'OTHER': 'bg-gray-100 text-gray-800'\n    }\n    return typeColors[type as keyof typeof typeColors] || 'bg-gray-100 text-gray-800'\n  }\n\n  // الحصول على تسمية الفترة\n  const getPeriodLabel = () => {\n    switch (dateFilter) {\n      case 'today': return 'اليوم'\n      case 'week': return 'آخر أسبوع'\n      case 'month': return 'آخر شهر'\n      case 'year': return 'آخر سنة'\n      case 'custom': return `من ${startDate} إلى ${endDate}`\n      default: return 'جميع الفترات'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"text-gray-500\">جاري تحميل بيانات الإيرادات...</div>\n      </div>\n    )\n  }\n\n  const stats = getStatistics()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* عنوان القسم */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900 flex items-center\">\n            <TrendingUp className=\"w-5 h-5 ml-2\" />\n            تقارير الإيرادات\n          </h2>\n          <p className=\"text-gray-600\">تقارير شاملة عن الإيرادات والمساهمات</p>\n        </div>\n        <div className=\"flex space-x-2 space-x-reverse\">\n          <Button\n            onClick={() => handleExport('pdf', 'detailed')}\n            className=\"bg-red-600 hover:bg-red-700 text-white\"\n          >\n            <FileText className=\"w-4 h-4 ml-2\" />\n            تقرير مفصل PDF\n          </Button>\n          <Button\n            onClick={() => handleExport('csv', 'detailed')}\n            className=\"bg-green-600 hover:bg-green-700 text-white\"\n          >\n            <Download className=\"w-4 h-4 ml-2\" />\n            تصدير CSV\n          </Button>\n        </div>\n      </div>\n\n      {/* أدوات البحث والفلترة */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div className=\"relative\">\n              <Search className=\"absolute right-3 top-3 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"البحث في الإيرادات...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pr-10\"\n              />\n            </div>\n            \n            <Select value={typeFilter} onValueChange={setTypeFilter}>\n              <SelectTrigger>\n                <SelectValue placeholder=\"فلترة حسب النوع\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">جميع الأنواع</SelectItem>\n                <SelectItem value=\"SUBSCRIPTION\">اشتراك</SelectItem>\n                <SelectItem value=\"DONATION\">تبرع</SelectItem>\n                <SelectItem value=\"EVENT\">فعالية</SelectItem>\n                <SelectItem value=\"OTHER\">أخرى</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <Select value={dateFilter} onValueChange={setDateFilter}>\n              <SelectTrigger>\n                <SelectValue placeholder=\"فلترة حسب التاريخ\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">جميع الفترات</SelectItem>\n                <SelectItem value=\"today\">اليوم</SelectItem>\n                <SelectItem value=\"week\">آخر أسبوع</SelectItem>\n                <SelectItem value=\"month\">آخر شهر</SelectItem>\n                <SelectItem value=\"year\">آخر سنة</SelectItem>\n                <SelectItem value=\"custom\">فترة مخصصة</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <div className=\"text-sm text-gray-600 flex items-center\">\n              <Filter className=\"w-4 h-4 ml-2\" />\n              عدد النتائج: {filteredIncomes.length} من {incomes.length}\n            </div>\n          </div>\n\n          {/* فلتر التاريخ المخصص */}\n          {dateFilter === 'custom' && (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">من تاريخ</label>\n                <Input\n                  type=\"date\"\n                  value={startDate}\n                  onChange={(e) => setStartDate(e.target.value)}\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">إلى تاريخ</label>\n                <Input\n                  type=\"date\"\n                  value={endDate}\n                  onChange={(e) => setEndDate(e.target.value)}\n                />\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* الإحصائيات السريعة */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <DollarSign className=\"w-8 h-8 text-green-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">إجمالي الإيرادات</p>\n                <p className=\"text-2xl font-bold text-green-600\">{formatCurrency(stats.total)}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <Receipt className=\"w-8 h-8 text-blue-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">عدد المعاملات</p>\n                <p className=\"text-2xl font-bold text-blue-600\">{stats.count}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <TrendingUp className=\"w-8 h-8 text-purple-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">متوسط المبلغ</p>\n                <p className=\"text-2xl font-bold text-purple-600\">{formatCurrency(stats.average)}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <Calendar className=\"w-8 h-8 text-orange-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">الفترة</p>\n                <p className=\"text-lg font-bold text-orange-600\">{getPeriodLabel()}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* أزرار التقارير المختلفة */}\n      <Card>\n        <CardHeader>\n          <CardTitle>تقارير متخصصة</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium\">تقرير حسب النوع</h4>\n              <div className=\"flex space-x-2 space-x-reverse\">\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('pdf', 'by-type')}\n                  className=\"bg-red-600 hover:bg-red-700 text-white\"\n                >\n                  PDF\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('csv', 'by-type')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                >\n                  CSV\n                </Button>\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium\">تقرير حسب العضو</h4>\n              <div className=\"flex space-x-2 space-x-reverse\">\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('pdf', 'by-member')}\n                  className=\"bg-red-600 hover:bg-red-700 text-white\"\n                >\n                  PDF\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('csv', 'by-member')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                >\n                  CSV\n                </Button>\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium\">ملخص الإيرادات</h4>\n              <div className=\"flex space-x-2 space-x-reverse\">\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('pdf', 'summary')}\n                  className=\"bg-red-600 hover:bg-red-700 text-white\"\n                >\n                  PDF\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('csv', 'summary')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                >\n                  CSV\n                </Button>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* جدول الإيرادات */}\n      <Card>\n        <CardHeader>\n          <CardTitle>تفاصيل الإيرادات</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full border-collapse\">\n              <thead>\n                <tr className=\"border-b\">\n                  <th className=\"text-right p-3 font-medium text-gray-700\">التاريخ</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">العضو</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">النوع</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">المصدر</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">المبلغ</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">الوصف</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredIncomes.map((income) => (\n                  <tr key={income.id} className=\"border-b hover:bg-gray-50\">\n                    <td className=\"p-3\">{formatDate(income.date)}</td>\n                    <td className=\"p-3\">\n                      <div className=\"flex items-center\">\n                        <User className=\"w-4 h-4 ml-2 text-gray-400\" />\n                        {income.member?.name || 'غير محدد'}\n                      </div>\n                    </td>\n                    <td className=\"p-3\">\n                      <Badge className={getTypeColor(income.type)}>\n                        {getTypeLabel(income.type)}\n                      </Badge>\n                    </td>\n                    <td className=\"p-3\">{income.source}</td>\n                    <td className=\"p-3 font-bold text-green-600\">\n                      {formatCurrency(income.amount)}\n                    </td>\n                    <td className=\"p-3 text-sm text-gray-600\">\n                      {income.description || 'غير محدد'}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AA0Ce,SAAS,eAAe,EAAE,WAAW,EAAE,WAAW,EAAuB;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uBAAuB;IACvB,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,WAAW,KAAK,OAAO,IAAI,EAAE;YAC7B,mBAAmB,KAAK,OAAO,IAAI,EAAE;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,aAAa;QACb,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,SACzB,OAAO,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,OAAO,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW,OACjE,OAAO,MAAM,EAAE,KAAK,cAAc,SAAS,WAAW,WAAW;QAErE;QAEA,aAAa;QACb,IAAI,eAAe,OAAO;YACxB,WAAW,SAAS,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;QACvD;QAEA,eAAe;QACf,IAAI,eAAe,OAAO;YACxB,MAAM,MAAM,IAAI;YAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;YAErE,OAAQ;gBACN,KAAK;oBACH,WAAW,SAAS,MAAM,CAAC,CAAA;wBACzB,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;wBACvC,OAAO,cAAc;oBACvB;oBACA;gBACF,KAAK;oBACH,MAAM,UAAU,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;oBAC9D,WAAW,SAAS,MAAM,CAAC,CAAA;wBACzB,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;wBACvC,OAAO,cAAc;oBACvB;oBACA;gBACF,KAAK;oBACH,MAAM,WAAW,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,KAAK,GAAG,MAAM,OAAO;oBAClF,WAAW,SAAS,MAAM,CAAC,CAAA;wBACzB,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;wBACvC,OAAO,cAAc;oBACvB;oBACA;gBACF,KAAK;oBACH,MAAM,UAAU,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,IAAI,MAAM,OAAO;oBACjF,WAAW,SAAS,MAAM,CAAC,CAAA;wBACzB,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;wBACvC,OAAO,cAAc;oBACvB;oBACA;gBACF,KAAK;oBACH,IAAI,aAAa,SAAS;wBACxB,MAAM,QAAQ,IAAI,KAAK;wBACvB,MAAM,MAAM,IAAI,KAAK;wBACrB,WAAW,SAAS,MAAM,CAAC,CAAA;4BACzB,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;4BACvC,OAAO,cAAc,SAAS,cAAc;wBAC9C;oBACF;oBACA;YACJ;QACF;QAEA,mBAAmB;IACrB,GAAG;QAAC;QAAS;QAAY;QAAY;QAAY;QAAW;KAAQ;IAEpE,kBAAkB;IAClB,MAAM,gBAAgB;QACpB,MAAM,QAAQ,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;QAC3E,MAAM,QAAQ,gBAAgB,MAAM;QACpC,MAAM,UAAU,QAAQ,IAAI,QAAQ,QAAQ;QAE5C,qBAAqB;QACrB,MAAM,SAAS,gBAAgB,MAAM,CAAC,CAAC,KAAK;YAC1C,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,MAAM;YAC1D,OAAO;QACT,GAAG,CAAC;QAEJ,qBAAqB;QACrB,MAAM,WAAW,gBAAgB,MAAM,CAAC,CAAC,KAAK;YAC5C,IAAI,OAAO,MAAM,EAAE;gBACjB,MAAM,aAAa,OAAO,MAAM,CAAC,IAAI;gBACrC,GAAG,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,IAAI,OAAO,MAAM;YAC1D;YACA,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAC,QAAuB;QAC3C,MAAM,QAAQ;QAEd,MAAM,aAAkB;YACtB,OAAO;YACP,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,QAAQ;YACR,YAAY;QACd;QAEA,OAAQ;YACN,KAAK;gBACH,WAAW,KAAK,GAAG;gBACnB,WAAW,OAAO,GAAG,gBAAgB,GAAG,CAAC,CAAA,SAAU,CAAC;wBAClD,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI;wBAC5B,YAAY,OAAO,MAAM,EAAE,QAAQ;wBACnC,MAAM,aAAa,OAAO,IAAI;wBAC9B,QAAQ,OAAO,MAAM;wBACrB,QAAQ,OAAO,MAAM;wBACrB,aAAa,OAAO,WAAW,IAAI;wBACnC,WAAW,OAAO,SAAS,EAAE,QAAQ;oBACvC,CAAC;gBACD;YAEF,KAAK;gBACH,WAAW,KAAK,GAAG;gBACnB,WAAW,OAAO,GAAG;oBACnB,aAAa,MAAM,KAAK;oBACxB,YAAY,MAAM,KAAK;oBACvB,eAAe,MAAM,OAAO;gBAC9B;gBACA;YAEF,KAAK;gBACH,WAAW,KAAK,GAAG;gBACnB,WAAW,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,GAAK,CAAC;wBACxE,MAAM,aAAa;wBACnB;wBACA,YAAY,CAAC,AAAC,SAAS,MAAM,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;oBACrD,CAAC;gBACD;YAEF,KAAK;gBACH,WAAW,KAAK,GAAG;gBACnB,WAAW,QAAQ,GAAG,OAAO,OAAO,CAAC,MAAM,QAAQ,EAChD,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,IAAI,GACzB,GAAG,CAAC,CAAC,CAAC,YAAY,OAAO,GAAK,CAAC;wBAC9B;wBACA;wBACA,YAAY,CAAC,AAAC,SAAS,MAAM,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;oBACrD,CAAC;gBACH;QACJ;QAEA,IAAI,WAAW,OAAO;YACpB,YAAY,YAAY,CAAC,QAAQ,EAAE,YAAY;QACjD,OAAO;YACL,YAAY,YAAY,CAAC,QAAQ,EAAE,YAAY;QACjD;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa;YACjB,gBAAgB;YAChB,YAAY;YACZ,SAAS;YACT,SAAS;QACX;QACA,OAAO,UAAU,CAAC,KAAgC,IAAI;IACxD;IAEA,uBAAuB;IACvB,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa;YACjB,gBAAgB;YAChB,YAAY;YACZ,SAAS;YACT,SAAS;QACX;QACA,OAAO,UAAU,CAAC,KAAgC,IAAI;IACxD;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO,CAAC,GAAG,EAAE,UAAU,KAAK,EAAE,SAAS;YACtD;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,MAAM,QAAQ;IAEd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGzC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,aAAa,OAAO;gCACnC,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,aAAa,OAAO;gCACnC,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAId,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAY,eAAe;;sDACxC,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAe;;;;;;8DACjC,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;8DAC7B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAI9B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAY,eAAe;;sDACxC,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;wCACrB,gBAAgB,MAAM;wCAAC;wCAAK,QAAQ,MAAM;;;;;;;;;;;;;wBAK3D,eAAe,0BACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGhD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMpF,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMpE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAsC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvF,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5D,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;;;;;;;;;;;;kDAG7D,8OAAC;kDACE,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;gDAAmB,WAAU;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI;;;;;;kEAC3C,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,OAAO,MAAM,EAAE,QAAQ;;;;;;;;;;;;kEAG5B,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAW,aAAa,OAAO,IAAI;sEACvC,aAAa,OAAO,IAAI;;;;;;;;;;;kEAG7B,8OAAC;wDAAG,WAAU;kEAAO,OAAO,MAAM;;;;;;kEAClC,8OAAC;wDAAG,WAAU;kEACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM;;;;;;kEAE/B,8OAAC;wDAAG,WAAU;kEACX,OAAO,WAAW,IAAI;;;;;;;+CAlBlB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BpC", "debugId": null}}, {"offset": {"line": 3572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/reports/ExpensesReports.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { formatCurrency, formatDate } from '@/lib/utils'\nimport {\n  TrendingDown,\n  FileText,\n  Download,\n  Search,\n  Filter,\n  Calendar,\n  DollarSign,\n  Building,\n  Receipt\n} from 'lucide-react'\n\ninterface Expense {\n  id: string\n  amount: number\n  date: string\n  description: string\n  category: 'MEETINGS' | 'GENERAL' | 'MAINTENANCE' | 'SOCIAL' | 'EVENTS' | 'OTHER'\n  recipient?: string\n  createdBy?: {\n    name: string\n  }\n}\n\ninterface ExpensesReportsProps {\n  onExportPDF: (data: any, type: string) => void\n  onExportCSV: (data: any, type: string) => void\n}\n\nexport default function ExpensesReports({ onExportPDF, onExportCSV }: ExpensesReportsProps) {\n  const [expenses, setExpenses] = useState<Expense[]>([])\n  const [filteredExpenses, setFilteredExpenses] = useState<Expense[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [categoryFilter, setCategoryFilter] = useState('all')\n  const [dateFilter, setDateFilter] = useState('all')\n  const [startDate, setStartDate] = useState('')\n  const [endDate, setEndDate] = useState('')\n\n  // جلب بيانات المصروفات\n  const fetchExpenses = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/expenses?limit=1000')\n      if (!response.ok) throw new Error('فشل في جلب بيانات المصروفات')\n      \n      const data = await response.json()\n      setExpenses(data.expenses || [])\n      setFilteredExpenses(data.expenses || [])\n    } catch (error) {\n      console.error('خطأ في جلب بيانات المصروفات:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchExpenses()\n  }, [])\n\n  // تطبيق الفلاتر\n  useEffect(() => {\n    let filtered = expenses\n\n    // فلتر البحث\n    if (searchTerm) {\n      filtered = filtered.filter(expense =>\n        expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        expense.recipient?.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // فلتر الفئة\n    if (categoryFilter !== 'all') {\n      filtered = filtered.filter(expense => expense.category === categoryFilter)\n    }\n\n    // فلتر التاريخ\n    if (dateFilter !== 'all') {\n      const now = new Date()\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())\n      \n      switch (dateFilter) {\n        case 'today':\n          filtered = filtered.filter(expense => {\n            const expenseDate = new Date(expense.date)\n            return expenseDate >= today\n          })\n          break\n        case 'week':\n          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)\n          filtered = filtered.filter(expense => {\n            const expenseDate = new Date(expense.date)\n            return expenseDate >= weekAgo\n          })\n          break\n        case 'month':\n          const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())\n          filtered = filtered.filter(expense => {\n            const expenseDate = new Date(expense.date)\n            return expenseDate >= monthAgo\n          })\n          break\n        case 'year':\n          const yearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate())\n          filtered = filtered.filter(expense => {\n            const expenseDate = new Date(expense.date)\n            return expenseDate >= yearAgo\n          })\n          break\n        case 'custom':\n          if (startDate && endDate) {\n            const start = new Date(startDate)\n            const end = new Date(endDate)\n            filtered = filtered.filter(expense => {\n              const expenseDate = new Date(expense.date)\n              return expenseDate >= start && expenseDate <= end\n            })\n          }\n          break\n      }\n    }\n\n    setFilteredExpenses(filtered)\n  }, [expenses, searchTerm, categoryFilter, dateFilter, startDate, endDate])\n\n  // حساب الإحصائيات\n  const getStatistics = () => {\n    const total = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0)\n    const count = filteredExpenses.length\n    const average = count > 0 ? total / count : 0\n\n    // إحصائيات حسب الفئة\n    const byCategory = filteredExpenses.reduce((acc, expense) => {\n      acc[expense.category] = (acc[expense.category] || 0) + expense.amount\n      return acc\n    }, {} as Record<string, number>)\n\n    // إحصائيات حسب المستفيد\n    const byRecipient = filteredExpenses.reduce((acc, expense) => {\n      if (expense.recipient) {\n        acc[expense.recipient] = (acc[expense.recipient] || 0) + expense.amount\n      }\n      return acc\n    }, {} as Record<string, number>)\n\n    return {\n      total,\n      count,\n      average,\n      byCategory,\n      byRecipient\n    }\n  }\n\n  // تصدير التقرير\n  const handleExport = (format: 'pdf' | 'csv', reportType: 'detailed' | 'summary' | 'by-category' | 'by-recipient') => {\n    const stats = getStatistics()\n    \n    const reportData: any = {\n      title: '',\n      date: new Date().toLocaleDateString('ar-JO'),\n      period: getPeriodLabel(),\n      statistics: stats\n    }\n\n    switch (reportType) {\n      case 'detailed':\n        reportData.title = 'تقرير المصروفات المفصل'\n        reportData.expenses = filteredExpenses.map(expense => ({\n          date: formatDate(expense.date),\n          description: expense.description,\n          category: getCategoryLabel(expense.category),\n          amount: expense.amount,\n          recipient: expense.recipient || 'غير محدد',\n          createdBy: expense.createdBy?.name || 'غير محدد'\n        }))\n        break\n      \n      case 'summary':\n        reportData.title = 'ملخص المصروفات'\n        reportData.summary = {\n          totalAmount: stats.total,\n          totalCount: stats.count,\n          averageAmount: stats.average\n        }\n        break\n      \n      case 'by-category':\n        reportData.title = 'تقرير المصروفات حسب الفئة'\n        reportData.byCategory = Object.entries(stats.byCategory).map(([category, amount]) => ({\n          category: getCategoryLabel(category),\n          amount,\n          percentage: ((amount / stats.total) * 100).toFixed(1)\n        }))\n        break\n      \n      case 'by-recipient':\n        reportData.title = 'تقرير المصروفات حسب المستفيد'\n        reportData.byRecipient = Object.entries(stats.byRecipient)\n          .sort(([,a], [,b]) => b - a)\n          .map(([recipient, amount]) => ({\n            recipient,\n            amount,\n            percentage: ((amount / stats.total) * 100).toFixed(1)\n          }))\n        break\n    }\n\n    if (format === 'pdf') {\n      onExportPDF(reportData, `expenses-${reportType}`)\n    } else {\n      onExportCSV(reportData, `expenses-${reportType}`)\n    }\n  }\n\n  // الحصول على تسمية الفئة\n  const getCategoryLabel = (category: string) => {\n    const categoryLabels = {\n      'MEETINGS': 'اجتماعات',\n      'GENERAL': 'عامة',\n      'MAINTENANCE': 'صيانة',\n      'SOCIAL': 'اجتماعية',\n      'EVENTS': 'فعاليات',\n      'OTHER': 'أخرى'\n    }\n    return categoryLabels[category as keyof typeof categoryLabels] || category\n  }\n\n  // الحصول على لون الفئة\n  const getCategoryColor = (category: string) => {\n    const categoryColors = {\n      'MEETINGS': 'bg-blue-100 text-blue-800',\n      'GENERAL': 'bg-gray-100 text-gray-800',\n      'MAINTENANCE': 'bg-orange-100 text-orange-800',\n      'SOCIAL': 'bg-green-100 text-green-800',\n      'EVENTS': 'bg-purple-100 text-purple-800',\n      'OTHER': 'bg-yellow-100 text-yellow-800'\n    }\n    return categoryColors[category as keyof typeof categoryColors] || 'bg-gray-100 text-gray-800'\n  }\n\n  // الحصول على تسمية الفترة\n  const getPeriodLabel = () => {\n    switch (dateFilter) {\n      case 'today': return 'اليوم'\n      case 'week': return 'آخر أسبوع'\n      case 'month': return 'آخر شهر'\n      case 'year': return 'آخر سنة'\n      case 'custom': return `من ${startDate} إلى ${endDate}`\n      default: return 'جميع الفترات'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"text-gray-500\">جاري تحميل بيانات المصروفات...</div>\n      </div>\n    )\n  }\n\n  const stats = getStatistics()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* عنوان القسم */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900 flex items-center\">\n            <TrendingDown className=\"w-5 h-5 ml-2\" />\n            تقارير المصروفات\n          </h2>\n          <p className=\"text-gray-600\">تقارير شاملة عن المصروفات والنفقات</p>\n        </div>\n        <div className=\"flex space-x-2 space-x-reverse\">\n          <Button\n            onClick={() => handleExport('pdf', 'detailed')}\n            className=\"bg-red-600 hover:bg-red-700 text-white\"\n          >\n            <FileText className=\"w-4 h-4 ml-2\" />\n            تقرير مفصل PDF\n          </Button>\n          <Button\n            onClick={() => handleExport('csv', 'detailed')}\n            className=\"bg-green-600 hover:bg-green-700 text-white\"\n          >\n            <Download className=\"w-4 h-4 ml-2\" />\n            تصدير CSV\n          </Button>\n        </div>\n      </div>\n\n      {/* أدوات البحث والفلترة */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div className=\"relative\">\n              <Search className=\"absolute right-3 top-3 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"البحث في المصروفات...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pr-10\"\n              />\n            </div>\n            \n            <Select value={categoryFilter} onValueChange={setCategoryFilter}>\n              <SelectTrigger>\n                <SelectValue placeholder=\"فلترة حسب الفئة\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">جميع الفئات</SelectItem>\n                <SelectItem value=\"MEETINGS\">اجتماعات</SelectItem>\n                <SelectItem value=\"GENERAL\">عامة</SelectItem>\n                <SelectItem value=\"MAINTENANCE\">صيانة</SelectItem>\n                <SelectItem value=\"SOCIAL\">اجتماعية</SelectItem>\n                <SelectItem value=\"EVENTS\">فعاليات</SelectItem>\n                <SelectItem value=\"OTHER\">أخرى</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <Select value={dateFilter} onValueChange={setDateFilter}>\n              <SelectTrigger>\n                <SelectValue placeholder=\"فلترة حسب التاريخ\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">جميع الفترات</SelectItem>\n                <SelectItem value=\"today\">اليوم</SelectItem>\n                <SelectItem value=\"week\">آخر أسبوع</SelectItem>\n                <SelectItem value=\"month\">آخر شهر</SelectItem>\n                <SelectItem value=\"year\">آخر سنة</SelectItem>\n                <SelectItem value=\"custom\">فترة مخصصة</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <div className=\"text-sm text-gray-600 flex items-center\">\n              <Filter className=\"w-4 h-4 ml-2\" />\n              عدد النتائج: {filteredExpenses.length} من {expenses.length}\n            </div>\n          </div>\n\n          {/* فلتر التاريخ المخصص */}\n          {dateFilter === 'custom' && (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">من تاريخ</label>\n                <Input\n                  type=\"date\"\n                  value={startDate}\n                  onChange={(e) => setStartDate(e.target.value)}\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">إلى تاريخ</label>\n                <Input\n                  type=\"date\"\n                  value={endDate}\n                  onChange={(e) => setEndDate(e.target.value)}\n                />\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* الإحصائيات السريعة */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <DollarSign className=\"w-8 h-8 text-red-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">إجمالي المصروفات</p>\n                <p className=\"text-2xl font-bold text-red-600\">{formatCurrency(stats.total)}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <Receipt className=\"w-8 h-8 text-blue-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">عدد المعاملات</p>\n                <p className=\"text-2xl font-bold text-blue-600\">{stats.count}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <TrendingDown className=\"w-8 h-8 text-purple-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">متوسط المبلغ</p>\n                <p className=\"text-2xl font-bold text-purple-600\">{formatCurrency(stats.average)}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <Calendar className=\"w-8 h-8 text-orange-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">الفترة</p>\n                <p className=\"text-lg font-bold text-orange-600\">{getPeriodLabel()}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* أزرار التقارير المختلفة */}\n      <Card>\n        <CardHeader>\n          <CardTitle>تقارير متخصصة</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium\">تقرير حسب الفئة</h4>\n              <div className=\"flex space-x-2 space-x-reverse\">\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('pdf', 'by-category')}\n                  className=\"bg-red-600 hover:bg-red-700 text-white\"\n                >\n                  PDF\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('csv', 'by-category')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                >\n                  CSV\n                </Button>\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium\">تقرير حسب المستفيد</h4>\n              <div className=\"flex space-x-2 space-x-reverse\">\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('pdf', 'by-recipient')}\n                  className=\"bg-red-600 hover:bg-red-700 text-white\"\n                >\n                  PDF\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('csv', 'by-recipient')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                >\n                  CSV\n                </Button>\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium\">ملخص المصروفات</h4>\n              <div className=\"flex space-x-2 space-x-reverse\">\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('pdf', 'summary')}\n                  className=\"bg-red-600 hover:bg-red-700 text-white\"\n                >\n                  PDF\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExport('csv', 'summary')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                >\n                  CSV\n                </Button>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* جدول المصروفات */}\n      <Card>\n        <CardHeader>\n          <CardTitle>تفاصيل المصروفات</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full border-collapse\">\n              <thead>\n                <tr className=\"border-b\">\n                  <th className=\"text-right p-3 font-medium text-gray-700\">التاريخ</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">الوصف</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">الفئة</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">المبلغ</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">المستفيد</th>\n                  <th className=\"text-right p-3 font-medium text-gray-700\">المسؤول</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredExpenses.map((expense) => (\n                  <tr key={expense.id} className=\"border-b hover:bg-gray-50\">\n                    <td className=\"p-3\">{formatDate(expense.date)}</td>\n                    <td className=\"p-3 font-medium\">{expense.description}</td>\n                    <td className=\"p-3\">\n                      <Badge className={getCategoryColor(expense.category)}>\n                        {getCategoryLabel(expense.category)}\n                      </Badge>\n                    </td>\n                    <td className=\"p-3 font-bold text-red-600\">\n                      {formatCurrency(expense.amount)}\n                    </td>\n                    <td className=\"p-3\">\n                      <div className=\"flex items-center\">\n                        <Building className=\"w-4 h-4 ml-2 text-gray-400\" />\n                        {expense.recipient || 'غير محدد'}\n                      </div>\n                    </td>\n                    <td className=\"p-3 text-sm text-gray-600\">\n                      {expense.createdBy?.name || 'غير محدد'}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAsCe,SAAS,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAwB;IACxF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uBAAuB;IACvB,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,QAAQ,IAAI,EAAE;YAC/B,oBAAoB,KAAK,QAAQ,IAAI,EAAE;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,aAAa;QACb,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,QAAQ,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW;QAEpE;QAEA,aAAa;QACb,IAAI,mBAAmB,OAAO;YAC5B,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;QAC7D;QAEA,eAAe;QACf,IAAI,eAAe,OAAO;YACxB,MAAM,MAAM,IAAI;YAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;YAErE,OAAQ;gBACN,KAAK;oBACH,WAAW,SAAS,MAAM,CAAC,CAAA;wBACzB,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;wBACzC,OAAO,eAAe;oBACxB;oBACA;gBACF,KAAK;oBACH,MAAM,UAAU,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;oBAC9D,WAAW,SAAS,MAAM,CAAC,CAAA;wBACzB,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;wBACzC,OAAO,eAAe;oBACxB;oBACA;gBACF,KAAK;oBACH,MAAM,WAAW,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,KAAK,GAAG,MAAM,OAAO;oBAClF,WAAW,SAAS,MAAM,CAAC,CAAA;wBACzB,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;wBACzC,OAAO,eAAe;oBACxB;oBACA;gBACF,KAAK;oBACH,MAAM,UAAU,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,IAAI,MAAM,OAAO;oBACjF,WAAW,SAAS,MAAM,CAAC,CAAA;wBACzB,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;wBACzC,OAAO,eAAe;oBACxB;oBACA;gBACF,KAAK;oBACH,IAAI,aAAa,SAAS;wBACxB,MAAM,QAAQ,IAAI,KAAK;wBACvB,MAAM,MAAM,IAAI,KAAK;wBACrB,WAAW,SAAS,MAAM,CAAC,CAAA;4BACzB,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;4BACzC,OAAO,eAAe,SAAS,eAAe;wBAChD;oBACF;oBACA;YACJ;QACF;QAEA,oBAAoB;IACtB,GAAG;QAAC;QAAU;QAAY;QAAgB;QAAY;QAAW;KAAQ;IAEzE,kBAAkB;IAClB,MAAM,gBAAgB;QACpB,MAAM,QAAQ,iBAAiB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QAC9E,MAAM,QAAQ,iBAAiB,MAAM;QACrC,MAAM,UAAU,QAAQ,IAAI,QAAQ,QAAQ;QAE5C,qBAAqB;QACrB,MAAM,aAAa,iBAAiB,MAAM,CAAC,CAAC,KAAK;YAC/C,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,MAAM;YACrE,OAAO;QACT,GAAG,CAAC;QAEJ,wBAAwB;QACxB,MAAM,cAAc,iBAAiB,MAAM,CAAC,CAAC,KAAK;YAChD,IAAI,QAAQ,SAAS,EAAE;gBACrB,GAAG,CAAC,QAAQ,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,SAAS,CAAC,IAAI,CAAC,IAAI,QAAQ,MAAM;YACzE;YACA,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAC,QAAuB;QAC3C,MAAM,QAAQ;QAEd,MAAM,aAAkB;YACtB,OAAO;YACP,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,QAAQ;YACR,YAAY;QACd;QAEA,OAAQ;YACN,KAAK;gBACH,WAAW,KAAK,GAAG;gBACnB,WAAW,QAAQ,GAAG,iBAAiB,GAAG,CAAC,CAAA,UAAW,CAAC;wBACrD,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,IAAI;wBAC7B,aAAa,QAAQ,WAAW;wBAChC,UAAU,iBAAiB,QAAQ,QAAQ;wBAC3C,QAAQ,QAAQ,MAAM;wBACtB,WAAW,QAAQ,SAAS,IAAI;wBAChC,WAAW,QAAQ,SAAS,EAAE,QAAQ;oBACxC,CAAC;gBACD;YAEF,KAAK;gBACH,WAAW,KAAK,GAAG;gBACnB,WAAW,OAAO,GAAG;oBACnB,aAAa,MAAM,KAAK;oBACxB,YAAY,MAAM,KAAK;oBACvB,eAAe,MAAM,OAAO;gBAC9B;gBACA;YAEF,KAAK;gBACH,WAAW,KAAK,GAAG;gBACnB,WAAW,UAAU,GAAG,OAAO,OAAO,CAAC,MAAM,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO,GAAK,CAAC;wBACpF,UAAU,iBAAiB;wBAC3B;wBACA,YAAY,CAAC,AAAC,SAAS,MAAM,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;oBACrD,CAAC;gBACD;YAEF,KAAK;gBACH,WAAW,KAAK,GAAG;gBACnB,WAAW,WAAW,GAAG,OAAO,OAAO,CAAC,MAAM,WAAW,EACtD,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,IAAI,GACzB,GAAG,CAAC,CAAC,CAAC,WAAW,OAAO,GAAK,CAAC;wBAC7B;wBACA;wBACA,YAAY,CAAC,AAAC,SAAS,MAAM,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;oBACrD,CAAC;gBACH;QACJ;QAEA,IAAI,WAAW,OAAO;YACpB,YAAY,YAAY,CAAC,SAAS,EAAE,YAAY;QAClD,OAAO;YACL,YAAY,YAAY,CAAC,SAAS,EAAE,YAAY;QAClD;IACF;IAEA,yBAAyB;IACzB,MAAM,mBAAmB,CAAC;QACxB,MAAM,iBAAiB;YACrB,YAAY;YACZ,WAAW;YACX,eAAe;YACf,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,OAAO,cAAc,CAAC,SAAwC,IAAI;IACpE;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,CAAC;QACxB,MAAM,iBAAiB;YACrB,YAAY;YACZ,WAAW;YACX,eAAe;YACf,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,OAAO,cAAc,CAAC,SAAwC,IAAI;IACpE;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO,CAAC,GAAG,EAAE,UAAU,KAAK,EAAE,SAAS;YACtD;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,MAAM,QAAQ;IAEd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAG3C,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,aAAa,OAAO;gCACnC,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,aAAa,OAAO;gCACnC,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAId,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAgB,eAAe;;sDAC5C,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;8DAC7B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAc;;;;;;8DAChC,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;8DAC3B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;8DAC3B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAI9B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAY,eAAe;;sDACxC,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;wCACrB,iBAAiB,MAAM;wCAAC;wCAAK,SAAS,MAAM;;;;;;;;;;;;;wBAK7D,eAAe,0BACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGhD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAmC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMlF,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMpE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAsC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvF,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5D,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO;oDACnC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;;;;;;;;;;;;kDAG7D,8OAAC;kDACE,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAG,WAAU;kEAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,IAAI;;;;;;kEAC5C,8OAAC;wDAAG,WAAU;kEAAmB,QAAQ,WAAW;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAW,iBAAiB,QAAQ,QAAQ;sEAChD,iBAAiB,QAAQ,QAAQ;;;;;;;;;;;kEAGtC,8OAAC;wDAAG,WAAU;kEACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;kEAEhC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,QAAQ,SAAS,IAAI;;;;;;;;;;;;kEAG1B,8OAAC;wDAAG,WAAU;kEACX,QAAQ,SAAS,EAAE,QAAQ;;;;;;;+CAlBvB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BrC", "debugId": null}}, {"offset": {"line": 4818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { X } from \"lucide-react\"\n\ninterface DialogProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n  enableScrollInteraction?: boolean // خيار لتفعيل التفاعل مع التمرير\n  maxHeight?: string // الحد الأقصى للارتفاع\n}\n\ninterface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Dialog = ({\n  open,\n  onOpenChange,\n  children,\n  enableScrollInteraction = true,\n  maxHeight = \"90vh\"\n}: DialogProps) => {\n  const dialogRef = React.useRef<HTMLDivElement>(null)\n  const containerRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onOpenChange?.(false)\n      }\n    }\n\n    const handleWheel = (e: WheelEvent) => {\n      if (!open || !enableScrollInteraction || !dialogRef.current) return\n\n      // السماح بالتمرير داخل الحوار فقط\n      const dialogElement = dialogRef.current\n      const isScrollable = dialogElement.scrollHeight > dialogElement.clientHeight\n\n      if (isScrollable) {\n        // التحقق من أن الماوس داخل منطقة الحوار\n        const rect = dialogElement.getBoundingClientRect()\n        const isInsideDialog = e.clientX >= rect.left && e.clientX <= rect.right &&\n                              e.clientY >= rect.top && e.clientY <= rect.bottom\n\n        if (isInsideDialog) {\n          // السماح بالتمرير الطبيعي داخل الحوار\n          return\n        }\n      }\n\n      // منع التمرير خارج الحوار\n      e.preventDefault()\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEscape)\n      if (enableScrollInteraction) {\n        document.addEventListener('wheel', handleWheel, { passive: false })\n      }\n      // عدم منع التمرير في الخلفية للسماح بالتمرير داخل الحوار\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.removeEventListener('wheel', handleWheel)\n    }\n  }, [open, onOpenChange, enableScrollInteraction])\n\n  if (!open) return null\n\n  return (\n    <div\n      ref={containerRef}\n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n    >\n      <div\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300\"\n        onClick={() => onOpenChange?.(false)}\n      />\n      <div\n        ref={dialogRef}\n        className={cn(\n          \"relative z-50 w-full overflow-hidden animate-in fade-in-0 zoom-in-95 duration-300\",\n          \"smooth-scroll\", // فئة CSS للتمرير السلس\n          enableScrollInteraction ? \"overflow-y-auto\" : \"overflow-hidden\"\n        )}\n        style={{\n          maxHeight: maxHeight,\n          transform: 'translate3d(0, 0, 0)', // تحسين الأداء\n          willChange: 'transform', // تحسين الأداء\n        }}\n      >\n        {children}\n      </div>\n    </div>\n  )\n}\n\nconst DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200\",\n        \"backdrop-blur-sm bg-white/95\", // تحسين الشفافية\n        \"hover:shadow-3xl transition-shadow duration-300\", // تأثير الظل عند التمرير\n        \"max-h-full overflow-y-auto smooth-scroll\", // تمكين التمرير السلس\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogContent.displayName = \"DialogContent\"\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-bold leading-none tracking-tight text-slate-800\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = \"DialogTitle\"\n\nconst DialogDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-slate-600 font-medium\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = \"DialogDescription\"\n\nconst DialogTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement>\n>(({ className, ...props }, ref) => (\n  <button\n    ref={ref}\n    className={className}\n    {...props}\n  />\n))\nDialogTrigger.displayName = \"DialogTrigger\"\n\nconst DialogClose = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & { onOpenChange?: (open: boolean) => void }\n>(({ className, onOpenChange, ...props }, ref) => (\n  <button\n    ref={ref}\n    type=\"button\"\n    onClick={() => onOpenChange?.(false)}\n    className={cn(\n      \"absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10\",\n      className\n    )}\n    {...props}\n  >\n    <X className=\"h-4 w-4 text-slate-600\" />\n    <span className=\"sr-only\">إغلاق</span>\n  </button>\n))\nDialogClose.displayName = \"DialogClose\"\n\nexport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n  DialogClose,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAkBA,MAAM,SAAS,CAAC,EACd,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,0BAA0B,IAAI,EAC9B,YAAY,MAAM,EACN;IACZ,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAkB;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAkB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,eAAe;YACjB;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,UAAU,OAAO,EAAE;YAE7D,kCAAkC;YAClC,MAAM,gBAAgB,UAAU,OAAO;YACvC,MAAM,eAAe,cAAc,YAAY,GAAG,cAAc,YAAY;YAE5E,IAAI,cAAc;gBAChB,wCAAwC;gBACxC,MAAM,OAAO,cAAc,qBAAqB;gBAChD,MAAM,iBAAiB,EAAE,OAAO,IAAI,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,KAAK,KAAK,IAClD,EAAE,OAAO,IAAI,KAAK,GAAG,IAAI,EAAE,OAAO,IAAI,KAAK,MAAM;gBAEvE,IAAI,gBAAgB;oBAClB,sCAAsC;oBACtC;gBACF;YACF;YAEA,0BAA0B;YAC1B,EAAE,cAAc;QAClB;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,IAAI,yBAAyB;gBAC3B,SAAS,gBAAgB,CAAC,SAAS,aAAa;oBAAE,SAAS;gBAAM;YACnE;QACA,yDAAyD;QAC3D;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,SAAS;QACxC;IACF,GAAG;QAAC;QAAM;QAAc;KAAwB;IAEhD,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAEV,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAEhC,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA,iBACA,0BAA0B,oBAAoB;gBAEhD,OAAO;oBACL,WAAW;oBACX,WAAW;oBACX,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAIT;AAEA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA,gCACA,mDACA,4CACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE,oBACxC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,SAAS,IAAM,eAAe;QAC9B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wRACA;QAED,GAAG,KAAK;;0BAET,8OAAC,4LAAA,CAAA,IAAC;gBAAC,WAAU;;;;;;0BACb,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/members/member-search-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Search,\n  User,\n  Phone,\n  MapPin,\n  FileText,\n  X\n} from 'lucide-react'\nimport { Badge } from '@/components/ui/badge'\nimport { getMemberStatusText, getMemberStatusColor } from '@/lib/utils'\n\ninterface Member {\n  id: string\n  name: string\n  phone?: string\n  address?: string\n  status: string\n  _count: {\n    incomes: number\n  }\n}\n\ninterface MemberSearchDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSelectMember: (memberId: string) => void\n  title?: string\n  description?: string\n}\n\nexport default function MemberSearchDialog({\n  open,\n  onOpenChange,\n  onSelectMember,\n  title = \"اختيار عضو\",\n  description = \"ابحث عن العضو المطلوب\"\n}: MemberSearchDialogProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [members, setMembers] = useState<Member[]>([])\n  const [loading, setLoading] = useState(false)\n  const [selectedMember, setSelectedMember] = useState<Member | null>(null)\n\n  // جلب الأعضاء عند البحث\n  useEffect(() => {\n    if (open) {\n      fetchMembers()\n    } else {\n      // إعادة تعيين البيانات عند إغلاق النافذة\n      setSearchTerm('')\n      setMembers([])\n      setSelectedMember(null)\n    }\n  }, [open, searchTerm])\n\n  const fetchMembers = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch(\n        `/api/members?search=${encodeURIComponent(searchTerm)}&limit=20&includeIncomes=true`\n      )\n      if (response.ok) {\n        const data = await response.json()\n        setMembers(data.members || [])\n      }\n    } catch (error) {\n      console.error('خطأ في جلب الأعضاء:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSelectMember = (member: Member) => {\n    setSelectedMember(member)\n  }\n\n  const handleConfirmSelection = () => {\n    if (selectedMember) {\n      onSelectMember(selectedMember.id)\n      onOpenChange(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-[50vw] max-h-[80vh] overflow-hidden flex flex-col\">\n        <DialogHeader className=\"pb-4 border-b\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <Search className=\"w-5 h-5 text-purple-600\" />\n              </div>\n              <div>\n                <DialogTitle className=\"text-lg font-bold text-gray-900\">\n                  {title}\n                </DialogTitle>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  {description}\n                </p>\n              </div>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => onOpenChange(false)}\n              className=\"text-gray-500 hover:text-gray-700\"\n            >\n              <X className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </DialogHeader>\n\n        <div className=\"flex-1 overflow-hidden flex flex-col\">\n          {/* شريط البحث */}\n          <div className=\"p-4 border-b bg-gray-50\">\n            <div className=\"relative\">\n              <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <Input\n                placeholder=\"ابحث بالاسم أو الهاتف أو العنوان...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pr-10\"\n                autoFocus\n              />\n            </div>\n          </div>\n\n          {/* قائمة الأعضاء */}\n          <div className=\"flex-1 overflow-y-auto p-4\">\n            {loading ? (\n              <div className=\"flex justify-center items-center h-32\">\n                <div className=\"text-gray-500\">جاري البحث...</div>\n              </div>\n            ) : members.length === 0 ? (\n              <div className=\"flex flex-col justify-center items-center h-32 text-gray-500\">\n                <User className=\"w-12 h-12 mb-2 text-gray-300\" />\n                <p>\n                  {searchTerm ? 'لا توجد نتائج للبحث' : 'ابدأ بكتابة اسم العضو للبحث'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"space-y-2\">\n                {members.map((member) => (\n                  <div\n                    key={member.id}\n                    onClick={() => handleSelectMember(member)}\n                    className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${\n                      selectedMember?.id === member.id\n                        ? 'border-purple-500 bg-purple-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-2 mb-1\">\n                          <h3 className=\"font-medium text-gray-900\">{member.name}</h3>\n                          <Badge className={getMemberStatusColor(member.status)}>\n                            {getMemberStatusText(member.status)}\n                          </Badge>\n                        </div>\n                        <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n                          {member.phone && (\n                            <div className=\"flex items-center gap-1\">\n                              <Phone className=\"w-3 h-3\" />\n                              {member.phone}\n                            </div>\n                          )}\n                          {member.address && (\n                            <div className=\"flex items-center gap-1\">\n                              <MapPin className=\"w-3 h-3\" />\n                              {member.address}\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {member._count.incomes} مساهمة\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* أزرار التحكم */}\n          <div className=\"p-4 border-t bg-gray-50 flex justify-end gap-2\">\n            <Button\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n            >\n              إلغاء\n            </Button>\n            <Button\n              onClick={handleConfirmSelection}\n              disabled={!selectedMember}\n              className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n            >\n              <FileText className=\"w-4 h-4 ml-2\" />\n              عرض كشف الحساب\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AApBA;;;;;;;;;AAyCe,SAAS,mBAAmB,EACzC,IAAI,EACJ,YAAY,EACZ,cAAc,EACd,QAAQ,YAAY,EACpB,cAAc,uBAAuB,EACb;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF,OAAO;YACL,yCAAyC;YACzC,cAAc;YACd,WAAW,EAAE;YACb,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAM;KAAW;IAErB,MAAM,eAAe;QACnB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,oBAAoB,EAAE,mBAAmB,YAAY,6BAA6B,CAAC;YAEtF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO,IAAI,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;IACpB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,gBAAgB;YAClB,eAAe,eAAe,EAAE;YAChC,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;;0DACC,8OAAC,kIAAA,CAAA,cAAW;gDAAC,WAAU;0DACpB;;;;;;0DAEH,8OAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;;;;;;;;0CAIP,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;wCACV,SAAS;;;;;;;;;;;;;;;;;sCAMf,8OAAC;4BAAI,WAAU;sCACZ,wBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;uCAE/B,QAAQ,MAAM,KAAK,kBACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDACE,aAAa,wBAAwB;;;;;;;;;;;qDAI1C,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wCAEC,SAAS,IAAM,mBAAmB;wCAClC,WAAW,CAAC,oEAAoE,EAC9E,gBAAgB,OAAO,OAAO,EAAE,GAC5B,mCACA,yCACJ;kDAEF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA6B,OAAO,IAAI;;;;;;8EACtD,8OAAC,iIAAA,CAAA,QAAK;oEAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,MAAM;8EACjD,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;;;;;;;;;;;;sEAGtC,8OAAC;4DAAI,WAAU;;gEACZ,OAAO,KAAK,kBACX,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,OAAO,KAAK;;;;;;;gEAGhB,OAAO,OAAO,kBACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,OAAO,OAAO;;;;;;;;;;;;;;;;;;;8DAKvB,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,MAAM,CAAC,OAAO;wDAAC;;;;;;;;;;;;;uCAhCtB,OAAO,EAAE;;;;;;;;;;;;;;;sCA0CxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;8CAC7B;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC;oCACX,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 5416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/reports/ComprehensiveReports.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\n// import { Input } from '@/components/ui/input'\n// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { formatCurrency, formatDate } from '@/lib/utils'\nimport MemberSearchDialog from '@/components/members/member-search-dialog'\nimport {\n  BarChart3,\n  FileText,\n  Download,\n  Search,\n  TrendingUp,\n  TrendingDown,\n  DollarSign,\n  Users,\n  Calculator,\n  PieChart\n} from 'lucide-react'\n\ninterface Member {\n  id: string\n  name: string\n  status: string\n  incomes: { amount: number; date: string }[]\n}\n\ninterface ComprehensiveReportsProps {\n  onExportPDF: (data: any, type: string) => void\n  onExportCSV: (data: any, type: string) => void\n}\n\nexport default function ComprehensiveReports({ onExportPDF, onExportCSV }: ComprehensiveReportsProps) {\n  const [members, setMembers] = useState<Member[]>([])\n  const [selectedMember, setSelectedMember] = useState<string>('')\n  const [loading, setLoading] = useState(true)\n  const [reportData, setReportData] = useState<any>(null)\n  const [isMemberSearchOpen, setIsMemberSearchOpen] = useState(false)\n  const [selectedMemberName, setSelectedMemberName] = useState<string>('')\n\n  // جلب البيانات الشاملة\n  const fetchComprehensiveData = async () => {\n    try {\n      setLoading(true)\n      \n      // جلب بيانات التقارير الأساسية\n      const [membersRes, reportsRes] = await Promise.all([\n        fetch('/api/members?limit=1000'),\n        fetch('/api/reports?period=all-time')\n      ])\n\n      if (!membersRes.ok || !reportsRes.ok) {\n        throw new Error('فشل في جلب البيانات')\n      }\n\n      const [membersData, reportsData] = await Promise.all([\n        membersRes.json(),\n        reportsRes.json()\n      ])\n\n      setMembers(membersData.members || [])\n      setReportData(reportsData)\n    } catch (error) {\n      console.error('خطأ في جلب البيانات:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchComprehensiveData()\n  }, [])\n\n  // فتح نافذة البحث عن عضو\n  const handleOpenMemberSearch = () => {\n    setIsMemberSearchOpen(true)\n  }\n\n  // اختيار عضو من نافذة البحث\n  const handleSelectMemberForStatement = (memberId: string) => {\n    const member = members.find(m => m.id === memberId)\n    setSelectedMember(memberId)\n    setSelectedMemberName(member?.name || '')\n    setIsMemberSearchOpen(false)\n  }\n\n  // حساب كشف حساب العضو\n  const getMemberAccountStatement = (memberId: string) => {\n    const member = members.find(m => m.id === memberId)\n    if (!member) return null\n\n    const totalContributions = member.incomes.reduce((sum, income) => sum + income.amount, 0)\n    const contributionsCount = member.incomes.length\n\n    // حساب حصة المصاريف (افتراضياً نقسم المصاريف على جميع الأعضاء النشطين)\n    const activeMembers = members.filter(m => m.status === 'ACTIVE').length\n    const memberExpenseShare = activeMembers > 0 ? (reportData?.summary?.totalExpenses || 0) / activeMembers : 0\n\n    const netBalance = totalContributions - memberExpenseShare\n\n    return {\n      member,\n      totalContributions,\n      contributionsCount,\n      memberExpenseShare,\n      netBalance,\n      status: netBalance >= 0 ? 'دائن' : 'مدين'\n    }\n  }\n\n  // تصدير كشف حساب العضو\n  const handleExportMemberStatement = (memberId: string, format: 'pdf' | 'csv') => {\n    const statement = getMemberAccountStatement(memberId)\n    if (!statement) return\n\n    const reportData = {\n      title: `كشف حساب العضو: ${statement.member.name}`,\n      date: new Date().toLocaleDateString('ar-JO'),\n      member: statement.member,\n      statement: {\n        totalContributions: statement.totalContributions,\n        contributionsCount: statement.contributionsCount,\n        memberExpenseShare: statement.memberExpenseShare,\n        netBalance: statement.netBalance,\n        status: statement.status\n      },\n      contributions: statement.member.incomes.map(income => ({\n        date: formatDate(income.date),\n        amount: income.amount\n      }))\n    }\n\n    if (format === 'pdf') {\n      onExportPDF(reportData, 'member-statement')\n    } else {\n      onExportCSV(reportData, 'member-statement')\n    }\n  }\n\n  // تصدير ملخص الحسابات العامة\n  const handleExportGeneralSummary = (format: 'pdf' | 'csv') => {\n    if (!reportData) return\n\n    const coverageRatio = reportData.summary.totalIncomes > 0 \n      ? (reportData.summary.totalIncomes / (reportData.summary.totalIncomes + Math.abs(reportData.summary.balance))) * 100\n      : 0\n\n    const summaryData = {\n      title: 'ملخص الحسابات العامة',\n      date: new Date().toLocaleDateString('ar-JO'),\n      summary: {\n        totalMembers: reportData.summary.totalMembers,\n        activeMembers: reportData.summary.activeMembers,\n        totalIncomes: reportData.summary.totalIncomes,\n        totalExpenses: reportData.summary.totalExpenses,\n        currentBalance: reportData.summary.balance,\n        coverageRatio: coverageRatio.toFixed(1),\n        totalActivities: reportData.summary.totalActivities\n      },\n      monthlyData: reportData.monthlyData || []\n    }\n\n    if (format === 'pdf') {\n      onExportPDF(summaryData, 'general-summary')\n    } else {\n      onExportCSV(summaryData, 'general-summary')\n    }\n  }\n\n  // تصدير تقرير المقارنة\n  const handleExportComparison = (format: 'pdf' | 'csv') => {\n    if (!reportData) return\n\n    const monthlyComparison = (reportData.monthlyData || []).map((month: any) => ({\n      month: new Date(month.month + '-01').toLocaleDateString('ar-JO', { year: 'numeric', month: 'long' }),\n      incomes: month.incomes,\n      expenses: month.expenses,\n      balance: month.balance,\n      growth: month.incomes > month.expenses ? 'نمو' : 'تراجع',\n      efficiency: month.expenses > 0 ? ((month.incomes / month.expenses) * 100).toFixed(1) + '%' : 'غير محدد'\n    }))\n\n    const comparisonData = {\n      title: 'تقرير مقارنة الإيرادات والمصروفات',\n      date: new Date().toLocaleDateString('ar-JO'),\n      summary: {\n        totalIncomes: reportData.summary.totalIncomes,\n        totalExpenses: reportData.summary.totalExpenses,\n        netResult: reportData.summary.balance,\n        averageMonthlyIncome: reportData.monthlyData.length > 0 \n          ? reportData.summary.totalIncomes / reportData.monthlyData.length \n          : 0,\n        averageMonthlyExpense: reportData.monthlyData.length > 0 \n          ? reportData.summary.totalExpenses / reportData.monthlyData.length \n          : 0\n      },\n      monthlyComparison,\n      insights: [\n        reportData.summary.balance >= 0 ? 'الوضع المالي مستقر' : 'يحتاج مراجعة الإنفاق',\n        reportData.summary.totalIncomes > reportData.summary.totalExpenses ? 'الإيرادات تفوق المصروفات' : 'المصروفات تفوق الإيرادات',\n        `معدل الكفاءة المالية: ${reportData.summary.totalExpenses > 0 ? ((reportData.summary.totalIncomes / reportData.summary.totalExpenses) * 100).toFixed(1) : 'غير محدد'}%`\n      ]\n    }\n\n    if (format === 'pdf') {\n      onExportPDF(comparisonData, 'comparison-report')\n    } else {\n      onExportCSV(comparisonData, 'comparison-report')\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"text-gray-500\">جاري تحميل البيانات الشاملة...</div>\n      </div>\n    )\n  }\n\n  if (!reportData) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"text-gray-500\">لا توجد بيانات متاحة</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* عنوان القسم */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900 flex items-center\">\n            <BarChart3 className=\"w-5 h-5 ml-2\" />\n            التقارير الشاملة\n          </h2>\n          <p className=\"text-gray-600\">تقارير متقدمة وكشوف حسابات شاملة</p>\n        </div>\n      </div>\n\n      {/* الإحصائيات العامة */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <TrendingUp className=\"w-8 h-8 text-green-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">إجمالي الإيرادات</p>\n                <p className=\"text-2xl font-bold text-green-600\">\n                  {formatCurrency(reportData.summary.totalIncomes)}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <TrendingDown className=\"w-8 h-8 text-red-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">إجمالي المصروفات</p>\n                <p className=\"text-2xl font-bold text-red-600\">\n                  {formatCurrency(reportData.summary.totalExpenses)}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <DollarSign className={`w-8 h-8 ${reportData.summary.balance >= 0 ? 'text-green-500' : 'text-red-500'}`} />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">الرصيد الحالي</p>\n                <p className={`text-2xl font-bold ${reportData.summary.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {formatCurrency(reportData.summary.balance)}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <Users className=\"w-8 h-8 text-blue-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">الأعضاء النشطون</p>\n                <p className=\"text-2xl font-bold text-blue-600\">\n                  {reportData.summary.activeMembers} / {reportData.summary.totalMembers}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* كشف حساب العضو */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Calculator className=\"w-5 h-5 ml-2\" />\n            كشف حساب العضو\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">اختر العضو</label>\n                <div className=\"flex gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    onClick={handleOpenMemberSearch}\n                    className=\"flex-1 justify-start text-gray-500\"\n                  >\n                    <Search className=\"w-4 h-4 ml-2\" />\n                    {selectedMemberName || \"ابحث عن عضو لعرض كشف حسابه\"}\n                  </Button>\n                  {selectedMember && (\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => {\n                        setSelectedMember('')\n                        setSelectedMemberName('')\n                      }}\n                      className=\"text-gray-500 hover:text-red-600\"\n                    >\n                      ✕\n                    </Button>\n                  )}\n                </div>\n              </div>\n              \n              {selectedMember && (\n                <div className=\"flex items-end space-x-2 space-x-reverse\">\n                  <Button\n                    onClick={() => handleExportMemberStatement(selectedMember, 'pdf')}\n                    className=\"bg-red-600 hover:bg-red-700 text-white\"\n                  >\n                    <FileText className=\"w-4 h-4 ml-2\" />\n                    تصدير PDF\n                  </Button>\n                  <Button\n                    onClick={() => handleExportMemberStatement(selectedMember, 'csv')}\n                    className=\"bg-green-600 hover:bg-green-700 text-white\"\n                  >\n                    <Download className=\"w-4 h-4 ml-2\" />\n                    تصدير CSV\n                  </Button>\n                </div>\n              )}\n            </div>\n\n            {/* عرض كشف الحساب */}\n            {selectedMember && (() => {\n              const statement = getMemberAccountStatement(selectedMember)\n              if (!statement) return null\n\n              return (\n                <div className=\"mt-6 p-4 border border-gray-200 rounded-lg bg-gray-50\">\n                  <h3 className=\"font-medium text-gray-900 mb-4\">كشف حساب: {statement.member.name}</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">إجمالي المساهمات:</span>\n                        <span className=\"font-medium text-green-600\">\n                          {formatCurrency(statement.totalContributions)}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">عدد المساهمات:</span>\n                        <span className=\"font-medium\">{statement.contributionsCount}</span>\n                      </div>\n                    </div>\n                    <div className=\"space-y-2\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">حصة المصاريف:</span>\n                        <span className=\"font-medium text-red-600\">\n                          {formatCurrency(statement.memberExpenseShare)}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">الرصيد الصافي:</span>\n                        <span className={`font-bold ${statement.netBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                          {formatCurrency(statement.netBalance)} ({statement.status})\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )\n            })()}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* التقارير الشاملة الأخرى */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* ملخص الحسابات العامة */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <PieChart className=\"w-5 h-5 ml-2\" />\n              ملخص الحسابات العامة\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">نسبة التغطية المالية:</span>\n                  <span className=\"font-medium\">\n                    {reportData.summary.totalIncomes > 0 \n                      ? ((reportData.summary.totalIncomes / (reportData.summary.totalIncomes + Math.abs(reportData.summary.balance))) * 100).toFixed(1)\n                      : '0'\n                    }%\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">معدل الإنفاق:</span>\n                  <span className=\"font-medium\">\n                    {reportData.summary.totalIncomes > 0 \n                      ? ((reportData.summary.totalExpenses / reportData.summary.totalIncomes) * 100).toFixed(1)\n                      : '0'\n                    }%\n                  </span>\n                </div>\n              </div>\n              \n              <div className=\"flex space-x-2 space-x-reverse\">\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExportGeneralSummary('pdf')}\n                  className=\"bg-red-600 hover:bg-red-700 text-white\"\n                >\n                  <FileText className=\"w-4 h-4 ml-2\" />\n                  PDF\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExportGeneralSummary('csv')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                >\n                  <Download className=\"w-4 h-4 ml-2\" />\n                  CSV\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* تقرير المقارنة */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <BarChart3 className=\"w-5 h-5 ml-2\" />\n              تقرير المقارنة\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">الاتجاه العام:</span>\n                  <Badge className={reportData.summary.balance >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>\n                    {reportData.summary.balance >= 0 ? 'إيجابي' : 'سلبي'}\n                  </Badge>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">كفاءة الإنفاق:</span>\n                  <span className=\"font-medium\">\n                    {reportData.summary.totalExpenses > 0 \n                      ? ((reportData.summary.totalIncomes / reportData.summary.totalExpenses) * 100).toFixed(1)\n                      : 'غير محدد'\n                    }%\n                  </span>\n                </div>\n              </div>\n              \n              <div className=\"flex space-x-2 space-x-reverse\">\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExportComparison('pdf')}\n                  className=\"bg-red-600 hover:bg-red-700 text-white\"\n                >\n                  <FileText className=\"w-4 h-4 ml-2\" />\n                  PDF\n                </Button>\n                <Button\n                  size=\"sm\"\n                  onClick={() => handleExportComparison('csv')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                >\n                  <Download className=\"w-4 h-4 ml-2\" />\n                  CSV\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* نافذة البحث عن عضو */}\n      <MemberSearchDialog\n        open={isMemberSearchOpen}\n        onOpenChange={setIsMemberSearchOpen}\n        onSelectMember={handleSelectMemberForStatement}\n        title=\"اختيار عضو لكشف الحساب\"\n        description=\"ابحث عن العضو المطلوب لعرض كشف حسابه في التقارير الشاملة\"\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA,gDAAgD;AAChD,yGAAyG;AACzG;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;AAmCe,SAAS,qBAAqB,EAAE,WAAW,EAAE,WAAW,EAA6B;IAClG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAClD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErE,uBAAuB;IACvB,MAAM,yBAAyB;QAC7B,IAAI;YACF,WAAW;YAEX,+BAA+B;YAC/B,MAAM,CAAC,YAAY,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjD,MAAM;gBACN,MAAM;aACP;YAED,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,WAAW,IAAI;gBACf,WAAW,IAAI;aAChB;YAED,WAAW,YAAY,OAAO,IAAI,EAAE;YACpC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,yBAAyB;QAC7B,sBAAsB;IACxB;IAEA,4BAA4B;IAC5B,MAAM,iCAAiC,CAAC;QACtC,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,kBAAkB;QAClB,sBAAsB,QAAQ,QAAQ;QACtC,sBAAsB;IACxB;IAEA,sBAAsB;IACtB,MAAM,4BAA4B,CAAC;QACjC,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,qBAAqB,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;QACvF,MAAM,qBAAqB,OAAO,OAAO,CAAC,MAAM;QAEhD,uEAAuE;QACvE,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QACvE,MAAM,qBAAqB,gBAAgB,IAAI,CAAC,YAAY,SAAS,iBAAiB,CAAC,IAAI,gBAAgB;QAE3G,MAAM,aAAa,qBAAqB;QAExC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA,QAAQ,cAAc,IAAI,SAAS;QACrC;IACF;IAEA,uBAAuB;IACvB,MAAM,8BAA8B,CAAC,UAAkB;QACrD,MAAM,YAAY,0BAA0B;QAC5C,IAAI,CAAC,WAAW;QAEhB,MAAM,aAAa;YACjB,OAAO,CAAC,gBAAgB,EAAE,UAAU,MAAM,CAAC,IAAI,EAAE;YACjD,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,QAAQ,UAAU,MAAM;YACxB,WAAW;gBACT,oBAAoB,UAAU,kBAAkB;gBAChD,oBAAoB,UAAU,kBAAkB;gBAChD,oBAAoB,UAAU,kBAAkB;gBAChD,YAAY,UAAU,UAAU;gBAChC,QAAQ,UAAU,MAAM;YAC1B;YACA,eAAe,UAAU,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;oBACrD,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI;oBAC5B,QAAQ,OAAO,MAAM;gBACvB,CAAC;QACH;QAEA,IAAI,WAAW,OAAO;YACpB,YAAY,YAAY;QAC1B,OAAO;YACL,YAAY,YAAY;QAC1B;IACF;IAEA,6BAA6B;IAC7B,MAAM,6BAA6B,CAAC;QAClC,IAAI,CAAC,YAAY;QAEjB,MAAM,gBAAgB,WAAW,OAAO,CAAC,YAAY,GAAG,IACpD,AAAC,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC,WAAW,OAAO,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,WAAW,OAAO,CAAC,OAAO,CAAC,IAAK,MAC/G;QAEJ,MAAM,cAAc;YAClB,OAAO;YACP,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,SAAS;gBACP,cAAc,WAAW,OAAO,CAAC,YAAY;gBAC7C,eAAe,WAAW,OAAO,CAAC,aAAa;gBAC/C,cAAc,WAAW,OAAO,CAAC,YAAY;gBAC7C,eAAe,WAAW,OAAO,CAAC,aAAa;gBAC/C,gBAAgB,WAAW,OAAO,CAAC,OAAO;gBAC1C,eAAe,cAAc,OAAO,CAAC;gBACrC,iBAAiB,WAAW,OAAO,CAAC,eAAe;YACrD;YACA,aAAa,WAAW,WAAW,IAAI,EAAE;QAC3C;QAEA,IAAI,WAAW,OAAO;YACpB,YAAY,aAAa;QAC3B,OAAO;YACL,YAAY,aAAa;QAC3B;IACF;IAEA,uBAAuB;IACvB,MAAM,yBAAyB,CAAC;QAC9B,IAAI,CAAC,YAAY;QAEjB,MAAM,oBAAoB,CAAC,WAAW,WAAW,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,QAAe,CAAC;gBAC5E,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,kBAAkB,CAAC,SAAS;oBAAE,MAAM;oBAAW,OAAO;gBAAO;gBAClG,SAAS,MAAM,OAAO;gBACtB,UAAU,MAAM,QAAQ;gBACxB,SAAS,MAAM,OAAO;gBACtB,QAAQ,MAAM,OAAO,GAAG,MAAM,QAAQ,GAAG,QAAQ;gBACjD,YAAY,MAAM,QAAQ,GAAG,IAAI,CAAC,AAAC,MAAM,OAAO,GAAG,MAAM,QAAQ,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK,MAAM;YAC/F,CAAC;QAED,MAAM,iBAAiB;YACrB,OAAO;YACP,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,SAAS;gBACP,cAAc,WAAW,OAAO,CAAC,YAAY;gBAC7C,eAAe,WAAW,OAAO,CAAC,aAAa;gBAC/C,WAAW,WAAW,OAAO,CAAC,OAAO;gBACrC,sBAAsB,WAAW,WAAW,CAAC,MAAM,GAAG,IAClD,WAAW,OAAO,CAAC,YAAY,GAAG,WAAW,WAAW,CAAC,MAAM,GAC/D;gBACJ,uBAAuB,WAAW,WAAW,CAAC,MAAM,GAAG,IACnD,WAAW,OAAO,CAAC,aAAa,GAAG,WAAW,WAAW,CAAC,MAAM,GAChE;YACN;YACA;YACA,UAAU;gBACR,WAAW,OAAO,CAAC,OAAO,IAAI,IAAI,uBAAuB;gBACzD,WAAW,OAAO,CAAC,YAAY,GAAG,WAAW,OAAO,CAAC,aAAa,GAAG,6BAA6B;gBAClG,CAAC,sBAAsB,EAAE,WAAW,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,AAAC,WAAW,OAAO,CAAC,YAAY,GAAG,WAAW,OAAO,CAAC,aAAa,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK,WAAW,CAAC,CAAC;aACxK;QACH;QAEA,IAAI,WAAW,OAAO;YACpB,YAAY,gBAAgB;QAC9B,OAAO;YACL,YAAY,gBAAgB;QAC9B;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,kNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGxC,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOzD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1D,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,OAAO,CAAC,OAAO,IAAI,IAAI,mBAAmB,gBAAgB;;;;;;kDACvG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAW,CAAC,mBAAmB,EAAE,WAAW,OAAO,CAAC,OAAO,IAAI,IAAI,mBAAmB,gBAAgB;0DACtG,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOpD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDACV,WAAW,OAAO,CAAC,aAAa;oDAAC;oDAAI,WAAW,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,8MAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI3C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS;4DACT,WAAU;;8EAEV,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,sBAAsB;;;;;;;wDAExB,gCACC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;gEACP,kBAAkB;gEAClB,sBAAsB;4DACxB;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;;wCAON,gCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,4BAA4B,gBAAgB;oDAC3D,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,4BAA4B,gBAAgB;oDAC3D,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;gCAQ5C,kBAAkB,CAAC;oCAClB,MAAM,YAAY,0BAA0B;oCAC5C,IAAI,CAAC,WAAW,OAAO;oCAEvB,qBACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;oDAAiC;oDAAW,UAAU,MAAM,CAAC,IAAI;;;;;;;0DAC/E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,kBAAkB;;;;;;;;;;;;0EAGhD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;kEAG/D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,kBAAkB;;;;;;;;;;;;0EAGhD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAW,CAAC,UAAU,EAAE,UAAU,UAAU,IAAI,IAAI,mBAAmB,gBAAgB;;4EAC1F,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,UAAU;4EAAE;4EAAG,UAAU,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAOxE,CAAC;;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIzC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEACb,WAAW,OAAO,CAAC,YAAY,GAAG,IAC/B,CAAC,AAAC,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC,WAAW,OAAO,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,WAAW,OAAO,CAAC,OAAO,CAAC,IAAK,GAAG,EAAE,OAAO,CAAC,KAC7H;gEACH;;;;;;;;;;;;;8DAGL,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEACb,WAAW,OAAO,CAAC,YAAY,GAAG,IAC/B,CAAC,AAAC,WAAW,OAAO,CAAC,aAAa,GAAG,WAAW,OAAO,CAAC,YAAY,GAAI,GAAG,EAAE,OAAO,CAAC,KACrF;gEACH;;;;;;;;;;;;;;;;;;;sDAKP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,2BAA2B;oDAC1C,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,2BAA2B;oDAC1C,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI1C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAW,WAAW,OAAO,CAAC,OAAO,IAAI,IAAI,gCAAgC;sEACjF,WAAW,OAAO,CAAC,OAAO,IAAI,IAAI,WAAW;;;;;;;;;;;;8DAGlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEACb,WAAW,OAAO,CAAC,aAAa,GAAG,IAChC,CAAC,AAAC,WAAW,OAAO,CAAC,YAAY,GAAG,WAAW,OAAO,CAAC,aAAa,GAAI,GAAG,EAAE,OAAO,CAAC,KACrF;gEACH;;;;;;;;;;;;;;;;;;;sDAKP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,uBAAuB;oDACtC,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,uBAAuB;oDACtC,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,8OAAC,2JAAA,CAAA,UAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,gBAAgB;gBAChB,OAAM;gBACN,aAAY;;;;;;;;;;;;AAIpB", "debugId": null}}, {"offset": {"line": 6564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/reports/HelperReports.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { formatCurrency, formatDate } from '@/lib/utils'\nimport {\n  AlertTriangle,\n  FileText,\n  Download,\n  Clock,\n  Heart,\n  Users,\n  Phone,\n  // Mail,\n  // Calendar,\n  DollarSign,\n  MessageCircle\n} from 'lucide-react'\n\ninterface Member {\n  id: string\n  name: string\n  phone?: string\n  email?: string\n  status: 'ACTIVE' | 'INACTIVE' | 'LATE' | 'NON_COMPLIANT'\n  createdAt: string\n  incomes: {\n    id: string\n    amount: number\n    date: string\n    source: string\n  }[]\n}\n\ninterface EmergencyContribution {\n  id: string\n  amount: number\n  date: string\n  description: string\n  member?: {\n    name: string\n  }\n}\n\ninterface HelperReportsProps {\n  onExportPDF: (data: any, type: string) => void\n  onExportCSV: (data: any, type: string) => void\n}\n\nexport default function HelperReports({ onExportPDF, onExportCSV }: HelperReportsProps) {\n  const [members, setMembers] = useState<Member[]>([])\n  const [emergencyContributions, setEmergencyContributions] = useState<EmergencyContribution[]>([])\n  const [loading, setLoading] = useState(true)\n  const [latePaymentThreshold, setLatePaymentThreshold] = useState(30) // أيام\n  const [sendingNotifications, setSendingNotifications] = useState(false)\n\n  // جلب البيانات\n  const fetchData = async () => {\n    try {\n      setLoading(true)\n      \n      const [membersRes, incomesRes] = await Promise.all([\n        fetch('/api/members?limit=1000&includeIncomes=true'),\n        fetch('/api/incomes?limit=1000') // جميع الإيرادات للبحث عن الطوارئ\n      ])\n\n      if (!membersRes.ok || !incomesRes.ok) {\n        throw new Error('فشل في جلب البيانات')\n      }\n\n      const [membersData, incomesData] = await Promise.all([\n        membersRes.json(),\n        incomesRes.json()\n      ])\n\n      setMembers(membersData.members || [])\n      \n      // فلترة التبرعات الطارئة (التي تحتوي على كلمات مثل \"طوارئ\" أو \"وفاة\" أو \"كارثة\")\n      const emergencyKeywords = ['طوارئ', 'وفاة', 'كارثة', 'مساعدة', 'عاجل', 'طارئ', 'مرض', 'علاج', 'عملية', 'مستشفى']\n      const emergencyIncomes = (incomesData.incomes || []).filter((income: any) =>\n        emergencyKeywords.some(keyword =>\n          income.source?.toLowerCase().includes(keyword) ||\n          income.description?.toLowerCase().includes(keyword) ||\n          income.type === 'EMERGENCY' // إذا كان هناك نوع خاص للطوارئ\n        )\n      )\n\n\n\n      setEmergencyContributions(emergencyIncomes)\n    } catch (error) {\n      console.error('خطأ في جلب البيانات:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchData()\n  }, [])\n\n  // حساب الأعضاء المتأخرين\n  const getLateMembers = () => {\n    if (!members || !Array.isArray(members)) {\n      return []\n    }\n\n    const now = new Date()\n    const thresholdDate = new Date(now.getTime() - latePaymentThreshold * 24 * 60 * 60 * 1000)\n\n\n\n    return members.filter(member => {\n      // إذا كان العضو غير نشط، لا نعتبره متأخر\n      if (member.status !== 'ACTIVE' && member.status !== 'LATE') {\n        return false\n      }\n\n      // إذا لم يكن له أي مساهمات\n      if (!member.incomes || !Array.isArray(member.incomes) || member.incomes.length === 0) {\n        const joinDate = new Date(member.createdAt)\n        return joinDate < thresholdDate\n      }\n\n      // آخر مساهمة - تصفية التواريخ الصحيحة فقط\n      const validDates = member.incomes\n        .filter(income => income && income.date)\n        .map(income => new Date(income.date))\n        .filter(date => !isNaN(date.getTime()))\n\n      if (validDates.length === 0) {\n        const joinDate = new Date(member.createdAt)\n        return joinDate < thresholdDate\n      }\n\n      const lastContribution = new Date(Math.max(...validDates.map(date => date.getTime())))\n      return lastContribution < thresholdDate\n    }).map(member => {\n      const totalContributions = (member.incomes || []).reduce((sum, income) => sum + (income?.amount || 0), 0)\n\n      // تصفية التواريخ الصحيحة فقط\n      const validDates = (member.incomes || [])\n        .filter(income => income && income.date)\n        .map(income => new Date(income.date))\n        .filter(date => !isNaN(date.getTime()))\n\n      const lastContribution = validDates.length > 0\n        ? new Date(Math.max(...validDates.map(date => date.getTime())))\n        : new Date(member.createdAt)\n      \n      const daysSinceLastPayment = Math.floor((now.getTime() - lastContribution.getTime()) / (24 * 60 * 60 * 1000))\n      \n      // حساب المبلغ المستحق (افتراضياً 50 دينار شهرياً)\n      const monthlyDue = 50\n      const monthsLate = Math.floor(daysSinceLastPayment / 30)\n      const amountDue = monthsLate * monthlyDue\n\n      return {\n        ...member,\n        totalContributions,\n        lastContribution,\n        daysSinceLastPayment,\n        monthsLate,\n        amountDue\n      }\n    }).sort((a, b) => b.daysSinceLastPayment - a.daysSinceLastPayment)\n  }\n\n  // تصدير تقرير الأعضاء المتأخرين\n  const handleExportLateMembers = (format: 'pdf' | 'csv') => {\n    const lateMembers = getLateMembers() || []\n\n    const reportData = {\n      title: 'تقرير الأعضاء المتأخرين عن الدفع',\n      date: new Date().toLocaleDateString('ar-JO'),\n      threshold: `${latePaymentThreshold} يوم`,\n      summary: {\n        totalLateMembers: lateMembers.length,\n        totalAmountDue: lateMembers.reduce((sum, member) => sum + (member?.amountDue || 0), 0)\n      },\n      lateMembers: lateMembers.map(member => ({\n        name: member.name,\n        phone: member.phone || 'غير محدد',\n        email: member.email || 'غير محدد',\n        status: getStatusLabel(member.status),\n        lastContribution: member.lastContribution && !isNaN(member.lastContribution.getTime())\n          ? formatDate(member.lastContribution.toISOString())\n          : 'غير محدد',\n        daysSinceLastPayment: member.daysSinceLastPayment,\n        monthsLate: member.monthsLate,\n        amountDue: member.amountDue,\n        totalContributions: member.totalContributions\n      }))\n    }\n\n    if (format === 'pdf') {\n      onExportPDF(reportData, 'late-members')\n    } else {\n      onExportCSV(reportData, 'late-members')\n    }\n  }\n\n  // إرسال إشعارات للأعضاء المتأخرين\n  const handleSendNotifications = async () => {\n    const lateMembers = getLateMembers() || []\n    const membersWithPhone = lateMembers.filter(member => member?.phone)\n\n    if (membersWithPhone.length === 0) {\n      alert('لا يوجد أعضاء متأخرون لديهم أرقام هواتف')\n      return\n    }\n\n    const confirmed = confirm(`هل تريد إرسال إشعارات واتساب لـ ${membersWithPhone.length} عضو متأخر؟`)\n    if (!confirmed) return\n\n    setSendingNotifications(true)\n\n    try {\n      // إرسال رسائل واتساب\n      membersWithPhone.forEach((member, index) => {\n        setTimeout(() => {\n          const message = `السلام عليكم ${member.name}،\\n\\nنذكركم بسداد المساهمة المستحقة.\\n\\nالمبلغ المستحق: ${formatCurrency(member.amountDue)}\\nأيام التأخير: ${member.daysSinceLastPayment} يوم\\n\\nشكراً لتعاونكم\\nديوان آل أبو علوش`\n          const phoneNumber = member.phone.replace(/\\D/g, '')\n          window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, '_blank')\n        }, index * 2000) // تأخير 2 ثانية بين كل رسالة\n      })\n\n      alert(`تم فتح ${membersWithPhone.length} نافذة واتساب لإرسال الإشعارات`)\n    } catch (error) {\n      console.error('خطأ في إرسال الإشعارات:', error)\n      alert('حدث خطأ في إرسال الإشعارات')\n    } finally {\n      setSendingNotifications(false)\n    }\n  }\n\n  // تصدير تقرير الطوارئ\n  const handleExportEmergencyReport = (format: 'pdf' | 'csv') => {\n    const totalEmergencyAmount = (emergencyContributions || []).reduce((sum, contribution) => sum + (contribution?.amount || 0), 0)\n    \n    const reportData = {\n      title: 'تقرير الطوارئ والمساعدات',\n      date: new Date().toLocaleDateString('ar-JO'),\n      summary: {\n        totalContributions: emergencyContributions.length,\n        totalAmount: totalEmergencyAmount,\n        averageContribution: emergencyContributions.length > 0 ? totalEmergencyAmount / emergencyContributions.length : 0\n      },\n      contributions: emergencyContributions.map(contribution => ({\n        date: formatDate(contribution.date),\n        memberName: contribution.member?.name || 'غير محدد',\n        amount: contribution.amount,\n        description: contribution.description,\n        purpose: contribution.description // يمكن تحسينه لاستخراج الغرض\n      }))\n    }\n\n    if (format === 'pdf') {\n      onExportPDF(reportData, 'emergency-report')\n    } else {\n      onExportCSV(reportData, 'emergency-report')\n    }\n  }\n\n  // الحصول على تسمية الحالة\n  const getStatusLabel = (status: string) => {\n    const statusLabels = {\n      'ACTIVE': 'نشط',\n      'INACTIVE': 'غير نشط',\n      'LATE': 'متأخر',\n      'NON_COMPLIANT': 'غير ملتزم'\n    }\n    return statusLabels[status as keyof typeof statusLabels] || status\n  }\n\n  // الحصول على لون الحالة\n  const getStatusColor = (status: string) => {\n    const statusColors = {\n      'ACTIVE': 'bg-green-100 text-green-800',\n      'INACTIVE': 'bg-gray-100 text-gray-800',\n      'LATE': 'bg-yellow-100 text-yellow-800',\n      'NON_COMPLIANT': 'bg-red-100 text-red-800'\n    }\n    return statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"text-gray-500\">جاري تحميل البيانات...</div>\n      </div>\n    )\n  }\n\n\n\n  const lateMembers = getLateMembers() || []\n  const totalAmountDue = lateMembers.reduce((sum, member) => sum + (member.amountDue || 0), 0)\n  const totalEmergencyAmount = (emergencyContributions || []).reduce((sum, contribution) => sum + (contribution.amount || 0), 0)\n\n  return (\n    <div className=\"space-y-6\">\n      {/* عنوان القسم */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900 flex items-center\">\n            <AlertTriangle className=\"w-5 h-5 ml-2\" />\n            التقارير المساعدة\n          </h2>\n          <p className=\"text-gray-600\">تقارير الأعضاء المتأخرين وحالات الطوارئ</p>\n        </div>\n      </div>\n\n      {/* الإحصائيات السريعة */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <Clock className=\"w-8 h-8 text-yellow-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">الأعضاء المتأخرون</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">{lateMembers.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <DollarSign className=\"w-8 h-8 text-red-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">المبالغ المستحقة</p>\n                <p className=\"text-2xl font-bold text-red-600\">{formatCurrency(totalAmountDue)}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <Heart className=\"w-8 h-8 text-purple-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">تبرعات الطوارئ</p>\n                <p className=\"text-2xl font-bold text-purple-600\">{emergencyContributions.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <DollarSign className=\"w-8 h-8 text-green-500\" />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">مبلغ الطوارئ</p>\n                <p className=\"text-2xl font-bold text-green-600\">{formatCurrency(totalEmergencyAmount)}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* تقرير الأعضاء المتأخرين */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex justify-between items-center\">\n            <span className=\"flex items-center\">\n              <Clock className=\"w-5 h-5 ml-2\" />\n              الأعضاء المتأخرون عن الدفع\n            </span>\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <label className=\"text-sm text-gray-600\">عتبة التأخير (أيام):</label>\n                <Input\n                  type=\"number\"\n                  value={latePaymentThreshold}\n                  onChange={(e) => setLatePaymentThreshold(Number(e.target.value))}\n                  className=\"w-20\"\n                  min=\"1\"\n                />\n              </div>\n              <Button\n                onClick={() => handleExportLateMembers('pdf')}\n                className=\"bg-red-600 hover:bg-red-700 text-white\"\n              >\n                <FileText className=\"w-4 h-4 ml-2\" />\n                PDF\n              </Button>\n              <Button\n                onClick={() => handleExportLateMembers('csv')}\n                className=\"bg-green-600 hover:bg-green-700 text-white\"\n              >\n                <Download className=\"w-4 h-4 ml-2\" />\n                CSV\n              </Button>\n              <Button\n                onClick={handleSendNotifications}\n                disabled={sendingNotifications || lateMembers.length === 0}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50\"\n              >\n                <Phone className=\"w-4 h-4 ml-2\" />\n                {sendingNotifications ? 'جاري الإرسال...' : 'إرسال إشعارات'}\n              </Button>\n            </div>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {lateMembers.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-500 mb-4\">\n                <div className=\"mb-2\">✅ لا يوجد أعضاء متأخرون عن الدفع حالياً</div>\n                <div className=\"text-sm\">عتبة التأخير الحالية: {latePaymentThreshold} يوم</div>\n                <div className=\"text-sm\">عدد الأعضاء الكلي: {members.length}</div>\n              </div>\n              {members.length > 0 && (\n                <div className=\"text-sm text-blue-600\">\n                  جميع الأعضاء النشطين لديهم مساهمات حديثة (خلال آخر {latePaymentThreshold} يوم)\n                </div>\n              )}\n              {members.length === 0 && (\n                <div className=\"text-sm text-orange-600\">\n                  لا توجد بيانات أعضاء في النظام\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full border-collapse\">\n                <thead>\n                  <tr className=\"border-b\">\n                    <th className=\"text-right p-3 font-medium text-gray-700\">الاسم</th>\n                    <th className=\"text-right p-3 font-medium text-gray-700\">الهاتف</th>\n                    <th className=\"text-right p-3 font-medium text-gray-700\">الحالة</th>\n                    <th className=\"text-right p-3 font-medium text-gray-700\">آخر دفعة</th>\n                    <th className=\"text-right p-3 font-medium text-gray-700\">أيام التأخير</th>\n                    <th className=\"text-right p-3 font-medium text-gray-700\">المبلغ المستحق</th>\n                    <th className=\"text-right p-3 font-medium text-gray-700\">الإجراءات</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {lateMembers.map((member) => (\n                    <tr key={member.id} className=\"border-b hover:bg-gray-50\">\n                      <td className=\"p-3 font-medium\">{member.name}</td>\n                      <td className=\"p-3\">\n                        <div className=\"flex items-center\">\n                          <Phone className=\"w-4 h-4 ml-2 text-gray-400\" />\n                          {member.phone || 'غير محدد'}\n                        </div>\n                      </td>\n                      <td className=\"p-3\">\n                        <Badge className={getStatusColor(member.status)}>\n                          {getStatusLabel(member.status)}\n                        </Badge>\n                      </td>\n                      <td className=\"p-3\">\n                        {member.lastContribution && !isNaN(member.lastContribution.getTime())\n                          ? formatDate(member.lastContribution.toISOString())\n                          : 'غير محدد'\n                        }\n                      </td>\n                      <td className=\"p-3\">\n                        <span className=\"font-medium text-red-600\">\n                          {member.daysSinceLastPayment} يوم\n                        </span>\n                      </td>\n                      <td className=\"p-3\">\n                        <span className=\"font-bold text-red-600\">\n                          {formatCurrency(member.amountDue)}\n                        </span>\n                      </td>\n                      <td className=\"p-3\">\n                        <div className=\"flex space-x-1 space-x-reverse\">\n                          <Button size=\"sm\" variant=\"outline\">\n                            إشعار\n                          </Button>\n                          {member.phone && (\n                            <Button\n                              size=\"sm\"\n                              variant=\"outline\"\n                              onClick={() => {\n                                const message = `السلام عليكم ${member.name}،\\n\\nنذكركم بسداد المساهمة المستحقة.\\n\\nالمبلغ المستحق: ${formatCurrency(member.amountDue)}\\nأيام التأخير: ${member.daysSinceLastPayment} يوم\\n\\nشكراً لتعاونكم\\nديوان آل أبو علوش`\n                                const phoneNumber = member.phone.replace(/\\D/g, '')\n                                window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, '_blank')\n                              }}\n                              className=\"text-green-600 hover:text-green-700\"\n                            >\n                              <MessageCircle className=\"w-3 h-3 ml-1\" />\n                              واتساب\n                            </Button>\n                          )}\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* تقرير الطوارئ */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex justify-between items-center\">\n            <span className=\"flex items-center\">\n              <Heart className=\"w-5 h-5 ml-2\" />\n              تقرير الطوارئ والمساعدات\n            </span>\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <Button\n                onClick={() => handleExportEmergencyReport('pdf')}\n                className=\"bg-red-600 hover:bg-red-700 text-white\"\n              >\n                <FileText className=\"w-4 h-4 ml-2\" />\n                PDF\n              </Button>\n              <Button\n                onClick={() => handleExportEmergencyReport('csv')}\n                className=\"bg-green-600 hover:bg-green-700 text-white\"\n              >\n                <Download className=\"w-4 h-4 ml-2\" />\n                CSV\n              </Button>\n            </div>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {emergencyContributions.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-500 mb-4\">\n                <div className=\"mb-2\">ℹ️ لا توجد تبرعات طوارئ مسجلة</div>\n                <div className=\"text-sm mb-2\">يتم البحث التلقائي عن الكلمات المفتاحية في مصدر أو وصف التبرع:</div>\n                <div className=\"text-xs bg-gray-100 p-2 rounded inline-block\">\n                  طوارئ، وفاة، كارثة، مساعدة، عاجل، طارئ، مرض، علاج، عملية، مستشفى\n                </div>\n              </div>\n              <div className=\"text-sm text-blue-600\">\n                لإظهار تبرع كطارئ، تأكد من تضمين إحدى هذه الكلمات في مصدر التبرع أو وصفه\n              </div>\n            </div>\n          ) : (\n            <>\n              {/* ملخص الطوارئ */}\n              <div className=\"mb-6 p-4 bg-purple-50 rounded-lg\">\n                <h3 className=\"font-medium text-purple-900 mb-2\">ملخص تبرعات الطوارئ</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <span className=\"text-sm text-purple-600\">إجمالي التبرعات:</span>\n                    <div className=\"font-bold text-purple-900\">{emergencyContributions.length}</div>\n                  </div>\n                  <div>\n                    <span className=\"text-sm text-purple-600\">إجمالي المبلغ:</span>\n                    <div className=\"font-bold text-purple-900\">{formatCurrency(totalEmergencyAmount)}</div>\n                  </div>\n                  <div>\n                    <span className=\"text-sm text-purple-600\">متوسط التبرع:</span>\n                    <div className=\"font-bold text-purple-900\">\n                      {formatCurrency(emergencyContributions.length > 0 ? totalEmergencyAmount / emergencyContributions.length : 0)}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* جدول تبرعات الطوارئ */}\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full border-collapse\">\n                  <thead>\n                    <tr className=\"border-b\">\n                      <th className=\"text-right p-3 font-medium text-gray-700\">التاريخ</th>\n                      <th className=\"text-right p-3 font-medium text-gray-700\">المتبرع</th>\n                      <th className=\"text-right p-3 font-medium text-gray-700\">المبلغ</th>\n                      <th className=\"text-right p-3 font-medium text-gray-700\">الغرض</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {emergencyContributions.map((contribution) => (\n                      <tr key={contribution.id} className=\"border-b hover:bg-gray-50\">\n                        <td className=\"p-3\">{formatDate(contribution.date)}</td>\n                        <td className=\"p-3\">\n                          <div className=\"flex items-center\">\n                            <Users className=\"w-4 h-4 ml-2 text-gray-400\" />\n                            {contribution.member?.name || 'غير محدد'}\n                          </div>\n                        </td>\n                        <td className=\"p-3 font-bold text-green-600\">\n                          {formatCurrency(contribution.amount)}\n                        </td>\n                        <td className=\"p-3 text-sm\">{contribution.description}</td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAoDe,SAAS,cAAc,EAAE,WAAW,EAAE,WAAW,EAAsB;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,EAAE;IAChG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO;;IAC5E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,eAAe;IACf,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YAEX,MAAM,CAAC,YAAY,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjD,MAAM;gBACN,MAAM,2BAA2B,kCAAkC;aACpE;YAED,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,WAAW,IAAI;gBACf,WAAW,IAAI;aAChB;YAED,WAAW,YAAY,OAAO,IAAI,EAAE;YAEpC,iFAAiF;YACjF,MAAM,oBAAoB;gBAAC;gBAAS;gBAAQ;gBAAS;gBAAU;gBAAQ;gBAAQ;gBAAO;gBAAQ;gBAAS;aAAS;YAChH,MAAM,mBAAmB,CAAC,YAAY,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,SAC3D,kBAAkB,IAAI,CAAC,CAAA,UACrB,OAAO,MAAM,EAAE,cAAc,SAAS,YACtC,OAAO,WAAW,EAAE,cAAc,SAAS,YAC3C,OAAO,IAAI,KAAK,YAAY,+BAA+B;;YAM/D,0BAA0B;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,UAAU;YACvC,OAAO,EAAE;QACX;QAEA,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,IAAI,KAAK,IAAI,OAAO,KAAK,uBAAuB,KAAK,KAAK,KAAK;QAIrF,OAAO,QAAQ,MAAM,CAAC,CAAA;YACpB,yCAAyC;YACzC,IAAI,OAAO,MAAM,KAAK,YAAY,OAAO,MAAM,KAAK,QAAQ;gBAC1D,OAAO;YACT;YAEA,2BAA2B;YAC3B,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,OAAO,KAAK,OAAO,OAAO,CAAC,MAAM,KAAK,GAAG;gBACpF,MAAM,WAAW,IAAI,KAAK,OAAO,SAAS;gBAC1C,OAAO,WAAW;YACpB;YAEA,0CAA0C;YAC1C,MAAM,aAAa,OAAO,OAAO,CAC9B,MAAM,CAAC,CAAA,SAAU,UAAU,OAAO,IAAI,EACtC,GAAG,CAAC,CAAA,SAAU,IAAI,KAAK,OAAO,IAAI,GAClC,MAAM,CAAC,CAAA,OAAQ,CAAC,MAAM,KAAK,OAAO;YAErC,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,MAAM,WAAW,IAAI,KAAK,OAAO,SAAS;gBAC1C,OAAO,WAAW;YACpB;YAEA,MAAM,mBAAmB,IAAI,KAAK,KAAK,GAAG,IAAI,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;YACjF,OAAO,mBAAmB;QAC5B,GAAG,GAAG,CAAC,CAAA;YACL,MAAM,qBAAqB,CAAC,OAAO,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,CAAC,QAAQ,UAAU,CAAC,GAAG;YAEvG,6BAA6B;YAC7B,MAAM,aAAa,CAAC,OAAO,OAAO,IAAI,EAAE,EACrC,MAAM,CAAC,CAAA,SAAU,UAAU,OAAO,IAAI,EACtC,GAAG,CAAC,CAAA,SAAU,IAAI,KAAK,OAAO,IAAI,GAClC,MAAM,CAAC,CAAA,OAAQ,CAAC,MAAM,KAAK,OAAO;YAErC,MAAM,mBAAmB,WAAW,MAAM,GAAG,IACzC,IAAI,KAAK,KAAK,GAAG,IAAI,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,QACxD,IAAI,KAAK,OAAO,SAAS;YAE7B,MAAM,uBAAuB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,iBAAiB,OAAO,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI;YAE3G,kDAAkD;YAClD,MAAM,aAAa;YACnB,MAAM,aAAa,KAAK,KAAK,CAAC,uBAAuB;YACrD,MAAM,YAAY,aAAa;YAE/B,OAAO;gBACL,GAAG,MAAM;gBACT;gBACA;gBACA;gBACA;gBACA;YACF;QACF,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,oBAAoB,GAAG,EAAE,oBAAoB;IACnE;IAEA,gCAAgC;IAChC,MAAM,0BAA0B,CAAC;QAC/B,MAAM,cAAc,oBAAoB,EAAE;QAE1C,MAAM,aAAa;YACjB,OAAO;YACP,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,WAAW,GAAG,qBAAqB,IAAI,CAAC;YACxC,SAAS;gBACP,kBAAkB,YAAY,MAAM;gBACpC,gBAAgB,YAAY,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,CAAC,QAAQ,aAAa,CAAC,GAAG;YACtF;YACA,aAAa,YAAY,GAAG,CAAC,CAAA,SAAU,CAAC;oBACtC,MAAM,OAAO,IAAI;oBACjB,OAAO,OAAO,KAAK,IAAI;oBACvB,OAAO,OAAO,KAAK,IAAI;oBACvB,QAAQ,eAAe,OAAO,MAAM;oBACpC,kBAAkB,OAAO,gBAAgB,IAAI,CAAC,MAAM,OAAO,gBAAgB,CAAC,OAAO,MAC/E,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,gBAAgB,CAAC,WAAW,MAC9C;oBACJ,sBAAsB,OAAO,oBAAoB;oBACjD,YAAY,OAAO,UAAU;oBAC7B,WAAW,OAAO,SAAS;oBAC3B,oBAAoB,OAAO,kBAAkB;gBAC/C,CAAC;QACH;QAEA,IAAI,WAAW,OAAO;YACpB,YAAY,YAAY;QAC1B,OAAO;YACL,YAAY,YAAY;QAC1B;IACF;IAEA,kCAAkC;IAClC,MAAM,0BAA0B;QAC9B,MAAM,cAAc,oBAAoB,EAAE;QAC1C,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA,SAAU,QAAQ;QAE9D,IAAI,iBAAiB,MAAM,KAAK,GAAG;YACjC,MAAM;YACN;QACF;QAEA,MAAM,YAAY,QAAQ,CAAC,gCAAgC,EAAE,iBAAiB,MAAM,CAAC,WAAW,CAAC;QACjG,IAAI,CAAC,WAAW;QAEhB,wBAAwB;QAExB,IAAI;YACF,qBAAqB;YACrB,iBAAiB,OAAO,CAAC,CAAC,QAAQ;gBAChC,WAAW;oBACT,MAAM,UAAU,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC,wDAAwD,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS,EAAE,gBAAgB,EAAE,OAAO,oBAAoB,CAAC,yCAAyC,CAAC;oBAC/N,MAAM,cAAc,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO;oBAChD,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY,MAAM,EAAE,mBAAmB,UAAU,EAAE;gBAClF,GAAG,QAAQ,MAAM,6BAA6B;;YAChD;YAEA,MAAM,CAAC,OAAO,EAAE,iBAAiB,MAAM,CAAC,8BAA8B,CAAC;QACzE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,wBAAwB;QAC1B;IACF;IAEA,sBAAsB;IACtB,MAAM,8BAA8B,CAAC;QACnC,MAAM,uBAAuB,CAAC,0BAA0B,EAAE,EAAE,MAAM,CAAC,CAAC,KAAK,eAAiB,MAAM,CAAC,cAAc,UAAU,CAAC,GAAG;QAE7H,MAAM,aAAa;YACjB,OAAO;YACP,MAAM,IAAI,OAAO,kBAAkB,CAAC;YACpC,SAAS;gBACP,oBAAoB,uBAAuB,MAAM;gBACjD,aAAa;gBACb,qBAAqB,uBAAuB,MAAM,GAAG,IAAI,uBAAuB,uBAAuB,MAAM,GAAG;YAClH;YACA,eAAe,uBAAuB,GAAG,CAAC,CAAA,eAAgB,CAAC;oBACzD,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,aAAa,IAAI;oBAClC,YAAY,aAAa,MAAM,EAAE,QAAQ;oBACzC,QAAQ,aAAa,MAAM;oBAC3B,aAAa,aAAa,WAAW;oBACrC,SAAS,aAAa,WAAW,CAAC,6BAA6B;gBACjE,CAAC;QACH;QAEA,IAAI,WAAW,OAAO;YACpB,YAAY,YAAY;QAC1B,OAAO;YACL,YAAY,YAAY;QAC1B;IACF;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,iBAAiB;QACnB;QACA,OAAO,YAAY,CAAC,OAAoC,IAAI;IAC9D;IAEA,wBAAwB;IACxB,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,iBAAiB;QACnB;QACA,OAAO,YAAY,CAAC,OAAoC,IAAI;IAC9D;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAIA,MAAM,cAAc,oBAAoB,EAAE;IAC1C,MAAM,iBAAiB,YAAY,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,SAAS,IAAI,CAAC,GAAG;IAC1F,MAAM,uBAAuB,CAAC,0BAA0B,EAAE,EAAE,MAAM,CAAC,CAAC,KAAK,eAAiB,MAAM,CAAC,aAAa,MAAM,IAAI,CAAC,GAAG;IAE5H,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAG5C,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAsC,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7E,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAmC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAsC,uBAAuB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxF,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3E,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;oCAAK,WAAU;;sDACd,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAwB;;;;;;8DACzC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,wBAAwB,OAAO,EAAE,MAAM,CAAC,KAAK;oDAC9D,WAAU;oDACV,KAAI;;;;;;;;;;;;sDAGR,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,wBAAwB;4CACvC,WAAU;;8DAEV,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,wBAAwB;4CACvC,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,wBAAwB,YAAY,MAAM,KAAK;4CACzD,WAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAChB,uBAAuB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;kCAKpD,8OAAC,gIAAA,CAAA,cAAW;kCACT,YAAY,MAAM,KAAK,kBACtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAO;;;;;;sDACtB,8OAAC;4CAAI,WAAU;;gDAAU;gDAAuB;gDAAqB;;;;;;;sDACrE,8OAAC;4CAAI,WAAU;;gDAAU;gDAAoB,QAAQ,MAAM;;;;;;;;;;;;;gCAE5D,QAAQ,MAAM,GAAG,mBAChB,8OAAC;oCAAI,WAAU;;wCAAwB;wCACe;wCAAqB;;;;;;;gCAG5E,QAAQ,MAAM,KAAK,mBAClB,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;;;;;;iDAM7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;;;;;;;;;;;;kDAG7D,8OAAC;kDACE,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;gDAAmB,WAAU;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAmB,OAAO,IAAI;;;;;;kEAC5C,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,OAAO,KAAK,IAAI;;;;;;;;;;;;kEAGrB,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAW,eAAe,OAAO,MAAM;sEAC3C,eAAe,OAAO,MAAM;;;;;;;;;;;kEAGjC,8OAAC;wDAAG,WAAU;kEACX,OAAO,gBAAgB,IAAI,CAAC,MAAM,OAAO,gBAAgB,CAAC,OAAO,MAC9D,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,gBAAgB,CAAC,WAAW,MAC9C;;;;;;kEAGN,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAU;;gEACb,OAAO,oBAAoB;gEAAC;;;;;;;;;;;;kEAGjC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS;;;;;;;;;;;kEAGpC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;8EAAU;;;;;;gEAGnC,OAAO,KAAK,kBACX,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS;wEACP,MAAM,UAAU,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC,wDAAwD,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS,EAAE,gBAAgB,EAAE,OAAO,oBAAoB,CAAC,yCAAyC,CAAC;wEAC/N,MAAM,cAAc,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO;wEAChD,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY,MAAM,EAAE,mBAAmB,UAAU,EAAE;oEAClF;oEACA,WAAU;;sFAEV,8OAAC,wNAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;+CA7C3C,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA6DhC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;oCAAK,WAAU;;sDACd,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,4BAA4B;4CAC3C,WAAU;;8DAEV,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,4BAA4B;4CAC3C,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,gIAAA,CAAA,cAAW;kCACT,uBAAuB,MAAM,KAAK,kBACjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAO;;;;;;sDACtB,8OAAC;4CAAI,WAAU;sDAAe;;;;;;sDAC9B,8OAAC;4CAAI,WAAU;sDAA+C;;;;;;;;;;;;8CAIhE,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;iDAKzC;;8CAEE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA0B;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;sEAA6B,uBAAuB,MAAM;;;;;;;;;;;;8DAE3E,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA0B;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;sEAA6B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;8DAE7D,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA0B;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,uBAAuB,MAAM,GAAG,IAAI,uBAAuB,uBAAuB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;8CAOnH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;0DACC,cAAA,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;;;;;;;;;;;;0DAG7D,8OAAC;0DACE,uBAAuB,GAAG,CAAC,CAAC,6BAC3B,8OAAC;wDAAyB,WAAU;;0EAClC,8OAAC;gEAAG,WAAU;0EAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,aAAa,IAAI;;;;;;0EACjD,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,aAAa,MAAM,EAAE,QAAQ;;;;;;;;;;;;0EAGlC,8OAAC;gEAAG,WAAU;0EACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,MAAM;;;;;;0EAErC,8OAAC;gEAAG,WAAU;0EAAe,aAAa,WAAW;;;;;;;uDAX9C,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuB9C", "debugId": null}}, {"offset": {"line": 7902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/dashboard/reports-advanced/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'\nimport { exportToPDF, exportToCSV } from '@/components/reports/ReportExporter'\nimport MembersReports from '@/components/reports/MembersReports'\nimport IncomesReports from '@/components/reports/IncomesReports'\nimport ExpensesReports from '@/components/reports/ExpensesReports'\nimport ComprehensiveReports from '@/components/reports/ComprehensiveReports'\nimport HelperReports from '@/components/reports/HelperReports'\nimport {\n  Users,\n  TrendingUp,\n  TrendingDown,\n  BarChart3,\n  AlertTriangle,\n  FileText,\n  Calculator\n} from 'lucide-react'\n\nexport default function AdvancedReportsPage() {\n  const { data: session } = useSession()\n  const [activeTab, setActiveTab] = useState('members')\n\n  // دالة معالجة تصدير PDF\n  const handleExportPDF = async (data: unknown, type: string) => {\n    await exportToPDF(data, type)\n  }\n\n  // دالة معالجة تصدير CSV\n  const handleExportCSV = (data: unknown, type: string) => {\n    exportToCSV(data, type)\n  }\n\n  if (!session) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"text-gray-500\">يرجى تسجيل الدخول للوصول إلى التقارير</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6 arabic-text\">\n      {/* العنوان الرئيسي */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            نظام التقارير المتقدم\n          </h1>\n          <p className=\"text-gray-600 mt-2\" style={{ fontFamily: 'Cairo, Almarai, sans-serif' }}>\n            تقارير شاملة ومفصلة لجميع جوانب إدارة ديوان أبو علوش\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-2 space-x-reverse\">\n          <div className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\n            نظام متقدم\n          </div>\n        </div>\n      </div>\n\n      {/* نظرة عامة سريعة */}\n      <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <Card className=\"cursor-pointer hover:shadow-md transition-shadow\" onClick={() => setActiveTab('members')}>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <Users className={`w-8 h-8 ${activeTab === 'members' ? 'text-blue-600' : 'text-gray-400'}`} />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">تقارير الأعضاء</p>\n                <p className=\"text-xs text-gray-500\">قوائم وكشوف مفصلة</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"cursor-pointer hover:shadow-md transition-shadow\" onClick={() => setActiveTab('incomes')}>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <TrendingUp className={`w-8 h-8 ${activeTab === 'incomes' ? 'text-green-600' : 'text-gray-400'}`} />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">تقارير الإيرادات</p>\n                <p className=\"text-xs text-gray-500\">تحليل المساهمات</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"cursor-pointer hover:shadow-md transition-shadow\" onClick={() => setActiveTab('expenses')}>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <TrendingDown className={`w-8 h-8 ${activeTab === 'expenses' ? 'text-red-600' : 'text-gray-400'}`} />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">تقارير المصروفات</p>\n                <p className=\"text-xs text-gray-500\">تحليل النفقات</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"cursor-pointer hover:shadow-md transition-shadow\" onClick={() => setActiveTab('comprehensive')}>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <BarChart3 className={`w-8 h-8 ${activeTab === 'comprehensive' ? 'text-purple-600' : 'text-gray-400'}`} />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">التقارير الشاملة</p>\n                <p className=\"text-xs text-gray-500\">كشوف ومقارنات</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"cursor-pointer hover:shadow-md transition-shadow\" onClick={() => setActiveTab('helper')}>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <AlertTriangle className={`w-8 h-8 ${activeTab === 'helper' ? 'text-orange-600' : 'text-gray-400'}`} />\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">التقارير المساعدة</p>\n                <p className=\"text-xs text-gray-500\">متأخرين وطوارئ</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* التبويبات الرئيسية */}\n      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-5\">\n          <TabsTrigger value=\"members\" className=\"flex items-center space-x-2 space-x-reverse\">\n            <Users className=\"w-4 h-4\" />\n            <span>تقارير الأعضاء</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"incomes\" className=\"flex items-center space-x-2 space-x-reverse\">\n            <TrendingUp className=\"w-4 h-4\" />\n            <span>تقارير الإيرادات</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"expenses\" className=\"flex items-center space-x-2 space-x-reverse\">\n            <TrendingDown className=\"w-4 h-4\" />\n            <span>تقارير المصروفات</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"comprehensive\" className=\"flex items-center space-x-2 space-x-reverse\">\n            <BarChart3 className=\"w-4 h-4\" />\n            <span>التقارير الشاملة</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"helper\" className=\"flex items-center space-x-2 space-x-reverse\">\n            <AlertTriangle className=\"w-4 h-4\" />\n            <span>التقارير المساعدة</span>\n          </TabsTrigger>\n        </TabsList>\n\n        {/* محتوى التبويبات */}\n        <TabsContent value=\"members\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Users className=\"w-5 h-5 ml-2\" />\n                تقارير الأعضاء\n              </CardTitle>\n              <p className=\"text-sm text-gray-600\">\n                تقارير شاملة عن الأعضاء تشمل قائمة الأعضاء والتقارير المفصلة لكل عضو\n              </p>\n            </CardHeader>\n            <CardContent>\n              <MembersReports \n                onExportPDF={handleExportPDF}\n                onExportCSV={handleExportCSV}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"incomes\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <TrendingUp className=\"w-5 h-5 ml-2\" />\n                تقارير الإيرادات\n              </CardTitle>\n              <p className=\"text-sm text-gray-600\">\n                تقارير مفصلة عن الإيرادات والمساهمات حسب الفترة والنوع والعضو\n              </p>\n            </CardHeader>\n            <CardContent>\n              <IncomesReports \n                onExportPDF={handleExportPDF}\n                onExportCSV={handleExportCSV}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"expenses\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <TrendingDown className=\"w-5 h-5 ml-2\" />\n                تقارير المصروفات\n              </CardTitle>\n              <p className=\"text-sm text-gray-600\">\n                تقارير شاملة عن المصروفات والنفقات حسب الفئة والمستفيد والفترة الزمنية\n              </p>\n            </CardHeader>\n            <CardContent>\n              <ExpensesReports \n                onExportPDF={handleExportPDF}\n                onExportCSV={handleExportCSV}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"comprehensive\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Calculator className=\"w-5 h-5 ml-2\" />\n                التقارير الشاملة\n              </CardTitle>\n              <p className=\"text-sm text-gray-600\">\n                كشوف حسابات الأعضاء وملخص الحسابات العامة وتقارير المقارنة\n              </p>\n            </CardHeader>\n            <CardContent>\n              <ComprehensiveReports \n                onExportPDF={handleExportPDF}\n                onExportCSV={handleExportCSV}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"helper\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <AlertTriangle className=\"w-5 h-5 ml-2\" />\n                التقارير المساعدة\n              </CardTitle>\n              <p className=\"text-sm text-gray-600\">\n                تقارير الأعضاء المتأخرين عن الدفع وتقارير الطوارئ والمساعدات\n              </p>\n            </CardHeader>\n            <CardContent>\n              <HelperReports \n                onExportPDF={handleExportPDF}\n                onExportCSV={handleExportCSV}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n\n      {/* معلومات إضافية */}\n      <Card className=\"bg-blue-50 border-blue-200\">\n        <CardContent className=\"pt-6\">\n          <div className=\"flex items-start space-x-3 space-x-reverse\">\n            <FileText className=\"w-5 h-5 text-blue-600 mt-0.5\" />\n            <div>\n              <h3 className=\"font-medium text-blue-900 mb-2\">ميزات نظام التقارير المتقدم</h3>\n              <ul className=\"text-sm text-blue-800 space-y-1\">\n                <li>• تصدير جميع التقارير بصيغة PDF مع دعم كامل للغة العربية</li>\n                <li>• تصدير البيانات بصيغة CSV للتحليل في برامج أخرى</li>\n                <li>• فلترة متقدمة حسب التاريخ والنوع والعضو</li>\n                <li>• بحث ذكي في جميع البيانات</li>\n                <li>• إحصائيات وتحليلات تفاعلية</li>\n                <li>• تقارير الأعضاء المتأخرين مع إمكانية إرسال تذكيرات واتساب</li>\n                <li>• كشوف حسابات مفصلة لكل عضو</li>\n                <li>• تقارير مقارنة شهرية وسنوية</li>\n              </ul>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;;;AAsBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wBAAwB;IACxB,MAAM,kBAAkB,OAAO,MAAe;QAC5C,MAAM,CAAA,GAAA,+IAAA,CAAA,cAAW,AAAD,EAAE,MAAM;IAC1B;IAEA,wBAAwB;IACxB,MAAM,kBAAkB,CAAC,MAAe;QACtC,CAAA,GAAA,+IAAA,CAAA,cAAW,AAAD,EAAE,MAAM;IACpB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;gCAAmC,OAAO;oCAAE,YAAY;gCAA6B;0CAAG;;;;;;0CAGtG,8OAAC;gCAAE,WAAU;gCAAqB,OAAO;oCAAE,YAAY;gCAA6B;0CAAG;;;;;;;;;;;;kCAIzF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAuE;;;;;;;;;;;;;;;;;0BAO1F,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,aAAa;kCAC7F,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,YAAY,kBAAkB,iBAAiB;;;;;;kDAC1F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,aAAa;kCAC7F,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,YAAY,mBAAmB,iBAAiB;;;;;;kDAChG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,aAAa;kCAC7F,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,aAAa,iBAAiB,iBAAiB;;;;;;kDACjG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,aAAa;kCAC7F,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,kBAAkB,oBAAoB,iBAAiB;;;;;;kDACtG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,aAAa;kCAC7F,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,WAAW,oBAAoB,iBAAiB;;;;;;kDACnG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;gBAAc,WAAU;;kCAC7D,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;;kDACrC,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;;kDACrC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAgB,WAAU;;kDAC3C,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;;kDACpC,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,+IAAA,CAAA,UAAc;wCACb,aAAa;wCACb,aAAa;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGzC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,+IAAA,CAAA,UAAc;wCACb,aAAa;wCACb,aAAa;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,gJAAA,CAAA,UAAe;wCACd,aAAa;wCACb,aAAa;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAgB,WAAU;kCAC3C,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGzC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,qJAAA,CAAA,UAAoB;wCACnB,aAAa;wCACb,aAAa;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;kCACpC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,8IAAA,CAAA,UAAa;wCACZ,aAAa;wCACb,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}]}