"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>T<PERSON>le, DialogFooter } from './dialog'
import { Button } from './button'
import { Card, CardContent, CardHeader, CardTitle } from './card'
import { Input } from './input'
import { Label } from './label'
import { Textarea } from './textarea'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  CreditCard, 
  Settings, 
  Bell,
  Shield,
  Palette,
  Database,
  FileText,
  Image,
  Video,
  Music,
  Download,
  Upload,
  Share,
  Heart,
  Star,
  MessageCircle
} from 'lucide-react'

interface DialogExamplesProps {
  className?: string
}

export default function DialogExamples({ className }: DialogExamplesProps) {
  const [profileDialogOpen, setProfileDialogOpen] = useState(false)
  const [formDialogOpen, setFormDialogOpen] = useState(false)
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)
  const [mediaD<PERSON><PERSON><PERSON><PERSON>, setMediaDialogOpen] = useState(false)
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false)

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center">
        <h3 className="text-xl font-bold text-gray-900 mb-2">أمثلة متنوعة للشاشات المنبثقة</h3>
        <p className="text-gray-600">تجربة أنواع مختلفة من المحتوى مع تتبع الماوس</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* حوار الملف الشخصي */}
        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setProfileDialogOpen(true)}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <User className="w-5 h-5" />
              الملف الشخصي
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">عرض وتحرير معلومات المستخدم</p>
          </CardContent>
        </Card>

        {/* حوار النموذج */}
        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setFormDialogOpen(true)}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-900">
              <FileText className="w-5 h-5" />
              نموذج تفاعلي
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">نموذج مع حقول متعددة</p>
          </CardContent>
        </Card>

        {/* حوار التأكيد */}
        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setConfirmDialogOpen(true)}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-red-900">
              <Shield className="w-5 h-5" />
              تأكيد العملية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">حوار تأكيد بسيط</p>
          </CardContent>
        </Card>

        {/* حوار الوسائط */}
        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setMediaDialogOpen(true)}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-900">
              <Image className="w-5 h-5" />
              معرض الوسائط
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">عرض الصور والفيديوهات</p>
          </CardContent>
        </Card>

        {/* حوار الإعدادات */}
        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setSettingsDialogOpen(true)}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-indigo-900">
              <Settings className="w-5 h-5" />
              الإعدادات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">إعدادات التطبيق المتقدمة</p>
          </CardContent>
        </Card>
      </div>

      {/* حوار الملف الشخصي */}
      <Dialog 
        open={profileDialogOpen} 
        onOpenChange={setProfileDialogOpen}
        followMouse={true}
        centerOnOpen={true}
        smoothTransition={true}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <User className="w-5 h-5 text-blue-600" />
              الملف الشخصي
            </DialogTitle>
          </DialogHeader>
          <div className="p-6 space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="w-8 h-8 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">أحمد محمد</h3>
                <p className="text-sm text-gray-600">مطور واجهات أمامية</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-gray-500" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-gray-500" />
                <span className="text-sm">+962 79 123 4567</span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="w-4 h-4 text-gray-500" />
                <span className="text-sm">عمان، الأردن</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setProfileDialogOpen(false)}>
              إغلاق
            </Button>
            <Button>تحرير</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار النموذج */}
      <Dialog 
        open={formDialogOpen} 
        onOpenChange={setFormDialogOpen}
        followMouse={false} // للنماذج نفضل عدم التتبع
        centerOnOpen={true}
        smoothTransition={true}
      >
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-green-600" />
              إضافة عضو جديد
            </DialogTitle>
          </DialogHeader>
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">الاسم الأول</Label>
                <Input id="firstName" placeholder="أدخل الاسم الأول" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">الاسم الأخير</Label>
                <Input id="lastName" placeholder="أدخل الاسم الأخير" />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">البريد الإلكتروني</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone">رقم الهاتف</Label>
              <Input id="phone" placeholder="+962 79 123 4567" />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">ملاحظات</Label>
              <Textarea id="notes" placeholder="أضف أي ملاحظات إضافية..." rows={3} />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setFormDialogOpen(false)}>
              إلغاء
            </Button>
            <Button>حفظ</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار التأكيد */}
      <Dialog 
        open={confirmDialogOpen} 
        onOpenChange={setConfirmDialogOpen}
        followMouse={true}
        centerOnOpen={true}
        smoothTransition={true}
      >
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-red-600" />
              تأكيد الحذف
            </DialogTitle>
          </DialogHeader>
          <div className="p-6">
            <p className="text-gray-600">
              هل أنت متأكد من أنك تريد حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>
              إلغاء
            </Button>
            <Button variant="destructive">حذف</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار الوسائط */}
      <Dialog 
        open={mediaDialogOpen} 
        onOpenChange={setMediaDialogOpen}
        followMouse={true}
        centerOnOpen={true}
        smoothTransition={true}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Image className="w-5 h-5 text-purple-600" />
              معرض الوسائط
            </DialogTitle>
          </DialogHeader>
          <div className="p-6">
            <div className="grid grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <div key={item} className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                  <Image className="w-8 h-8 text-gray-400" />
                </div>
              ))}
            </div>
            
            <div className="flex justify-center gap-4 mt-6">
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                رفع
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                تحميل
              </Button>
              <Button variant="outline" size="sm">
                <Share className="w-4 h-4 mr-2" />
                مشاركة
              </Button>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setMediaDialogOpen(false)}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار الإعدادات */}
      <Dialog 
        open={settingsDialogOpen} 
        onOpenChange={setSettingsDialogOpen}
        followMouse={false}
        centerOnOpen={true}
        smoothTransition={true}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-indigo-600" />
              إعدادات التطبيق
            </DialogTitle>
          </DialogHeader>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                  <Bell className="w-4 h-4" />
                  الإشعارات
                </h4>
                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">إشعارات البريد الإلكتروني</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">إشعارات الهاتف</span>
                  </label>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                  <Palette className="w-4 h-4" />
                  المظهر
                </h4>
                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input type="radio" name="theme" className="rounded" />
                    <span className="text-sm">فاتح</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="radio" name="theme" className="rounded" />
                    <span className="text-sm">داكن</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setSettingsDialogOpen(false)}>
              إلغاء
            </Button>
            <Button>حفظ الإعدادات</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
