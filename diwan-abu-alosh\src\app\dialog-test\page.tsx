"use client"

import React from 'react'
import DialogDemo from '@/components/ui/dialog-demo'
import DialogExamples from '@/components/ui/dialog-examples'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Eye } from 'lucide-react'

export default function DialogTestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* العنوان الرئيسي */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <div className="p-3 bg-blue-100 rounded-full">
              <MousePointer className="w-8 h-8 text-blue-600" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">
              اختبار الشاشات المنبثقة التفاعلية
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            تجربة شاملة للشاشات المنبثقة المحسنة التي تتفاعل مع حركة الماوس وتوفر تجربة مستخدم متطورة
          </p>
        </div>

        {/* بطاقات الميزات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-blue-900">
                <MousePointer className="w-5 h-5" />
                تتبع الماوس
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-blue-800">
                الحوار يتبع حركة الماوس بسلاسة ويتحرك معه في الوقت الفعلي
              </p>
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-green-900">
                <Sparkles className="w-5 h-5" />
                انتقالات سلسة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-green-800">
                انتقالات محسنة وسلسة مع تأثيرات بصرية جذابة
              </p>
            </CardContent>
          </Card>

          <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-purple-900">
                <Zap className="w-5 h-5" />
                أداء محسن
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-purple-800">
                استخدام requestAnimationFrame لأداء سلس وسريع
              </p>
            </CardContent>
          </Card>

          <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-orange-900">
                <Eye className="w-5 h-5" />
                حماية الحدود
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-orange-800">
                يبقى الحوار دائماً داخل حدود الشاشة المرئية
              </p>
            </CardContent>
          </Card>
        </div>

        {/* منطقة التجربة */}
        <Card className="border-gray-200 shadow-lg">
          <CardHeader>
            <CardTitle className="text-center text-2xl text-gray-900">
              منطقة التجربة التفاعلية
            </CardTitle>
            <p className="text-center text-gray-600">
              اختبر الأنواع المختلفة من الشاشات المنبثقة وشاهد كيف تتفاعل مع حركة الماوس
            </p>
          </CardHeader>
          <CardContent>
            <DialogDemo />
          </CardContent>
        </Card>

        {/* أمثلة متنوعة */}
        <Card className="border-gray-200 shadow-lg">
          <CardHeader>
            <CardTitle className="text-center text-2xl text-gray-900">
              أمثلة متنوعة للاستخدام
            </CardTitle>
            <p className="text-center text-gray-600">
              تجربة أنواع مختلفة من المحتوى والتفاعلات مع الشاشات المنبثقة
            </p>
          </CardHeader>
          <CardContent>
            <DialogExamples />
          </CardContent>
        </Card>

        {/* معلومات تقنية */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg text-gray-900">الخصائص المتاحة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <code className="text-sm font-mono">followMouse</code>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">boolean</span>
                </div>
                <p className="text-xs text-gray-600 pr-4">تفعيل/إلغاء تتبع حركة الماوس</p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <code className="text-sm font-mono">centerOnOpen</code>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">boolean</span>
                </div>
                <p className="text-xs text-gray-600 pr-4">توسيط الحوار عند الفتح</p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <code className="text-sm font-mono">smoothTransition</code>
                  <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">boolean</span>
                </div>
                <p className="text-xs text-gray-600 pr-4">تفعيل الانتقالات السلسة</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg text-gray-900">التحسينات التقنية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium text-gray-900">requestAnimationFrame</h4>
                  <p className="text-sm text-gray-600">لضمان انتقالات سلسة وأداء محسن</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium text-gray-900">Boundary Detection</h4>
                  <p className="text-sm text-gray-600">حماية من الخروج عن حدود الشاشة</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium text-gray-900">Event Optimization</h4>
                  <p className="text-sm text-gray-600">تحسين معالجة أحداث الماوس</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium text-gray-900">Responsive Design</h4>
                  <p className="text-sm text-gray-600">يتكيف مع جميع أحجام الشاشات</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* تعليمات الاستخدام */}
        <Card className="bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200">
          <CardHeader>
            <CardTitle className="text-xl text-indigo-900">كيفية الاستخدام</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-indigo-900">للمطورين:</h4>
                <div className="bg-white p-4 rounded-lg border border-indigo-100">
                  <pre className="text-sm text-gray-800 overflow-x-auto">
{`<Dialog 
  open={isOpen}
  onOpenChange={setIsOpen}
  followMouse={true}
  centerOnOpen={true}
  smoothTransition={true}
>
  <DialogContent>
    {/* المحتوى */}
  </DialogContent>
</Dialog>`}
                  </pre>
                </div>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-semibold text-indigo-900">للمستخدمين:</h4>
                <ol className="space-y-2 text-sm text-indigo-800">
                  <li className="flex items-start gap-2">
                    <span className="bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">1</span>
                    <span>اضغط على أي زر لفتح الحوار</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">2</span>
                    <span>حرك الماوس لترى الحوار يتبعه</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">3</span>
                    <span>جرب الأنواع المختلفة من الحوارات</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">4</span>
                    <span>لاحظ كيف يبقى الحوار داخل الشاشة</span>
                  </li>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
