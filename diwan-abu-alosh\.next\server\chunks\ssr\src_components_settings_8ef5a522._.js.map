{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/settings/general-settings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Switch } from '@/components/ui/switch'\n// import { Button } from '@/components/ui/button'\n// import { Badge } from '@/components/ui/badge'\nimport {\n  Building2,\n  Globe,\n  FileText,\n  Calendar\n} from 'lucide-react'\n\ninterface GeneralSettingsProps {\n  settings: any\n  onChange: (settings: any) => void\n  canEdit: boolean\n}\n\ninterface GeneralSettingsData {\n  // معلومات الديوان الأساسية\n  diwanName: string\n  diwanDescription: string\n  diwanAddress: string\n  diwanPhone: string\n  diwanEmail: string\n  diwanWebsite: string\n  \n  // الإعدادات الإقليمية\n  defaultCurrency: string\n  currencySymbol: string\n  timezone: string\n  dateFormat: string\n  timeFormat: string\n  language: string\n  \n  // إعدادات النظام\n  itemsPerPage: number\n  autoSave: boolean\n  enableAuditLog: boolean\n  sessionTimeout: number\n  \n  // إعدادات العرض\n  showWelcomeMessage: boolean\n  welcomeMessage: string\n  enableDashboardStats: boolean\n  enableQuickActions: boolean\n}\n\nconst defaultSettings: GeneralSettingsData = {\n  diwanName: 'ديوان آل أبو علوش',\n  diwanDescription: 'نظام إدارة ديوان آل أبو علوش',\n  diwanAddress: '',\n  diwanPhone: '',\n  diwanEmail: '',\n  diwanWebsite: '',\n  defaultCurrency: 'JOD',\n  currencySymbol: 'د.أ',\n  timezone: 'Asia/Amman',\n  dateFormat: 'DD/MM/YYYY',\n  timeFormat: '24h',\n  language: 'ar',\n  itemsPerPage: 10,\n  autoSave: true,\n  enableAuditLog: true,\n  sessionTimeout: 30,\n  showWelcomeMessage: true,\n  welcomeMessage: 'مرحباً بك في ديوان آل أبو علوش',\n  enableDashboardStats: true,\n  enableQuickActions: true\n}\n\nconst currencies = [\n  { value: 'JOD', label: 'دينار أردني (JOD)', symbol: 'د.أ' },\n  { value: 'USD', label: 'دولار أمريكي (USD)', symbol: '$' },\n  { value: 'EUR', label: 'يورو (EUR)', symbol: '€' },\n  { value: 'SAR', label: 'ريال سعودي (SAR)', symbol: 'ر.س' },\n  { value: 'AED', label: 'درهم إماراتي (AED)', symbol: 'د.إ' },\n  { value: 'KWD', label: 'دينار كويتي (KWD)', symbol: 'د.ك' },\n  { value: 'QAR', label: 'ريال قطري (QAR)', symbol: 'ر.ق' },\n  { value: 'BHD', label: 'دينار بحريني (BHD)', symbol: 'د.ب' }\n]\n\nconst timezones = [\n  { value: 'Asia/Amman', label: 'عمان (UTC+3)' },\n  { value: 'Asia/Riyadh', label: 'الرياض (UTC+3)' },\n  { value: 'Asia/Dubai', label: 'دبي (UTC+4)' },\n  { value: 'Asia/Kuwait', label: 'الكويت (UTC+3)' },\n  { value: 'Asia/Qatar', label: 'الدوحة (UTC+3)' },\n  { value: 'Asia/Bahrain', label: 'المنامة (UTC+3)' }\n]\n\nconst dateFormats = [\n  { value: 'DD/MM/YYYY', label: 'يوم/شهر/سنة (31/12/2024)' },\n  { value: 'MM/DD/YYYY', label: 'شهر/يوم/سنة (12/31/2024)' },\n  { value: 'YYYY-MM-DD', label: 'سنة-شهر-يوم (2024-12-31)' },\n  { value: 'DD-MM-YYYY', label: 'يوم-شهر-سنة (31-12-2024)' }\n]\n\nexport default function GeneralSettings({ settings, onChange, canEdit }: GeneralSettingsProps) {\n  const [localSettings, setLocalSettings] = useState<GeneralSettingsData>(defaultSettings)\n\n  useEffect(() => {\n    if (settings) {\n      setLocalSettings({ ...defaultSettings, ...settings })\n    }\n  }, [settings])\n\n  const handleChange = (key: keyof GeneralSettingsData, value: any) => {\n    const newSettings = { ...localSettings, [key]: value }\n    setLocalSettings(newSettings)\n    onChange(newSettings)\n  }\n\n  const handleCurrencyChange = (currencyCode: string) => {\n    const currency = currencies.find(c => c.value === currencyCode)\n    if (currency) {\n      handleChange('defaultCurrency', currencyCode)\n      handleChange('currencySymbol', currency.symbol)\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* معلومات الديوان الأساسية */}\n      <div className=\"diwan-card\">\n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg\">\n            <Building2 className=\"w-5 h-5 text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">معلومات الديوان الأساسية</h3>\n        </div>\n        <div className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"diwanName\">اسم الديوان</Label>\n              <Input\n                id=\"diwanName\"\n                value={localSettings.diwanName}\n                onChange={(e) => handleChange('diwanName', e.target.value)}\n                disabled={!canEdit}\n                placeholder=\"اسم الديوان\"\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"diwanEmail\">البريد الإلكتروني</Label>\n              <Input\n                id=\"diwanEmail\"\n                type=\"email\"\n                value={localSettings.diwanEmail}\n                onChange={(e) => handleChange('diwanEmail', e.target.value)}\n                disabled={!canEdit}\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"diwanDescription\">وصف الديوان</Label>\n            <Textarea\n              id=\"diwanDescription\"\n              value={localSettings.diwanDescription}\n              onChange={(e) => handleChange('diwanDescription', e.target.value)}\n              disabled={!canEdit}\n              placeholder=\"وصف مختصر عن الديوان\"\n              rows={3}\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"diwanPhone\">رقم الهاتف</Label>\n              <Input\n                id=\"diwanPhone\"\n                value={localSettings.diwanPhone}\n                onChange={(e) => handleChange('diwanPhone', e.target.value)}\n                disabled={!canEdit}\n                placeholder=\"+962 6 1234567\"\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"diwanWebsite\">الموقع الإلكتروني</Label>\n              <Input\n                id=\"diwanWebsite\"\n                value={localSettings.diwanWebsite}\n                onChange={(e) => handleChange('diwanWebsite', e.target.value)}\n                disabled={!canEdit}\n                placeholder=\"https://example.com\"\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"diwanAddress\">العنوان</Label>\n            <Textarea\n              id=\"diwanAddress\"\n              value={localSettings.diwanAddress}\n              onChange={(e) => handleChange('diwanAddress', e.target.value)}\n              disabled={!canEdit}\n              placeholder=\"العنوان الكامل للديوان\"\n              rows={2}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* الإعدادات الإقليمية */}\n      <div className=\"diwan-card\">\n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg\">\n            <Globe className=\"w-5 h-5 text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">الإعدادات الإقليمية</h3>\n        </div>\n        <div className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>العملة الافتراضية</Label>\n              <Select\n                value={localSettings.defaultCurrency}\n                onValueChange={handleCurrencyChange}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"اختر العملة\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {currencies.map((currency) => (\n                    <SelectItem key={currency.value} value={currency.value}>\n                      <div className=\"flex items-center gap-2\">\n                        <span>{currency.symbol}</span>\n                        <span>{currency.label}</span>\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>المنطقة الزمنية</Label>\n              <Select\n                value={localSettings.timezone}\n                onValueChange={(value) => handleChange('timezone', value)}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"اختر المنطقة الزمنية\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {timezones.map((tz) => (\n                    <SelectItem key={tz.value} value={tz.value}>\n                      {tz.label}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>تنسيق التاريخ</Label>\n              <Select\n                value={localSettings.dateFormat}\n                onValueChange={(value) => handleChange('dateFormat', value)}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"اختر تنسيق التاريخ\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {dateFormats.map((format) => (\n                    <SelectItem key={format.value} value={format.value}>\n                      {format.label}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>تنسيق الوقت</Label>\n              <Select\n                value={localSettings.timeFormat}\n                onValueChange={(value) => handleChange('timeFormat', value)}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"اختر تنسيق الوقت\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"12h\">12 ساعة (1:30 PM)</SelectItem>\n                  <SelectItem value=\"24h\">24 ساعة (13:30)</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* إعدادات النظام */}\n      <div className=\"diwan-card\">\n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg\">\n            <FileText className=\"w-5 h-5 text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">إعدادات النظام</h3>\n        </div>\n        <div className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"itemsPerPage\">عدد العناصر في الصفحة</Label>\n              <Select\n                value={localSettings.itemsPerPage.toString()}\n                onValueChange={(value) => handleChange('itemsPerPage', parseInt(value))}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"5\">5 عناصر</SelectItem>\n                  <SelectItem value=\"10\">10 عناصر</SelectItem>\n                  <SelectItem value=\"20\">20 عنصر</SelectItem>\n                  <SelectItem value=\"50\">50 عنصر</SelectItem>\n                  <SelectItem value=\"100\">100 عنصر</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"sessionTimeout\">مدة انتهاء الجلسة (دقيقة)</Label>\n              <Select\n                value={localSettings.sessionTimeout.toString()}\n                onValueChange={(value) => handleChange('sessionTimeout', parseInt(value))}\n                disabled={!canEdit}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"15\">15 دقيقة</SelectItem>\n                  <SelectItem value=\"30\">30 دقيقة</SelectItem>\n                  <SelectItem value=\"60\">ساعة واحدة</SelectItem>\n                  <SelectItem value=\"120\">ساعتان</SelectItem>\n                  <SelectItem value=\"480\">8 ساعات</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>الحفظ التلقائي</Label>\n                <p className=\"text-sm text-gray-600\">حفظ التغييرات تلقائياً أثناء الكتابة</p>\n              </div>\n              <Switch\n                checked={localSettings.autoSave}\n                onCheckedChange={(checked) => handleChange('autoSave', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"space-y-1\">\n                <Label>سجل العمليات</Label>\n                <p className=\"text-sm text-gray-600\">تسجيل جميع العمليات والتغييرات</p>\n              </div>\n              <Switch\n                checked={localSettings.enableAuditLog}\n                onCheckedChange={(checked) => handleChange('enableAuditLog', checked)}\n                disabled={!canEdit}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* إعدادات العرض */}\n      <div className=\"diwan-card\">\n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg\">\n            <Calendar className=\"w-5 h-5 text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">إعدادات العرض</h3>\n        </div>\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <Label>رسالة الترحيب</Label>\n              <p className=\"text-sm text-gray-600\">عرض رسالة ترحيب في لوحة التحكم</p>\n            </div>\n            <Switch\n              checked={localSettings.showWelcomeMessage}\n              onCheckedChange={(checked) => handleChange('showWelcomeMessage', checked)}\n              disabled={!canEdit}\n            />\n          </div>\n\n          {localSettings.showWelcomeMessage && (\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"welcomeMessage\">نص رسالة الترحيب</Label>\n              <Input\n                id=\"welcomeMessage\"\n                value={localSettings.welcomeMessage}\n                onChange={(e) => handleChange('welcomeMessage', e.target.value)}\n                disabled={!canEdit}\n                placeholder=\"مرحباً بك في ديوان آل أبو علوش\"\n              />\n            </div>\n          )}\n\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <Label>إحصائيات لوحة التحكم</Label>\n              <p className=\"text-sm text-gray-600\">عرض الإحصائيات في لوحة التحكم</p>\n            </div>\n            <Switch\n              checked={localSettings.enableDashboardStats}\n              onCheckedChange={(checked) => handleChange('enableDashboardStats', checked)}\n              disabled={!canEdit}\n            />\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <Label>الإجراءات السريعة</Label>\n              <p className=\"text-sm text-gray-600\">عرض أزرار الإجراءات السريعة</p>\n            </div>\n            <Switch\n              checked={localSettings.enableQuickActions}\n              onCheckedChange={(checked) => handleChange('enableQuickActions', checked)}\n              disabled={!canEdit}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA,kFAAkF;AAClF;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD,gDAAgD;AAChD;AAAA;AAAA;AAAA;AAXA;;;;;;;;;AAsDA,MAAM,kBAAuC;IAC3C,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,iBAAiB;IACjB,gBAAgB;IAChB,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,cAAc;IACd,UAAU;IACV,gBAAgB;IAChB,gBAAgB;IAChB,oBAAoB;IACpB,gBAAgB;IAChB,sBAAsB;IACtB,oBAAoB;AACtB;AAEA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;QAAqB,QAAQ;IAAM;IAC1D;QAAE,OAAO;QAAO,OAAO;QAAsB,QAAQ;IAAI;IACzD;QAAE,OAAO;QAAO,OAAO;QAAc,QAAQ;IAAI;IACjD;QAAE,OAAO;QAAO,OAAO;QAAoB,QAAQ;IAAM;IACzD;QAAE,OAAO;QAAO,OAAO;QAAsB,QAAQ;IAAM;IAC3D;QAAE,OAAO;QAAO,OAAO;QAAqB,QAAQ;IAAM;IAC1D;QAAE,OAAO;QAAO,OAAO;QAAmB,QAAQ;IAAM;IACxD;QAAE,OAAO;QAAO,OAAO;QAAsB,QAAQ;IAAM;CAC5D;AAED,MAAM,YAAY;IAChB;QAAE,OAAO;QAAc,OAAO;IAAe;IAC7C;QAAE,OAAO;QAAe,OAAO;IAAiB;IAChD;QAAE,OAAO;QAAc,OAAO;IAAc;IAC5C;QAAE,OAAO;QAAe,OAAO;IAAiB;IAChD;QAAE,OAAO;QAAc,OAAO;IAAiB;IAC/C;QAAE,OAAO;QAAgB,OAAO;IAAkB;CACnD;AAED,MAAM,cAAc;IAClB;QAAE,OAAO;QAAc,OAAO;IAA2B;IACzD;QAAE,OAAO;QAAc,OAAO;IAA2B;IACzD;QAAE,OAAO;QAAc,OAAO;IAA2B;IACzD;QAAE,OAAO;QAAc,OAAO;IAA2B;CAC1D;AAEc,SAAS,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAwB;IAC3F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAExE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,iBAAiB;gBAAE,GAAG,eAAe;gBAAE,GAAG,QAAQ;YAAC;QACrD;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe,CAAC,KAAgC;QACpD,MAAM,cAAc;YAAE,GAAG,aAAa;YAAE,CAAC,IAAI,EAAE;QAAM;QACrD,iBAAiB;QACjB,SAAS;IACX;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAClD,IAAI,UAAU;YACZ,aAAa,mBAAmB;YAChC,aAAa,kBAAkB,SAAS,MAAM;QAChD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,cAAc,SAAS;gDAC9B,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;gDACzD,UAAU,CAAC;gDACX,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,cAAc,UAAU;gDAC/B,UAAU,CAAC,IAAM,aAAa,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC1D,UAAU,CAAC;gDACX,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAmB;;;;;;kDAClC,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,cAAc,gBAAgB;wCACrC,UAAU,CAAC,IAAM,aAAa,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCAChE,UAAU,CAAC;wCACX,aAAY;wCACZ,MAAM;;;;;;;;;;;;0CAIV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,cAAc,UAAU;gDAC/B,UAAU,CAAC,IAAM,aAAa,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC1D,UAAU,CAAC;gDACX,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,cAAc,YAAY;gDACjC,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC5D,UAAU,CAAC;gDACX,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAe;;;;;;kDAC9B,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,cAAc,YAAY;wCACjC,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC5D,UAAU,CAAC;wCACX,aAAY;wCACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,eAAe;gDACpC,eAAe;gDACf,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;kEACX,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,aAAU;gEAAsB,OAAO,SAAS,KAAK;0EACpD,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAM,SAAS,MAAM;;;;;;sFACtB,8OAAC;sFAAM,SAAS,KAAK;;;;;;;;;;;;+DAHR,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;kDAWvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,QAAQ;gDAC7B,eAAe,CAAC,QAAU,aAAa,YAAY;gDACnD,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;kEACX,UAAU,GAAG,CAAC,CAAC,mBACd,8OAAC,kIAAA,CAAA,aAAU;gEAAgB,OAAO,GAAG,KAAK;0EACvC,GAAG,KAAK;+DADM,GAAG,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,UAAU;gDAC/B,eAAe,CAAC,QAAU,aAAa,cAAc;gDACrD,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;kEACX,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,kIAAA,CAAA,aAAU;gEAAoB,OAAO,OAAO,KAAK;0EAC/C,OAAO,KAAK;+DADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;kDAQrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,UAAU;gDAC/B,eAAe,CAAC,QAAU,aAAa,cAAc;gDACrD,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,YAAY,CAAC,QAAQ;gDAC1C,eAAe,CAAC,QAAU,aAAa,gBAAgB,SAAS;gDAChE,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAI;;;;;;0EACtB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;kDAK9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,cAAc,cAAc,CAAC,QAAQ;gDAC5C,eAAe,CAAC,QAAU,aAAa,kBAAkB,SAAS;gDAClE,UAAU,CAAC;;kEAEX,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,QAAQ;gDAC/B,iBAAiB,CAAC,UAAY,aAAa,YAAY;gDACvD,UAAU,CAAC;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,cAAc;gDACrC,iBAAiB,CAAC,UAAY,aAAa,kBAAkB;gDAC7D,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,cAAc,kBAAkB;wCACzC,iBAAiB,CAAC,UAAY,aAAa,sBAAsB;wCACjE,UAAU,CAAC;;;;;;;;;;;;4BAId,cAAc,kBAAkB,kBAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAiB;;;;;;kDAChC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO,cAAc,cAAc;wCACnC,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCAC9D,UAAU,CAAC;wCACX,aAAY;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,cAAc,oBAAoB;wCAC3C,iBAAiB,CAAC,UAAY,aAAa,wBAAwB;wCACnE,UAAU,CAAC;;;;;;;;;;;;0CAIf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,cAAc,kBAAkB;wCACzC,iBAAiB,CAAC,UAAY,aAAa,sBAAsB;wCACjE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB", "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/settings/detailed-permissions.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Label } from '@/components/ui/label'\nimport { Switch } from '@/components/ui/switch'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { Users, Camera, Settings, User } from 'lucide-react'\n\ninterface User {\n  id: string\n  name: string\n  email: string\n  role: string\n}\n\ninterface Member {\n  id: string\n  name: string\n}\n\ninterface UserPermissions {\n  id?: string\n  userId: string\n  // صلاحيات عامة\n  canViewAllMembers: boolean\n  canEditMembers: boolean\n  canDeleteMembers: boolean\n  canViewAllIncomes: boolean\n  canEditIncomes: boolean\n  canDeleteIncomes: boolean\n  canViewAllExpenses: boolean\n  canEditExpenses: boolean\n  canDeleteExpenses: boolean\n  canViewGallery: boolean\n  canUploadToGallery: boolean\n  canDeleteFromGallery: boolean\n  canViewReports: boolean\n  canExportData: boolean\n  canManageUsers: boolean\n  canManageSettings: boolean\n  // صلاحيات محددة\n  specificMemberId?: string\n  canViewMemberAccount: boolean\n  canViewMemberDetails: boolean\n  galleryReadOnly: boolean\n  canCreateGalleryFolders: boolean\n  user?: User\n  specificMember?: Member\n}\n\ninterface DetailedPermissionsProps {\n  users: User[]\n  onClose: () => void\n}\n\nexport default function DetailedPermissions({ users, onClose }: DetailedPermissionsProps) {\n  const [selectedUserId, setSelectedUserId] = useState<string>('')\n  const [members, setMembers] = useState<Member[]>([])\n  const [permissions, setPermissions] = useState<UserPermissions>({\n    userId: '',\n    canViewAllMembers: false,\n    canEditMembers: false,\n    canDeleteMembers: false,\n    canViewAllIncomes: false,\n    canEditIncomes: false,\n    canDeleteIncomes: false,\n    canViewAllExpenses: false,\n    canEditExpenses: false,\n    canDeleteExpenses: false,\n    canViewGallery: false,\n    canUploadToGallery: false,\n    canDeleteFromGallery: false,\n    canViewReports: false,\n    canExportData: false,\n    canManageUsers: false,\n    canManageSettings: false,\n    canViewMemberAccount: false,\n    canViewMemberDetails: false,\n    galleryReadOnly: true,\n    canCreateGalleryFolders: false\n  })\n  const [loading, setLoading] = useState(false)\n\n  // جلب قائمة الأعضاء\n  useEffect(() => {\n    const fetchMembers = async () => {\n      try {\n        const response = await fetch('/api/members?limit=1000')\n        if (response.ok) {\n          const data = await response.json()\n          setMembers(data.members || data)\n        }\n      } catch (error) {\n        console.error('خطأ في جلب الأعضاء:', error)\n      }\n    }\n    fetchMembers()\n  }, [])\n\n  // جلب صلاحيات المستخدم المحدد\n  useEffect(() => {\n    if (selectedUserId) {\n      fetchUserPermissions(selectedUserId)\n    }\n  }, [selectedUserId])\n\n  const fetchUserPermissions = async (userId: string) => {\n    try {\n      setLoading(true)\n      const response = await fetch(`/api/admin/user-permissions?userId=${userId}`)\n      if (response.ok) {\n        const data = await response.json()\n        if (data) {\n          setPermissions(data)\n        } else {\n          // إنشاء صلاحيات افتراضية\n          setPermissions({\n            userId,\n            canViewAllMembers: false,\n            canEditMembers: false,\n            canDeleteMembers: false,\n            canViewAllIncomes: false,\n            canEditIncomes: false,\n            canDeleteIncomes: false,\n            canViewAllExpenses: false,\n            canEditExpenses: false,\n            canDeleteExpenses: false,\n            canViewGallery: false,\n            canUploadToGallery: false,\n            canDeleteFromGallery: false,\n            canViewReports: false,\n            canExportData: false,\n            canManageUsers: false,\n            canManageSettings: false,\n            canViewMemberAccount: false,\n            canViewMemberDetails: false,\n            galleryReadOnly: true,\n            canCreateGalleryFolders: false\n          })\n        }\n      }\n    } catch (error) {\n      console.error('خطأ في جلب صلاحيات المستخدم:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handlePermissionChange = (key: keyof UserPermissions, value: boolean | string) => {\n    setPermissions(prev => ({\n      ...prev,\n      [key]: value\n    }))\n  }\n\n  const handleSave = async () => {\n    if (!selectedUserId) {\n      alert('يرجى اختيار مستخدم')\n      return\n    }\n\n    try {\n      setLoading(true)\n      const response = await fetch('/api/admin/user-permissions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          userId: selectedUserId,\n          permissions: {\n            canViewAllMembers: permissions.canViewAllMembers,\n            canEditMembers: permissions.canEditMembers,\n            canDeleteMembers: permissions.canDeleteMembers,\n            canViewAllIncomes: permissions.canViewAllIncomes,\n            canEditIncomes: permissions.canEditIncomes,\n            canDeleteIncomes: permissions.canDeleteIncomes,\n            canViewAllExpenses: permissions.canViewAllExpenses,\n            canEditExpenses: permissions.canEditExpenses,\n            canDeleteExpenses: permissions.canDeleteExpenses,\n            canViewGallery: permissions.canViewGallery,\n            canUploadToGallery: permissions.canUploadToGallery,\n            canDeleteFromGallery: permissions.canDeleteFromGallery,\n            canViewReports: permissions.canViewReports,\n            canExportData: permissions.canExportData,\n            canManageUsers: permissions.canManageUsers,\n            canManageSettings: permissions.canManageSettings,\n            specificMemberId: permissions.specificMemberId || null,\n            canViewMemberAccount: permissions.canViewMemberAccount,\n            canViewMemberDetails: permissions.canViewMemberDetails,\n            galleryReadOnly: permissions.galleryReadOnly,\n            canCreateGalleryFolders: permissions.canCreateGalleryFolders\n          }\n        }),\n      })\n\n      if (response.ok) {\n        alert('تم حفظ الصلاحيات بنجاح')\n      } else {\n        const error = await response.json()\n        alert(error.message || 'فشل في حفظ الصلاحيات')\n      }\n    } catch (error) {\n      console.error('خطأ في حفظ الصلاحيات:', error)\n      alert('حدث خطأ أثناء حفظ الصلاحيات')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const selectedUser = users.find(u => u.id === selectedUserId)\n\n  return (\n    <Card className=\"border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50 shadow-xl\">\n      <CardHeader className=\"bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-t-lg\">\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-2 bg-white bg-opacity-20 rounded-lg\">\n              <Settings className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h3 className=\"text-xl font-bold\">إدارة الصلاحيات المفصلة</h3>\n              <p className=\"text-purple-100 text-sm mt-1\">تحديد صلاحيات دقيقة لكل مستخدم</p>\n            </div>\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onClose}\n            className=\"bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-white hover:bg-opacity-30\"\n          >\n            إغلاق\n          </Button>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6 p-6\">\n        {/* اختيار المستخدم */}\n        <div className=\"space-y-2\">\n          <Label>اختيار المستخدم</Label>\n          <Select value={selectedUserId} onValueChange={setSelectedUserId}>\n            <SelectTrigger>\n              <SelectValue placeholder=\"اختر مستخدماً لتعديل صلاحياته\" />\n            </SelectTrigger>\n            <SelectContent>\n              {users.filter(u => u.role !== 'ADMIN').map((user) => (\n                <SelectItem key={user.id} value={user.id}>\n                  <div className=\"flex items-center gap-2\">\n                    <User className=\"w-4 h-4\" />\n                    <span>{user.name}</span>\n                    <Badge variant=\"outline\" className=\"text-xs\">\n                      {user.role === 'DATA_ENTRY' ? 'مدخل بيانات' : \n                       user.role === 'VIEWER' ? 'مطلع' :\n                       user.role === 'MEMBER_VIEWER' ? 'مطلع على عضو' :\n                       user.role === 'GALLERY_VIEWER' ? 'مطلع على المعرض' : user.role}\n                    </Badge>\n                  </div>\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        </div>\n\n        {selectedUser && (\n          <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n            <h4 className=\"font-semibold text-gray-900 mb-2\">المستخدم المحدد:</h4>\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center\">\n                <User className=\"w-5 h-5 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"font-medium\">{selectedUser.name}</p>\n                <p className=\"text-sm text-gray-600\">{selectedUser.email}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {selectedUserId && !loading && (\n          <div className=\"space-y-6\">\n            {/* صلاحيات الأعضاء */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2 text-lg\">\n                  <Users className=\"w-5 h-5 text-blue-600\" />\n                  صلاحيات الأعضاء\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <Label>عرض جميع الأعضاء</Label>\n                    <Switch\n                      checked={permissions.canViewAllMembers}\n                      onCheckedChange={(checked) => handlePermissionChange('canViewAllMembers', checked)}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Label>تعديل الأعضاء</Label>\n                    <Switch\n                      checked={permissions.canEditMembers}\n                      onCheckedChange={(checked) => handlePermissionChange('canEditMembers', checked)}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Label>حذف الأعضاء</Label>\n                    <Switch\n                      checked={permissions.canDeleteMembers}\n                      onCheckedChange={(checked) => handlePermissionChange('canDeleteMembers', checked)}\n                    />\n                  </div>\n                </div>\n\n                {/* صلاحية عضو محدد */}\n                <div className=\"border-t pt-4\">\n                  <h5 className=\"font-medium mb-3\">صلاحيات عضو محدد</h5>\n                  <div className=\"space-y-3\">\n                    <div className=\"space-y-2\">\n                      <Label>اختيار عضو محدد (اختياري)</Label>\n                      <Select \n                        value={permissions.specificMemberId || ''} \n                        onValueChange={(value) => handlePermissionChange('specificMemberId', value)}\n                      >\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"اختر عضواً محدداً\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          <SelectItem value=\"\">لا يوجد عضو محدد</SelectItem>\n                          {members.map((member) => (\n                            <SelectItem key={member.id} value={member.id}>\n                              {member.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                    </div>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <Label>عرض كشف حساب العضو</Label>\n                        <Switch\n                          checked={permissions.canViewMemberAccount}\n                          onCheckedChange={(checked) => handlePermissionChange('canViewMemberAccount', checked)}\n                        />\n                      </div>\n                      <div className=\"flex items-center justify-between\">\n                        <Label>عرض تفاصيل العضو</Label>\n                        <Switch\n                          checked={permissions.canViewMemberDetails}\n                          onCheckedChange={(checked) => handlePermissionChange('canViewMemberDetails', checked)}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* صلاحيات المعرض */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2 text-lg\">\n                  <Camera className=\"w-5 h-5 text-green-600\" />\n                  صلاحيات المعرض\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <Label>عرض المعرض</Label>\n                    <Switch\n                      checked={permissions.canViewGallery}\n                      onCheckedChange={(checked) => handlePermissionChange('canViewGallery', checked)}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Label>المعرض للقراءة فقط</Label>\n                    <Switch\n                      checked={permissions.galleryReadOnly}\n                      onCheckedChange={(checked) => handlePermissionChange('galleryReadOnly', checked)}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Label>رفع صور للمعرض</Label>\n                    <Switch\n                      checked={permissions.canUploadToGallery}\n                      onCheckedChange={(checked) => handlePermissionChange('canUploadToGallery', checked)}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Label>إنشاء مجلدات</Label>\n                    <Switch\n                      checked={permissions.canCreateGalleryFolders}\n                      onCheckedChange={(checked) => handlePermissionChange('canCreateGalleryFolders', checked)}\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* أزرار الحفظ */}\n            <div className=\"flex justify-end gap-3 pt-4\">\n              <Button variant=\"outline\" onClick={onClose}>\n                إلغاء\n              </Button>\n              <Button \n                onClick={handleSave} \n                disabled={loading}\n                className=\"bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white\"\n              >\n                {loading ? 'جاري الحفظ...' : 'حفظ الصلاحيات'}\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {loading && (\n          <div className=\"flex justify-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600\"></div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AA0De,SAAS,oBAAoB,EAAE,KAAK,EAAE,OAAO,EAA4B;IACtF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAC9D,QAAQ;QACR,mBAAmB;QACnB,gBAAgB;QAChB,kBAAkB;QAClB,mBAAmB;QACnB,gBAAgB;QAChB,kBAAkB;QAClB,oBAAoB;QACpB,iBAAiB;QACjB,mBAAmB;QACnB,gBAAgB;QAChB,oBAAoB;QACpB,sBAAsB;QACtB,gBAAgB;QAChB,eAAe;QACf,gBAAgB;QAChB,mBAAmB;QACnB,sBAAsB;QACtB,sBAAsB;QACtB,iBAAiB;QACjB,yBAAyB;IAC3B;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,WAAW,KAAK,OAAO,IAAI;gBAC7B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;YACvC;QACF;QACA;IACF,GAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,qBAAqB;QACvB;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,mCAAmC,EAAE,QAAQ;YAC3E,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,MAAM;oBACR,eAAe;gBACjB,OAAO;oBACL,yBAAyB;oBACzB,eAAe;wBACb;wBACA,mBAAmB;wBACnB,gBAAgB;wBAChB,kBAAkB;wBAClB,mBAAmB;wBACnB,gBAAgB;wBAChB,kBAAkB;wBAClB,oBAAoB;wBACpB,iBAAiB;wBACjB,mBAAmB;wBACnB,gBAAgB;wBAChB,oBAAoB;wBACpB,sBAAsB;wBACtB,gBAAgB;wBAChB,eAAe;wBACf,gBAAgB;wBAChB,mBAAmB;wBACnB,sBAAsB;wBACtB,sBAAsB;wBACtB,iBAAiB;wBACjB,yBAAyB;oBAC3B;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB,CAAC,KAA4B;QAC1D,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB,MAAM;YACN;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,aAAa;wBACX,mBAAmB,YAAY,iBAAiB;wBAChD,gBAAgB,YAAY,cAAc;wBAC1C,kBAAkB,YAAY,gBAAgB;wBAC9C,mBAAmB,YAAY,iBAAiB;wBAChD,gBAAgB,YAAY,cAAc;wBAC1C,kBAAkB,YAAY,gBAAgB;wBAC9C,oBAAoB,YAAY,kBAAkB;wBAClD,iBAAiB,YAAY,eAAe;wBAC5C,mBAAmB,YAAY,iBAAiB;wBAChD,gBAAgB,YAAY,cAAc;wBAC1C,oBAAoB,YAAY,kBAAkB;wBAClD,sBAAsB,YAAY,oBAAoB;wBACtD,gBAAgB,YAAY,cAAc;wBAC1C,eAAe,YAAY,aAAa;wBACxC,gBAAgB,YAAY,cAAc;wBAC1C,mBAAmB,YAAY,iBAAiB;wBAChD,kBAAkB,YAAY,gBAAgB,IAAI;wBAClD,sBAAsB,YAAY,oBAAoB;wBACtD,sBAAsB,YAAY,oBAAoB;wBACtD,iBAAiB,YAAY,eAAe;wBAC5C,yBAAyB,YAAY,uBAAuB;oBAC9D;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,OAAO,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE9C,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;sCAGhD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAKL,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAgB,eAAe;;kDAC5C,8OAAC,kIAAA,CAAA,gBAAa;kDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;kDACX,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,GAAG,CAAC,CAAC,qBAC1C,8OAAC,kIAAA,CAAA,aAAU;gDAAe,OAAO,KAAK,EAAE;0DACtC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAM,KAAK,IAAI;;;;;;sEAChB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,KAAK,IAAI,KAAK,eAAe,gBAC7B,KAAK,IAAI,KAAK,WAAW,SACzB,KAAK,IAAI,KAAK,kBAAkB,iBAChC,KAAK,IAAI,KAAK,mBAAmB,oBAAoB,KAAK,IAAI;;;;;;;;;;;;+CARpD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAiB/B,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAe,aAAa,IAAI;;;;;;0DAC7C,8OAAC;gDAAE,WAAU;0DAAyB,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;oBAM/D,kBAAkB,CAAC,yBAClB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA0B;;;;;;;;;;;;kDAI/C,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,YAAY,iBAAiB;gEACtC,iBAAiB,CAAC,UAAY,uBAAuB,qBAAqB;;;;;;;;;;;;kEAG9E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,YAAY,cAAc;gEACnC,iBAAiB,CAAC,UAAY,uBAAuB,kBAAkB;;;;;;;;;;;;kEAG3E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,YAAY,gBAAgB;gEACrC,iBAAiB,CAAC,UAAY,uBAAuB,oBAAoB;;;;;;;;;;;;;;;;;;0DAM/E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,YAAY,gBAAgB,IAAI;wEACvC,eAAe,CAAC,QAAU,uBAAuB,oBAAoB;;0FAErE,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kGACZ,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAG;;;;;;oFACpB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kIAAA,CAAA,aAAU;4FAAiB,OAAO,OAAO,EAAE;sGACzC,OAAO,IAAI;2FADG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;0EAOlC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iIAAA,CAAA,QAAK;0FAAC;;;;;;0FACP,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAS,YAAY,oBAAoB;gFACzC,iBAAiB,CAAC,UAAY,uBAAuB,wBAAwB;;;;;;;;;;;;kFAGjF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iIAAA,CAAA,QAAK;0FAAC;;;;;;0FACP,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAS,YAAY,oBAAoB;gFACzC,iBAAiB,CAAC,UAAY,uBAAuB,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU3F,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;;;;;;kDAIjD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,YAAY,cAAc;4DACnC,iBAAiB,CAAC,UAAY,uBAAuB,kBAAkB;;;;;;;;;;;;8DAG3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,YAAY,eAAe;4DACpC,iBAAiB,CAAC,UAAY,uBAAuB,mBAAmB;;;;;;;;;;;;8DAG5E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,YAAY,kBAAkB;4DACvC,iBAAiB,CAAC,UAAY,uBAAuB,sBAAsB;;;;;;;;;;;;8DAG/E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,YAAY,uBAAuB;4DAC5C,iBAAiB,CAAC,UAAY,uBAAuB,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ1F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;kDAAS;;;;;;kDAG5C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;oBAMpC,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 2148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/settings/advanced-settings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\n// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\n// import { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n} from '@/components/ui/dialog'\nimport {\n  Download,\n  Upload,\n  History,\n  RotateCcw,\n  FileText,\n  AlertTriangle,\n  CheckCircle,\n  // Settings,\n  Database,\n  Clock\n} from 'lucide-react'\nimport { toast } from 'sonner'\n\ninterface AdvancedSettingsProps {\n  onExportSettings: () => void\n  onImportSettings: (file: File) => void\n  onResetSettings: () => void\n  canEdit: boolean\n}\n\nexport default function AdvancedSettings({ \n  onExportSettings, \n  onImportSettings, \n  onResetSettings, \n  canEdit \n}: AdvancedSettingsProps) {\n  const [importFile, setImportFile] = useState<File | null>(null)\n  const [showChangeLog, setShowChangeLog] = useState(false)\n  const [changeLog] = useState([\n    {\n      id: 1,\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      user: 'أحمد محمد',\n      action: 'تحديث إعدادات المظهر',\n      details: 'تغيير اللون الأساسي إلى الأزرق',\n      category: 'appearance'\n    },\n    {\n      id: 2,\n      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),\n      user: 'سارة أحمد',\n      action: 'تحديث إعدادات الإشعارات',\n      details: 'تفعيل إشعارات البريد الإلكتروني',\n      category: 'notifications'\n    },\n    {\n      id: 3,\n      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n      user: 'محمد علي',\n      action: 'تحديث إعدادات الأمان',\n      details: 'تغيير سياسة كلمات المرور',\n      category: 'security'\n    }\n  ])\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file) {\n      setImportFile(file)\n    }\n  }\n\n  const handleImport = () => {\n    if (importFile) {\n      onImportSettings(importFile)\n      setImportFile(null)\n      toast.success('تم استيراد الإعدادات بنجاح')\n    }\n  }\n\n  const handleExport = () => {\n    onExportSettings()\n    toast.success('تم تصدير الإعدادات بنجاح')\n  }\n\n  const handleReset = () => {\n    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ لا يمكن التراجع عن هذا الإجراء.')) {\n      onResetSettings()\n      toast.success('تم إعادة تعيين الإعدادات بنجاح')\n    }\n  }\n\n  const getCategoryBadge = (category: string) => {\n    const categories = {\n      general: { label: 'عام', color: 'bg-blue-100 text-blue-800' },\n      appearance: { label: 'مظهر', color: 'bg-purple-100 text-purple-800' },\n      notifications: { label: 'إشعارات', color: 'bg-green-100 text-green-800' },\n      security: { label: 'أمان', color: 'bg-red-100 text-red-800' },\n      backup: { label: 'نسخ احتياطي', color: 'bg-orange-100 text-orange-800' }\n    }\n    \n    const cat = categories[category as keyof typeof categories] || categories.general\n    return <Badge className={cat.color}>{cat.label}</Badge>\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Import and export settings */}\n      <div className=\"diwan-card\">\n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg\">\n            <FileText className=\"w-5 h-5 text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">استيراد وتصدير الإعدادات</h3>\n        </div>\n        <div className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* Export settings */}\n            <div className=\"space-y-3\">\n              <h4 className=\"font-medium\">تصدير الإعدادات</h4>\n              <p className=\"text-sm text-gray-600\">\n                احفظ نسخة من إعداداتك الحالية كملف JSON\n              </p>\n              <Button\n                onClick={handleExport}\n                disabled={!canEdit}\n                className=\"w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg\"\n              >\n                <Download className=\"w-4 h-4 ml-2\" />\n                تصدير الإعدادات\n              </Button>\n            </div>\n\n            {/* Import settings */}\n            <div className=\"space-y-3\">\n              <h4 className=\"font-medium\">استيراد الإعدادات</h4>\n              <p className=\"text-sm text-gray-600\">\n                استعد إعداداتك من ملف JSON محفوظ مسبقاً\n              </p>\n              <div className=\"space-y-2\">\n                <Input\n                  type=\"file\"\n                  accept=\".json\"\n                  onChange={handleFileUpload}\n                  disabled={!canEdit}\n                />\n                <Button\n                  onClick={handleImport}\n                  disabled={!canEdit || !importFile}\n                  className=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg disabled:opacity-50\"\n                >\n                  <Upload className=\"w-4 h-4 ml-2\" />\n                  استيراد الإعدادات\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"p-4 bg-amber-50 border border-amber-200 rounded-lg\">\n            <div className=\"flex items-start gap-3\">\n              <AlertTriangle className=\"w-5 h-5 text-amber-600 mt-0.5\" />\n              <div>\n                <p className=\"text-amber-800 font-medium\">تحذير</p>\n                <p className=\"text-amber-700 text-sm mt-1\">\n                  استيراد الإعدادات سيستبدل جميع الإعدادات الحالية. \n                  تأكد من تصدير إعداداتك الحالية أولاً كنسخة احتياطية.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Reset settings */}\n      <div className=\"diwan-card\">\n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg\">\n            <RotateCcw className=\"w-5 h-5 text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">إعادة تعيين الإعدادات</h3>\n        </div>\n        <div className=\"space-y-4\">\n          <p className=\"text-gray-600\">\n            إعادة تعيين جميع الإعدادات إلى القيم الافتراضية. \n            هذا الإجراء لا يمكن التراجع عنه.\n          </p>\n          \n          <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <div className=\"flex items-start gap-3\">\n              <AlertTriangle className=\"w-5 h-5 text-red-600 mt-0.5\" />\n              <div>\n                <p className=\"text-red-800 font-medium\">تحذير شديد</p>\n                <p className=\"text-red-700 text-sm mt-1\">\n                  سيتم حذف جميع الإعدادات المخصصة وإعادة تعيينها إلى القيم الافتراضية.\n                  تأكد من تصدير إعداداتك أولاً إذا كنت تريد الاحتفاظ بها.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <Button\n            onClick={handleReset}\n            disabled={!canEdit}\n            variant=\"destructive\"\n            className=\"w-full md:w-auto\"\n          >\n            <RotateCcw className=\"w-4 h-4 ml-2\" />\n            إعادة تعيين جميع الإعدادات\n          </Button>\n        </div>\n      </div>\n\n      {/* Change log */}\n      <div className=\"diwan-card\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg\">\n              <History className=\"w-5 h-5 text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">سجل التغييرات</h3>\n          </div>\n          <Dialog open={showChangeLog} onOpenChange={setShowChangeLog}>\n              <DialogTrigger asChild>\n                <Button variant=\"outline\" size=\"sm\">\n                  عرض السجل الكامل\n                </Button>\n              </DialogTrigger>\n              <DialogContent className=\"max-w-[50vw] max-h-[80vh] overflow-y-auto\">\n                <DialogHeader>\n                  <DialogTitle>سجل التغييرات الكامل</DialogTitle>\n                </DialogHeader>\n                <div className=\"space-y-4\">\n                  {changeLog.map((entry) => (\n                    <div key={entry.id} className=\"border rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <div className=\"flex items-center gap-2\">\n                          {getCategoryBadge(entry.category)}\n                          <span className=\"font-medium\">{entry.action}</span>\n                        </div>\n                        <div className=\"flex items-center gap-2 text-sm text-gray-500\">\n                          <Clock className=\"w-4 h-4\" />\n                          {entry.timestamp.toLocaleString('ar-SA')}\n                        </div>\n                      </div>\n                      <p className=\"text-gray-600 mb-2\">{entry.details}</p>\n                      <p className=\"text-sm text-gray-500\">بواسطة: {entry.user}</p>\n                    </div>\n                  ))}\n                </div>\n              </DialogContent>\n          </Dialog>\n        </div>\n        <div>\n          <div className=\"space-y-3\">\n            {changeLog.slice(0, 3).map((entry) => (\n              <div key={entry.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                <div className=\"flex items-center gap-3\">\n                  {getCategoryBadge(entry.category)}\n                  <div>\n                    <p className=\"font-medium text-sm\">{entry.action}</p>\n                    <p className=\"text-xs text-gray-600\">{entry.details}</p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-xs text-gray-500\">{entry.user}</p>\n                  <p className=\"text-xs text-gray-400\">\n                    {entry.timestamp.toLocaleDateString('ar-SA')}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* System information */}\n      <div className=\"diwan-card\">\n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg\">\n            <Database className=\"w-5 h-5 text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">معلومات النظام</h3>\n        </div>\n        <div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>إصدار النظام</Label>\n              <p className=\"text-sm text-gray-600\">1.0.0</p>\n            </div>\n            <div className=\"space-y-2\">\n              <Label>آخر تحديث</Label>\n              <p className=\"text-sm text-gray-600\">2024-12-21</p>\n            </div>\n            <div className=\"space-y-2\">\n              <Label>قاعدة البيانات</Label>\n              <p className=\"text-sm text-gray-600\">SQLite</p>\n            </div>\n            <div className=\"space-y-2\">\n              <Label>حالة النظام</Label>\n              <div className=\"flex items-center gap-2\">\n                <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                <span className=\"text-sm text-green-600\">يعمل بشكل طبيعي</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA,kFAAkF;AAClF;AACA;AACA;AACA,sDAAsD;AACtD;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AA5BA;;;;;;;;;;AAqCe,SAAS,iBAAiB,EACvC,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,OAAO,EACe;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3B;YACE,IAAI;YACJ,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK;YAC/C,MAAM;YACN,QAAQ;YACR,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;YAChD,MAAM;YACN,QAAQ;YACR,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YACpD,MAAM;YACN,QAAQ;YACR,SAAS;YACT,UAAU;QACZ;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,YAAY;YACd,iBAAiB;YACjB,cAAc;YACd,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,MAAM,eAAe;QACnB;QACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,cAAc;QAClB,IAAI,QAAQ,gFAAgF;YAC1F;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa;YACjB,SAAS;gBAAE,OAAO;gBAAO,OAAO;YAA4B;YAC5D,YAAY;gBAAE,OAAO;gBAAQ,OAAO;YAAgC;YACpE,eAAe;gBAAE,OAAO;gBAAW,OAAO;YAA8B;YACxE,UAAU;gBAAE,OAAO;gBAAQ,OAAO;YAA0B;YAC5D,QAAQ;gBAAE,OAAO;gBAAe,OAAO;YAAgC;QACzE;QAEA,MAAM,MAAM,UAAU,CAAC,SAAoC,IAAI,WAAW,OAAO;QACjF,qBAAO,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAW,IAAI,KAAK;sBAAG,IAAI,KAAK;;;;;;IAChD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAc;;;;;;0DAC5B,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU,CAAC;gDACX,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAMzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAc;;;;;;0DAC5B,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DAGrC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,QAAO;wDACP,UAAU;wDACV,UAAU,CAAC;;;;;;kEAEb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU,CAAC,WAAW,CAAC;wDACvB,WAAU;;0EAEV,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0CAO3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAK7B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;0CAQ/C,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,SAAQ;gCACR,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAEtD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAM;gCAAe,cAAc;;kDACvC,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,OAAO;kDACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;kDAItC,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,8OAAC,kIAAA,CAAA,eAAY;0DACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;8DAAC;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;0DACZ,UAAU,GAAG,CAAC,CAAC,sBACd,8OAAC;wDAAmB,WAAU;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EACZ,iBAAiB,MAAM,QAAQ;0FAChC,8OAAC;gFAAK,WAAU;0FAAe,MAAM,MAAM;;;;;;;;;;;;kFAE7C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,MAAM,SAAS,CAAC,cAAc,CAAC;;;;;;;;;;;;;0EAGpC,8OAAC;gEAAE,WAAU;0EAAsB,MAAM,OAAO;;;;;;0EAChD,8OAAC;gEAAE,WAAU;;oEAAwB;oEAAS,MAAM,IAAI;;;;;;;;uDAZhD,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAmB9B,8OAAC;kCACC,cAAA,8OAAC;4BAAI,WAAU;sCACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBAC1B,8OAAC;oCAAmB,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;;gDACZ,iBAAiB,MAAM,QAAQ;8DAChC,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAuB,MAAM,MAAM;;;;;;sEAChD,8OAAC;4DAAE,WAAU;sEAAyB,MAAM,OAAO;;;;;;;;;;;;;;;;;;sDAGvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAyB,MAAM,IAAI;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DACV,MAAM,SAAS,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;mCAXhC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0BAqB1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAEtD,8OAAC;kCACC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD", "debugId": null}}]}