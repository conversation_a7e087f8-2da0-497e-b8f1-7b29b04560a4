{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { X } from \"lucide-react\"\n\ninterface DialogProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n  followMouse?: boolean // خيار لتفعيل/إلغاء تتبع الماوس\n  centerOnOpen?: boolean // خيار لتوسيط الحوار عند الفتح\n  smoothTransition?: boolean // خيار للانتقال السلس\n}\n\ninterface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Dialog = ({\n  open,\n  onOpenChange,\n  children,\n  followMouse = true,\n  centerOnOpen = true,\n  smoothTransition = true\n}: DialogProps) => {\n  const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 })\n  const [dialogPosition, setDialogPosition] = React.useState({ x: 0, y: 0 })\n  const [isInitialized, setIsInitialized] = React.useState(false)\n  const dialogRef = React.useRef<HTMLDivElement>(null)\n  const containerRef = React.useRef<HTMLDivElement>(null)\n  const animationFrameRef = React.useRef<number>()\n\n  // تهيئة موضع الحوار عند الفتح\n  React.useEffect(() => {\n    if (open && !isInitialized && dialogRef.current && centerOnOpen) {\n      const viewportWidth = window.innerWidth\n      const viewportHeight = window.innerHeight\n      const dialogRect = dialogRef.current.getBoundingClientRect()\n\n      const centerX = (viewportWidth - dialogRect.width) / 2\n      const centerY = (viewportHeight - dialogRect.height) / 2\n\n      setDialogPosition({ x: centerX, y: centerY })\n      setIsInitialized(true)\n    } else if (!open) {\n      setIsInitialized(false)\n    }\n  }, [open, centerOnOpen, isInitialized])\n\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onOpenChange?.(false)\n      }\n    }\n\n    const updateDialogPosition = (mouseX: number, mouseY: number) => {\n      if (!dialogRef.current) return\n\n      // الحصول على أبعاد الشاشة والحوار\n      const viewportWidth = window.innerWidth\n      const viewportHeight = window.innerHeight\n      const dialogRect = dialogRef.current.getBoundingClientRect()\n\n      // حساب الموضع المثالي للحوار بناءً على موضع الماوس\n      let newX = mouseX - dialogRect.width / 2\n      let newY = mouseY - dialogRect.height / 2\n\n      // التأكد من أن الحوار يبقى داخل حدود الشاشة مع هامش أمان\n      const padding = 20\n      newX = Math.max(padding, Math.min(newX, viewportWidth - dialogRect.width - padding))\n      newY = Math.max(padding, Math.min(newY, viewportHeight - dialogRect.height - padding))\n\n      // تطبيق التحريك السلس باستخدام requestAnimationFrame\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current)\n      }\n\n      animationFrameRef.current = requestAnimationFrame(() => {\n        setDialogPosition({ x: newX, y: newY })\n      })\n    }\n\n    const handleMouseMove = (e: MouseEvent) => {\n      if (!open || !followMouse || !isInitialized) return\n\n      const mouseX = e.clientX\n      const mouseY = e.clientY\n      setMousePosition({ x: mouseX, y: mouseY })\n\n      updateDialogPosition(mouseX, mouseY)\n    }\n\n    const handleResize = () => {\n      if (!open || !dialogRef.current) return\n\n      // إعادة حساب الموضع عند تغيير حجم النافذة\n      const viewportWidth = window.innerWidth\n      const viewportHeight = window.innerHeight\n      const dialogRect = dialogRef.current.getBoundingClientRect()\n\n      const padding = 20\n      let newX = dialogPosition.x\n      let newY = dialogPosition.y\n\n      newX = Math.max(padding, Math.min(newX, viewportWidth - dialogRect.width - padding))\n      newY = Math.max(padding, Math.min(newY, viewportHeight - dialogRect.height - padding))\n\n      setDialogPosition({ x: newX, y: newY })\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEscape)\n      if (followMouse) {\n        document.addEventListener('mousemove', handleMouseMove, { passive: true })\n      }\n      window.addEventListener('resize', handleResize)\n      document.body.style.overflow = 'hidden'\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.removeEventListener('mousemove', handleMouseMove)\n      window.removeEventListener('resize', handleResize)\n      document.body.style.overflow = 'unset'\n\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current)\n      }\n    }\n  }, [open, onOpenChange, followMouse, isInitialized, dialogPosition])\n\n  if (!open) return null\n\n  return (\n    <div\n      ref={containerRef}\n      className=\"fixed inset-0 z-50\"\n    >\n      <div\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-md transition-opacity duration-300\"\n        onClick={() => onOpenChange?.(false)}\n      />\n      <div\n        ref={dialogRef}\n        className={cn(\n          \"absolute z-50 max-h-[90vh] overflow-y-auto animate-in fade-in-0 zoom-in-95 duration-300\",\n          smoothTransition ? \"transition-all ease-out duration-200\" : \"\",\n          followMouse ? \"cursor-move\" : \"\"\n        )}\n        style={{\n          left: `${dialogPosition.x}px`,\n          top: `${dialogPosition.y}px`,\n          transform: 'translate3d(0, 0, 0)', // تحسين الأداء\n          willChange: 'transform', // تحسين الأداء\n          transition: smoothTransition ? 'left 0.2s ease-out, top 0.2s ease-out' : 'none',\n        }}\n        onMouseEnter={() => {\n          // إضافة تأثير بصري عند تمرير الماوس\n          if (dialogRef.current && followMouse) {\n            dialogRef.current.style.filter = 'drop-shadow(0 25px 25px rgb(0 0 0 / 0.15))'\n          }\n        }}\n        onMouseLeave={() => {\n          // إزالة التأثير البصري\n          if (dialogRef.current) {\n            dialogRef.current.style.filter = 'drop-shadow(0 20px 25px rgb(0 0 0 / 0.1))'\n          }\n        }}\n      >\n        {children}\n      </div>\n\n      {/* مؤشر بصري لموضع الماوس (اختياري) */}\n      {followMouse && process.env.NODE_ENV === 'development' && (\n        <div\n          className=\"absolute w-2 h-2 bg-red-500 rounded-full pointer-events-none z-40 opacity-50\"\n          style={{\n            left: `${mousePosition.x - 4}px`,\n            top: `${mousePosition.y - 4}px`,\n            transition: 'all 0.1s ease-out',\n          }}\n        />\n      )}\n    </div>\n  )\n}\n\nconst DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative w-full max-w-[50vw] mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200 p-0 overflow-hidden\",\n        \"backdrop-blur-sm bg-white/95\", // تحسين الشفافية\n        \"hover:shadow-3xl transition-shadow duration-300\", // تأثير الظل عند التمرير\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogContent.displayName = \"DialogContent\"\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-right p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 sm:space-x-reverse p-6 bg-gradient-to-r from-slate-50 to-slate-100 border-t border-slate-200\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-bold leading-none tracking-tight text-slate-800\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = \"DialogTitle\"\n\nconst DialogDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-slate-600 font-medium\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = \"DialogDescription\"\n\nconst DialogTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement>\n>(({ className, ...props }, ref) => (\n  <button\n    ref={ref}\n    className={className}\n    {...props}\n  />\n))\nDialogTrigger.displayName = \"DialogTrigger\"\n\nconst DialogClose = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & { onOpenChange?: (open: boolean) => void }\n>(({ className, onOpenChange, ...props }, ref) => (\n  <button\n    ref={ref}\n    type=\"button\"\n    onClick={() => onOpenChange?.(false)}\n    className={cn(\n      \"absolute left-4 top-4 rounded-full p-2 bg-white shadow-lg opacity-80 ring-offset-background transition-all duration-200 hover:opacity-100 hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 disabled:pointer-events-none z-10\",\n      className\n    )}\n    {...props}\n  >\n    <X className=\"h-4 w-4 text-slate-600\" />\n    <span className=\"sr-only\">إغلاق</span>\n  </button>\n))\nDialogClose.displayName = \"DialogClose\"\n\nexport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n  DialogClose,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAmBA,MAAM,SAAS,CAAC,EACd,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,cAAc,IAAI,EAClB,eAAe,IAAI,EACnB,mBAAmB,IAAI,EACX;IACZ,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAkB;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAkB;IAClD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD;IAErC,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,QAAQ,CAAC,iBAAiB,UAAU,OAAO,IAAI,cAAc;YAC/D,MAAM,gBAAgB,OAAO,UAAU;YACvC,MAAM,iBAAiB,OAAO,WAAW;YACzC,MAAM,aAAa,UAAU,OAAO,CAAC,qBAAqB;YAE1D,MAAM,UAAU,CAAC,gBAAgB,WAAW,KAAK,IAAI;YACrD,MAAM,UAAU,CAAC,iBAAiB,WAAW,MAAM,IAAI;YAEvD,kBAAkB;gBAAE,GAAG;gBAAS,GAAG;YAAQ;YAC3C,iBAAiB;QACnB,OAAO,IAAI,CAAC,MAAM;YAChB,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAM;QAAc;KAAc;IAEtC,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,eAAe;YACjB;QACF;QAEA,MAAM,uBAAuB,CAAC,QAAgB;YAC5C,IAAI,CAAC,UAAU,OAAO,EAAE;YAExB,kCAAkC;YAClC,MAAM,gBAAgB,OAAO,UAAU;YACvC,MAAM,iBAAiB,OAAO,WAAW;YACzC,MAAM,aAAa,UAAU,OAAO,CAAC,qBAAqB;YAE1D,mDAAmD;YACnD,IAAI,OAAO,SAAS,WAAW,KAAK,GAAG;YACvC,IAAI,OAAO,SAAS,WAAW,MAAM,GAAG;YAExC,yDAAyD;YACzD,MAAM,UAAU;YAChB,OAAO,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,MAAM,gBAAgB,WAAW,KAAK,GAAG;YAC3E,OAAO,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,MAAM,iBAAiB,WAAW,MAAM,GAAG;YAE7E,qDAAqD;YACrD,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;YAEA,kBAAkB,OAAO,GAAG,sBAAsB;gBAChD,kBAAkB;oBAAE,GAAG;oBAAM,GAAG;gBAAK;YACvC;QACF;QAEA,MAAM,kBAAkB,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,eAAe;YAE7C,MAAM,SAAS,EAAE,OAAO;YACxB,MAAM,SAAS,EAAE,OAAO;YACxB,iBAAiB;gBAAE,GAAG;gBAAQ,GAAG;YAAO;YAExC,qBAAqB,QAAQ;QAC/B;QAEA,MAAM,eAAe;YACnB,IAAI,CAAC,QAAQ,CAAC,UAAU,OAAO,EAAE;YAEjC,0CAA0C;YAC1C,MAAM,gBAAgB,OAAO,UAAU;YACvC,MAAM,iBAAiB,OAAO,WAAW;YACzC,MAAM,aAAa,UAAU,OAAO,CAAC,qBAAqB;YAE1D,MAAM,UAAU;YAChB,IAAI,OAAO,eAAe,CAAC;YAC3B,IAAI,OAAO,eAAe,CAAC;YAE3B,OAAO,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,MAAM,gBAAgB,WAAW,KAAK,GAAG;YAC3E,OAAO,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,MAAM,iBAAiB,WAAW,MAAM,GAAG;YAE7E,kBAAkB;gBAAE,GAAG;gBAAM,GAAG;YAAK;QACvC;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,IAAI,aAAa;gBACf,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;oBAAE,SAAS;gBAAK;YAC1E;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,aAAa;YAC1C,OAAO,mBAAmB,CAAC,UAAU;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAE/B,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;QACF;IACF,GAAG;QAAC;QAAM;QAAc;QAAa;QAAe;KAAe;IAEnE,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAEV,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAEhC,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,mBAAmB,yCAAyC,IAC5D,cAAc,gBAAgB;gBAEhC,OAAO;oBACL,MAAM,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC;oBAC7B,KAAK,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC;oBAC5B,WAAW;oBACX,YAAY;oBACZ,YAAY,mBAAmB,0CAA0C;gBAC3E;gBACA,cAAc;oBACZ,oCAAoC;oBACpC,IAAI,UAAU,OAAO,IAAI,aAAa;wBACpC,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;oBACnC;gBACF;gBACA,cAAc;oBACZ,uBAAuB;oBACvB,IAAI,UAAU,OAAO,EAAE;wBACrB,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;oBACnC;gBACF;0BAEC;;;;;;YAIF,eAAe,oDAAyB,+BACvC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,MAAM,GAAG,cAAc,CAAC,GAAG,EAAE,EAAE,CAAC;oBAChC,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,EAAE,CAAC;oBAC/B,YAAY;gBACd;;;;;;;;;;;;AAKV;AAEA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iHACA,gCACA,mDACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE,oBACxC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,SAAS,IAAM,eAAe;QAC9B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wRACA;QAED,GAAG,KAAK;;0BAET,8OAAC,4LAAA,CAAA,IAAC;gBAAC,WAAU;;;;;;0BACb,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/mouse-tracker.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface MouseTrackerProps {\n  show?: boolean\n  className?: string\n}\n\ninterface MousePosition {\n  x: number\n  y: number\n}\n\nexport default function MouseTracker({ show = false, className }: MouseTrackerProps) {\n  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 })\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    if (!show) return\n\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY })\n      setIsVisible(true)\n    }\n\n    const handleMouseLeave = () => {\n      setIsVisible(false)\n    }\n\n    document.addEventListener('mousemove', handleMouseMove)\n    document.addEventListener('mouseleave', handleMouseLeave)\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove)\n      document.removeEventListener('mouseleave', handleMouseLeave)\n    }\n  }, [show])\n\n  if (!show || !isVisible) return null\n\n  return (\n    <>\n      {/* مؤشر الماوس الرئيسي */}\n      <div\n        className={cn(\n          \"fixed w-4 h-4 bg-blue-500 rounded-full pointer-events-none z-[9999] opacity-70 transition-all duration-100 ease-out\",\n          \"shadow-lg border-2 border-white\",\n          className\n        )}\n        style={{\n          left: `${mousePosition.x - 8}px`,\n          top: `${mousePosition.y - 8}px`,\n          transform: 'translate3d(0, 0, 0)',\n        }}\n      />\n      \n      {/* الدائرة الخارجية */}\n      <div\n        className=\"fixed w-8 h-8 border-2 border-blue-400 rounded-full pointer-events-none z-[9998] opacity-50 transition-all duration-200 ease-out\"\n        style={{\n          left: `${mousePosition.x - 16}px`,\n          top: `${mousePosition.y - 16}px`,\n          transform: 'translate3d(0, 0, 0)',\n        }}\n      />\n      \n      {/* معلومات الإحداثيات */}\n      <div\n        className=\"fixed bg-gray-900 text-white text-xs px-2 py-1 rounded shadow-lg pointer-events-none z-[9997] font-mono\"\n        style={{\n          left: `${mousePosition.x + 15}px`,\n          top: `${mousePosition.y - 25}px`,\n          transform: 'translate3d(0, 0, 0)',\n        }}\n      >\n        {mousePosition.x}, {mousePosition.y}\n      </div>\n    </>\n  )\n}\n\n// مكون لإظهار معلومات تفصيلية عن الماوس\ninterface MouseInfoPanelProps {\n  show?: boolean\n  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n}\n\nexport function MouseInfoPanel({ show = false, position = 'top-left' }: MouseInfoPanelProps) {\n  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 })\n  const [velocity, setVelocity] = useState<MousePosition>({ x: 0, y: 0 })\n  const [lastPosition, setLastPosition] = useState<MousePosition>({ x: 0, y: 0 })\n  const [lastTime, setLastTime] = useState<number>(Date.now())\n\n  useEffect(() => {\n    if (!show) return\n\n    const handleMouseMove = (e: MouseEvent) => {\n      const currentTime = Date.now()\n      const deltaTime = currentTime - lastTime\n      \n      if (deltaTime > 0) {\n        const deltaX = e.clientX - lastPosition.x\n        const deltaY = e.clientY - lastPosition.y\n        \n        setVelocity({\n          x: Math.round((deltaX / deltaTime) * 1000), // pixels per second\n          y: Math.round((deltaY / deltaTime) * 1000)\n        })\n      }\n      \n      setMousePosition({ x: e.clientX, y: e.clientY })\n      setLastPosition({ x: e.clientX, y: e.clientY })\n      setLastTime(currentTime)\n    }\n\n    document.addEventListener('mousemove', handleMouseMove)\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove)\n    }\n  }, [show, lastPosition, lastTime])\n\n  if (!show) return null\n\n  const positionClasses = {\n    'top-left': 'top-4 left-4',\n    'top-right': 'top-4 right-4',\n    'bottom-left': 'bottom-4 left-4',\n    'bottom-right': 'bottom-4 right-4'\n  }\n\n  return (\n    <div className={cn(\n      \"fixed bg-gray-900/90 backdrop-blur-sm text-white p-4 rounded-lg shadow-xl z-[9996] font-mono text-sm\",\n      \"border border-gray-700\",\n      positionClasses[position]\n    )}>\n      <div className=\"space-y-2\">\n        <div className=\"text-blue-400 font-semibold border-b border-gray-700 pb-2\">\n          معلومات الماوس\n        </div>\n        \n        <div className=\"grid grid-cols-2 gap-4\">\n          <div>\n            <div className=\"text-gray-400 text-xs\">الإحداثيات</div>\n            <div className=\"text-white\">\n              X: {mousePosition.x}px<br/>\n              Y: {mousePosition.y}px\n            </div>\n          </div>\n          \n          <div>\n            <div className=\"text-gray-400 text-xs\">السرعة</div>\n            <div className=\"text-white\">\n              X: {velocity.x}px/s<br/>\n              Y: {velocity.y}px/s\n            </div>\n          </div>\n        </div>\n        \n        <div>\n          <div className=\"text-gray-400 text-xs\">معلومات الشاشة</div>\n          <div className=\"text-white text-xs\">\n            العرض: {window.innerWidth}px<br/>\n            الارتفاع: {window.innerHeight}px\n          </div>\n        </div>\n        \n        <div>\n          <div className=\"text-gray-400 text-xs\">المنطقة</div>\n          <div className=\"text-white text-xs\">\n            {mousePosition.x < window.innerWidth / 2 ? 'يسار' : 'يمين'} - \n            {mousePosition.y < window.innerHeight / 2 ? 'أعلى' : 'أسفل'}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Hook مخصص لتتبع الماوس\nexport function useMousePosition() {\n  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 })\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY })\n    }\n\n    document.addEventListener('mousemove', handleMouseMove)\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove)\n    }\n  }, [])\n\n  return mousePosition\n}\n\n// Hook لحساب سرعة الماوس\nexport function useMouseVelocity() {\n  const [velocity, setVelocity] = useState<MousePosition>({ x: 0, y: 0 })\n  const [lastPosition, setLastPosition] = useState<MousePosition>({ x: 0, y: 0 })\n  const [lastTime, setLastTime] = useState<number>(Date.now())\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      const currentTime = Date.now()\n      const deltaTime = currentTime - lastTime\n      \n      if (deltaTime > 0) {\n        const deltaX = e.clientX - lastPosition.x\n        const deltaY = e.clientY - lastPosition.y\n        \n        setVelocity({\n          x: Math.round((deltaX / deltaTime) * 1000),\n          y: Math.round((deltaY / deltaTime) * 1000)\n        })\n      }\n      \n      setLastPosition({ x: e.clientX, y: e.clientY })\n      setLastTime(currentTime)\n    }\n\n    document.addEventListener('mousemove', handleMouseMove)\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove)\n    }\n  }, [lastPosition, lastTime])\n\n  return velocity\n}\n\n// Hook للتحقق من منطقة الماوس\nexport function useMouseRegion() {\n  const mousePosition = useMousePosition()\n  \n  const region = {\n    horizontal: mousePosition.x < window.innerWidth / 2 ? 'left' : 'right',\n    vertical: mousePosition.y < window.innerHeight / 2 ? 'top' : 'bottom',\n    quadrant: `${mousePosition.y < window.innerHeight / 2 ? 'top' : 'bottom'}-${mousePosition.x < window.innerWidth / 2 ? 'left' : 'right'}`\n  }\n  \n  return region\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAHA;;;;AAee,SAAS,aAAa,EAAE,OAAO,KAAK,EAAE,SAAS,EAAqB;IACjF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IAC/E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;QAEX,MAAM,kBAAkB,CAAC;YACvB,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAC9C,aAAa;QACf;QAEA,MAAM,mBAAmB;YACvB,aAAa;QACf;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,cAAc;QAExC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,cAAc;QAC7C;IACF,GAAG;QAAC;KAAK;IAET,IAAI,CAAC,QAAQ,CAAC,WAAW,OAAO;IAEhC,qBACE;;0BAEE,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uHACA,mCACA;gBAEF,OAAO;oBACL,MAAM,GAAG,cAAc,CAAC,GAAG,EAAE,EAAE,CAAC;oBAChC,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,EAAE,CAAC;oBAC/B,WAAW;gBACb;;;;;;0BAIF,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,MAAM,GAAG,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC;oBACjC,KAAK,GAAG,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC;oBAChC,WAAW;gBACb;;;;;;0BAIF,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,MAAM,GAAG,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC;oBACjC,KAAK,GAAG,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC;oBAChC,WAAW;gBACb;;oBAEC,cAAc,CAAC;oBAAC;oBAAG,cAAc,CAAC;;;;;;;;;AAI3C;AAQO,SAAS,eAAe,EAAE,OAAO,KAAK,EAAE,WAAW,UAAU,EAAuB;IACzF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IAC/E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IAC7E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,KAAK,GAAG;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;QAEX,MAAM,kBAAkB,CAAC;YACvB,MAAM,cAAc,KAAK,GAAG;YAC5B,MAAM,YAAY,cAAc;YAEhC,IAAI,YAAY,GAAG;gBACjB,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;gBACzC,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;gBAEzC,YAAY;oBACV,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,YAAa;oBACrC,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,YAAa;gBACvC;YACF;YAEA,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAC9C,gBAAgB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAC7C,YAAY;QACd;QAEA,SAAS,gBAAgB,CAAC,aAAa;QAEvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;QAAM;QAAc;KAAS;IAEjC,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,kBAAkB;QACtB,YAAY;QACZ,aAAa;QACb,eAAe;QACf,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,wGACA,0BACA,eAAe,CAAC,SAAS;kBAEzB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAA4D;;;;;;8BAI3E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;wCAAa;wCACtB,cAAc,CAAC;wCAAC;sDAAE,8OAAC;;;;;wCAAI;wCACvB,cAAc,CAAC;wCAAC;;;;;;;;;;;;;sCAIxB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;wCAAa;wCACtB,SAAS,CAAC;wCAAC;sDAAI,8OAAC;;;;;wCAAI;wCACpB,SAAS,CAAC;wCAAC;;;;;;;;;;;;;;;;;;;8BAKrB,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;sCACvC,8OAAC;4BAAI,WAAU;;gCAAqB;gCAC1B,OAAO,UAAU;gCAAC;8CAAE,8OAAC;;;;;gCAAI;gCACtB,OAAO,WAAW;gCAAC;;;;;;;;;;;;;8BAIlC,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;sCACvC,8OAAC;4BAAI,WAAU;;gCACZ,cAAc,CAAC,GAAG,OAAO,UAAU,GAAG,IAAI,SAAS;gCAAO;gCAC1D,cAAc,CAAC,GAAG,OAAO,WAAW,GAAG,IAAI,SAAS;;;;;;;;;;;;;;;;;;;;;;;;AAMjE;AAGO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IAE/E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAChD;QAEA,SAAS,gBAAgB,CAAC,aAAa;QAEvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IAC7E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,KAAK,GAAG;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,MAAM,cAAc,KAAK,GAAG;YAC5B,MAAM,YAAY,cAAc;YAEhC,IAAI,YAAY,GAAG;gBACjB,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;gBACzC,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;gBAEzC,YAAY;oBACV,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,YAAa;oBACrC,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,YAAa;gBACvC;YACF;YAEA,gBAAgB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAC7C,YAAY;QACd;QAEA,SAAS,gBAAgB,CAAC,aAAa;QAEvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;QAAc;KAAS;IAE3B,OAAO;AACT;AAGO,SAAS;IACd,MAAM,gBAAgB;IAEtB,MAAM,SAAS;QACb,YAAY,cAAc,CAAC,GAAG,OAAO,UAAU,GAAG,IAAI,SAAS;QAC/D,UAAU,cAAc,CAAC,GAAG,OAAO,WAAW,GAAG,IAAI,QAAQ;QAC7D,UAAU,GAAG,cAAc,CAAC,GAAG,OAAO,WAAW,GAAG,IAAI,QAAQ,SAAS,CAAC,EAAE,cAAc,CAAC,GAAG,OAAO,UAAU,GAAG,IAAI,SAAS,SAAS;IAC1I;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog-demo.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON><PERSON>, DialogFooter } from './dialog'\nimport { Button } from './button'\nimport { <PERSON><PERSON><PERSON>, MousePointer, Move, Eye, Info } from 'lucide-react'\nimport MouseTracker, { MouseInfoPanel } from './mouse-tracker'\n\ninterface DialogDemoProps {\n  className?: string\n}\n\nexport default function DialogDemo({ className }: DialogDemoProps) {\n  const [basicDialogOpen, setBasicDialogOpen] = useState(false)\n  const [mouseFollowDialogOpen, setMouseFollowDialogOpen] = useState(false)\n  const [staticDialogOpen, setStaticDialogOpen] = useState(false)\n  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false)\n  const [showMouseTracker, setShowMouseTracker] = useState(false)\n  const [showMouseInfo, setShowMouseInfo] = useState(false)\n\n  return (\n    <div className={`space-y-4 p-6 ${className}`}>\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">تجربة الشاشات المنبثقة المحسنة</h2>\n        <p className=\"text-gray-600\">اختبر الأنواع المختلفة من الشاشات المنبثقة التفاعلية</p>\n      </div>\n\n      {/* أدوات التحكم في تتبع الماوس */}\n      <div className=\"flex justify-center gap-4 mb-6\">\n        <Button\n          variant={showMouseTracker ? \"default\" : \"outline\"}\n          size=\"sm\"\n          onClick={() => setShowMouseTracker(!showMouseTracker)}\n          className=\"flex items-center gap-2\"\n        >\n          <MousePointer className=\"w-4 h-4\" />\n          {showMouseTracker ? 'إخفاء مؤشر الماوس' : 'إظهار مؤشر الماوس'}\n        </Button>\n\n        <Button\n          variant={showMouseInfo ? \"default\" : \"outline\"}\n          size=\"sm\"\n          onClick={() => setShowMouseInfo(!showMouseInfo)}\n          className=\"flex items-center gap-2\"\n        >\n          <Info className=\"w-4 h-4\" />\n          {showMouseInfo ? 'إخفاء معلومات الماوس' : 'إظهار معلومات الماوس'}\n        </Button>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {/* الحوار الأساسي مع تتبع الماوس */}\n        <Button\n          onClick={() => setBasicDialogOpen(true)}\n          className=\"flex items-center gap-2 h-20 flex-col justify-center\"\n          variant=\"outline\"\n        >\n          <MousePointer className=\"w-6 h-6\" />\n          <span>حوار يتبع الماوس</span>\n        </Button>\n\n        {/* حوار بدون تتبع الماوس */}\n        <Button\n          onClick={() => setStaticDialogOpen(true)}\n          className=\"flex items-center gap-2 h-20 flex-col justify-center\"\n          variant=\"outline\"\n        >\n          <Eye className=\"w-6 h-6\" />\n          <span>حوار ثابت</span>\n        </Button>\n\n        {/* حوار متقدم */}\n        <Button\n          onClick={() => setMouseFollowDialogOpen(true)}\n          className=\"flex items-center gap-2 h-20 flex-col justify-center\"\n          variant=\"outline\"\n        >\n          <Move className=\"w-6 h-6\" />\n          <span>حوار متقدم</span>\n        </Button>\n\n        {/* حوار الإعدادات */}\n        <Button\n          onClick={() => setSettingsDialogOpen(true)}\n          className=\"flex items-center gap-2 h-20 flex-col justify-center\"\n          variant=\"outline\"\n        >\n          <Settings className=\"w-6 h-6\" />\n          <span>إعدادات الحوار</span>\n        </Button>\n      </div>\n\n      {/* الحوار الأساسي مع تتبع الماوس */}\n      <Dialog \n        open={basicDialogOpen} \n        onOpenChange={setBasicDialogOpen}\n        followMouse={true}\n        centerOnOpen={true}\n        smoothTransition={true}\n      >\n        <DialogContent className=\"max-w-md\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <MousePointer className=\"w-5 h-5 text-blue-600\" />\n              حوار يتبع الماوس\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6\">\n            <p className=\"text-gray-600 mb-4\">\n              هذا الحوار يتبع حركة الماوس ويتحرك معه بسلاسة. حرك الماوس لترى كيف يتفاعل الحوار!\n            </p>\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <h4 className=\"font-semibold text-blue-900 mb-2\">الميزات:</h4>\n              <ul className=\"text-sm text-blue-800 space-y-1\">\n                <li>• يتبع حركة الماوس</li>\n                <li>• انتقال سلس</li>\n                <li>• يبقى داخل حدود الشاشة</li>\n                <li>• تأثيرات بصرية محسنة</li>\n              </ul>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setBasicDialogOpen(false)}>\n              إغلاق\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار بدون تتبع الماوس */}\n      <Dialog \n        open={staticDialogOpen} \n        onOpenChange={setStaticDialogOpen}\n        followMouse={false}\n        centerOnOpen={true}\n        smoothTransition={false}\n      >\n        <DialogContent className=\"max-w-md\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Eye className=\"w-5 h-5 text-green-600\" />\n              حوار ثابت\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6\">\n            <p className=\"text-gray-600 mb-4\">\n              هذا الحوار يبقى في المركز ولا يتبع حركة الماوس. مناسب للمحتوى الذي يحتاج تركيز ثابت.\n            </p>\n            <div className=\"bg-green-50 p-4 rounded-lg\">\n              <h4 className=\"font-semibold text-green-900 mb-2\">الميزات:</h4>\n              <ul className=\"text-sm text-green-800 space-y-1\">\n                <li>• موضع ثابت في المركز</li>\n                <li>• لا يتأثر بحركة الماوس</li>\n                <li>• مناسب للنماذج الطويلة</li>\n                <li>• أداء محسن</li>\n              </ul>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setStaticDialogOpen(false)}>\n              إغلاق\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار متقدم */}\n      <Dialog \n        open={mouseFollowDialogOpen} \n        onOpenChange={setMouseFollowDialogOpen}\n        followMouse={true}\n        centerOnOpen={false}\n        smoothTransition={true}\n      >\n        <DialogContent className=\"max-w-lg\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Move className=\"w-5 h-5 text-purple-600\" />\n              حوار متقدم مع تتبع الماوس\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6 space-y-4\">\n            <p className=\"text-gray-600\">\n              حوار متقدم مع ميزات إضافية وتفاعل محسن مع الماوس.\n            </p>\n            \n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-purple-50 p-4 rounded-lg\">\n                <h4 className=\"font-semibold text-purple-900 mb-2\">التفاعل</h4>\n                <p className=\"text-sm text-purple-800\">\n                  يتفاعل مع حركة الماوس بطريقة ذكية ومرنة\n                </p>\n              </div>\n              <div className=\"bg-orange-50 p-4 rounded-lg\">\n                <h4 className=\"font-semibold text-orange-900 mb-2\">الأداء</h4>\n                <p className=\"text-sm text-orange-800\">\n                  محسن للأداء باستخدام requestAnimationFrame\n                </p>\n              </div>\n            </div>\n\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\n              <h4 className=\"font-semibold text-gray-900 mb-2\">تعليمات الاستخدام:</h4>\n              <ol className=\"text-sm text-gray-700 space-y-1\">\n                <li>1. حرك الماوس لترى الحوار يتبعه</li>\n                <li>2. لاحظ كيف يبقى داخل حدود الشاشة</li>\n                <li>3. جرب تغيير حجم النافذة</li>\n              </ol>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button \n              variant=\"outline\" \n              onClick={() => setMouseFollowDialogOpen(false)}\n            >\n              إلغاء\n            </Button>\n            <Button onClick={() => setMouseFollowDialogOpen(false)}>\n              موافق\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار الإعدادات */}\n      <Dialog \n        open={settingsDialogOpen} \n        onOpenChange={setSettingsDialogOpen}\n        followMouse={true}\n        centerOnOpen={true}\n        smoothTransition={true}\n      >\n        <DialogContent className=\"max-w-2xl\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Settings className=\"w-5 h-5 text-indigo-600\" />\n              إعدادات الحوار التفاعلي\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6 space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-gray-900\">الخصائص المتاحة</h4>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                    <span className=\"text-sm font-medium\">followMouse</span>\n                    <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">true</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                    <span className=\"text-sm font-medium\">centerOnOpen</span>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">true</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                    <span className=\"text-sm font-medium\">smoothTransition</span>\n                    <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">true</span>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-gray-900\">التحسينات</h4>\n                <div className=\"space-y-2 text-sm text-gray-600\">\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span>تحسين الأداء مع requestAnimationFrame</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span>حماية من الخروج عن حدود الشاشة</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                    <span>انتقالات سلسة ومرنة</span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                    <span>تأثيرات بصرية محسنة</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"bg-indigo-50 p-4 rounded-lg\">\n              <h4 className=\"font-semibold text-indigo-900 mb-2\">💡 نصائح للاستخدام</h4>\n              <ul className=\"text-sm text-indigo-800 space-y-1\">\n                <li>• استخدم followMouse=true للحوارات التفاعلية</li>\n                <li>• استخدم followMouse=false للنماذج الطويلة</li>\n                <li>• centerOnOpen=true يضع الحوار في المركز عند الفتح</li>\n                <li>• smoothTransition=true يضيف انتقالات سلسة</li>\n              </ul>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setSettingsDialogOpen(false)}>\n              فهمت\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* أدوات تتبع الماوس */}\n      <MouseTracker show={showMouseTracker} />\n      <MouseInfoPanel show={showMouseInfo} position=\"top-right\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAYe,SAAS,WAAW,EAAE,SAAS,EAAmB;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,8OAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;;0BAC1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,mBAAmB,YAAY;wBACxC,MAAK;wBACL,SAAS,IAAM,oBAAoB,CAAC;wBACpC,WAAU;;0CAEV,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BACvB,mBAAmB,sBAAsB;;;;;;;kCAG5C,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,gBAAgB,YAAY;wBACrC,MAAK;wBACL,SAAS,IAAM,iBAAiB,CAAC;wBACjC,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,gBAAgB,yBAAyB;;;;;;;;;;;;;0BAI9C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,mBAAmB;wBAClC,WAAU;wBACV,SAAQ;;0CAER,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,8OAAC;0CAAK;;;;;;;;;;;;kCAIR,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,oBAAoB;wBACnC,WAAU;wBACV,SAAQ;;0CAER,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;kCAIR,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,yBAAyB;wBACxC,WAAU;wBACV,SAAQ;;0CAER,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;kCAIR,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,sBAAsB;wBACrC,WAAU;wBACV,SAAQ;;0CAER,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,cAAc;gBACd,kBAAkB;0BAElB,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;;;;;;sCAItD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAIV,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,mBAAmB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,cAAc;gBACd,kBAAkB;0BAElB,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;;;;;;sCAI9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAIV,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,oBAAoB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;0BAQzD,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,cAAc;gBACd,kBAAkB;0BAElB,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;;;;;;sCAIhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAI7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAIV,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,yBAAyB;8CACzC;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,yBAAyB;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,cAAc;gBACd,kBAAkB;0BAElB,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,8OAAC;oEAAK,WAAU;8EAAwD;;;;;;;;;;;;sEAE1E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,8OAAC;oEAAK,WAAU;8EAAsD;;;;;;;;;;;;sEAExE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,8OAAC;oEAAK,WAAU;8EAA0D;;;;;;;;;;;;;;;;;;;;;;;;sDAKhF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAIV,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,sBAAsB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;0BAQ3D,8OAAC,4IAAA,CAAA,UAAY;gBAAC,MAAM;;;;;;0BACpB,8OAAC,4IAAA,CAAA,iBAAc;gBAAC,MAAM;gBAAe,UAAS;;;;;;;;;;;;AAGpD", "debugId": null}}, {"offset": {"line": 1716, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // This interface extends the base input props\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-11 w-full rounded-xl border-2 border-secondary-200 bg-white px-4 py-3 text-sm font-medium text-secondary-700 transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-400 focus-visible:outline-none focus-visible:border-primary-500 focus-visible:ring-4 focus-visible:ring-primary-500/20 disabled:cursor-not-allowed disabled:opacity-50 hover:border-secondary-300\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gbACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {\n  // This interface extends the base label props\n}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = \"Label\"\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  // This interface extends the base textarea props\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1799, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/components/ui/dialog-examples.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>T<PERSON>le, DialogFooter } from './dialog'\nimport { Button } from './button'\nimport { Card, CardContent, CardHeader, CardTitle } from './card'\nimport { Input } from './input'\nimport { Label } from './label'\nimport { Textarea } from './textarea'\nimport { \n  User, \n  Mail, \n  Phone, \n  MapPin, \n  Calendar, \n  CreditCard, \n  Settings, \n  Bell,\n  Shield,\n  Palette,\n  Database,\n  FileText,\n  Image,\n  Video,\n  Music,\n  Download,\n  Upload,\n  Share,\n  Heart,\n  Star,\n  MessageCircle\n} from 'lucide-react'\n\ninterface DialogExamplesProps {\n  className?: string\n}\n\nexport default function DialogExamples({ className }: DialogExamplesProps) {\n  const [profileDialogOpen, setProfileDialogOpen] = useState(false)\n  const [formDialogOpen, setFormDialogOpen] = useState(false)\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)\n  const [mediaD<PERSON><PERSON><PERSON><PERSON>, setMediaDialogOpen] = useState(false)\n  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false)\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      <div className=\"text-center\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">أمثلة متنوعة للشاشات المنبثقة</h3>\n        <p className=\"text-gray-600\">تجربة أنواع مختلفة من المحتوى مع تتبع الماوس</p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {/* حوار الملف الشخصي */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setProfileDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-blue-900\">\n              <User className=\"w-5 h-5\" />\n              الملف الشخصي\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">عرض وتحرير معلومات المستخدم</p>\n          </CardContent>\n        </Card>\n\n        {/* حوار النموذج */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setFormDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-green-900\">\n              <FileText className=\"w-5 h-5\" />\n              نموذج تفاعلي\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">نموذج مع حقول متعددة</p>\n          </CardContent>\n        </Card>\n\n        {/* حوار التأكيد */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setConfirmDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-red-900\">\n              <Shield className=\"w-5 h-5\" />\n              تأكيد العملية\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">حوار تأكيد بسيط</p>\n          </CardContent>\n        </Card>\n\n        {/* حوار الوسائط */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setMediaDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-purple-900\">\n              <Image className=\"w-5 h-5\" />\n              معرض الوسائط\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">عرض الصور والفيديوهات</p>\n          </CardContent>\n        </Card>\n\n        {/* حوار الإعدادات */}\n        <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\" onClick={() => setSettingsDialogOpen(true)}>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"flex items-center gap-2 text-indigo-900\">\n              <Settings className=\"w-5 h-5\" />\n              الإعدادات\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-gray-600\">إعدادات التطبيق المتقدمة</p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* حوار الملف الشخصي */}\n      <Dialog \n        open={profileDialogOpen} \n        onOpenChange={setProfileDialogOpen}\n        followMouse={true}\n        centerOnOpen={true}\n        smoothTransition={true}\n      >\n        <DialogContent className=\"max-w-md\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <User className=\"w-5 h-5 text-blue-600\" />\n              الملف الشخصي\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6 space-y-4\">\n            <div className=\"flex items-center gap-4\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center\">\n                <User className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">أحمد محمد</h3>\n                <p className=\"text-sm text-gray-600\">مطور واجهات أمامية</p>\n              </div>\n            </div>\n            \n            <div className=\"space-y-3\">\n              <div className=\"flex items-center gap-3\">\n                <Mail className=\"w-4 h-4 text-gray-500\" />\n                <span className=\"text-sm\"><EMAIL></span>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <Phone className=\"w-4 h-4 text-gray-500\" />\n                <span className=\"text-sm\">+962 79 123 4567</span>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <MapPin className=\"w-4 h-4 text-gray-500\" />\n                <span className=\"text-sm\">عمان، الأردن</span>\n              </div>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setProfileDialogOpen(false)}>\n              إغلاق\n            </Button>\n            <Button>تحرير</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار النموذج */}\n      <Dialog \n        open={formDialogOpen} \n        onOpenChange={setFormDialogOpen}\n        followMouse={false} // للنماذج نفضل عدم التتبع\n        centerOnOpen={true}\n        smoothTransition={true}\n      >\n        <DialogContent className=\"max-w-lg\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <FileText className=\"w-5 h-5 text-green-600\" />\n              إضافة عضو جديد\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6 space-y-4\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"firstName\">الاسم الأول</Label>\n                <Input id=\"firstName\" placeholder=\"أدخل الاسم الأول\" />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"lastName\">الاسم الأخير</Label>\n                <Input id=\"lastName\" placeholder=\"أدخل الاسم الأخير\" />\n              </div>\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">البريد الإلكتروني</Label>\n              <Input id=\"email\" type=\"email\" placeholder=\"<EMAIL>\" />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"phone\">رقم الهاتف</Label>\n              <Input id=\"phone\" placeholder=\"+962 79 123 4567\" />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"notes\">ملاحظات</Label>\n              <Textarea id=\"notes\" placeholder=\"أضف أي ملاحظات إضافية...\" rows={3} />\n            </div>\n          </div>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setFormDialogOpen(false)}>\n              إلغاء\n            </Button>\n            <Button>حفظ</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار التأكيد */}\n      <Dialog \n        open={confirmDialogOpen} \n        onOpenChange={setConfirmDialogOpen}\n        followMouse={true}\n        centerOnOpen={true}\n        smoothTransition={true}\n      >\n        <DialogContent className=\"max-w-sm\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Shield className=\"w-5 h-5 text-red-600\" />\n              تأكيد الحذف\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6\">\n            <p className=\"text-gray-600\">\n              هل أنت متأكد من أنك تريد حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.\n            </p>\n          </div>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setConfirmDialogOpen(false)}>\n              إلغاء\n            </Button>\n            <Button variant=\"destructive\">حذف</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار الوسائط */}\n      <Dialog \n        open={mediaDialogOpen} \n        onOpenChange={setMediaDialogOpen}\n        followMouse={true}\n        centerOnOpen={true}\n        smoothTransition={true}\n      >\n        <DialogContent className=\"max-w-2xl\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Image className=\"w-5 h-5 text-purple-600\" />\n              معرض الوسائط\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-3 gap-4\">\n              {[1, 2, 3, 4, 5, 6].map((item) => (\n                <div key={item} className=\"aspect-square bg-gray-100 rounded-lg flex items-center justify-center\">\n                  <Image className=\"w-8 h-8 text-gray-400\" />\n                </div>\n              ))}\n            </div>\n            \n            <div className=\"flex justify-center gap-4 mt-6\">\n              <Button variant=\"outline\" size=\"sm\">\n                <Upload className=\"w-4 h-4 mr-2\" />\n                رفع\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <Download className=\"w-4 h-4 mr-2\" />\n                تحميل\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <Share className=\"w-4 h-4 mr-2\" />\n                مشاركة\n              </Button>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setMediaDialogOpen(false)}>\n              إغلاق\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار الإعدادات */}\n      <Dialog \n        open={settingsDialogOpen} \n        onOpenChange={setSettingsDialogOpen}\n        followMouse={false}\n        centerOnOpen={true}\n        smoothTransition={true}\n      >\n        <DialogContent className=\"max-w-3xl\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Settings className=\"w-5 h-5 text-indigo-600\" />\n              إعدادات التطبيق\n            </DialogTitle>\n          </DialogHeader>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-gray-900 flex items-center gap-2\">\n                  <Bell className=\"w-4 h-4\" />\n                  الإشعارات\n                </h4>\n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center gap-2\">\n                    <input type=\"checkbox\" className=\"rounded\" />\n                    <span className=\"text-sm\">إشعارات البريد الإلكتروني</span>\n                  </label>\n                  <label className=\"flex items-center gap-2\">\n                    <input type=\"checkbox\" className=\"rounded\" />\n                    <span className=\"text-sm\">إشعارات الهاتف</span>\n                  </label>\n                </div>\n              </div>\n              \n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-gray-900 flex items-center gap-2\">\n                  <Palette className=\"w-4 h-4\" />\n                  المظهر\n                </h4>\n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center gap-2\">\n                    <input type=\"radio\" name=\"theme\" className=\"rounded\" />\n                    <span className=\"text-sm\">فاتح</span>\n                  </label>\n                  <label className=\"flex items-center gap-2\">\n                    <input type=\"radio\" name=\"theme\" className=\"rounded\" />\n                    <span className=\"text-sm\">داكن</span>\n                  </label>\n                </div>\n              </div>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setSettingsDialogOpen(false)}>\n              إلغاء\n            </Button>\n            <Button>حفظ الإعدادات</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAqCe,SAAS,eAAe,EAAE,SAAS,EAAuB;IACvE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAG/B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,qBAAqB;;0CACrG,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIhC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAKzC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,kBAAkB;;0CAClG,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIpC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAKzC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,qBAAqB;;0CACrG,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIlC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAKzC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,mBAAmB;;0CACnG,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIjC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAKzC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;wBAAmD,SAAS,IAAM,sBAAsB;;0CACtG,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIpC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,cAAc;gBACd,kBAAkB;0BAElB,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;;;;;;sCAI9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAIzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAIhC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,qBAAqB;8CAAQ;;;;;;8CAGtE,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAMd,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,cAAc;gBACd,kBAAkB;0BAElB,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;;;;;;sCAInD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,8OAAC,iIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAY,aAAY;;;;;;;;;;;;sDAEpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,8OAAC,iIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAW,aAAY;;;;;;;;;;;;;;;;;;8CAIrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,IAAG;4CAAQ,MAAK;4CAAQ,aAAY;;;;;;;;;;;;8CAG7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,IAAG;4CAAQ,aAAY;;;;;;;;;;;;8CAGhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,IAAG;4CAAQ,aAAY;4CAA2B,MAAM;;;;;;;;;;;;;;;;;;sCAGtE,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,kBAAkB;8CAAQ;;;;;;8CAGnE,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAMd,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,cAAc;gBACd,kBAAkB;0BAElB,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;sCAI/B,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,qBAAqB;8CAAQ;;;;;;8CAGtE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAMpC,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,cAAc;gBACd,kBAAkB;0BAElB,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;;;;;;sCAIjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAG;wCAAG;wCAAG;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;4CAAe,WAAU;sDACxB,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;2CADT;;;;;;;;;;8CAMd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGrC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAKxC,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,mBAAmB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,cAAc;gBACd,kBAAkB;0BAElB,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEAAM,MAAK;gEAAW,WAAU;;;;;;0EACjC,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEAAM,MAAK;gEAAW,WAAU;;;;;;0EACjC,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAKhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEAAM,MAAK;gEAAQ,MAAK;gEAAQ,WAAU;;;;;;0EAC3C,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEAAM,MAAK;gEAAQ,MAAK;gEAAQ,WAAU;;;;;;0EAC3C,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMpC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,sBAAsB;8CAAQ;;;;;;8CAGvE,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 3040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/dialog-test/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from 'react'\nimport DialogDemo from '@/components/ui/dialog-demo'\nimport DialogExamples from '@/components/ui/dialog-examples'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Eye } from 'lucide-react'\n\nexport default function DialogTestPage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6\">\n      <div className=\"max-w-6xl mx-auto space-y-8\">\n        {/* العنوان الرئيسي */}\n        <div className=\"text-center space-y-4\">\n          <div className=\"flex items-center justify-center gap-3\">\n            <div className=\"p-3 bg-blue-100 rounded-full\">\n              <MousePointer className=\"w-8 h-8 text-blue-600\" />\n            </div>\n            <h1 className=\"text-4xl font-bold text-gray-900\">\n              اختبار الشاشات المنبثقة التفاعلية\n            </h1>\n          </div>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            تجربة شاملة للشاشات المنبثقة المحسنة التي تتفاعل مع حركة الماوس وتوفر تجربة مستخدم متطورة\n          </p>\n        </div>\n\n        {/* بطاقات الميزات */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card className=\"border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"flex items-center gap-2 text-blue-900\">\n                <MousePointer className=\"w-5 h-5\" />\n                تتبع الماوس\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-blue-800\">\n                الحوار يتبع حركة الماوس بسلاسة ويتحرك معه في الوقت الفعلي\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-green-200 bg-gradient-to-br from-green-50 to-green-100\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"flex items-center gap-2 text-green-900\">\n                <Sparkles className=\"w-5 h-5\" />\n                انتقالات سلسة\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-green-800\">\n                انتقالات محسنة وسلسة مع تأثيرات بصرية جذابة\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"flex items-center gap-2 text-purple-900\">\n                <Zap className=\"w-5 h-5\" />\n                أداء محسن\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-purple-800\">\n                استخدام requestAnimationFrame لأداء سلس وسريع\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"flex items-center gap-2 text-orange-900\">\n                <Eye className=\"w-5 h-5\" />\n                حماية الحدود\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-orange-800\">\n                يبقى الحوار دائماً داخل حدود الشاشة المرئية\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* منطقة التجربة */}\n        <Card className=\"border-gray-200 shadow-lg\">\n          <CardHeader>\n            <CardTitle className=\"text-center text-2xl text-gray-900\">\n              منطقة التجربة التفاعلية\n            </CardTitle>\n            <p className=\"text-center text-gray-600\">\n              اختبر الأنواع المختلفة من الشاشات المنبثقة وشاهد كيف تتفاعل مع حركة الماوس\n            </p>\n          </CardHeader>\n          <CardContent>\n            <DialogDemo />\n          </CardContent>\n        </Card>\n\n        {/* أمثلة متنوعة */}\n        <Card className=\"border-gray-200 shadow-lg\">\n          <CardHeader>\n            <CardTitle className=\"text-center text-2xl text-gray-900\">\n              أمثلة متنوعة للاستخدام\n            </CardTitle>\n            <p className=\"text-center text-gray-600\">\n              تجربة أنواع مختلفة من المحتوى والتفاعلات مع الشاشات المنبثقة\n            </p>\n          </CardHeader>\n          <CardContent>\n            <DialogExamples />\n          </CardContent>\n        </Card>\n\n        {/* معلومات تقنية */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg text-gray-900\">الخصائص المتاحة</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <code className=\"text-sm font-mono\">followMouse</code>\n                  <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">boolean</span>\n                </div>\n                <p className=\"text-xs text-gray-600 pr-4\">تفعيل/إلغاء تتبع حركة الماوس</p>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <code className=\"text-sm font-mono\">centerOnOpen</code>\n                  <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">boolean</span>\n                </div>\n                <p className=\"text-xs text-gray-600 pr-4\">توسيط الحوار عند الفتح</p>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <code className=\"text-sm font-mono\">smoothTransition</code>\n                  <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">boolean</span>\n                </div>\n                <p className=\"text-xs text-gray-600 pr-4\">تفعيل الانتقالات السلسة</p>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg text-gray-900\">التحسينات التقنية</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">requestAnimationFrame</h4>\n                  <p className=\"text-sm text-gray-600\">لضمان انتقالات سلسة وأداء محسن</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Boundary Detection</h4>\n                  <p className=\"text-sm text-gray-600\">حماية من الخروج عن حدود الشاشة</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-2 h-2 bg-purple-500 rounded-full mt-2\"></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Event Optimization</h4>\n                  <p className=\"text-sm text-gray-600\">تحسين معالجة أحداث الماوس</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2\"></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Responsive Design</h4>\n                  <p className=\"text-sm text-gray-600\">يتكيف مع جميع أحجام الشاشات</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* تعليمات الاستخدام */}\n        <Card className=\"bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200\">\n          <CardHeader>\n            <CardTitle className=\"text-xl text-indigo-900\">كيفية الاستخدام</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-3\">\n                <h4 className=\"font-semibold text-indigo-900\">للمطورين:</h4>\n                <div className=\"bg-white p-4 rounded-lg border border-indigo-100\">\n                  <pre className=\"text-sm text-gray-800 overflow-x-auto\">\n{`<Dialog \n  open={isOpen}\n  onOpenChange={setIsOpen}\n  followMouse={true}\n  centerOnOpen={true}\n  smoothTransition={true}\n>\n  <DialogContent>\n    {/* المحتوى */}\n  </DialogContent>\n</Dialog>`}\n                  </pre>\n                </div>\n              </div>\n              \n              <div className=\"space-y-3\">\n                <h4 className=\"font-semibold text-indigo-900\">للمستخدمين:</h4>\n                <ol className=\"space-y-2 text-sm text-indigo-800\">\n                  <li className=\"flex items-start gap-2\">\n                    <span className=\"bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold\">1</span>\n                    <span>اضغط على أي زر لفتح الحوار</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <span className=\"bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold\">2</span>\n                    <span>حرك الماوس لترى الحوار يتبعه</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <span className=\"bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold\">3</span>\n                    <span>جرب الأنواع المختلفة من الحوارات</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <span className=\"bg-indigo-100 text-indigo-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold\">4</span>\n                    <span>لاحظ كيف يبقى الحوار داخل الشاشة</span>\n                  </li>\n                </ol>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;AAQe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;sCAInD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIxC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAMzC,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIpC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;sCAM1C,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAI/B,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;sCAM3C,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAI/B,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;8BAQ7C,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAqC;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;;;;;;;sCAI3C,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,0IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;8BAKf,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAqC;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;;;;;;;sCAI3C,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,8IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAwB;;;;;;;;;;;8CAE/C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,8OAAC;4DAAK,WAAU;sEAAsD;;;;;;;;;;;;8DAExE,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAG5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,8OAAC;4DAAK,WAAU;sEAAwD;;;;;;;;;;;;8DAE1E,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAG5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,8OAAC;4DAAK,WAAU;sEAA0D;;;;;;;;;;;;8DAE5E,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;sCAKhD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAwB;;;;;;;;;;;8CAE/C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA0B;;;;;;;;;;;sCAEjD,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAChC,CAAC;;;;;;;;;;SAUO,CAAC;;;;;;;;;;;;;;;;;kDAKI,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAwG;;;;;;0EACxH,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAwG;;;;;;0EACxH,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAwG;;;;;;0EACxH,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAwG;;;;;;0EACxH,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}]}