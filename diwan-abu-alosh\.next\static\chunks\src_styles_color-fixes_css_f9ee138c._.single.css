/* [project]/src/styles/color-fixes.css [app-client] (css) */
th, thead th, table th, .table th, [role="columnheader"], [data-testid*="table"] th, [class*="TableHead"], [class*="table-head"] {
  color: #fff !important;
  background-color: #191970 !important;
  border: none !important;
  font-weight: 600 !important;
}

[data-radix-collection-item], [data-state="open"] th, [data-state="closed"] th {
  color: #fff !important;
  background-color: #191970 !important;
}

button[style*="007bff"], .btn-primary, .bg-primary {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

button[style*="28a745"], .btn-success, .bg-success {
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

button[style*="dc3545"], .btn-danger, .bg-danger {
  color: #fff !important;
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}

.text-blue-600, .text-blue-500 {
  color: #0056cc !important;
}

.text-green-600, .text-green-500 {
  color: #1e7e34 !important;
}

.text-red-600, .text-red-500 {
  color: #c82333 !important;
}

p, span, div, label, td, li {
  color: #333 !important;
  font-weight: 500 !important;
}

.bg-white p, .bg-white span, .bg-white div, .bg-white label {
  color: #212529 !important;
}

.bg-gradient-to-br {
  background: #fff !important;
}

.card h1, .card h2, .card h3, .card h4, .card h5, .card h6 {
  color: #191970 !important;
  font-weight: 700 !important;
}

.card p, .card span, .card div {
  color: #212529 !important;
  font-weight: 500 !important;
}

.card-title, [data-testid="card-title"], h3[class*="text-sm"] {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.stats-card-value, div[class*="text-2xl"], .text-2xl {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.stats-card-title, .stats-card-description, [role="region"] h3, [role="region"] div[class*="text-2xl"], .card-content div, .card-header h3 {
  color: #212529 !important;
  font-weight: 700 !important;
}

.members-stats .card-title, .members-stats h3 {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.members-stats .text-2xl, .members-stats div[class*="font-bold"] {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.text-blue-600 svg, .text-green-600 svg, .text-red-600 svg {
  color: inherit !important;
}

.table-actions button {
  border: 1px solid #0000 !important;
  border-radius: .5rem !important;
  padding: .5rem !important;
  transition: all .2s !important;
}

button[style*="color: rgb(0, 123, 255)"], .table-actions .text-blue-600 {
  color: #0056cc !important;
  background-color: #0056cc1a !important;
  border-color: #0056cc33 !important;
}

button[style*="color: rgb(0, 123, 255)"]:hover, .table-actions .text-blue-600:hover {
  color: #003d82 !important;
  background-color: #0056cc33 !important;
}

button[style*="color: rgb(40, 167, 69)"], .table-actions .text-green-600 {
  color: #1e7e34 !important;
  background-color: #1e7e341a !important;
  border-color: #1e7e3433 !important;
}

button[style*="color: rgb(40, 167, 69)"]:hover, .table-actions .text-green-600:hover {
  color: #155724 !important;
  background-color: #1e7e3433 !important;
}

button[style*="color: rgb(220, 53, 69)"], .table-actions .text-red-600 {
  color: #c82333 !important;
  background-color: #c823331a !important;
  border-color: #c8233333 !important;
}

button[style*="color: rgb(220, 53, 69)"]:hover, .table-actions .text-red-600:hover {
  color: #a71e2a !important;
  background-color: #c8233333 !important;
}

button[style*="color: rgb(128, 0, 32)"], .table-actions .text-purple-600 {
  color: #800020 !important;
  background-color: #8000201a !important;
  border-color: #80002033 !important;
}

button[style*="color: rgb(128, 0, 32)"]:hover, .table-actions .text-purple-600:hover {
  color: #660019 !important;
  background-color: #80002033 !important;
}

button[style*="color: rgb(255, 193, 7)"], .table-actions .text-orange-600 {
  color: #b8860b !important;
  background-color: #b8860b1a !important;
  border-color: #b8860b33 !important;
}

button[style*="color: rgb(255, 193, 7)"]:hover, .table-actions .text-orange-600:hover {
  color: #9a7209 !important;
  background-color: #b8860b33 !important;
}

table, .table {
  background-color: #fff !important;
}

table tbody tr:hover, .table tbody tr:hover {
  background-color: #f8f9fa !important;
}

table td, .table td {
  color: #212529 !important;
  border-bottom: 1px solid #f3f4f6 !important;
  font-weight: 500 !important;
}

table td span, table td div, .table td span, .table td div {
  color: #212529 !important;
  font-weight: 500 !important;
}

.badge {
  padding: .375rem .75rem !important;
  font-size: .75rem !important;
  font-weight: 600 !important;
}

.pagination button {
  color: #333 !important;
  background-color: #fff !important;
  border: 1px solid #dee2e6 !important;
}

.pagination button:hover {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.pagination button:disabled {
  color: #6c757d !important;
  background-color: #f8f9fa !important;
  border-color: #dee2e6 !important;
}

.search-container button {
  color: #fff !important;
  background-color: #007bff !important;
  border: none !important;
}

.search-container button:hover {
  background-color: #0056cc !important;
}

input, select, textarea {
  color: #333 !important;
  background-color: #fff !important;
  border: 1px solid #dee2e6 !important;
}

input:focus, select:focus, textarea:focus {
  border-color: #007bff !important;
  box-shadow: 0 0 0 .2rem #007bff40 !important;
}

.badge, .badge-primary {
  color: #007bff !important;
  background-color: #007bff1a !important;
  border: 1px solid #007bff33 !important;
}

.badge-success {
  color: #28a745 !important;
  background-color: #28a7451a !important;
  border: 1px solid #28a74533 !important;
}

.badge-danger {
  color: #dc3545 !important;
  background-color: #dc35451a !important;
  border: 1px solid #dc354533 !important;
}

.badge-warning {
  color: #191970 !important;
  background-color: #1919701a !important;
  border: 1px solid #19197033 !important;
}

.alert-primary {
  color: #004085 !important;
  background-color: #007bff1a !important;
  border-color: #007bff !important;
}

.alert-success {
  color: #155724 !important;
  background-color: #28a7451a !important;
  border-color: #28a745 !important;
}

.alert-danger {
  color: #721c24 !important;
  background-color: #dc35451a !important;
  border-color: #dc3545 !important;
}

a {
  color: #007bff !important;
}

a:hover {
  color: #0056cc !important;
}

.text-primary {
  color: #007bff !important;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #191970 !important;
}

.text-info {
  color: #17a2b8 !important;
}

.bg-primary {
  color: #fff !important;
  background-color: #007bff !important;
}

.bg-success {
  color: #fff !important;
  background-color: #28a745 !important;
}

.bg-danger {
  color: #fff !important;
  background-color: #dc3545 !important;
}

.bg-warning {
  color: #fff !important;
  background-color: #191970 !important;
}

.bg-info {
  color: #fff !important;
  background-color: #17a2b8 !important;
}

.border-primary {
  border-color: #007bff !important;
}

.border-success {
  border-color: #28a745 !important;
}

.border-danger {
  border-color: #dc3545 !important;
}

.border-warning {
  border-color: #191970 !important;
}

.border-info {
  border-color: #17a2b8 !important;
}

body {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-family: Cairo, Almarai, system-ui, sans-serif !important;
}

.text-xs, .text-sm {
  color: #495057 !important;
  font-weight: 600 !important;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6c757d !important;
  font-weight: 500 !important;
}

input::placeholder, textarea::placeholder, select option {
  color: #6c757d !important;
  font-weight: 500 !important;
}

.dropdown-menu {
  color: #212529 !important;
}

.dropdown-item {
  color: #212529 !important;
  font-weight: 500 !important;
}

.dropdown-item:hover {
  color: #191970 !important;
  background-color: #f8f9fa !important;
}

.nav-link {
  color: #495057 !important;
  font-weight: 500 !important;
}

.nav-link.active {
  color: #191970 !important;
  font-weight: 600 !important;
}

.sidebar a {
  color: #e9ecef !important;
  font-weight: 500 !important;
}

.sidebar a:hover {
  color: #fff !important;
  font-weight: 600 !important;
}

* {
  text-rendering: optimizeLegibility !important;
}

.bg-primary *, .bg-success *, .bg-danger *, .bg-warning *, .bg-info * {
  text-shadow: 0 1px 2px #0000001a !important;
}

/*# sourceMappingURL=src_styles_color-fixes_css_f9ee138c._.single.css.map*/