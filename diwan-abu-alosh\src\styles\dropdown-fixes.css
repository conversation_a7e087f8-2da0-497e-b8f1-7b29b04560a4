/* إصلاحات ألوان القوائم المنسدلة */

/* إصلاح ألوان Select العامة */
[data-radix-select-content] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

[data-radix-select-item] {
  color: black !important;
  background-color: transparent !important;
}

[data-radix-select-item]:hover,
[data-radix-select-item][data-highlighted] {
  background-color: #dbeafe !important; /* blue-50 */
  color: #1e40af !important; /* blue-800 */
}

[data-radix-select-item][data-state="checked"] {
  background-color: #dbeafe !important; /* blue-100 */
  color: #1e40af !important; /* blue-800 */
}

[data-radix-select-trigger] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

[data-radix-select-trigger]:hover {
  border-color: #9ca3af !important; /* gray-400 */
}

[data-radix-select-trigger]:focus {
  border-color: #3b82f6 !important; /* blue-500 */
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2) !important;
}

[data-radix-select-trigger] [data-radix-select-icon] {
  color: #6b7280 !important; /* gray-600 */
}

/* إصلاح ألوان Dropdown Menu */
[data-radix-dropdown-menu-content] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

[data-radix-dropdown-menu-item] {
  color: black !important;
  background-color: transparent !important;
}

[data-radix-dropdown-menu-item]:hover,
[data-radix-dropdown-menu-item][data-highlighted] {
  background-color: #dbeafe !important; /* blue-50 */
  color: #1e40af !important; /* blue-800 */
}

[data-radix-dropdown-menu-label] {
  color: #374151 !important; /* gray-700 */
}

/* إصلاح ألوان Combobox إذا كان موجوداً */
[role="combobox"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

[role="listbox"] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

[role="option"] {
  color: black !important;
  background-color: transparent !important;
}

[role="option"]:hover,
[role="option"][aria-selected="true"] {
  background-color: #dbeafe !important; /* blue-50 */
  color: #1e40af !important; /* blue-800 */
}

/* إصلاح ألوان Enhanced Select المخصص */
.enhanced-select-content {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

.enhanced-select-item {
  color: black !important;
  background-color: transparent !important;
}

.enhanced-select-item:hover {
  background-color: #dbeafe !important; /* blue-50 */
  color: #1e40af !important; /* blue-800 */
}

.enhanced-select-item[data-selected="true"] {
  background-color: #dbeafe !important; /* blue-100 */
  color: #1e40af !important; /* blue-800 */
}

/* إصلاح ألوان Native Select */
select {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

select:hover {
  border-color: #9ca3af !important; /* gray-400 */
}

select:focus {
  border-color: #3b82f6 !important; /* blue-500 */
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2) !important;
}

select option {
  background-color: white !important;
  color: black !important;
}

select option:hover,
select option:checked {
  background-color: #dbeafe !important; /* blue-50 */
  color: #1e40af !important; /* blue-800 */
}

/* إصلاح ألوان Autocomplete */
[role="textbox"][aria-autocomplete] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح ألوان Popover */
[data-radix-popover-content] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

/* إصلاح ألوان Command */
[cmdk-root] {
  background-color: white !important;
  color: black !important;
}

[cmdk-input] {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

[cmdk-item] {
  color: black !important;
  background-color: transparent !important;
}

[cmdk-item]:hover,
[cmdk-item][data-selected="true"] {
  background-color: #dbeafe !important; /* blue-50 */
  color: #1e40af !important; /* blue-800 */
}

/* إصلاح ألوان عامة للقوائم المنسدلة */
.dropdown-content,
.select-content,
.combobox-content,
.popover-content {
  background-color: white !important;
  color: black !important;
  border-color: #d1d5db !important; /* gray-300 */
}

.dropdown-item,
.select-item,
.combobox-item,
.popover-item {
  color: black !important;
  background-color: transparent !important;
}

.dropdown-item:hover,
.select-item:hover,
.combobox-item:hover,
.popover-item:hover,
.dropdown-item[data-highlighted],
.select-item[data-highlighted],
.combobox-item[data-highlighted],
.popover-item[data-highlighted] {
  background-color: #dbeafe !important; /* blue-50 */
  color: #1e40af !important; /* blue-800 */
}

/* إصلاح ألوان النصوص المساعدة */
.dropdown-label,
.select-label,
.combobox-label {
  color: #374151 !important; /* gray-700 */
}

.dropdown-placeholder,
.select-placeholder,
.combobox-placeholder {
  color: #6b7280 !important; /* gray-500 */
}

/* إصلاح ألوان الأيقونات */
.dropdown-icon,
.select-icon,
.combobox-icon {
  color: #6b7280 !important; /* gray-600 */
}

/* إصلاح ألوان الحدود عند التركيز */
.dropdown-trigger:focus,
.select-trigger:focus,
.combobox-trigger:focus {
  border-color: #3b82f6 !important; /* blue-500 */
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2) !important;
}

/* إصلاح ألوان الحالة المعطلة */
.dropdown-trigger:disabled,
.select-trigger:disabled,
.combobox-trigger:disabled {
  background-color: #f9fafb !important; /* gray-50 */
  color: #9ca3af !important; /* gray-400 */
  border-color: #e5e7eb !important; /* gray-200 */
}

/* إصلاح ألوان الحالة الخطأ */
.dropdown-trigger[data-error="true"],
.select-trigger[data-error="true"],
.combobox-trigger[data-error="true"] {
  border-color: #ef4444 !important; /* red-500 */
  color: #dc2626 !important; /* red-600 */
}

.dropdown-trigger[data-error="true"]:focus,
.select-trigger[data-error="true"]:focus,
.combobox-trigger[data-error="true"]:focus {
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2) !important;
}

/* إصلاح ألوان شريط التمرير في القوائم */
.dropdown-content::-webkit-scrollbar,
.select-content::-webkit-scrollbar,
.combobox-content::-webkit-scrollbar {
  width: 8px;
}

.dropdown-content::-webkit-scrollbar-track,
.select-content::-webkit-scrollbar-track,
.combobox-content::-webkit-scrollbar-track {
  background: #f3f4f6; /* gray-100 */
  border-radius: 4px;
}

.dropdown-content::-webkit-scrollbar-thumb,
.select-content::-webkit-scrollbar-thumb,
.combobox-content::-webkit-scrollbar-thumb {
  background: #d1d5db; /* gray-300 */
  border-radius: 4px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover,
.select-content::-webkit-scrollbar-thumb:hover,
.combobox-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af; /* gray-400 */
}

/* إصلاح ألوان الوضع الداكن (إذا كان مفعلاً) */
@media (prefers-color-scheme: dark) {
  /* تجاهل الوضع الداكن وفرض الألوان الفاتحة */
  [data-radix-select-content],
  [data-radix-dropdown-menu-content],
  [role="listbox"],
  .dropdown-content,
  .select-content,
  .combobox-content {
    background-color: white !important;
    color: black !important;
    border-color: #d1d5db !important;
  }
}
