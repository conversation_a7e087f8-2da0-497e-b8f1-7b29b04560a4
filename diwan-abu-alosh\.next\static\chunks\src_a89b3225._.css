@import "https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Almarai:wght@300;400;700;800&display=swap";
/* [project]/src/app/globals.css [app-client] (css) */
*, :before, :after, ::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

*, :before, :after {
  box-sizing: border-box;
  border: 0 solid #e5e7eb;
}

:before, :after {
  --tw-content: "";
}

html, :host {
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
  font-family: Cairo, Tajawal, system-ui, sans-serif;
  line-height: 1.5;
}

body {
  line-height: inherit;
  margin: 0;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button, input:where([type="button"]), input:where([type="reset"]), input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: #0000;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  margin: 0;
  padding: 0;
  list-style: none;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  vertical-align: middle;
  display: block;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.sr-only {
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.inset-0 {
  inset: 0;
}

.inset-y-0 {
  top: 0;
  bottom: 0;
}

.-right-1 {
  right: -.25rem;
}

.-top-1 {
  top: -.25rem;
}

.bottom-0 {
  bottom: 0;
}

.bottom-2 {
  bottom: .5rem;
}

.bottom-4 {
  bottom: 1rem;
}

.left-0 {
  left: 0;
}

.left-1\/2 {
  left: 50%;
}

.left-2 {
  left: .5rem;
}

.left-3 {
  left: .75rem;
}

.left-4 {
  left: 1rem;
}

.right-0 {
  right: 0;
}

.right-2 {
  right: .5rem;
}

.right-3 {
  right: .75rem;
}

.right-4 {
  right: 1rem;
}

.top-1\/2 {
  top: 50%;
}

.top-2 {
  top: .5rem;
}

.top-3 {
  top: .75rem;
}

.top-4 {
  top: 1rem;
}

.top-\[1vh\] {
  top: 1vh;
}

.-z-10 {
  z-index: -10;
}

.z-10 {
  z-index: 10;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[9996\] {
  z-index: 9996;
}

.z-\[9997\] {
  z-index: 9997;
}

.z-\[9998\] {
  z-index: 9998;
}

.z-\[9999\] {
  z-index: 9999;
}

.-mx-1 {
  margin-left: -.25rem;
  margin-right: -.25rem;
}

.-mx-6 {
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-1 {
  margin-top: .25rem;
  margin-bottom: .25rem;
}

.mb-1 {
  margin-bottom: .25rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-2 {
  margin-bottom: .5rem;
}

.mb-3 {
  margin-bottom: .75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-1 {
  margin-left: .25rem;
}

.ml-2 {
  margin-left: .5rem;
}

.ml-3 {
  margin-left: .75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: .25rem;
}

.mr-2 {
  margin-right: .5rem;
}

.mr-3 {
  margin-right: .75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-auto {
  margin-right: auto;
}

.mt-0\.5 {
  margin-top: .125rem;
}

.mt-1 {
  margin-top: .25rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: .5rem;
}

.mt-3 {
  margin-top: .75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.aspect-square {
  aspect-ratio: 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.h-1 {
  height: .25rem;
}

.h-1\.5 {
  height: .375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: .5rem;
}

.h-2\.5 {
  height: .625rem;
}

.h-20 {
  height: 5rem;
}

.h-24 {
  height: 6rem;
}

.h-3 {
  height: .75rem;
}

.h-3\.5 {
  height: .875rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-64 {
  height: 16rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-80 {
  height: 20rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[85vh\] {
  height: 85vh;
}

.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.max-h-16 {
  max-height: 4rem;
}

.max-h-40 {
  max-height: 10rem;
}

.max-h-48 {
  max-height: 12rem;
}

.max-h-60 {
  max-height: 15rem;
}

.max-h-64 {
  max-height: 16rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[70vh\] {
  max-height: 70vh;
}

.max-h-\[80vh\] {
  max-height: 80vh;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.max-h-\[95vh\] {
  max-height: 95vh;
}

.max-h-full {
  max-height: 100%;
}

.max-h-none {
  max-height: none;
}

.min-h-\[100px\] {
  min-height: 100px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-1 {
  width: .25rem;
}

.w-1\.5 {
  width: .375rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.3333%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: .5rem;
}

.w-2\.5 {
  width: .625rem;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-3 {
  width: .75rem;
}

.w-3\.5 {
  width: .875rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/6 {
  width: 83.3333%;
}

.w-56 {
  width: 14rem;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-8 {
  width: 2rem;
}

.w-9 {
  width: 2.25rem;
}

.w-\[50vw\] {
  width: 50vw;
}

.w-full {
  width: 100%;
}

.min-w-0 {
  min-width: 0;
}

.min-w-\[150px\] {
  min-width: 150px;
}

.min-w-\[200px\] {
  min-width: 200px;
}

.min-w-\[8rem\] {
  min-width: 8rem;
}

.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[50vw\] {
  max-width: 50vw;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-sm {
  max-width: 24rem;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0, .shrink-0 {
  flex-shrink: 0;
}

.caption-bottom {
  caption-side: bottom;
}

.border-collapse {
  border-collapse: collapse;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-5 {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-100\%\] {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-skew-x-12 {
  --tw-skew-x: -12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}

.animate-bounce {
  animation: 1s infinite bounce;
}

@keyframes ping {
  75%, 100% {
    opacity: 0;
    transform: scale(2);
  }
}

.animate-ping {
  animation: 1s cubic-bezier(0, 0, .2, 1) infinite ping;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: 1s linear infinite spin;
}

.cursor-default {
  cursor: default;
}

.cursor-move {
  cursor: move;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.resize-none {
  resize: none;
}

.resize {
  resize: both;
}

.list-inside {
  list-style-position: inside;
}

.list-disc {
  list-style-type: disc;
}

.appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: .25rem;
}

.gap-2 {
  gap: .5rem;
}

.gap-3 {
  gap: .75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.25rem * var(--tw-space-x-reverse));
  margin-left: calc(.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.5rem * var(--tw-space-x-reverse));
  margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.75rem * var(--tw-space-x-reverse));
  margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.375rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity, 1));
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: .25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: var(--radius);
}

.rounded-md {
  border-radius: calc(var(--radius)  - 2px);
}

.rounded-sm {
  border-radius: calc(var(--radius)  - 4px);
}

.rounded-xl {
  border-radius: .75rem;
}

.rounded-b-xl {
  border-bottom-right-radius: .75rem;
  border-bottom-left-radius: .75rem;
}

.rounded-r-full {
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.rounded-t-2xl {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}

.rounded-t-lg {
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}

.rounded-t-xl {
  border-top-left-radius: .75rem;
  border-top-right-radius: .75rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-0 {
  border-bottom-width: 0;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-r-4 {
  border-right-width: 4px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-2 {
  border-top-width: 2px;
}

.border-dashed {
  border-style: dashed;
}

.border-amber-200 {
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));
}

.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}

.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}

.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.border-danger-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}

.border-destructive\/50 {
  border-color: hsl(var(--destructive) / .5);
}

.border-emerald-200 {
  --tw-border-opacity: 1;
  border-color: rgb(167 243 208 / var(--tw-border-opacity, 1));
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-green-100 {
  --tw-border-opacity: 1;
  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));
}

.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}

.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}

.border-indigo-100 {
  --tw-border-opacity: 1;
  border-color: rgb(224 231 255 / var(--tw-border-opacity, 1));
}

.border-indigo-200 {
  --tw-border-opacity: 1;
  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));
}

.border-indigo-300 {
  --tw-border-opacity: 1;
  border-color: rgb(165 180 252 / var(--tw-border-opacity, 1));
}

.border-indigo-600 {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}

.border-input {
  border-color: hsl(var(--input));
}

.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}

.border-primary-300 {
  --tw-border-opacity: 1;
  border-color: rgb(102 184 255 / var(--tw-border-opacity, 1));
}

.border-purple-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));
}

.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}

.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.border-purple-600 {
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}

.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}

.border-secondary-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}

.border-sky-100 {
  --tw-border-opacity: 1;
  border-color: rgb(224 242 254 / var(--tw-border-opacity, 1));
}

.border-sky-200 {
  --tw-border-opacity: 1;
  border-color: rgb(186 230 253 / var(--tw-border-opacity, 1));
}

.border-sky-500 {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
}

.border-sky-600 {
  --tw-border-opacity: 1;
  border-color: rgb(2 132 199 / var(--tw-border-opacity, 1));
}

.border-slate-100 {
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
}

.border-slate-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}

.border-slate-300 {
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}

.border-slate-500 {
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}

.border-slate-700 {
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: #0000;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-white\/30 {
  border-color: #ffffff4d;
}

.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}

.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}

.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.border-t-red-600 {
  --tw-border-opacity: 1;
  border-top-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}

.border-t-slate-600 {
  --tw-border-opacity: 1;
  border-top-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}

.border-t-transparent {
  border-top-color: #0000;
}

.border-t-white {
  --tw-border-opacity: 1;
  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-t-white\/60 {
  border-top-color: #fff9;
}

.border-opacity-20 {
  --tw-border-opacity: .2;
}

.border-opacity-30 {
  --tw-border-opacity: .3;
}

.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}

.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}

.bg-background {
  background-color: hsl(var(--background));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/0 {
  background-color: #0000;
}

.bg-black\/60 {
  background-color: #0009;
}

.bg-black\/70 {
  background-color: #000000b3;
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-destructive {
  background-color: hsl(var(--destructive));
}

.bg-emerald-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}

.bg-gold-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-50\/30 {
  background-color: #f9fafb4d;
}

.bg-gray-50\/80 {
  background-color: #f9fafbcc;
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}

.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}

.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.bg-input {
  background-color: hsl(var(--input));
}

.bg-muted {
  background-color: hsl(var(--muted));
}

.bg-muted\/50 {
  background-color: hsl(var(--muted) / .5);
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-orange-500\/80 {
  background-color: #f97316cc;
}

.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.bg-popover {
  background-color: hsl(var(--popover));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.bg-secondary-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}

.bg-sky-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));
}

.bg-sky-50\/50 {
  background-color: #f0f9ff80;
}

.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));
}

.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

.bg-slate-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}

.bg-slate-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: #0000;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/20 {
  background-color: #fff3;
}

.bg-white\/40 {
  background-color: #fff6;
}

.bg-white\/50 {
  background-color: #ffffff80;
}

.bg-white\/80 {
  background-color: #fffc;
}

.bg-white\/90 {
  background-color: #ffffffe6;
}

.bg-white\/95 {
  background-color: #fffffff2;
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-gray-900\/90 {
  background-color: #111827e6;
}

.bg-opacity-0 {
  --tw-bg-opacity: 0;
}

.bg-opacity-10 {
  --tw-bg-opacity: .1;
}

.bg-opacity-20 {
  --tw-bg-opacity: .2;
}

.bg-opacity-40 {
  --tw-bg-opacity: .4;
}

.bg-opacity-50 {
  --tw-bg-opacity: .5;
}

.bg-opacity-60 {
  --tw-bg-opacity: .6;
}

.bg-opacity-75 {
  --tw-bg-opacity: .75;
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-amber-50 {
  --tw-gradient-from: #fffbeb var(--tw-gradient-from-position);
  --tw-gradient-to: #fffbeb00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/50 {
  --tw-gradient-from: #00000080 var(--tw-gradient-from-position);
  --tw-gradient-to: #0000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: #60a5fa00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: #eff6ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: #3b82f600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: #2563eb00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-danger-500 {
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: #ef444400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-emerald-600 {
  --tw-gradient-from: #059669 var(--tw-gradient-from-position);
  --tw-gradient-to: #05966900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gold-500 {
  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);
  --tw-gradient-to: #f59e0b00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-50 {
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: #f9fafb00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-500 {
  --tw-gradient-from: #6b7280 var(--tw-gradient-from-position);
  --tw-gradient-to: #6b728000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-700 {
  --tw-gradient-from: #374151 var(--tw-gradient-from-position);
  --tw-gradient-to: #37415100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-100 {
  --tw-gradient-from: #dcfce7 var(--tw-gradient-from-position);
  --tw-gradient-to: #dcfce700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-400 {
  --tw-gradient-from: #4ade80 var(--tw-gradient-from-position);
  --tw-gradient-to: #4ade8000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-50 {
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: #f0fdf400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: #22c55e00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-600 {
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: #16a34a00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-indigo-100 {
  --tw-gradient-from: #e0e7ff var(--tw-gradient-from-position);
  --tw-gradient-to: #e0e7ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-indigo-400 {
  --tw-gradient-from: #818cf8 var(--tw-gradient-from-position);
  --tw-gradient-to: #818cf800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-indigo-50 {
  --tw-gradient-from: #eef2ff var(--tw-gradient-from-position);
  --tw-gradient-to: #eef2ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-indigo-500 {
  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);
  --tw-gradient-to: #6366f100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-indigo-600 {
  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);
  --tw-gradient-to: #4f46e500 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-info-500 {
  --tw-gradient-from: #0ea5e9 var(--tw-gradient-from-position);
  --tw-gradient-to: #0ea5e900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-orange-50 {
  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);
  --tw-gradient-to: #fff7ed00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-orange-500 {
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: #f9731600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-500 {
  --tw-gradient-from: #007bff var(--tw-gradient-from-position);
  --tw-gradient-to: #007bff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-400 {
  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);
  --tw-gradient-to: #c084fc00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-50 {
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: #faf5ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-500 {
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: #a855f700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-600 {
  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);
  --tw-gradient-to: #9333ea00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-400 {
  --tw-gradient-from: #f87171 var(--tw-gradient-from-position);
  --tw-gradient-to: #f8717100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-50 {
  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);
  --tw-gradient-to: #fef2f200 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-500 {
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: #ef444400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-600 {
  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);
  --tw-gradient-to: #dc262600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-secondary-100 {
  --tw-gradient-from: #f1f5f9 var(--tw-gradient-from-position);
  --tw-gradient-to: #f1f5f900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-secondary-50 {
  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);
  --tw-gradient-to: #f8fafc00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-sky-50 {
  --tw-gradient-from: #f0f9ff var(--tw-gradient-from-position);
  --tw-gradient-to: #f0f9ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-sky-500 {
  --tw-gradient-from: #0ea5e9 var(--tw-gradient-from-position);
  --tw-gradient-to: #0ea5e900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-sky-600 {
  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);
  --tw-gradient-to: #0284c700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-slate-100 {
  --tw-gradient-from: #f1f5f9 var(--tw-gradient-from-position);
  --tw-gradient-to: #f1f5f900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-slate-50 {
  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);
  --tw-gradient-to: #f8fafc00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-slate-700 {
  --tw-gradient-from: #334155 var(--tw-gradient-from-position);
  --tw-gradient-to: #33415500 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-slate-800 {
  --tw-gradient-from: #1e293b var(--tw-gradient-from-position);
  --tw-gradient-to: #1e293b00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-slate-900 {
  --tw-gradient-from: #0f172a var(--tw-gradient-from-position);
  --tw-gradient-to: #0f172a00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-success-500 {
  --tw-gradient-from: #28a745 var(--tw-gradient-from-position);
  --tw-gradient-to: #28a74500 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: #0000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-warning-500 {
  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);
  --tw-gradient-to: #f59e0b00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white\/20 {
  --tw-gradient-from: #fff3 var(--tw-gradient-from-position);
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-yellow-500 {
  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);
  --tw-gradient-to: #eab30800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-blue-400\/20 {
  --tw-gradient-to: #60a5fa00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #60a5fa33 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-blue-50 {
  --tw-gradient-to: #eff6ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #eff6ff var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-blue-600\/5 {
  --tw-gradient-to: #2563eb00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #2563eb0d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-green-600 {
  --tw-gradient-to: #16a34a00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #16a34a var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-indigo-50 {
  --tw-gradient-to: #eef2ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #eef2ff var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-700 {
  --tw-gradient-to: #7e22ce00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #7e22ce var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-red-700 {
  --tw-gradient-to: #b91c1c00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #b91c1c var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-transparent {
  --tw-gradient-to: #0000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white {
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/10 {
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ffffff1a var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-blue-100 {
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}

.to-blue-50 {
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}

.to-blue-500 {
  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}

.to-blue-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}

.to-blue-700 {
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}

.to-danger-600 {
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}

.to-emerald-100 {
  --tw-gradient-to: #d1fae5 var(--tw-gradient-to-position);
}

.to-emerald-50 {
  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);
}

.to-emerald-500 {
  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);
}

.to-emerald-600 {
  --tw-gradient-to: #059669 var(--tw-gradient-to-position);
}

.to-gold-600 {
  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);
}

.to-gray-100 {
  --tw-gradient-to: #f3f4f6 var(--tw-gradient-to-position);
}

.to-gray-50 {
  --tw-gradient-to: #f9fafb var(--tw-gradient-to-position);
}

.to-gray-600 {
  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);
}

.to-gray-800 {
  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);
}

.to-green-100 {
  --tw-gradient-to: #dcfce7 var(--tw-gradient-to-position);
}

.to-green-50 {
  --tw-gradient-to: #f0fdf4 var(--tw-gradient-to-position);
}

.to-green-600 {
  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);
}

.to-green-700 {
  --tw-gradient-to: #15803d var(--tw-gradient-to-position);
}

.to-indigo-100 {
  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);
}

.to-indigo-400\/20 {
  --tw-gradient-to: #818cf833 var(--tw-gradient-to-position);
}

.to-indigo-50 {
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.to-indigo-600 {
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}

.to-indigo-600\/5 {
  --tw-gradient-to: #4f46e50d var(--tw-gradient-to-position);
}

.to-indigo-700 {
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}

.to-info-600 {
  --tw-gradient-to: #0284c7 var(--tw-gradient-to-position);
}

.to-orange-100 {
  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);
}

.to-orange-50 {
  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);
}

.to-orange-500 {
  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);
}

.to-orange-600 {
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}

.to-pink-50 {
  --tw-gradient-to: #fdf2f8 var(--tw-gradient-to-position);
}

.to-pink-500 {
  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);
}

.to-pink-600 {
  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);
}

.to-primary-600 {
  --tw-gradient-to: #0056cc var(--tw-gradient-to-position);
}

.to-purple-100 {
  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);
}

.to-purple-50 {
  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);
}

.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}

.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}

.to-red-100 {
  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);
}

.to-red-50 {
  --tw-gradient-to: #fef2f2 var(--tw-gradient-to-position);
}

.to-red-600 {
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}

.to-secondary-100 {
  --tw-gradient-to: #f1f5f9 var(--tw-gradient-to-position);
}

.to-secondary-200 {
  --tw-gradient-to: #e2e8f0 var(--tw-gradient-to-position);
}

.to-sky-600 {
  --tw-gradient-to: #0284c7 var(--tw-gradient-to-position);
}

.to-sky-700 {
  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);
}

.to-slate-100 {
  --tw-gradient-to: #f1f5f9 var(--tw-gradient-to-position);
}

.to-slate-50 {
  --tw-gradient-to: #f8fafc var(--tw-gradient-to-position);
}

.to-slate-800 {
  --tw-gradient-to: #1e293b var(--tw-gradient-to-position);
}

.to-slate-900 {
  --tw-gradient-to: #0f172a var(--tw-gradient-to-position);
}

.to-success-600 {
  --tw-gradient-to: #228b3c var(--tw-gradient-to-position);
}

.to-teal-600 {
  --tw-gradient-to: #0d9488 var(--tw-gradient-to-position);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.to-warning-600 {
  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);
}

.to-yellow-600 {
  --tw-gradient-to: #ca8a04 var(--tw-gradient-to-position);
}

.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.fill-current {
  fill: currentColor;
}

.object-contain {
  -o-object-fit: contain;
  object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
  object-fit: cover;
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: .25rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-12 {
  padding: 3rem;
}

.p-2 {
  padding: .5rem;
}

.p-3 {
  padding: .75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-2 {
  padding-left: .5rem;
  padding-right: .5rem;
}

.px-2\.5 {
  padding-left: .625rem;
  padding-right: .625rem;
}

.px-3 {
  padding-left: .75rem;
  padding-right: .75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0\.5 {
  padding-top: .125rem;
  padding-bottom: .125rem;
}

.py-1 {
  padding-top: .25rem;
  padding-bottom: .25rem;
}

.py-1\.5 {
  padding-top: .375rem;
  padding-bottom: .375rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-2\.5 {
  padding-top: .625rem;
  padding-bottom: .625rem;
}

.py-3 {
  padding-top: .75rem;
  padding-bottom: .75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-0 {
  padding-bottom: 0;
}

.pb-1 {
  padding-bottom: .25rem;
}

.pb-2 {
  padding-bottom: .5rem;
}

.pb-3 {
  padding-bottom: .75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-12 {
  padding-left: 3rem;
}

.pl-3 {
  padding-left: .75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pr-2 {
  padding-right: .5rem;
}

.pr-3 {
  padding-right: .75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pt-0 {
  padding-top: 0;
}

.pt-2 {
  padding-top: .5rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-middle {
  vertical-align: middle;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: .75rem;
  line-height: 1rem;
}

.font-black {
  font-weight: 900;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.italic {
  font-style: italic;
}

.leading-none {
  line-height: 1;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-tight {
  letter-spacing: -.025em;
}

.tracking-widest {
  letter-spacing: .1em;
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}

.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}

.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}

.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.text-danger-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-danger-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-destructive {
  color: hsl(var(--destructive));
}

.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}

.text-emerald-600 {
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}

.text-emerald-700 {
  --tw-text-opacity: 1;
  color: rgb(4 120 87 / var(--tw-text-opacity, 1));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-100 {
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-green-900 {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

.text-indigo-100 {
  --tw-text-opacity: 1;
  color: rgb(224 231 255 / var(--tw-text-opacity, 1));
}

.text-indigo-400 {
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}

.text-indigo-500 {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}

.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}

.text-indigo-900 {
  --tw-text-opacity: 1;
  color: rgb(49 46 129 / var(--tw-text-opacity, 1));
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}

.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.text-orange-900 {
  --tw-text-opacity: 1;
  color: rgb(124 45 18 / var(--tw-text-opacity, 1));
}

.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}

.text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(0 86 204 / var(--tw-text-opacity, 1));
}

.text-primary-700 {
  --tw-text-opacity: 1;
  color: rgb(0 64 153 / var(--tw-text-opacity, 1));
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.text-purple-100 {
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}

.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}

.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}

.text-purple-900 {
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}

.text-red-100 {
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}

.text-red-200 {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-secondary-400 {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

.text-secondary-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.text-secondary-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}

.text-secondary-700 {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}

.text-secondary-800 {
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}

.text-sky-100 {
  --tw-text-opacity: 1;
  color: rgb(224 242 254 / var(--tw-text-opacity, 1));
}

.text-sky-600 {
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity, 1));
}

.text-sky-700 {
  --tw-text-opacity: 1;
  color: rgb(3 105 161 / var(--tw-text-opacity, 1));
}

.text-sky-800 {
  --tw-text-opacity: 1;
  color: rgb(7 89 133 / var(--tw-text-opacity, 1));
}

.text-slate-300 {
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}

.text-slate-400 {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.text-slate-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}

.text-slate-700 {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}

.text-slate-800 {
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}

.text-transparent {
  color: #0000;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}

.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.text-red-900 {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.opacity-0 {
  opacity: 0;
}

.opacity-20 {
  opacity: .2;
}

.opacity-30 {
  opacity: .3;
}

.opacity-50 {
  opacity: .5;
}

.opacity-60 {
  opacity: .6;
}

.opacity-80 {
  opacity: .8;
}

.opacity-90 {
  opacity: .9;
}

.opacity-70 {
  opacity: .7;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a;
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px #00000040;
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 #0000000d;
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000001a;
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline-offset: 2px;
  outline: 2px solid #0000;
}

.outline {
  outline-style: solid;
}

.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}

.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px #0000001a) drop-shadow(0 1px 1px #0000000f);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-all {
  transition-property: all;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-opacity {
  transition-property: opacity;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-shadow {
  transition-property: box-shadow;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-transform {
  transition-property: transform;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.duration-150 {
  transition-duration: .15s;
}

.duration-200 {
  transition-duration: .2s;
}

.duration-300 {
  transition-duration: .3s;
}

.duration-500 {
  transition-duration: .5s;
}

.duration-700 {
  transition-duration: .7s;
}

.duration-100 {
  transition-duration: .1s;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, .2, 1);
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

.animate-in {
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
  animation-name: enter;
  animation-duration: .15s;
}

.fade-in-0 {
  --tw-enter-opacity: 0;
}

.zoom-in-95 {
  --tw-enter-scale: .95;
}

.slide-in-from-top-2 {
  --tw-enter-translate-y: -.5rem;
}

.duration-150 {
  animation-duration: .15s;
}

.duration-200 {
  animation-duration: .2s;
}

.duration-300 {
  animation-duration: .3s;
}

.duration-500 {
  animation-duration: .5s;
}

.duration-700 {
  animation-duration: .7s;
}

.duration-100 {
  animation-duration: .1s;
}

.ease-out {
  animation-timing-function: cubic-bezier(0, 0, .2, 1);
}

:root {
  --background: 249 249 249;
  --foreground: 51 51 51;
  --card: 255 255 255;
  --card-foreground: 51 51 51;
  --popover: 255 255 255;
  --popover-foreground: 51 51 51;
  --primary: 0 123 255;
  --primary-foreground: 255 255 255;
  --secondary: 241 245 249;
  --secondary-foreground: 71 85 105;
  --muted: 241 245 249;
  --muted-foreground: 100 116 139;
  --accent: 40 167 69;
  --accent-foreground: 255 255 255;
  --destructive: 220 53 69;
  --destructive-foreground: 255 255 255;
  --border: 226 232 240;
  --input: 226 232 240;
  --ring: 0 123 255;
  --radius: .75rem;
  --success: 40 167 69;
  --success-foreground: 255 255 255;
  --warning: 25 25 112;
  --warning-foreground: 255 255 255;
  --info: 0 123 255;
  --info-foreground: 255 255 255;
  --danger: 220 53 69;
  --danger-foreground: 255 255 255;
  --header-primary: 25 25 112;
  --header-secondary: 128 0 32;
  --header-foreground: 255 255 255;
  --gradient-start: 249 249 249;
  --gradient-end: 255 255 255;
  --gradient-primary: 0 123 255;
  --gradient-accent: 40 167 69;
}

.dark {
  --background: 15 23 42;
  --foreground: 241 245 249;
  --card: 30 41 59;
  --card-foreground: 241 245 249;
  --popover: 30 41 59;
  --popover-foreground: 241 245 249;
  --primary: 139 147 255;
  --primary-foreground: 30 27 75;
  --secondary: 51 65 85;
  --secondary-foreground: 241 245 249;
  --muted: 51 65 85;
  --muted-foreground: 148 163 184;
  --accent: 251 191 36;
  --accent-foreground: 30 27 75;
  --destructive: 248 113 113;
  --destructive-foreground: 30 27 75;
  --border: 51 65 85;
  --input: 51 65 85;
  --ring: 139 147 255;
  --success: 134 239 172;
  --success-foreground: 5 46 22;
  --warning: 71 85 105;
  --warning-foreground: 241 245 249;
  --info: 125 211 252;
  --info-foreground: 8 47 73;
  --danger: 248 113 113;
  --danger-foreground: 69 10 10;
  --gradient-start: 15 23 42;
  --gradient-end: 30 41 59;
  --gradient-primary: 139 147 255;
  --gradient-accent: 251 191 36;
}

* {
  border-color: hsl(var(--border));
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  direction: rtl;
  font-family: Cairo, Almarai, system-ui, sans-serif;
}

.arabic-text {
  text-align: right;
  direction: rtl;
  font-family: Cairo, Almarai, system-ui, sans-serif;
}

.numbers {
  font-variant-numeric: tabular-nums;
}

.table-rtl {
  direction: rtl;
}

.table-rtl th, .table-rtl td {
  text-align: right;
}

.diwan-card {
  background: hsl(var(--card));
  border-radius: var(--radius);
  box-shadow: 0 4px 6px -1px hsl(var(--foreground) / .1), 0 2px 4px -2px hsl(var(--foreground) / .1);
  border: 1px solid hsl(var(--border));
  color: hsl(var(--card-foreground));
  padding: 1.5rem;
  transition: all .2s cubic-bezier(.4, 0, .2, 1);
}

.diwan-card:hover {
  box-shadow: 0 8px 25px -5px hsl(var(--foreground) / .15);
  transform: translateY(-1px);
}

.diwan-card-elevated {
  box-shadow: 0 10px 15px -3px hsl(var(--foreground) / .1), 0 4px 6px -4px hsl(var(--foreground) / .1);
}

.diwan-card-interactive {
  cursor: pointer;
}

.diwan-card-interactive:hover {
  box-shadow: 0 12px 30px -8px hsl(var(--foreground) / .2);
  transform: translateY(-2px);
}

.diwan-button {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
  color: hsl(var(--primary-foreground));
  border-radius: var(--radius);
  cursor: pointer;
  box-shadow: 0 2px 8px -2px hsl(var(--primary) / .25);
  border: none;
  padding: .75rem 1.5rem;
  font-size: .875rem;
  font-weight: 600;
  transition: all .2s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
}

.diwan-button:hover {
  box-shadow: 0 4px 12px -2px hsl(var(--primary) / .35);
  transform: translateY(-1px);
}

.diwan-button:active {
  transform: translateY(0);
}

.diwan-button-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
  color: hsl(var(--primary-foreground));
}

.diwan-button-accent {
  background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--accent)) 100%);
  color: hsl(var(--accent-foreground));
}

.diwan-button-success {
  background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(var(--success)) 100%);
  color: hsl(var(--success-foreground));
}

.diwan-button-outline {
  color: hsl(var(--primary));
  border: 2px solid hsl(var(--primary));
  box-shadow: none;
  background: none;
}

.diwan-button-outline:hover {
  background: hsl(var(--primary) / .05);
  border-color: hsl(var(--primary));
}

.diwan-input {
  border: 2px solid hsl(var(--border));
  border-radius: var(--radius);
  background: hsl(var(--background));
  width: 100%;
  color: hsl(var(--foreground));
  outline: none;
  padding: .75rem 1rem;
  font-size: .875rem;
  font-weight: 500;
  transition: all .2s cubic-bezier(.4, 0, .2, 1);
}

.diwan-input:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 4px hsl(var(--primary) / .1);
}

.diwan-input:hover {
  border-color: hsl(var(--border));
}

.diwan-input::-moz-placeholder {
  color: hsl(var(--muted-foreground));
  font-weight: 400;
}

.diwan-input::placeholder {
  color: hsl(var(--muted-foreground));
  font-weight: 400;
}

.stats-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--card-foreground));
  border-radius: var(--radius);
  box-shadow: 0 4px 6px -1px hsl(var(--foreground) / .1);
  padding: 1.5rem;
  transition: all .2s cubic-bezier(.4, 0, .2, 1);
}

.stats-card:hover {
  box-shadow: 0 8px 25px -5px hsl(var(--foreground) / .15);
  transform: translateY(-2px);
}

.stats-card-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / .8) 100%);
  color: hsl(var(--primary-foreground));
}

.stats-card-success {
  background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(var(--success) / .8) 100%);
  color: hsl(var(--success-foreground));
}

.stats-card-warning {
  background: linear-gradient(135deg, hsl(var(--warning)) 0%, hsl(var(--warning) / .8) 100%);
  color: hsl(var(--warning-foreground));
}

.stats-card-error {
  background: linear-gradient(135deg, hsl(var(--destructive)) 0%, hsl(var(--destructive) / .8) 100%);
  color: hsl(var(--destructive-foreground));
}

.gold-accent {
  color: hsl(var(--accent));
  font-weight: 700;
}

.diwan-dialog {
  background: hsl(var(--background));
  border-radius: calc(var(--radius) * 1.5);
  border: 1px solid hsl(var(--border));
  box-shadow: 0 25px 50px -12px hsl(var(--foreground) / .25);
  backdrop-filter: blur(8px);
}

.diwan-dialog-header {
  border-bottom: 1px solid hsl(var(--border));
  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--background)) 100%);
  border-radius: calc(var(--radius) * 1.5) calc(var(--radius) * 1.5) 0 0;
  padding: 1.5rem 1.5rem 1rem;
}

.diwan-dialog-content {
  padding: 1.5rem;
}

.diwan-dialog-footer {
  border-top: 1px solid hsl(var(--border));
  background: hsl(var(--muted) / .3);
  border-radius: 0 0 calc(var(--radius) * 1.5) calc(var(--radius) * 1.5);
  padding: 1rem 1.5rem 1.5rem;
}

.diwan-table {
  border-collapse: collapse;
  background: hsl(var(--card));
  border-radius: var(--radius);
  width: 100%;
  box-shadow: 0 4px 6px -1px hsl(var(--foreground) / .1);
  overflow: hidden;
}

.diwan-table th {
  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--muted) / .8) 100%);
  color: hsl(var(--foreground));
  text-align: right;
  border-bottom: 2px solid hsl(var(--border));
  padding: 1rem;
  font-weight: 600;
}

.diwan-table td {
  border-bottom: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  padding: .875rem 1rem;
}

.diwan-table tr:hover {
  background: hsl(var(--muted) / .3);
}

.diwan-table tr:last-child td {
  border-bottom: none;
}

.diwan-badge {
  border-radius: calc(var(--radius) * .75);
  text-transform: uppercase;
  letter-spacing: .025em;
  align-items: center;
  padding: .25rem .75rem;
  font-size: .75rem;
  font-weight: 600;
  display: inline-flex;
}

.diwan-badge-success {
  background: hsl(var(--success) / .1);
  color: hsl(var(--success));
  border: 1px solid hsl(var(--success) / .2);
}

.diwan-badge-warning {
  background: hsl(var(--warning) / .1);
  color: hsl(var(--warning));
  border: 1px solid hsl(var(--warning) / .2);
}

.diwan-badge-error {
  background: hsl(var(--destructive) / .1);
  color: hsl(var(--destructive));
  border: 1px solid hsl(var(--destructive) / .2);
}

.diwan-badge-info {
  background: hsl(var(--info) / .1);
  color: hsl(var(--info));
  border: 1px solid hsl(var(--info) / .2);
}

@media (width <= 768px) {
  .diwan-card {
    padding: 1rem;
  }

  .diwan-button {
    padding: .625rem 1.25rem;
    font-size: .8rem;
  }

  .diwan-input {
    padding: .625rem .875rem;
    font-size: .875rem;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--background)) 50%, hsl(var(--muted)) 75%);
  background-size: 200px 100%;
  animation: 1.5s infinite shimmer;
}

.smooth-transition {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.hover-lift:hover {
  box-shadow: 0 20px 40px -10px hsl(var(--foreground) / .2);
  transform: translateY(-4px);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.focus-ring:focus {
  ring: 2px;
  ring-color: hsl(var(--primary));
  ring-offset: 2px;
  outline: none;
}

.sr-only {
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

@media print {
  .no-print {
    display: none !important;
  }

  .diwan-card {
    box-shadow: none;
    border: 1px solid #000;
  }

  .diwan-button {
    color: #000 !important;
    background: none !important;
    border: 1px solid #000 !important;
  }
}

.dashboard-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

.dashboard-card-enhanced {
  backdrop-filter: blur(10px);
  background: #fffffff2;
  border: 1px solid #fff3;
  border-radius: 20px;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  box-shadow: 0 8px 32px #0000001a;
}

.dashboard-card-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px #00000026;
}

.dashboard-stats-card {
  backdrop-filter: blur(15px);
  background: linear-gradient(135deg, #ffffffe6 0%, #ffffffb3 100%);
  border: 2px solid #ffffff4d;
  border-radius: 24px;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 12px 40px #0000001a;
}

.dashboard-stats-card:before {
  content: "";
  opacity: 0;
  background: linear-gradient(90deg, #007bff 0%, #28a745 50%, #800020 100%);
  height: 4px;
  transition: opacity .3s;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.dashboard-stats-card:hover:before {
  opacity: 1;
}

.dashboard-stats-card:hover {
  border-color: #ffffff80;
  transform: translateY(-6px)scale(1.02);
  box-shadow: 0 25px 50px #0003;
}

.dashboard-number {
  -webkit-text-fill-color: transparent;
  background: linear-gradient(135deg, #191970 0%, #800020 100%);
  -webkit-background-clip: text;
  background-clip: text;
  font-size: 2.5rem;
  font-weight: 900;
  line-height: 1.2;
  transition: all .3s;
}

.dashboard-number:hover {
  transform: scale(1.05);
}

.sidebar-enhanced {
  background: linear-gradient(#1e293b 0%, #0f172a 100%);
  box-shadow: 4px 0 20px #0000004d;
}

.sidebar-nav-item {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  position: relative;
}

.sidebar-nav-item:before {
  content: "";
  background: linear-gradient(135deg, #007bff 0%, #28a745 100%);
  border-radius: 0 8px 8px 0;
  width: 0;
  height: 60%;
  transition: width .3s;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.sidebar-nav-item.active:before {
  width: 4px;
}

.header-enhanced {
  backdrop-filter: blur(10px);
  background: #fffffff2;
  border-bottom: 2px solid #007bff1a;
  box-shadow: 0 4px 20px #0000000d;
}

@keyframes shimmer-enhanced {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer-enhanced {
  background: linear-gradient(90deg, #ffffff1a 25%, #ffffff4d 50%, #ffffff1a 75%) 0 0 / 200px 100%;
  animation: 1.5s infinite shimmer-enhanced;
}

@media (width <= 768px) {
  .dashboard-card-enhanced {
    border-radius: 16px;
    padding: 1rem;
  }

  .dashboard-stats-card {
    border-radius: 20px;
  }

  .dashboard-number {
    font-size: 2rem;
  }
}

.settings-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

.settings-card {
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all .2s ease-in-out;
  box-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
}

.settings-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
}

.settings-header-icon {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 4px 6px -1px #0ea5e94d;
}

.settings-section-icon {
  border-radius: 6px;
  padding: 6px;
  box-shadow: 0 2px 4px -1px #0003;
}

.settings-switch {
  transition: all .2s ease-in-out;
}

.settings-button {
  font-weight: 500;
  transition: all .2s ease-in-out;
}

.settings-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px #0000001a;
}

.settings-tabs {
  background: #f8fafc;
  border-radius: 8px;
  padding: 4px;
}

.settings-tab-trigger {
  border-radius: 6px;
  font-weight: 500;
  transition: all .2s ease-in-out;
}

.settings-tab-trigger[data-state="active"] {
  color: #0ea5e9;
  background: #fff;
  box-shadow: 0 2px 4px -1px #0000001a;
}

.settings-input {
  border-radius: 6px;
  transition: all .2s ease-in-out;
}

.settings-input:focus {
  ring: 2px;
  ring-color: #0ea5e94d;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px #0ea5e91a;
}

:root {
  --theme-primary-color: #007bff;
  --theme-secondary-color: #333;
  --theme-accent-color: #28a745;
  --theme-background-color: #f9f9f9;
  --theme-text-color: #333;
  --theme-header-color: #191970;
  --theme-header-alt-color: #800020;
  --theme-font-family: "Cairo", "Almarai", system-ui, sans-serif;
  --theme-font-size: 14px;
  --theme-font-weight: normal;
  --theme-line-height: 1.5;
}

.theme-applied {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size);
  font-weight: var(--theme-font-weight);
  line-height: var(--theme-line-height);
  background-color: var(--theme-background-color);
  color: var(--theme-text-color);
  transition: all .3s;
}

.dark-theme {
  --theme-background-color: #111827;
  --theme-text-color: #f9fafb;
}

.dark-theme .diwan-card, .dark-theme .settings-card {
  color: #f9fafb;
  background-color: #1f2937;
  border-color: #374151;
}

.custom-primary {
  background-color: var(--theme-primary-color) !important;
  border-color: var(--theme-primary-color) !important;
  color: #fff !important;
}

.custom-secondary {
  background-color: var(--theme-secondary-color) !important;
  border-color: var(--theme-secondary-color) !important;
  color: #fff !important;
}

.custom-accent {
  background-color: var(--theme-accent-color) !important;
  border-color: var(--theme-accent-color) !important;
  color: #fff !important;
}

.header-primary {
  background-color: var(--theme-header-color) !important;
  color: #fff !important;
  border-color: var(--theme-header-color) !important;
}

.header-secondary {
  background-color: var(--theme-header-alt-color) !important;
  color: #fff !important;
  border-color: var(--theme-header-alt-color) !important;
}

.btn-primary {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-primary:hover {
  background-color: #0056cc !important;
  border-color: #0056cc !important;
}

.btn-success {
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.btn-success:hover {
  background-color: #228b3c !important;
  border-color: #228b3c !important;
}

.text-primary-custom {
  color: #007bff !important;
}

.text-success-custom {
  color: #28a745 !important;
}

.text-header-primary {
  color: #191970 !important;
}

.text-header-secondary {
  color: #800020 !important;
}

.bg-light-custom {
  background-color: #f9f9f9 !important;
}

.bg-white-custom {
  background-color: #fff !important;
}

.bg-text-custom {
  color: #fff !important;
  background-color: #333 !important;
}

.file\:border-0::file-selector-button {
  border-width: 0;
}

.file\:bg-transparent::file-selector-button {
  background-color: #0000;
}

.file\:text-sm::file-selector-button {
  font-size: .875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-secondary-400::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

.placeholder\:text-secondary-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

.last\:border-b-0:last-child {
  border-bottom-width: 0;
}

.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-1:hover {
  --tw-translate-y: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-2:hover {
  --tw-translate-y: -.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:transform:hover {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-blue-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.hover\:border-green-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.hover\:border-green-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));
}

.hover\:border-primary-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(51 159 255 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}

.hover\:border-red-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.hover\:border-red-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}

.hover\:border-red-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.hover\:border-red-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.hover\:border-secondary-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(125 211 252 / var(--tw-border-opacity, 1));
}

.hover\:bg-black\/10:hover {
  background-color: #0000001a;
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50\/50:hover {
  background-color: #eff6ff80;
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / .8);
}

.hover\:bg-emerald-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / .5);
}

.hover\:bg-primary-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(204 231 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(230 243 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/80:hover {
  background-color: hsl(var(--primary) / .8);
}

.hover\:bg-purple-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50\/50:hover {
  background-color: #fef2f280;
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white\/20:hover {
  background-color: #fff3;
}

.hover\:bg-white\/50:hover {
  background-color: #ffffff80;
}

.hover\:bg-yellow-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-30:hover {
  --tw-bg-opacity: .3;
}

.hover\:bg-opacity-75:hover {
  --tw-bg-opacity: .75;
}

.hover\:bg-gradient-to-br:hover {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.hover\:bg-gradient-to-r:hover {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.hover\:from-blue-600:hover {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: #2563eb00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-blue-700:hover {
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: #1d4ed800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-danger-600:hover {
  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);
  --tw-gradient-to: #dc262600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-emerald-700:hover {
  --tw-gradient-from: #047857 var(--tw-gradient-from-position);
  --tw-gradient-to: #04785700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-gold-600:hover {
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: #d9770600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-green-600:hover {
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: #16a34a00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-green-700:hover {
  --tw-gradient-from: #15803d var(--tw-gradient-from-position);
  --tw-gradient-to: #15803d00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-indigo-50:hover {
  --tw-gradient-from: #eef2ff var(--tw-gradient-from-position);
  --tw-gradient-to: #eef2ff00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-indigo-600:hover {
  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);
  --tw-gradient-to: #4f46e500 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-info-600:hover {
  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);
  --tw-gradient-to: #0284c700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-primary-600:hover {
  --tw-gradient-from: #0056cc var(--tw-gradient-from-position);
  --tw-gradient-to: #0056cc00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-purple-600:hover {
  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);
  --tw-gradient-to: #9333ea00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-purple-700:hover {
  --tw-gradient-from: #7e22ce var(--tw-gradient-from-position);
  --tw-gradient-to: #7e22ce00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-red-500:hover {
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: #ef444400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-red-600:hover {
  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);
  --tw-gradient-to: #dc262600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-red-700:hover {
  --tw-gradient-from: #b91c1c var(--tw-gradient-from-position);
  --tw-gradient-to: #b91c1c00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-secondary-200:hover {
  --tw-gradient-from: #e2e8f0 var(--tw-gradient-from-position);
  --tw-gradient-to: #e2e8f000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-sky-600:hover {
  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);
  --tw-gradient-to: #0284c700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-sky-700:hover {
  --tw-gradient-from: #0369a1 var(--tw-gradient-from-position);
  --tw-gradient-to: #0369a100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-success-600:hover {
  --tw-gradient-from: #228b3c var(--tw-gradient-from-position);
  --tw-gradient-to: #228b3c00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-warning-600:hover {
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: #d9770600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:via-green-700:hover {
  --tw-gradient-to: #15803d00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #15803d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.hover\:via-purple-800:hover {
  --tw-gradient-to: #6b21a800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #6b21a8 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.hover\:via-red-800:hover {
  --tw-gradient-to: #991b1b00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #991b1b var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.hover\:to-blue-50:hover {
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}

.hover\:to-blue-700:hover {
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}

.hover\:to-blue-800:hover {
  --tw-gradient-to: #1e40af var(--tw-gradient-to-position);
}

.hover\:to-danger-700:hover {
  --tw-gradient-to: #b91c1c var(--tw-gradient-to-position);
}

.hover\:to-emerald-700:hover {
  --tw-gradient-to: #047857 var(--tw-gradient-to-position);
}

.hover\:to-gold-700:hover {
  --tw-gradient-to: #b45309 var(--tw-gradient-to-position);
}

.hover\:to-green-700:hover {
  --tw-gradient-to: #15803d var(--tw-gradient-to-position);
}

.hover\:to-green-800:hover {
  --tw-gradient-to: #166534 var(--tw-gradient-to-position);
}

.hover\:to-indigo-700:hover {
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}

.hover\:to-indigo-800:hover {
  --tw-gradient-to: #3730a3 var(--tw-gradient-to-position);
}

.hover\:to-info-700:hover {
  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);
}

.hover\:to-orange-700:hover {
  --tw-gradient-to: #c2410c var(--tw-gradient-to-position);
}

.hover\:to-primary-700:hover {
  --tw-gradient-to: #004099 var(--tw-gradient-to-position);
}

.hover\:to-purple-700:hover {
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}

.hover\:to-red-600:hover {
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}

.hover\:to-red-700:hover {
  --tw-gradient-to: #b91c1c var(--tw-gradient-to-position);
}

.hover\:to-secondary-300:hover {
  --tw-gradient-to: #cbd5e1 var(--tw-gradient-to-position);
}

.hover\:to-sky-700:hover {
  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);
}

.hover\:to-sky-800:hover {
  --tw-gradient-to: #075985 var(--tw-gradient-to-position);
}

.hover\:to-success-700:hover {
  --tw-gradient-to: #1c6e32 var(--tw-gradient-to-position);
}

.hover\:to-teal-700:hover {
  --tw-gradient-to: #0f766e var(--tw-gradient-to-position);
}

.hover\:to-warning-700:hover {
  --tw-gradient-to: #b45309 var(--tw-gradient-to-position);
}

.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-700:hover {
  --tw-text-opacity: 1;
  color: rgb(4 120 87 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-green-700:hover {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-800:hover {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-800:hover {
  --tw-text-opacity: 1;
  color: rgb(0 43 102 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-700:hover {
  --tw-text-opacity: 1;
  color: rgb(3 105 161 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px #00000040;
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000001a;
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:border-danger-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.focus\:border-indigo-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.focus\:border-primary-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(0 123 255 / var(--tw-border-opacity, 1));
}

.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.focus\:border-sky-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:bg-gray-50:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.focus\:bg-green-50:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.focus\:bg-primary-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(204 231 255 / var(--tw-bg-opacity, 1));
}

.focus\:bg-purple-50:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:text-primary-800:focus {
  --tw-text-opacity: 1;
  color: rgb(0 43 102 / var(--tw-text-opacity, 1));
}

.focus\:outline-none:focus {
  outline-offset: 2px;
  outline: 2px solid #0000;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-4:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-danger-500\/20:focus {
  --tw-ring-color: #ef444433;
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary-500\/20:focus {
  --tw-ring-color: #007bff33;
}

.focus\:ring-red-100:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 226 226 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 202 202 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-sky-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));
}

.focus\:ring-slate-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:border-primary-500:focus-visible {
  --tw-border-opacity: 1;
  border-color: rgb(0 123 255 / var(--tw-border-opacity, 1));
}

.focus-visible\:outline-none:focus-visible {
  outline-offset: 2px;
  outline: 2px solid #0000;
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-4:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-primary-500\/20:focus-visible {
  --tw-ring-color: #007bff33;
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:ring-offset-background:focus-visible {
  --tw-ring-offset-color: hsl(var(--background));
}

.active\:scale-95:active {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.active\:scale-\[0\.98\]:active {
  --tw-scale-x: .98;
  --tw-scale-y: .98;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:transform-none:disabled {
  transform: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

.disabled\:opacity-70:disabled {
  opacity: .7;
}

.group:focus-within .group-focus-within\:text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:translate-x-\[100\%\] {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-black\/20 {
  background-color: #0003;
}

.group:hover .group-hover\:bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:bg-white\/30 {
  background-color: #ffffff4d;
}

.group:hover .group-hover\:bg-white\/60 {
  background-color: #fff9;
}

.group:hover .group-hover\:bg-opacity-30 {
  --tw-bg-opacity: .3;
}

.group:hover .group-hover\:text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-indigo-700 {
  --tw-text-opacity: 1;
  color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-10 {
  opacity: .1;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:shadow-xl {
  --tw-shadow: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000001a;
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group.toaster .group-\[\.toaster\]\:border-border {
  border-color: hsl(var(--border));
}

.group.toast .group-\[\.toast\]\:bg-muted {
  background-color: hsl(var(--muted));
}

.group.toast .group-\[\.toast\]\:bg-primary {
  background-color: hsl(var(--primary));
}

.group.toaster .group-\[\.toaster\]\:bg-background {
  background-color: hsl(var(--background));
}

.group.toast .group-\[\.toast\]\:text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.group.toast .group-\[\.toast\]\:text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.group.toaster .group-\[\.toaster\]\:text-foreground {
  color: hsl(var(--foreground));
}

.group.toaster .group-\[\.toaster\]\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: .7;
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=open\]\:rotate-180[data-state="open"] {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}

.data-\[state\=checked\]\:bg-primary-100[data-state="checked"] {
  --tw-bg-opacity: 1;
  background-color: rgb(204 231 255 / var(--tw-bg-opacity, 1));
}

.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}

.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: hsl(var(--muted));
}

.data-\[state\=checked\]\:font-semibold[data-state="checked"] {
  font-weight: 600;
}

.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}

.data-\[state\=checked\]\:text-primary-800[data-state="checked"] {
  --tw-text-opacity: 1;
  color: rgb(0 43 102 / var(--tw-text-opacity, 1));
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: .5;
}

.data-\[state\=active\]\:shadow-sm[data-state="active"] {
  --tw-shadow: 0 1px 2px 0 #0000000d;
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
  animation-name: enter;
  animation-duration: .15s;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
  animation-name: exit;
  animation-duration: .15s;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: .5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: .5rem;
}

.dark\:border-destructive:is(.dark *) {
  border-color: hsl(var(--destructive));
}

@media (width >= 640px) {
  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:w-\[200px\] {
    width: 200px;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-3 {
    gap: .75rem;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.5rem * var(--tw-space-x-reverse));
    margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.75rem * var(--tw-space-x-reverse));
    margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:px-3 {
    padding-left: .75rem;
    padding-right: .75rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:text-right {
    text-align: right;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

@media (width >= 768px) {
  .md\:w-auto {
    width: auto;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

@media (width >= 1024px) {
  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:block {
    display: block;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:pr-64 {
    padding-right: 16rem;
  }

  .lg\:text-right {
    text-align: right;
  }
}

@media print {
  .print\:block {
    display: block;
  }

  .print\:hidden {
    display: none;
  }
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
  padding-right: 0;
}

.\[\&\>span\]\:line-clamp-1 > span {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.\[\&\>svg\+div\]\:translate-y-\[-3px\] > svg + div {
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\>svg\]\:absolute > svg {
  position: absolute;
}

.\[\&\>svg\]\:left-4 > svg {
  left: 1rem;
}

.\[\&\>svg\]\:top-4 > svg {
  top: 1rem;
}

.\[\&\>svg\]\:text-destructive > svg {
  color: hsl(var(--destructive));
}

.\[\&\>svg\]\:text-foreground > svg {
  color: hsl(var(--foreground));
}

.\[\&\>svg\~\*\]\:pl-7 > svg ~ * {
  padding-left: 1.75rem;
}

.\[\&\>tr\]\:last\:border-b-0:last-child > tr {
  border-bottom-width: 0;
}

.\[\&_p\]\:leading-relaxed p {
  line-height: 1.625;
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0;
}

.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}


/* [project]/src/styles/updated-colors.css [app-client] (css) */
:root {
  --new-background: #f9f9f9;
  --new-background-alt: #fff;
  --new-text: #333;
  --new-primary: #007bff;
  --new-success: #28a745;
  --new-header-primary: #191970;
  --new-header-secondary: #800020;
}

body {
  background-color: var(--new-background) !important;
  color: var(--new-text) !important;
}

.card, .diwan-card, .bg-white {
  background-color: var(--new-background-alt) !important;
  color: var(--new-text) !important;
}

.btn-primary, .bg-primary, .text-primary {
  background-color: var(--new-primary) !important;
  border-color: var(--new-primary) !important;
  color: #fff !important;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #0056cc !important;
  border-color: #0056cc !important;
}

.btn-success, .bg-success, .text-success {
  background-color: var(--new-success) !important;
  border-color: var(--new-success) !important;
  color: #fff !important;
}

.btn-success:hover, .btn-success:focus {
  background-color: #228b3c !important;
  border-color: #228b3c !important;
}

.header, .navbar, .page-header, h1, h2, h3, h4, h5, h6 {
  color: var(--new-header-primary) !important;
}

.header-alt, .navbar-alt, .secondary-header {
  background-color: var(--new-header-secondary) !important;
  color: #fff !important;
}

p, span, div, label, td, th {
  color: var(--new-text) !important;
}

a {
  color: var(--new-primary) !important;
}

a:hover {
  color: #0056cc !important;
}

.form-control, .form-select, input, textarea, select {
  background-color: var(--new-background-alt) !important;
  color: var(--new-text) !important;
  border-color: #dee2e6 !important;
}

.form-control:focus, .form-select:focus, input:focus, textarea:focus, select:focus {
  border-color: var(--new-primary) !important;
  box-shadow: 0 0 0 .2rem #007bff40 !important;
}

.table {
  background-color: var(--new-background-alt) !important;
  color: var(--new-text) !important;
}

.table th {
  background-color: var(--new-header-primary) !important;
  color: #fff !important;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: #f9f9f980 !important;
}

.modal-content, .dropdown-menu, .popover {
  background-color: var(--new-background-alt) !important;
  color: var(--new-text) !important;
}

.sidebar {
  background-color: var(--new-header-primary) !important;
  color: #fff !important;
}

.sidebar a {
  color: #fff !important;
}

.sidebar a:hover {
  background-color: #ffffff1a !important;
}

.alert-primary {
  border-color: var(--new-primary) !important;
  color: var(--new-primary) !important;
  background-color: #007bff1a !important;
}

.alert-success {
  border-color: var(--new-success) !important;
  color: var(--new-success) !important;
  background-color: #28a7451a !important;
}

.badge-primary {
  background-color: var(--new-primary) !important;
}

.badge-success {
  background-color: var(--new-success) !important;
}

.progress-bar {
  background-color: var(--new-primary) !important;
}

.progress-bar-success {
  background-color: var(--new-success) !important;
}

.nav-tabs .nav-link.active {
  background-color: var(--new-background-alt) !important;
  border-color: var(--new-primary) !important;
  color: var(--new-primary) !important;
}

.nav-tabs .nav-link {
  color: var(--new-text) !important;
}

.nav-tabs .nav-link:hover {
  color: var(--new-primary) !important;
}

.border-primary {
  border-color: var(--new-primary) !important;
}

.border-success {
  border-color: var(--new-success) !important;
}

.shadow-primary {
  box-shadow: 0 .5rem 1rem #007bff26 !important;
}

.shadow-success {
  box-shadow: 0 .5rem 1rem #28a74526 !important;
}

.diwan-primary {
  background-color: var(--new-primary) !important;
  color: #fff !important;
}

.diwan-success {
  background-color: var(--new-success) !important;
  color: #fff !important;
}

.diwan-header {
  background-color: var(--new-header-primary) !important;
  color: #fff !important;
}

.diwan-header-alt {
  background-color: var(--new-header-secondary) !important;
  color: #fff !important;
}

@media (width <= 768px) {
  .header, .navbar {
    background-color: var(--new-header-primary) !important;
    color: #fff !important;
  }
}


/* [project]/src/styles/sidebar-colors.css [app-client] (css) */
.sidebar, .fixed.inset-y-0.right-0.z-50.w-64 {
  color: #fff !important;
  background-color: #191970 !important;
}

.sidebar .border-b, .sidebar .border-gray-200 {
  border-color: #fff3 !important;
}

.sidebar h1, .sidebar .text-gray-900 {
  color: #fff !important;
}

.sidebar p, .sidebar .text-gray-500 {
  color: #d1d5db !important;
}

.sidebar .text-diwan-600, .sidebar .text-gray-400 {
  color: #fff !important;
}

.sidebar nav a, .sidebar .text-gray-700 {
  color: #e5e7eb !important;
}

.sidebar nav a:hover, .sidebar .hover\:text-gray-900:hover {
  color: #fff !important;
  background-color: #ffffff1a !important;
}

.sidebar .bg-diwan-100 {
  background-color: #fff3 !important;
}

.sidebar .text-diwan-700 {
  color: #fff !important;
}

.sidebar .text-diwan-500 {
  color: #60a5fa !important;
}

.sidebar .hover\:bg-gray-100:hover {
  background-color: #ffffff1a !important;
}

.sidebar .border-t, .sidebar .border-gray-200 {
  border-color: #fff3 !important;
}

.member-sidebar {
  color: #fff !important;
  background-color: #191970 !important;
}

.member-sidebar a {
  color: #e5e7eb !important;
}

.member-sidebar a:hover {
  color: #fff !important;
  background-color: #ffffff1a !important;
}

div[class*="fixed"][class*="inset-y-0"][class*="right-0"][class*="w-64"] {
  background-color: #191970 !important;
}

div[class*="fixed"][class*="inset-y-0"][class*="right-0"][class*="w-64"] h1 {
  color: #fff !important;
}

div[class*="fixed"][class*="inset-y-0"][class*="right-0"][class*="w-64"] p {
  color: #d1d5db !important;
}

div[class*="fixed"][class*="inset-y-0"][class*="right-0"][class*="w-64"] a {
  color: #e5e7eb !important;
}

div[class*="fixed"][class*="inset-y-0"][class*="right-0"][class*="w-64"] a:hover {
  color: #fff !important;
  background-color: #ffffff1a !important;
}

.lg\:block.hidden.fixed.inset-y-0.right-0.z-50.w-64 {
  background-color: #191970 !important;
}

@media (width <= 1024px) {
  .sidebar, .fixed.inset-y-0.right-0.w-64 {
    background-color: #191970 !important;
  }
}

[class*="sidebar"] {
  color: #fff !important;
  background-color: #191970 !important;
}

[class*="sidebar"] * {
  color: inherit !important;
}

[class*="sidebar"] h1, [class*="sidebar"] h2, [class*="sidebar"] h3, [class*="sidebar"] h4, [class*="sidebar"] h5, [class*="sidebar"] h6 {
  color: #fff !important;
}

[class*="sidebar"] p, [class*="sidebar"] span {
  color: #d1d5db !important;
}

[class*="sidebar"] a {
  transition: all .2s;
  color: #e5e7eb !important;
}

[class*="sidebar"] a:hover {
  color: #fff !important;
  background-color: #ffffff1a !important;
}

.sidebar .bg-white {
  background-color: #0000 !important;
}

.sidebar .text-gray-900, .sidebar .text-gray-700, .sidebar .text-gray-500, .sidebar .text-gray-400 {
  color: inherit !important;
}

.sidebar .border-gray-200, .sidebar .border-b, .sidebar .border-t {
  border-color: #fff3 !important;
}

.sidebar svg {
  color: currentColor !important;
}

.sidebar .group:hover svg {
  color: #fff !important;
}

.sidebar, .sidebar * {
  --tw-text-opacity: 1 !important;
}

.sidebar {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(25 25 112 / var(--tw-bg-opacity)) !important;
}


/* [project]/src/styles/button-colors.css [app-client] (css) */
.btn-primary, .bg-primary, button[class*="bg-primary"], .button-primary {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-primary:hover, .bg-primary:hover, button[class*="bg-primary"]:hover, .button-primary:hover {
  color: #fff !important;
  background-color: #0056cc !important;
  border-color: #0056cc !important;
}

.btn-primary:focus, .bg-primary:focus, button[class*="bg-primary"]:focus, .button-primary:focus {
  background-color: #0056cc !important;
  border-color: #0056cc !important;
  box-shadow: 0 0 0 .2rem #007bff40 !important;
}

.btn-success, .bg-success, button[class*="bg-success"], .button-success {
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.btn-success:hover, .bg-success:hover, button[class*="bg-success"]:hover, .button-success:hover {
  color: #fff !important;
  background-color: #228b3c !important;
  border-color: #228b3c !important;
}

.btn-success:focus, .bg-success:focus, button[class*="bg-success"]:focus, .button-success:focus {
  background-color: #228b3c !important;
  border-color: #228b3c !important;
  box-shadow: 0 0 0 .2rem #28a74540 !important;
}

.btn-danger, .bg-destructive, button[class*="bg-destructive"], .button-danger {
  color: #fff !important;
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}

.btn-danger:hover, .bg-destructive:hover, button[class*="bg-destructive"]:hover, .button-danger:hover {
  color: #fff !important;
  background-color: #c82333 !important;
  border-color: #c82333 !important;
}

.btn-warning, .bg-warning, button[class*="bg-warning"], .button-warning {
  color: #333 !important;
  background-color: #ffc107 !important;
  border-color: #ffc107 !important;
}

.btn-warning:hover, .bg-warning:hover, button[class*="bg-warning"]:hover, .button-warning:hover {
  color: #333 !important;
  background-color: #e0a800 !important;
  border-color: #e0a800 !important;
}

.btn-info, .bg-info, button[class*="bg-info"], .button-info {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-secondary, .bg-secondary, button[class*="bg-secondary"], .button-secondary {
  color: #fff !important;
  background-color: #6c757d !important;
  border-color: #6c757d !important;
}

.btn-secondary:hover, .bg-secondary:hover, button[class*="bg-secondary"]:hover, .button-secondary:hover {
  color: #fff !important;
  background-color: #5a6268 !important;
  border-color: #5a6268 !important;
}

.btn-outline-primary {
  color: #007bff !important;
  background-color: #0000 !important;
  border-color: #007bff !important;
}

.btn-outline-primary:hover {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-outline-success {
  color: #28a745 !important;
  background-color: #0000 !important;
  border-color: #28a745 !important;
}

.btn-outline-success:hover {
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.bg-blue-500, .bg-blue-600 {
  background-color: #007bff !important;
}

.bg-blue-500:hover, .bg-blue-600:hover, .hover\:bg-blue-600:hover, .hover\:bg-blue-700:hover {
  background-color: #0056cc !important;
}

.bg-green-500, .bg-green-600 {
  background-color: #28a745 !important;
}

.bg-green-500:hover, .bg-green-600:hover, .hover\:bg-green-600:hover, .hover\:bg-green-700:hover {
  background-color: #228b3c !important;
}

.diwan-btn-primary {
  border-radius: .375rem;
  padding: .5rem 1rem;
  font-weight: 500;
  transition: all .2s;
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.diwan-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #007bff4d;
  background-color: #0056cc !important;
  border-color: #0056cc !important;
}

.diwan-btn-success {
  border-radius: .375rem;
  padding: .5rem 1rem;
  font-weight: 500;
  transition: all .2s;
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.diwan-btn-success:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #28a7454d;
  background-color: #228b3c !important;
  border-color: #228b3c !important;
}

.save-button, button[type="submit"] {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.save-button:hover, button[type="submit"]:hover {
  background-color: #0056cc !important;
  border-color: #0056cc !important;
}

.cancel-button, button[type="button"][class*="cancel"] {
  color: #fff !important;
  background-color: #6c757d !important;
  border-color: #6c757d !important;
}

.cancel-button:hover, button[type="button"][class*="cancel"]:hover {
  background-color: #5a6268 !important;
  border-color: #5a6268 !important;
}

.action-button-edit {
  color: #fff !important;
  background-color: #007bff !important;
}

.action-button-delete {
  color: #fff !important;
  background-color: #dc3545 !important;
}

.action-button-view {
  color: #fff !important;
  background-color: #28a745 !important;
}

button:focus, .btn:focus {
  outline: none !important;
  box-shadow: 0 0 0 .2rem #007bff40 !important;
}

.btn-sm {
  padding: .25rem .5rem !important;
  font-size: .875rem !important;
}

.btn-lg {
  padding: .75rem 1.5rem !important;
  font-size: 1.125rem !important;
}

button, .btn {
  transition: all .2s !important;
}

button:hover, .btn:hover {
  transform: translateY(-1px) !important;
}

button:active, .btn:active {
  transform: translateY(0) !important;
}

button:disabled, .btn:disabled {
  opacity: .6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

button:disabled:hover, .btn:disabled:hover {
  background-color: inherit !important;
  transform: none !important;
}

.members-page .btn-primary, .members-page button[style*="background-color: rgb(0, 123, 255)"] {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.members-page .btn-success, .members-page button[style*="background-color: rgb(40, 167, 69)"] {
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.members-page .btn-danger, .members-page button[style*="background-color: rgb(220, 53, 69)"] {
  color: #fff !important;
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}

button[class*="bg-blue"], .bg-blue-500, .bg-blue-600 {
  color: #fff !important;
  background-color: #007bff !important;
}

button[class*="bg-green"], .bg-green-500, .bg-green-600 {
  color: #fff !important;
  background-color: #28a745 !important;
}

button[class*="bg-red"], .bg-red-500, .bg-red-600 {
  color: #fff !important;
  background-color: #dc3545 !important;
}

button[title*="تصدير"], button[aria-label*="تصدير"], .export-button {
  color: #fff !important;
  background-color: #28a745 !important;
}

button[title*="PDF"], button[aria-label*="PDF"], .pdf-button {
  color: #fff !important;
  background-color: #dc3545 !important;
}

button[title*="إضافة"], button[aria-label*="إضافة"], button[title*="جديد"], button[aria-label*="جديد"], .add-button {
  color: #fff !important;
  background-color: #007bff !important;
}


/* [project]/src/styles/header-colors.css [app-client] (css) */
h1, h2, h3, h4, h5, h6 {
  color: #191970 !important;
  font-weight: 600 !important;
}

.page-title, .page-header h1, .page-header h2 {
  color: #191970 !important;
  font-weight: 700 !important;
}

.card-header, .card-title, .diwan-card h1, .diwan-card h2, .diwan-card h3 {
  color: #191970 !important;
  font-weight: 600 !important;
}

.table th, .table thead th, table th {
  color: #fff !important;
  background-color: #191970 !important;
  border-color: #191970 !important;
  font-weight: 600 !important;
}

.modal-header, .modal-title, .dialog-header, .dialog-title {
  color: #fff !important;
  background-color: #191970 !important;
  font-weight: 600 !important;
}

.header, .navbar, .top-header {
  color: #fff !important;
  background-color: #191970 !important;
  border-bottom: 1px solid #fff3 !important;
}

.header h1, .header h2, .navbar h1, .navbar h2 {
  color: #fff !important;
}

.header-alt, .navbar-alt, .secondary-header {
  color: #fff !important;
  background-color: #800020 !important;
}

.header-alt h1, .header-alt h2, .navbar-alt h1, .navbar-alt h2, .secondary-header h1, .secondary-header h2 {
  color: #fff !important;
}

.section-header, .section-title {
  color: #191970 !important;
  border-bottom: 2px solid #191970 !important;
  margin-bottom: 1rem !important;
  padding-bottom: .5rem !important;
  font-weight: 700 !important;
}

.sub-header, .sub-title {
  color: #800020 !important;
  font-weight: 600 !important;
}

.stats-header, .stats-title {
  color: #191970 !important;
  text-align: center !important;
  font-weight: 700 !important;
}

.form-header, .form-title {
  color: #191970 !important;
  margin-bottom: 1rem !important;
  font-weight: 600 !important;
}

.report-header, .report-title {
  color: #fff !important;
  text-align: center !important;
  background-color: #191970 !important;
  border-radius: .5rem .5rem 0 0 !important;
  padding: 1rem !important;
  font-weight: 700 !important;
}

.settings-header, .settings-title {
  color: #191970 !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding-bottom: .5rem !important;
  font-weight: 600 !important;
}

.gallery-header, .gallery-title {
  color: #191970 !important;
  text-align: center !important;
  margin-bottom: 2rem !important;
  font-weight: 700 !important;
}

.notification-header, .notification-title {
  color: #800020 !important;
  font-weight: 600 !important;
}

.member-header, .member-title, .income-header, .expense-header {
  color: #191970 !important;
  font-weight: 600 !important;
}

.diwan-header-primary {
  color: #fff !important;
  text-align: center !important;
  background-color: #191970 !important;
  border-radius: .5rem !important;
  padding: 1rem !important;
  font-weight: 700 !important;
}

.diwan-header-secondary {
  color: #fff !important;
  text-align: center !important;
  background-color: #800020 !important;
  border-radius: .5rem !important;
  padding: 1rem !important;
  font-weight: 700 !important;
}

.diwan-header-text {
  color: #191970 !important;
  margin-bottom: 1rem !important;
  font-weight: 600 !important;
}

.gradient-header {
  color: #fff !important;
  text-align: center !important;
  background: linear-gradient(135deg, #191970 0%, #800020 100%) !important;
  border-radius: .5rem !important;
  padding: 1rem !important;
  font-weight: 700 !important;
}

.shadow-header {
  color: #191970 !important;
  text-shadow: 2px 2px 4px #1919704d !important;
  font-weight: 700 !important;
}

@media print {
  h1, h2, h3, h4, h5, h6 {
    color: #000 !important;
  }

  .header, .navbar, .modal-header {
    color: #000 !important;
    background-color: #fff !important;
    border: 1px solid #000 !important;
  }
}

@media (width <= 768px) {
  h1 {
    font-size: 1.5rem !important;
  }

  h2 {
    font-size: 1.25rem !important;
  }

  h3 {
    font-size: 1.125rem !important;
  }

  .page-title {
    text-align: center !important;
    font-size: 1.5rem !important;
  }

  .section-header {
    font-size: 1.25rem !important;
  }
}

.animated-header {
  color: #191970 !important;
  font-weight: 700 !important;
  transition: all .3s !important;
}

.animated-header:hover {
  color: #800020 !important;
  transform: translateY(-2px) !important;
}

.underlined-header {
  color: #191970 !important;
  font-weight: 600 !important;
  position: relative !important;
}

.underlined-header:after {
  content: "" !important;
  background-color: #800020 !important;
  width: 100% !important;
  height: 2px !important;
  position: absolute !important;
  bottom: -4px !important;
  left: 0 !important;
}

.icon-header {
  color: #191970 !important;
  align-items: center !important;
  gap: .5rem !important;
  font-weight: 600 !important;
  display: flex !important;
}

.icon-header svg {
  color: #800020 !important;
}

.text-primary {
  color: #007bff !important;
}

.text-success {
  color: #28a745 !important;
}

.text-header {
  color: #191970 !important;
}

.text-header-alt {
  color: #800020 !important;
}

.bg-header {
  color: #fff !important;
  background-color: #191970 !important;
}

.bg-header-alt {
  color: #fff !important;
  background-color: #800020 !important;
}


/* [project]/src/styles/page-layout.css [app-client] (css) */
.page-container {
  min-height: 100vh;
  padding: 1.5rem;
  background-color: #f9f9f9 !important;
}

.page-header {
  background-color: #fff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 1rem !important;
  margin-bottom: 2rem !important;
  padding: 2rem !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.page-title {
  color: #191970 !important;
  text-align: center !important;
  margin-bottom: .5rem !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
}

.page-subtitle {
  color: #333 !important;
  text-align: center !important;
  margin-bottom: 1rem !important;
  font-size: 1.125rem !important;
  font-weight: 500 !important;
}

.page-divider {
  background-color: #800020 !important;
  border-radius: 2px !important;
  width: 6rem !important;
  height: 4px !important;
  margin: 0 auto !important;
}

.enhanced-card {
  background-color: #fff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 1rem !important;
  transition: all .3s !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.enhanced-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 15px -3px #0000001a !important;
}

.card-header-enhanced {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 1.5rem !important;
}

.card-title-enhanced {
  color: #191970 !important;
  align-items: center !important;
  gap: .75rem !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  display: flex !important;
}

.card-content-enhanced {
  padding: 1.5rem !important;
}

.stats-card {
  background-color: #fff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 1rem !important;
  transition: all .3s !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.stats-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 15px -3px #0000001a !important;
}

.stats-card-header {
  justify-content: space-between !important;
  align-items: center !important;
  padding: 1rem !important;
  display: flex !important;
}

.stats-card-title {
  color: #333 !important;
  font-size: .875rem !important;
  font-weight: 600 !important;
}

.stats-card-icon {
  color: #fff !important;
  border-radius: .75rem !important;
  padding: .75rem !important;
}

.stats-card-content {
  padding: 0 1rem 1rem !important;
}

.stats-card-value {
  color: #191970 !important;
  margin-bottom: .25rem !important;
  font-size: 2rem !important;
  font-weight: 700 !important;
}

.stats-card-description {
  color: #333 !important;
  font-size: .75rem !important;
  font-weight: 500 !important;
}

.card-top-bar {
  border-radius: 1rem 1rem 0 0 !important;
  width: 100% !important;
  height: 4px !important;
}

.card-top-bar-blue {
  background: linear-gradient(90deg, #007bff 0%, #0056cc 100%) !important;
}

.card-top-bar-green {
  background: linear-gradient(90deg, #28a745 0%, #228b3c 100%) !important;
}

.card-top-bar-red {
  background: linear-gradient(90deg, #dc3545 0%, #c82333 100%) !important;
}

.card-top-bar-yellow {
  background: linear-gradient(90deg, #191970 0%, #14145a 100%) !important;
}

.card-top-bar-purple {
  background: linear-gradient(90deg, #800020 0%, #660019 100%) !important;
}

.card-top-bar-indigo {
  background: linear-gradient(90deg, #191970 0%, #14145a 100%) !important;
}

.enhanced-table {
  background-color: #fff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 1rem !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.enhanced-table thead th {
  color: #fff !important;
  text-align: right !important;
  background-color: #191970 !important;
  border: none !important;
  padding: 1rem !important;
  font-weight: 600 !important;
}

.enhanced-table tbody td {
  color: #333 !important;
  border-bottom: 1px solid #f3f4f6 !important;
  padding: 1rem !important;
}

.enhanced-table tbody tr:hover {
  background-color: #f8f9fa !important;
}

.enhanced-table tbody tr:last-child td {
  border-bottom: none !important;
}

.enhanced-form {
  background-color: #fff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 1rem !important;
  padding: 2rem !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.form-group-enhanced {
  margin-bottom: 1.5rem !important;
}

.form-label-enhanced {
  color: #333 !important;
  margin-bottom: .5rem !important;
  font-weight: 600 !important;
  display: block !important;
}

.form-input-enhanced {
  color: #333 !important;
  background-color: #fff !important;
  border: 1px solid #d1d5db !important;
  border-radius: .5rem !important;
  width: 100% !important;
  padding: .75rem 1rem !important;
  transition: all .2s !important;
}

.form-input-enhanced:focus {
  border-color: #007bff !important;
  outline: none !important;
  box-shadow: 0 0 0 3px #007bff1a !important;
}

.btn-enhanced {
  cursor: pointer !important;
  border: none !important;
  border-radius: .5rem !important;
  align-items: center !important;
  gap: .5rem !important;
  padding: .75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all .2s !important;
  display: inline-flex !important;
}

.btn-enhanced:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px #00000026 !important;
}

.btn-primary-enhanced {
  color: #fff !important;
  background-color: #007bff !important;
}

.btn-primary-enhanced:hover {
  background-color: #0056cc !important;
}

.btn-success-enhanced {
  color: #fff !important;
  background-color: #28a745 !important;
}

.btn-success-enhanced:hover {
  background-color: #228b3c !important;
}

.btn-danger-enhanced {
  color: #fff !important;
  background-color: #dc3545 !important;
}

.btn-danger-enhanced:hover {
  background-color: #c82333 !important;
}

.search-container {
  background-color: #fff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 1rem !important;
  margin-bottom: 2rem !important;
  padding: 1.5rem !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.search-input {
  color: #333 !important;
  background-color: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  border-radius: .5rem !important;
  padding: .75rem 1rem !important;
  transition: all .2s !important;
}

.search-input:focus {
  background-color: #fff !important;
  border-color: #007bff !important;
  box-shadow: 0 0 0 3px #007bff1a !important;
}

.alert-enhanced {
  border: 1px solid !important;
  border-radius: .75rem !important;
  margin-bottom: 1rem !important;
  padding: 1rem 1.5rem !important;
}

.alert-success {
  color: #155724 !important;
  background-color: #28a7451a !important;
  border-color: #28a745 !important;
}

.alert-danger {
  color: #721c24 !important;
  background-color: #dc35451a !important;
  border-color: #dc3545 !important;
}

.alert-warning {
  color: #191970 !important;
  background-color: #1919701a !important;
  border-color: #191970 !important;
}

.alert-info {
  color: #004085 !important;
  background-color: #007bff1a !important;
  border-color: #007bff !important;
}

.loading-container {
  background-color: #f8f9fa !important;
  border-radius: 1rem !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 4rem 2rem !important;
  display: flex !important;
}

.loading-spinner {
  border: 4px solid #e9ecef !important;
  border-top-color: #007bff !important;
  border-radius: 50% !important;
  width: 3rem !important;
  height: 3rem !important;
  margin-bottom: 1rem !important;
  animation: 1s linear infinite spin !important;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.empty-state {
  text-align: center !important;
  color: #6c757d !important;
  padding: 4rem 2rem !important;
}

.empty-state-icon {
  color: #dee2e6 !important;
  width: 4rem !important;
  height: 4rem !important;
  margin: 0 auto 1rem !important;
}

@media (width <= 768px) {
  .page-container {
    padding: 1rem !important;
  }

  .page-header {
    padding: 1.5rem !important;
  }

  .page-title {
    font-size: 2rem !important;
  }

  .enhanced-card {
    margin-bottom: 1rem !important;
  }

  .card-header-enhanced, .card-content-enhanced {
    padding: 1rem !important;
  }
}


/* [project]/src/styles/table-styles.css [app-client] (css) */
.table, table {
  background-color: #fff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 1rem !important;
  width: 100% !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.table thead th, table thead th, .table th, table th, [data-testid="table-header"] th, [role="columnheader"] {
  color: #fff !important;
  text-align: right !important;
  background-color: #191970 !important;
  border: none !important;
  padding: 1rem !important;
  font-size: .875rem !important;
  font-weight: 600 !important;
}

thead th, th[role="columnheader"], .table-header th, [class*="TableHead"] {
  color: #fff !important;
  background-color: #191970 !important;
}

.table tbody td, table tbody td, .table td, table td {
  color: #333 !important;
  vertical-align: middle !important;
  border-bottom: 1px solid #f3f4f6 !important;
  padding: 1rem !important;
  font-size: .875rem !important;
}

.table tbody tr, table tbody tr {
  transition: background-color .2s !important;
}

.table tbody tr:hover, table tbody tr:hover {
  background-color: #f8f9fa !important;
}

.table tbody tr:last-child td, table tbody tr:last-child td {
  border-bottom: none !important;
}

.table-striped tbody tr:nth-of-type(odd), table.table-striped tbody tr:nth-of-type(odd) {
  background-color: #f9f9f980 !important;
}

.table-striped tbody tr:nth-of-type(odd):hover, table.table-striped tbody tr:nth-of-type(odd):hover {
  background-color: #f8f9fa !important;
}

.table-sm th, .table-sm td {
  padding: .5rem !important;
}

.table-bordered, .table-bordered th, .table-bordered td {
  border: 1px solid #e5e7eb !important;
}

.table-borderless th, .table-borderless td, .table-borderless thead th, .table-borderless tbody + tbody {
  border: 0 !important;
}

.table-primary {
  background-color: #007bff1a !important;
}

.table-primary th, .table-primary td, .table-primary thead th, .table-primary tbody + tbody {
  border-color: #007bff33 !important;
}

.table-success {
  background-color: #28a7451a !important;
}

.table-success th, .table-success td, .table-success thead th, .table-success tbody + tbody {
  border-color: #28a74533 !important;
}

.table-hover tbody tr:hover {
  background-color: #007bff0d !important;
}

.table-shadow {
  box-shadow: 0 10px 15px -3px #0000001a !important;
}

.table-header-primary thead th {
  color: #fff !important;
  background-color: #007bff !important;
}

.table-header-success thead th {
  color: #fff !important;
  background-color: #28a745 !important;
}

.table-header-danger thead th {
  color: #fff !important;
  background-color: #dc3545 !important;
}

.table-header-warning thead th {
  color: #333 !important;
  background-color: #ffc107 !important;
}

.table-header-info thead th {
  color: #fff !important;
  background-color: #17a2b8 !important;
}

.table-header-dark thead th {
  color: #fff !important;
  background-color: #191970 !important;
}

.table-header-burgundy thead th {
  color: #fff !important;
  background-color: #800020 !important;
}

.table-responsive {
  -webkit-overflow-scrolling: touch !important;
  width: 100% !important;
  display: block !important;
  overflow-x: auto !important;
}

.table-responsive > .table {
  margin-bottom: 0 !important;
}

@media (width <= 768px) {
  .table th, .table td {
    padding: .75rem .5rem !important;
    font-size: .8rem !important;
  }

  .table-responsive {
    border: 1px solid #e5e7eb !important;
    border-radius: .5rem !important;
  }
}

.diwan-table {
  background-color: #fff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 1rem !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

.diwan-table thead th {
  color: #fff !important;
  text-align: right !important;
  background: linear-gradient(135deg, #191970 0%, #800020 100%) !important;
  border: none !important;
  padding: 1rem !important;
  font-weight: 600 !important;
}

.diwan-table tbody td {
  color: #333 !important;
  border-bottom: 1px solid #f3f4f6 !important;
  padding: 1rem !important;
}

.diwan-table tbody tr:hover {
  background-color: #f8f9fa !important;
}

.table-actions {
  justify-content: center !important;
  align-items: center !important;
  gap: .5rem !important;
  display: flex !important;
}

.table-action-btn {
  cursor: pointer !important;
  border: none !important;
  border-radius: .5rem !important;
  justify-content: center !important;
  align-items: center !important;
  padding: .5rem !important;
  transition: all .2s !important;
  display: inline-flex !important;
}

.table-action-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px #00000026 !important;
}

.table-action-edit {
  color: #fff !important;
  background-color: #007bff !important;
}

.table-action-edit:hover {
  background-color: #0056cc !important;
}

.table-action-delete {
  color: #fff !important;
  background-color: #dc3545 !important;
}

.table-action-delete:hover {
  background-color: #c82333 !important;
}

.table-action-view {
  color: #fff !important;
  background-color: #28a745 !important;
}

.table-action-view:hover {
  background-color: #228b3c !important;
}

.table-badge {
  text-align: center !important;
  border-radius: 1rem !important;
  padding: .25rem .75rem !important;
  font-size: .75rem !important;
  font-weight: 600 !important;
  display: inline-block !important;
}

.table-badge-primary {
  color: #007bff !important;
  background-color: #007bff1a !important;
  border: 1px solid #007bff33 !important;
}

.table-badge-success {
  color: #28a745 !important;
  background-color: #28a7451a !important;
  border: 1px solid #28a74533 !important;
}

.table-badge-danger {
  color: #dc3545 !important;
  background-color: #dc35451a !important;
  border: 1px solid #dc354533 !important;
}

.table-badge-warning {
  color: #856404 !important;
  background-color: #ffc1071a !important;
  border: 1px solid #ffc10733 !important;
}

.table-badge-info {
  color: #17a2b8 !important;
  background-color: #17a2b81a !important;
  border: 1px solid #17a2b833 !important;
}

@media print {
  .table {
    border-collapse: collapse !important;
  }

  .table th, .table td {
    border: 1px solid #000 !important;
    padding: .5rem !important;
  }

  .table thead th {
    color: #000 !important;
    background-color: #f8f9fa !important;
  }

  .table-actions {
    display: none !important;
  }
}

.table th[scope="col"] {
  text-align: right !important;
}

.table th[scope="row"] {
  text-align: right !important;
  font-weight: 600 !important;
}

.table-loading {
  position: relative !important;
}

.table-loading:after {
  content: "" !important;
  background-color: #fffc !important;
  justify-content: center !important;
  align-items: center !important;
  display: flex !important;
  position: absolute !important;
  inset: 0 !important;
}

.table-container {
  border: 1px solid #e5e7eb !important;
  border-radius: 1rem !important;
  max-height: 600px !important;
  overflow-y: auto !important;
}

.table-container::-webkit-scrollbar {
  width: 8px !important;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 4px !important;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1 !important;
  border-radius: 4px !important;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
}

[data-radix-collection-item], .table-header-row th, .table-header-row [role="columnheader"] {
  color: #fff !important;
  background-color: #191970 !important;
}

table > thead > tr > th, table thead tr th, .table > thead > tr > th, [role="table"] [role="columnheader"], [data-testid*="table"] [role="columnheader"] {
  color: #fff !important;
  background-color: #191970 !important;
  font-weight: 600 !important;
}

[class*="TableHead"], [class*="table-head"], [class*="tableHead"], th, [role="columnheader"] {
  color: #fff !important;
  background-color: #191970 !important;
}


/* [project]/src/styles/color-fixes.css [app-client] (css) */
th, thead th, table th, .table th, [role="columnheader"], [data-testid*="table"] th, [class*="TableHead"], [class*="table-head"] {
  color: #fff !important;
  background-color: #191970 !important;
  border: none !important;
  font-weight: 600 !important;
}

[data-radix-collection-item], [data-state="open"] th, [data-state="closed"] th {
  color: #fff !important;
  background-color: #191970 !important;
}

button[style*="007bff"], .btn-primary, .bg-primary {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

button[style*="28a745"], .btn-success, .bg-success {
  color: #fff !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

button[style*="dc3545"], .btn-danger, .bg-danger {
  color: #fff !important;
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}

.text-blue-600, .text-blue-500 {
  color: #0056cc !important;
}

.text-green-600, .text-green-500 {
  color: #1e7e34 !important;
}

.text-red-600, .text-red-500 {
  color: #c82333 !important;
}

p, span, div, label, td, li {
  color: #333 !important;
  font-weight: 500 !important;
}

.bg-white p, .bg-white span, .bg-white div, .bg-white label {
  color: #212529 !important;
}

.bg-gradient-to-br {
  background: #fff !important;
}

.card h1, .card h2, .card h3, .card h4, .card h5, .card h6 {
  color: #191970 !important;
  font-weight: 700 !important;
}

.card p, .card span, .card div {
  color: #212529 !important;
  font-weight: 500 !important;
}

.card-title, [data-testid="card-title"], h3[class*="text-sm"] {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.stats-card-value, div[class*="text-2xl"], .text-2xl {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.stats-card-title, .stats-card-description, [role="region"] h3, [role="region"] div[class*="text-2xl"], .card-content div, .card-header h3 {
  color: #212529 !important;
  font-weight: 700 !important;
}

.members-stats .card-title, .members-stats h3 {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.members-stats .text-2xl, .members-stats div[class*="font-bold"] {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.text-blue-600 svg, .text-green-600 svg, .text-red-600 svg {
  color: inherit !important;
}

.table-actions button {
  border: 1px solid #0000 !important;
  border-radius: .5rem !important;
  padding: .5rem !important;
  transition: all .2s !important;
}

button[style*="color: rgb(0, 123, 255)"], .table-actions .text-blue-600 {
  color: #0056cc !important;
  background-color: #0056cc1a !important;
  border-color: #0056cc33 !important;
}

button[style*="color: rgb(0, 123, 255)"]:hover, .table-actions .text-blue-600:hover {
  color: #003d82 !important;
  background-color: #0056cc33 !important;
}

button[style*="color: rgb(40, 167, 69)"], .table-actions .text-green-600 {
  color: #1e7e34 !important;
  background-color: #1e7e341a !important;
  border-color: #1e7e3433 !important;
}

button[style*="color: rgb(40, 167, 69)"]:hover, .table-actions .text-green-600:hover {
  color: #155724 !important;
  background-color: #1e7e3433 !important;
}

button[style*="color: rgb(220, 53, 69)"], .table-actions .text-red-600 {
  color: #c82333 !important;
  background-color: #c823331a !important;
  border-color: #c8233333 !important;
}

button[style*="color: rgb(220, 53, 69)"]:hover, .table-actions .text-red-600:hover {
  color: #a71e2a !important;
  background-color: #c8233333 !important;
}

button[style*="color: rgb(128, 0, 32)"], .table-actions .text-purple-600 {
  color: #800020 !important;
  background-color: #8000201a !important;
  border-color: #80002033 !important;
}

button[style*="color: rgb(128, 0, 32)"]:hover, .table-actions .text-purple-600:hover {
  color: #660019 !important;
  background-color: #80002033 !important;
}

button[style*="color: rgb(255, 193, 7)"], .table-actions .text-orange-600 {
  color: #b8860b !important;
  background-color: #b8860b1a !important;
  border-color: #b8860b33 !important;
}

button[style*="color: rgb(255, 193, 7)"]:hover, .table-actions .text-orange-600:hover {
  color: #9a7209 !important;
  background-color: #b8860b33 !important;
}

table, .table {
  background-color: #fff !important;
}

table tbody tr:hover, .table tbody tr:hover {
  background-color: #f8f9fa !important;
}

table td, .table td {
  color: #212529 !important;
  border-bottom: 1px solid #f3f4f6 !important;
  font-weight: 500 !important;
}

table td span, table td div, .table td span, .table td div {
  color: #212529 !important;
  font-weight: 500 !important;
}

.badge {
  padding: .375rem .75rem !important;
  font-size: .75rem !important;
  font-weight: 600 !important;
}

.pagination button {
  color: #333 !important;
  background-color: #fff !important;
  border: 1px solid #dee2e6 !important;
}

.pagination button:hover {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.pagination button:disabled {
  color: #6c757d !important;
  background-color: #f8f9fa !important;
  border-color: #dee2e6 !important;
}

.search-container button {
  color: #fff !important;
  background-color: #007bff !important;
  border: none !important;
}

.search-container button:hover {
  background-color: #0056cc !important;
}

input, select, textarea {
  color: #333 !important;
  background-color: #fff !important;
  border: 1px solid #dee2e6 !important;
}

input:focus, select:focus, textarea:focus {
  border-color: #007bff !important;
  box-shadow: 0 0 0 .2rem #007bff40 !important;
}

.badge, .badge-primary {
  color: #007bff !important;
  background-color: #007bff1a !important;
  border: 1px solid #007bff33 !important;
}

.badge-success {
  color: #28a745 !important;
  background-color: #28a7451a !important;
  border: 1px solid #28a74533 !important;
}

.badge-danger {
  color: #dc3545 !important;
  background-color: #dc35451a !important;
  border: 1px solid #dc354533 !important;
}

.badge-warning {
  color: #191970 !important;
  background-color: #1919701a !important;
  border: 1px solid #19197033 !important;
}

.alert-primary {
  color: #004085 !important;
  background-color: #007bff1a !important;
  border-color: #007bff !important;
}

.alert-success {
  color: #155724 !important;
  background-color: #28a7451a !important;
  border-color: #28a745 !important;
}

.alert-danger {
  color: #721c24 !important;
  background-color: #dc35451a !important;
  border-color: #dc3545 !important;
}

a {
  color: #007bff !important;
}

a:hover {
  color: #0056cc !important;
}

.text-primary {
  color: #007bff !important;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #191970 !important;
}

.text-info {
  color: #17a2b8 !important;
}

.bg-primary {
  color: #fff !important;
  background-color: #007bff !important;
}

.bg-success {
  color: #fff !important;
  background-color: #28a745 !important;
}

.bg-danger {
  color: #fff !important;
  background-color: #dc3545 !important;
}

.bg-warning {
  color: #fff !important;
  background-color: #191970 !important;
}

.bg-info {
  color: #fff !important;
  background-color: #17a2b8 !important;
}

.border-primary {
  border-color: #007bff !important;
}

.border-success {
  border-color: #28a745 !important;
}

.border-danger {
  border-color: #dc3545 !important;
}

.border-warning {
  border-color: #191970 !important;
}

.border-info {
  border-color: #17a2b8 !important;
}

body {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-family: Cairo, Almarai, system-ui, sans-serif !important;
}

.text-xs, .text-sm {
  color: #495057 !important;
  font-weight: 600 !important;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6c757d !important;
  font-weight: 500 !important;
}

input::placeholder, textarea::placeholder, select option {
  color: #6c757d !important;
  font-weight: 500 !important;
}

.dropdown-menu {
  color: #212529 !important;
}

.dropdown-item {
  color: #212529 !important;
  font-weight: 500 !important;
}

.dropdown-item:hover {
  color: #191970 !important;
  background-color: #f8f9fa !important;
}

.nav-link {
  color: #495057 !important;
  font-weight: 500 !important;
}

.nav-link.active {
  color: #191970 !important;
  font-weight: 600 !important;
}

.sidebar a {
  color: #e9ecef !important;
  font-weight: 500 !important;
}

.sidebar a:hover {
  color: #fff !important;
  font-weight: 600 !important;
}

* {
  text-rendering: optimizeLegibility !important;
}

.bg-primary *, .bg-success *, .bg-danger *, .bg-warning *, .bg-info * {
  text-shadow: 0 1px 2px #0000001a !important;
}


/* [project]/src/styles/members-text-fix.css [app-client] (css) */
.members-stats * {
  color: #212529 !important;
  font-weight: 700 !important;
}

.members-stats .text-2xl, .members-stats div[class*="text-2xl"], .members-stats div[class*="font-bold"], .members-stats div[class*="font-black"] {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
  line-height: 1.2 !important;
}

.members-stats h3, .members-stats .card-title, .members-stats [role="heading"] {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
  line-height: 1.4 !important;
}

.members-stats .bg-white h3 {
  color: #212529 !important;
  font-weight: 700 !important;
}

.members-stats .bg-white div[class*="text-2xl"] {
  color: #191970 !important;
  font-weight: 900 !important;
}

[class*="Card"] h3, [class*="Card"] div[class*="text-2xl"], [class*="CardTitle"], [class*="CardContent"] div {
  color: #212529 !important;
  font-weight: 700 !important;
}

[class*="CardContent"] div[class*="text-2xl"] {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

[data-testid*="card"] h3, [data-testid*="card"] div, .members-page h1, .members-page h2, .members-page h3, .members-page h4, .members-page h5, .members-page h6 {
  color: #212529 !important;
  font-weight: 700 !important;
}

.members-page p, .members-page span, .members-page div {
  color: #212529 !important;
  font-weight: 500 !important;
}

.members-page .text-2xl, .members-page .text-3xl, .members-page .text-4xl {
  color: #191970 !important;
  font-weight: 900 !important;
}

.members-page table td, .members-page table th {
  color: #212529 !important;
  font-weight: 500 !important;
}

.members-page table th {
  color: #fff !important;
  font-weight: 600 !important;
}

.members-page button {
  font-weight: 600 !important;
}

.members-page input, .members-page select, .members-page textarea {
  color: #212529 !important;
  font-weight: 500 !important;
}

.members-page .badge, .members-page span[class*="rounded-full"] {
  font-weight: 600 !important;
}

* {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

[data-radix-collection-item] *, [data-state] *, [role="region"] * {
  color: #212529 !important;
  font-weight: 500 !important;
}

.text-xs, .text-sm {
  color: #495057 !important;
  font-weight: 600 !important;
}

.text-base, .text-lg {
  color: #212529 !important;
  font-weight: 500 !important;
}

.text-xl, .text-2xl, .text-3xl, h1, h2, h3, h4, h5, h6 {
  color: #191970 !important;
  font-weight: 700 !important;
}

p, span, div, label {
  color: #212529 !important;
  font-weight: 500 !important;
}

.members-stats .bg-white .text-sm {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.members-stats .bg-white .text-2xl {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.card *, .Card *, [class*="card"] *, [class*="Card"] * {
  color: #212529 !important;
  font-weight: 500 !important;
}

.card .text-2xl, .Card .text-2xl, [class*="card"] .text-2xl, [class*="Card"] .text-2xl {
  color: #191970 !important;
  font-weight: 900 !important;
}

body * {
  color: #212529 !important;
}

.text-2xl, .font-bold, .font-black {
  color: #191970 !important;
  font-weight: 900 !important;
}

.text-sm {
  color: #212529 !important;
  font-weight: 700 !important;
}


/* [project]/src/styles/global-stats-fix.css [app-client] (css) */
[class*="Card"], .card, .stats-card, [role="region"], [data-testid*="card"] {
  background-color: #fff !important;
}

[class*="Card"] h3, [class*="Card"] [class*="CardTitle"], [class*="CardTitle"], .card h3, .card .card-title, .stats-card h3, [role="region"] h3, [data-testid*="card"] h3 {
  color: #212529 !important;
  text-shadow: none !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
  line-height: 1.4 !important;
}

[class*="Card"] .text-2xl, [class*="Card"] .text-3xl, [class*="Card"] [class*="font-bold"], [class*="Card"] [class*="font-black"], .card .text-2xl, .card .text-3xl, .stats-card .value, [role="region"] .text-2xl, [role="region"] .text-3xl, [data-testid*="card"] .text-2xl, [data-testid*="card"] .text-3xl {
  color: #191970 !important;
  text-shadow: none !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
  line-height: 1.2 !important;
}

[class*="Card"] p, [class*="Card"] span, [class*="Card"] div:not(.text-2xl):not(.text-3xl), .card p, .card span, .card div:not(.text-2xl):not(.text-3xl), .stats-card p, .stats-card span, [role="region"] p, [role="region"] span, [data-testid*="card"] p, [data-testid*="card"] span {
  color: #495057 !important;
  text-shadow: none !important;
  font-weight: 500 !important;
}

.dashboard-page [class*="Card"] h3, .dashboard-page [class*="CardTitle"] {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.dashboard-page [class*="Card"] .text-3xl {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.members-page [class*="Card"] h3, .members-page [class*="CardTitle"], .members-stats [class*="CardTitle"] {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.members-page [class*="Card"] .text-2xl, .members-stats .text-2xl {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.incomes-page [class*="Card"] h3, .incomes-page [class*="CardTitle"] {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.incomes-page [class*="Card"] .text-3xl {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.expenses-page [class*="Card"] h3, .expenses-page [class*="CardTitle"] {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.expenses-page [class*="Card"] .text-3xl {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.reports-page [class*="Card"] h3, .reports-page [class*="CardTitle"] {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.reports-page [class*="Card"] .text-2xl {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

[data-radix-collection-item] h3, [data-radix-collection-item] [class*="text-"], [data-state] h3, [data-state] [class*="text-"] {
  color: #212529 !important;
  font-weight: 700 !important;
}

[data-radix-collection-item] .text-2xl, [data-radix-collection-item] .text-3xl, [data-state] .text-2xl, [data-state] .text-3xl {
  color: #191970 !important;
  font-weight: 900 !important;
}

.text-gray-800, .text-gray-700, .text-gray-600 {
  color: #212529 !important;
  font-weight: 500 !important;
}

.text-orange-700, .text-green-700, .text-blue-700, .text-red-700 {
  color: #191970 !important;
  font-weight: 900 !important;
}

.text-xs, .text-sm {
  color: #495057 !important;
  font-weight: 600 !important;
}

.text-base {
  color: #212529 !important;
  font-weight: 500 !important;
}

.text-lg, .text-xl, .text-2xl, .text-3xl, .text-4xl {
  color: #191970 !important;
  font-weight: 700 !important;
}

.font-medium {
  font-weight: 600 !important;
}

.font-semibold {
  font-weight: 700 !important;
}

.font-bold, .font-black {
  font-weight: 900 !important;
}

* {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

h1, h2, h3, h4, h5, h6 {
  color: #191970 !important;
  text-shadow: none !important;
  font-weight: 700 !important;
}

p, span, div, label {
  color: #212529 !important;
  text-shadow: none !important;
  font-weight: 500 !important;
}

.number, .amount, .count, .value {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.badge, .tag, .chip {
  color: #212529 !important;
  font-weight: 600 !important;
}

a {
  color: #007bff !important;
  font-weight: 500 !important;
}

a:hover {
  color: #0056cc !important;
  font-weight: 600 !important;
}

input, select, textarea {
  color: #212529 !important;
  font-weight: 500 !important;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6c757d !important;
  font-weight: 400 !important;
}

input::placeholder, textarea::placeholder {
  color: #6c757d !important;
  font-weight: 400 !important;
}

button {
  font-weight: 600 !important;
}

ul, ol, li, table, th, td {
  color: #212529 !important;
  font-weight: 500 !important;
}

th {
  font-weight: 600 !important;
}

body * {
  text-shadow: none !important;
}

.stats-card-primary .value, .stats-card-success .value, .stats-card-warning .value, .stats-card-danger .value {
  color: #191970 !important;
  font-size: 2rem !important;
  font-weight: 900 !important;
}

.stats-card-primary h3, .stats-card-success h3, .stats-card-warning h3, .stats-card-danger h3 {
  color: #212529 !important;
  font-size: .875rem !important;
  font-weight: 700 !important;
}

.bg-gradient-to-br *, .bg-gradient-to-r *, .bg-gradient-to-l * {
  color: #212529 !important;
  font-weight: 500 !important;
}

.bg-gradient-to-br .text-2xl, .bg-gradient-to-r .text-2xl, .bg-gradient-to-l .text-2xl, .bg-gradient-to-br .text-3xl, .bg-gradient-to-r .text-3xl, .bg-gradient-to-l .text-3xl {
  color: #191970 !important;
  font-weight: 900 !important;
}


/* [project]/src/styles/final-colors.css [app-client] (css) */
:root {
  --final-background: #f9f9f9;
  --final-background-alt: #fff;
  --final-text: #333;
  --final-primary: #007bff;
  --final-success: #28a745;
  --final-header-primary: #191970;
  --final-header-secondary: #800020;
}

* {
  color: var(--final-text) !important;
}

body, .bg-gray-50, .bg-gray-100 {
  background-color: var(--final-background) !important;
}

.bg-white, .card, .modal-content, .dropdown-menu {
  background-color: var(--final-background-alt) !important;
}

p, span, div, label, td, li, a {
  color: var(--final-text) !important;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--final-header-primary) !important;
}

.btn-primary, .bg-blue-500, .bg-blue-600, button[class*="bg-blue"] {
  background-color: var(--final-primary) !important;
  border-color: var(--final-primary) !important;
  color: #fff !important;
}

.btn-primary:hover, .bg-blue-500:hover, .bg-blue-600:hover, button[class*="bg-blue"]:hover {
  background-color: #0056cc !important;
  border-color: #0056cc !important;
}

.btn-success, .bg-green-500, .bg-green-600, button[class*="bg-green"] {
  background-color: var(--final-success) !important;
  border-color: var(--final-success) !important;
  color: #fff !important;
}

.btn-success:hover, .bg-green-500:hover, .bg-green-600:hover, button[class*="bg-green"]:hover {
  background-color: #228b3c !important;
  border-color: #228b3c !important;
}

.sidebar, div[class*="fixed"][class*="inset-y-0"][class*="right-0"][class*="w-64"] {
  background-color: var(--final-header-primary) !important;
  color: #fff !important;
}

.sidebar *, div[class*="fixed"][class*="inset-y-0"][class*="right-0"][class*="w-64"] *, .sidebar h1, .sidebar h2, .sidebar h3 {
  color: #fff !important;
}

.sidebar p, .sidebar .text-gray-500 {
  color: #d1d5db !important;
}

.sidebar a {
  color: #e5e7eb !important;
}

.sidebar a:hover {
  color: #fff !important;
  background-color: #ffffff1a !important;
}

.table th, table th, thead th {
  background-color: var(--final-header-primary) !important;
  color: #fff !important;
}

.table, table {
  background-color: var(--final-background-alt) !important;
}

.table td, table td {
  color: var(--final-text) !important;
}

.form-control, .form-select, input, textarea, select {
  background-color: var(--final-background-alt) !important;
  color: var(--final-text) !important;
  border-color: #dee2e6 !important;
}

.form-control:focus, input:focus, textarea:focus, select:focus {
  border-color: var(--final-primary) !important;
  box-shadow: 0 0 0 .2rem #007bff40 !important;
}

a {
  color: var(--final-primary) !important;
}

a:hover {
  color: #0056cc !important;
}

.alert-primary {
  border-color: var(--final-primary) !important;
  color: var(--final-primary) !important;
  background-color: #007bff1a !important;
}

.alert-success {
  border-color: var(--final-success) !important;
  color: var(--final-success) !important;
  background-color: #28a7451a !important;
}

.badge-primary {
  background-color: var(--final-primary) !important;
  color: #fff !important;
}

.badge-success {
  background-color: var(--final-success) !important;
  color: #fff !important;
}

.modal-header {
  background-color: var(--final-header-primary) !important;
  color: #fff !important;
}

.modal-title {
  color: #fff !important;
}

.nav-tabs .nav-link.active {
  background-color: var(--final-background-alt) !important;
  border-color: var(--final-primary) !important;
  color: var(--final-primary) !important;
}

.nav-tabs .nav-link {
  color: var(--final-text) !important;
}

.nav-tabs .nav-link:hover {
  color: var(--final-primary) !important;
}

.progress-bar {
  background-color: var(--final-primary) !important;
}

.border-primary {
  border-color: var(--final-primary) !important;
}

.border-success {
  border-color: var(--final-success) !important;
}

.diwan-primary {
  background-color: var(--final-primary) !important;
  color: #fff !important;
}

.diwan-success {
  background-color: var(--final-success) !important;
  color: #fff !important;
}

.diwan-header {
  background-color: var(--final-header-primary) !important;
  color: #fff !important;
}

.diwan-header-alt {
  background-color: var(--final-header-secondary) !important;
  color: #fff !important;
}

.diwan-text {
  color: var(--final-text) !important;
}

.diwan-text-header {
  color: var(--final-header-primary) !important;
}

.text-gray-900, .text-gray-800, .text-gray-700, .text-gray-600 {
  color: var(--final-text) !important;
}

.text-gray-500, .text-gray-400 {
  color: #6b7280 !important;
}

.bg-gray-50, .bg-gray-100 {
  background-color: var(--final-background) !important;
}

.bg-white {
  background-color: var(--final-background-alt) !important;
}

@media (width <= 768px) {
  .sidebar {
    background-color: var(--final-header-primary) !important;
  }

  h1, h2, h3 {
    color: var(--final-header-primary) !important;
  }
}

body * {
  color: var(--final-text) !important;
}

body h1, body h2, body h3, body h4, body h5, body h6 {
  color: var(--final-header-primary) !important;
}

body .sidebar, body .sidebar *, body .sidebar h1, body .sidebar h2, body .sidebar h3 {
  color: #fff !important;
}

.text-blue-600, .text-blue-500 {
  color: var(--final-primary) !important;
}

.text-green-600, .text-green-500 {
  color: var(--final-success) !important;
}

.important-text {
  color: var(--final-text) !important;
}

.important-header {
  color: var(--final-header-primary) !important;
}

.important-primary {
  color: var(--final-primary) !important;
}

.important-success {
  color: var(--final-success) !important;
}


/* [project]/src/styles/enhanced-dialog.css [app-client] (css) */
.dialog-container {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.dialog-content {
  will-change: transform, filter;
  transition: all .2s cubic-bezier(.4, 0, .2, 1);
}

.dialog-shadow-enhanced {
  filter: drop-shadow(0 20px 25px #0000001a) drop-shadow(0 8px 10px #0000000a);
}

.dialog-shadow-hover {
  filter: drop-shadow(0 25px 25px #00000026) drop-shadow(0 10px 10px #0000000a);
}

.smooth-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #3b82f64d #f3f4f680;
  overflow: hidden auto;
}

.smooth-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.smooth-scroll::-webkit-scrollbar-track {
  background: #f3f4f680;
  border-radius: 4px;
  margin: 4px;
}

.smooth-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(#3b82f699, #2563ebcc);
  border: 1px solid #fff3;
  border-radius: 4px;
  -webkit-transition: all .2s;
  transition: all .2s;
}

.smooth-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(#3b82f6cc, #2563eb);
  transform: scale(1.1);
}

.smooth-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(#2563ebe6, #1d4ed8);
}

.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.dialog-scroll-indicator {
  opacity: 0;
  pointer-events: none;
  background: #3b82f61a;
  border-radius: 2px;
  width: 4px;
  height: 60px;
  transition: opacity .3s;
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
}

.smooth-scroll:hover .dialog-scroll-indicator {
  opacity: 1;
}

.dialog-scroll-thumb {
  background: #3b82f699;
  border-radius: 2px;
  width: 4px;
  transition: all .2s;
  position: absolute;
  right: 0;
}

.dialog-backdrop {
  backdrop-filter: blur(8px) saturate(180%);
  background: #0009;
  transition: all .3s ease-out;
}

.dialog-backdrop.enhanced {
  backdrop-filter: blur(12px) saturate(200%);
  background: #000000b3;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0)scale(.95);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0)scale(1);
  }
}

@keyframes dialogSlideOut {
  from {
    opacity: 1;
    transform: translate3d(0, 0, 0)scale(1);
  }

  to {
    opacity: 0;
    transform: translate3d(0, -20px, 0)scale(.95);
  }
}

.dialog-animate-in {
  animation: .3s cubic-bezier(.4, 0, .2, 1) forwards dialogSlideIn;
}

.dialog-animate-out {
  animation: .2s cubic-bezier(.4, 0, .2, 1) forwards dialogSlideOut;
}

.dialog-mouse-follow {
  cursor: move;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.dialog-mouse-follow:hover {
  transition: transform .2s ease-out;
  transform: scale(1.02);
}

.dialog-border-glow {
  border: 1px solid #3b82f64d;
  box-shadow: 0 0 0 1px #3b82f61a, 0 4px 6px -1px #0000001a, 0 2px 4px -1px #0000000f;
}

.dialog-border-glow:hover {
  border-color: #3b82f680;
  box-shadow: 0 0 0 1px #3b82f633, 0 10px 15px -3px #0000001a, 0 4px 6px -2px #0000000d;
}

.dialog-glass-effect {
  backdrop-filter: blur(10px) saturate(180%);
  background: #fffffff2;
  border: 1px solid #fff3;
}

.dialog-highlight {
  position: relative;
  overflow: hidden;
}

.dialog-highlight:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s ease-in-out;
  position: absolute;
  top: 0;
  left: -100%;
}

.dialog-highlight:hover:before {
  left: 100%;
}

@media (width <= 768px) {
  .dialog-content {
    margin: 10px;
    max-width: 95vw !important;
  }

  .dialog-mouse-follow {
    cursor: default;
  }

  .dialog-mouse-follow:hover {
    transform: none;
  }
}

@media (width <= 480px) {
  .dialog-content {
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
    margin: 0;
    max-width: 100vw !important;
  }
}

.dialog-performance-optimized {
  backface-visibility: hidden;
  perspective: 1000px;
  transform: translate3d(0, 0, 0);
}

.dialog-focus-trap {
  outline: none;
}

.dialog-focus-trap:focus-visible {
  outline-offset: 2px;
  outline: 2px solid #3b82f699;
}

.dialog-loading {
  pointer-events: none;
  opacity: .7;
}

.dialog-loading:after {
  content: "";
  border: 2px solid #3b82f64d;
  border-top-color: #3b82f6;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  animation: 1s linear infinite spin;
  position: absolute;
  top: 50%;
  left: 50%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.dialog-gradient-border {
  background: linear-gradient(#fff, #fff) padding-box padding-box, linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4) border-box;
  border: 2px solid #0000;
}

.dialog-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .8;
  }
}

.dialog-shake {
  animation: .5s ease-in-out shake;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

.dialog-zoom-in {
  animation: .3s ease-out zoomIn;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.dialog-slide-down {
  animation: .3s ease-out slideDown;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media print {
  .dialog-container {
    box-shadow: none !important;
    background: #fff !important;
    border: 1px solid #ccc !important;
    position: static !important;
  }

  .dialog-backdrop {
    display: none !important;
  }
}


/*# sourceMappingURL=src_a89b3225._.css.map*/