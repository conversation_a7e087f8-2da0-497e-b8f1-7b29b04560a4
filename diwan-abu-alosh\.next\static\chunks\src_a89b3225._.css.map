{"version": 3, "sources": [], "sections": [{"offset": {"line": 2, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/app/globals.css"], "sourcesContent": ["@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Almarai:wght@300;400;700;800&display=swap');\n*, ::before, ::after{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n::backdrop{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*/\n/*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n::before,\n::after {\n  --tw-content: '';\n}\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: Cairo, Tajawal, system-ui, sans-serif; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n/*\nRemove the default font size and weight for headings.\n*/\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n/*\nAdd the correct font weight in Edge and Safari.\n*/\nb,\nstrong {\n  font-weight: bolder;\n}\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n/*\nAdd the correct font size in all browsers.\n*/\nsmall {\n  font-size: 80%;\n}\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\nsub {\n  bottom: -0.25em;\n}\nsup {\n  top: -0.5em;\n}\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\nbutton,\nselect {\n  text-transform: none;\n}\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n:-moz-focusring {\n  outline: auto;\n}\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\nprogress {\n  vertical-align: baseline;\n}\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n/*\nAdd the correct display in Chrome and Safari.\n*/\nsummary {\n  display: list-item;\n}\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\nfieldset {\n  margin: 0;\n  padding: 0;\n}\nlegend {\n  padding: 0;\n}\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n/*\nPrevent resizing textareas horizontally by default.\n*/\ntextarea {\n  resize: vertical;\n}\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n/*\nSet the default cursor for buttons.\n*/\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n.sr-only{\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none{\n  pointer-events: none;\n}\n.visible{\n  visibility: visible;\n}\n.collapse{\n  visibility: collapse;\n}\n.static{\n  position: static;\n}\n.fixed{\n  position: fixed;\n}\n.absolute{\n  position: absolute;\n}\n.relative{\n  position: relative;\n}\n.inset-0{\n  inset: 0px;\n}\n.inset-y-0{\n  top: 0px;\n  bottom: 0px;\n}\n.-right-1{\n  right: -0.25rem;\n}\n.-top-1{\n  top: -0.25rem;\n}\n.bottom-0{\n  bottom: 0px;\n}\n.bottom-2{\n  bottom: 0.5rem;\n}\n.bottom-4{\n  bottom: 1rem;\n}\n.left-0{\n  left: 0px;\n}\n.left-1\\/2{\n  left: 50%;\n}\n.left-2{\n  left: 0.5rem;\n}\n.left-3{\n  left: 0.75rem;\n}\n.left-4{\n  left: 1rem;\n}\n.right-0{\n  right: 0px;\n}\n.right-2{\n  right: 0.5rem;\n}\n.right-3{\n  right: 0.75rem;\n}\n.right-4{\n  right: 1rem;\n}\n.top-1\\/2{\n  top: 50%;\n}\n.top-2{\n  top: 0.5rem;\n}\n.top-3{\n  top: 0.75rem;\n}\n.top-4{\n  top: 1rem;\n}\n.top-\\[1vh\\]{\n  top: 1vh;\n}\n.-z-10{\n  z-index: -10;\n}\n.z-10{\n  z-index: 10;\n}\n.z-40{\n  z-index: 40;\n}\n.z-50{\n  z-index: 50;\n}\n.z-\\[9996\\]{\n  z-index: 9996;\n}\n.z-\\[9997\\]{\n  z-index: 9997;\n}\n.z-\\[9998\\]{\n  z-index: 9998;\n}\n.z-\\[9999\\]{\n  z-index: 9999;\n}\n.-mx-1{\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\n.-mx-6{\n  margin-left: -1.5rem;\n  margin-right: -1.5rem;\n}\n.mx-4{\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\n.mx-auto{\n  margin-left: auto;\n  margin-right: auto;\n}\n.my-1{\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\n.mb-1{\n  margin-bottom: 0.25rem;\n}\n.mb-12{\n  margin-bottom: 3rem;\n}\n.mb-2{\n  margin-bottom: 0.5rem;\n}\n.mb-3{\n  margin-bottom: 0.75rem;\n}\n.mb-4{\n  margin-bottom: 1rem;\n}\n.mb-6{\n  margin-bottom: 1.5rem;\n}\n.mb-8{\n  margin-bottom: 2rem;\n}\n.ml-1{\n  margin-left: 0.25rem;\n}\n.ml-2{\n  margin-left: 0.5rem;\n}\n.ml-3{\n  margin-left: 0.75rem;\n}\n.ml-4{\n  margin-left: 1rem;\n}\n.ml-auto{\n  margin-left: auto;\n}\n.mr-1{\n  margin-right: 0.25rem;\n}\n.mr-2{\n  margin-right: 0.5rem;\n}\n.mr-3{\n  margin-right: 0.75rem;\n}\n.mr-4{\n  margin-right: 1rem;\n}\n.mr-auto{\n  margin-right: auto;\n}\n.mt-0\\.5{\n  margin-top: 0.125rem;\n}\n.mt-1{\n  margin-top: 0.25rem;\n}\n.mt-12{\n  margin-top: 3rem;\n}\n.mt-2{\n  margin-top: 0.5rem;\n}\n.mt-3{\n  margin-top: 0.75rem;\n}\n.mt-4{\n  margin-top: 1rem;\n}\n.mt-6{\n  margin-top: 1.5rem;\n}\n.mt-8{\n  margin-top: 2rem;\n}\n.line-clamp-2{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.block{\n  display: block;\n}\n.inline-block{\n  display: inline-block;\n}\n.inline{\n  display: inline;\n}\n.flex{\n  display: flex;\n}\n.inline-flex{\n  display: inline-flex;\n}\n.table{\n  display: table;\n}\n.grid{\n  display: grid;\n}\n.hidden{\n  display: none;\n}\n.aspect-square{\n  aspect-ratio: 1 / 1;\n}\n.aspect-video{\n  aspect-ratio: 16 / 9;\n}\n.h-1{\n  height: 0.25rem;\n}\n.h-1\\.5{\n  height: 0.375rem;\n}\n.h-10{\n  height: 2.5rem;\n}\n.h-11{\n  height: 2.75rem;\n}\n.h-12{\n  height: 3rem;\n}\n.h-14{\n  height: 3.5rem;\n}\n.h-16{\n  height: 4rem;\n}\n.h-2{\n  height: 0.5rem;\n}\n.h-2\\.5{\n  height: 0.625rem;\n}\n.h-20{\n  height: 5rem;\n}\n.h-24{\n  height: 6rem;\n}\n.h-3{\n  height: 0.75rem;\n}\n.h-3\\.5{\n  height: 0.875rem;\n}\n.h-32{\n  height: 8rem;\n}\n.h-4{\n  height: 1rem;\n}\n.h-40{\n  height: 10rem;\n}\n.h-5{\n  height: 1.25rem;\n}\n.h-6{\n  height: 1.5rem;\n}\n.h-64{\n  height: 16rem;\n}\n.h-7{\n  height: 1.75rem;\n}\n.h-8{\n  height: 2rem;\n}\n.h-80{\n  height: 20rem;\n}\n.h-9{\n  height: 2.25rem;\n}\n.h-\\[85vh\\]{\n  height: 85vh;\n}\n.h-\\[var\\(--radix-select-trigger-height\\)\\]{\n  height: var(--radix-select-trigger-height);\n}\n.h-auto{\n  height: auto;\n}\n.h-full{\n  height: 100%;\n}\n.h-px{\n  height: 1px;\n}\n.max-h-16{\n  max-height: 4rem;\n}\n.max-h-40{\n  max-height: 10rem;\n}\n.max-h-48{\n  max-height: 12rem;\n}\n.max-h-60{\n  max-height: 15rem;\n}\n.max-h-64{\n  max-height: 16rem;\n}\n.max-h-96{\n  max-height: 24rem;\n}\n.max-h-\\[70vh\\]{\n  max-height: 70vh;\n}\n.max-h-\\[80vh\\]{\n  max-height: 80vh;\n}\n.max-h-\\[90vh\\]{\n  max-height: 90vh;\n}\n.max-h-\\[95vh\\]{\n  max-height: 95vh;\n}\n.max-h-full{\n  max-height: 100%;\n}\n.max-h-none{\n  max-height: none;\n}\n.min-h-\\[100px\\]{\n  min-height: 100px;\n}\n.min-h-\\[80px\\]{\n  min-height: 80px;\n}\n.min-h-screen{\n  min-height: 100vh;\n}\n.w-1{\n  width: 0.25rem;\n}\n.w-1\\.5{\n  width: 0.375rem;\n}\n.w-1\\/2{\n  width: 50%;\n}\n.w-1\\/3{\n  width: 33.333333%;\n}\n.w-10{\n  width: 2.5rem;\n}\n.w-11{\n  width: 2.75rem;\n}\n.w-12{\n  width: 3rem;\n}\n.w-16{\n  width: 4rem;\n}\n.w-2{\n  width: 0.5rem;\n}\n.w-2\\.5{\n  width: 0.625rem;\n}\n.w-20{\n  width: 5rem;\n}\n.w-24{\n  width: 6rem;\n}\n.w-3{\n  width: 0.75rem;\n}\n.w-3\\.5{\n  width: 0.875rem;\n}\n.w-3\\/4{\n  width: 75%;\n}\n.w-32{\n  width: 8rem;\n}\n.w-4{\n  width: 1rem;\n}\n.w-5{\n  width: 1.25rem;\n}\n.w-5\\/6{\n  width: 83.333333%;\n}\n.w-56{\n  width: 14rem;\n}\n.w-6{\n  width: 1.5rem;\n}\n.w-64{\n  width: 16rem;\n}\n.w-7{\n  width: 1.75rem;\n}\n.w-8{\n  width: 2rem;\n}\n.w-9{\n  width: 2.25rem;\n}\n.w-\\[50vw\\]{\n  width: 50vw;\n}\n.w-full{\n  width: 100%;\n}\n.min-w-0{\n  min-width: 0px;\n}\n.min-w-\\[150px\\]{\n  min-width: 150px;\n}\n.min-w-\\[200px\\]{\n  min-width: 200px;\n}\n.min-w-\\[8rem\\]{\n  min-width: 8rem;\n}\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\]{\n  min-width: var(--radix-select-trigger-width);\n}\n.max-w-2xl{\n  max-width: 42rem;\n}\n.max-w-3xl{\n  max-width: 48rem;\n}\n.max-w-4xl{\n  max-width: 56rem;\n}\n.max-w-6xl{\n  max-width: 72rem;\n}\n.max-w-7xl{\n  max-width: 80rem;\n}\n.max-w-\\[50vw\\]{\n  max-width: 50vw;\n}\n.max-w-full{\n  max-width: 100%;\n}\n.max-w-lg{\n  max-width: 32rem;\n}\n.max-w-md{\n  max-width: 28rem;\n}\n.max-w-none{\n  max-width: none;\n}\n.max-w-sm{\n  max-width: 24rem;\n}\n.flex-1{\n  flex: 1 1 0%;\n}\n.flex-shrink-0{\n  flex-shrink: 0;\n}\n.shrink-0{\n  flex-shrink: 0;\n}\n.caption-bottom{\n  caption-side: bottom;\n}\n.border-collapse{\n  border-collapse: collapse;\n}\n.-translate-x-1\\/2{\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2{\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-0{\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-5{\n  --tw-translate-x: 1.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-\\[-100\\%\\]{\n  --tw-translate-x: -100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-0{\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-180{\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-skew-x-12{\n  --tw-skew-x: -12deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-105{\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform{\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes bounce{\n  0%, 100%{\n    transform: translateY(-25%);\n    animation-timing-function: cubic-bezier(0.8,0,1,1);\n  }\n  50%{\n    transform: none;\n    animation-timing-function: cubic-bezier(0,0,0.2,1);\n  }\n}\n.animate-bounce{\n  animation: bounce 1s infinite;\n}\n@keyframes ping{\n  75%, 100%{\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n.animate-ping{\n  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n}\n@keyframes pulse{\n  50%{\n    opacity: .5;\n  }\n}\n.animate-pulse{\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n@keyframes spin{\n  to{\n    transform: rotate(360deg);\n  }\n}\n.animate-spin{\n  animation: spin 1s linear infinite;\n}\n.cursor-default{\n  cursor: default;\n}\n.cursor-move{\n  cursor: move;\n}\n.cursor-not-allowed{\n  cursor: not-allowed;\n}\n.cursor-pointer{\n  cursor: pointer;\n}\n.select-none{\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n.resize-none{\n  resize: none;\n}\n.resize{\n  resize: both;\n}\n.list-inside{\n  list-style-position: inside;\n}\n.list-disc{\n  list-style-type: disc;\n}\n.appearance-none{\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n}\n.grid-cols-1{\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2{\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-3{\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.grid-cols-5{\n  grid-template-columns: repeat(5, minmax(0, 1fr));\n}\n.flex-row{\n  flex-direction: row;\n}\n.flex-col{\n  flex-direction: column;\n}\n.flex-col-reverse{\n  flex-direction: column-reverse;\n}\n.flex-wrap{\n  flex-wrap: wrap;\n}\n.items-start{\n  align-items: flex-start;\n}\n.items-end{\n  align-items: flex-end;\n}\n.items-center{\n  align-items: center;\n}\n.justify-start{\n  justify-content: flex-start;\n}\n.justify-end{\n  justify-content: flex-end;\n}\n.justify-center{\n  justify-content: center;\n}\n.justify-between{\n  justify-content: space-between;\n}\n.gap-1{\n  gap: 0.25rem;\n}\n.gap-2{\n  gap: 0.5rem;\n}\n.gap-3{\n  gap: 0.75rem;\n}\n.gap-4{\n  gap: 1rem;\n}\n.gap-6{\n  gap: 1.5rem;\n}\n.gap-8{\n  gap: 2rem;\n}\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-6 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-0 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0px * var(--tw-space-y-reverse));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.space-x-reverse > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 1;\n}\n.divide-y > :not([hidden]) ~ :not([hidden]){\n  --tw-divide-y-reverse: 0;\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\n}\n.divide-gray-100 > :not([hidden]) ~ :not([hidden]){\n  --tw-divide-opacity: 1;\n  border-color: rgb(243 244 246 / var(--tw-divide-opacity, 1));\n}\n.overflow-auto{\n  overflow: auto;\n}\n.overflow-hidden{\n  overflow: hidden;\n}\n.overflow-x-auto{\n  overflow-x: auto;\n}\n.overflow-y-auto{\n  overflow-y: auto;\n}\n.truncate{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-nowrap{\n  white-space: nowrap;\n}\n.break-all{\n  word-break: break-all;\n}\n.rounded{\n  border-radius: 0.25rem;\n}\n.rounded-2xl{\n  border-radius: 1rem;\n}\n.rounded-3xl{\n  border-radius: 1.5rem;\n}\n.rounded-full{\n  border-radius: 9999px;\n}\n.rounded-lg{\n  border-radius: var(--radius);\n}\n.rounded-md{\n  border-radius: calc(var(--radius) - 2px);\n}\n.rounded-sm{\n  border-radius: calc(var(--radius) - 4px);\n}\n.rounded-xl{\n  border-radius: 0.75rem;\n}\n.rounded-b-xl{\n  border-bottom-right-radius: 0.75rem;\n  border-bottom-left-radius: 0.75rem;\n}\n.rounded-r-full{\n  border-top-right-radius: 9999px;\n  border-bottom-right-radius: 9999px;\n}\n.rounded-t-2xl{\n  border-top-left-radius: 1rem;\n  border-top-right-radius: 1rem;\n}\n.rounded-t-lg{\n  border-top-left-radius: var(--radius);\n  border-top-right-radius: var(--radius);\n}\n.rounded-t-xl{\n  border-top-left-radius: 0.75rem;\n  border-top-right-radius: 0.75rem;\n}\n.border{\n  border-width: 1px;\n}\n.border-0{\n  border-width: 0px;\n}\n.border-2{\n  border-width: 2px;\n}\n.border-4{\n  border-width: 4px;\n}\n.border-b{\n  border-bottom-width: 1px;\n}\n.border-b-0{\n  border-bottom-width: 0px;\n}\n.border-b-2{\n  border-bottom-width: 2px;\n}\n.border-l-4{\n  border-left-width: 4px;\n}\n.border-r-4{\n  border-right-width: 4px;\n}\n.border-t{\n  border-top-width: 1px;\n}\n.border-t-2{\n  border-top-width: 2px;\n}\n.border-dashed{\n  border-style: dashed;\n}\n.border-amber-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));\n}\n.border-blue-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));\n}\n.border-blue-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\n.border-blue-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\n}\n.border-blue-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.border-blue-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\n.border-danger-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\n}\n.border-destructive\\/50{\n  border-color: hsl(var(--destructive) / 0.5);\n}\n.border-emerald-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(167 243 208 / var(--tw-border-opacity, 1));\n}\n.border-gray-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\n}\n.border-gray-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\n.border-gray-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.border-green-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));\n}\n.border-green-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\n.border-green-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));\n}\n.border-green-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\n}\n.border-green-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\n.border-indigo-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(224 231 255 / var(--tw-border-opacity, 1));\n}\n.border-indigo-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));\n}\n.border-indigo-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(165 180 252 / var(--tw-border-opacity, 1));\n}\n.border-indigo-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));\n}\n.border-input{\n  border-color: hsl(var(--input));\n}\n.border-orange-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\n}\n.border-primary-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(102 184 255 / var(--tw-border-opacity, 1));\n}\n.border-purple-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));\n}\n.border-purple-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\n}\n.border-purple-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));\n}\n.border-purple-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));\n}\n.border-red-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\n.border-red-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\n}\n.border-red-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n.border-red-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\n.border-secondary-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\n}\n.border-sky-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(224 242 254 / var(--tw-border-opacity, 1));\n}\n.border-sky-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(186 230 253 / var(--tw-border-opacity, 1));\n}\n.border-sky-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\n}\n.border-sky-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(2 132 199 / var(--tw-border-opacity, 1));\n}\n.border-slate-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));\n}\n.border-slate-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\n}\n.border-slate-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\n}\n.border-slate-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));\n}\n.border-slate-700{\n  --tw-border-opacity: 1;\n  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));\n}\n.border-transparent{\n  border-color: transparent;\n}\n.border-white{\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-white\\/30{\n  border-color: rgb(255 255 255 / 0.3);\n}\n.border-yellow-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\n}\n.border-yellow-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));\n}\n.border-blue-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\n.border-gray-700{\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n}\n.border-t-red-600{\n  --tw-border-opacity: 1;\n  border-top-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\n.border-t-slate-600{\n  --tw-border-opacity: 1;\n  border-top-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\n}\n.border-t-transparent{\n  border-top-color: transparent;\n}\n.border-t-white{\n  --tw-border-opacity: 1;\n  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-t-white\\/60{\n  border-top-color: rgb(255 255 255 / 0.6);\n}\n.border-opacity-20{\n  --tw-border-opacity: 0.2;\n}\n.border-opacity-30{\n  --tw-border-opacity: 0.3;\n}\n.bg-amber-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));\n}\n.bg-amber-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\n}\n.bg-background{\n  background-color: hsl(var(--background));\n}\n.bg-black{\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n.bg-black\\/0{\n  background-color: rgb(0 0 0 / 0);\n}\n.bg-black\\/60{\n  background-color: rgb(0 0 0 / 0.6);\n}\n.bg-black\\/70{\n  background-color: rgb(0 0 0 / 0.7);\n}\n.bg-blue-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.bg-destructive{\n  background-color: hsl(var(--destructive));\n}\n.bg-emerald-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));\n}\n.bg-gold-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-300{\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-400{\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-50\\/30{\n  background-color: rgb(249 250 251 / 0.3);\n}\n.bg-gray-50\\/80{\n  background-color: rgb(249 250 251 / 0.8);\n}\n.bg-gray-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-900{\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\n.bg-green-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\n.bg-green-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\n.bg-green-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\n.bg-indigo-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\n}\n.bg-indigo-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\n}\n.bg-indigo-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));\n}\n.bg-input{\n  background-color: hsl(var(--input));\n}\n.bg-muted{\n  background-color: hsl(var(--muted));\n}\n.bg-muted\\/50{\n  background-color: hsl(var(--muted) / 0.5);\n}\n.bg-orange-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-500\\/80{\n  background-color: rgb(249 115 22 / 0.8);\n}\n.bg-orange-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\n}\n.bg-popover{\n  background-color: hsl(var(--popover));\n}\n.bg-primary{\n  background-color: hsl(var(--primary));\n}\n.bg-purple-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\n}\n.bg-red-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\n.bg-red-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n.bg-red-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n.bg-red-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.bg-secondary-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\n}\n.bg-sky-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));\n}\n.bg-sky-50\\/50{\n  background-color: rgb(240 249 255 / 0.5);\n}\n.bg-sky-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\n}\n.bg-transparent{\n  background-color: transparent;\n}\n.bg-white{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.bg-white\\/20{\n  background-color: rgb(255 255 255 / 0.2);\n}\n.bg-white\\/40{\n  background-color: rgb(255 255 255 / 0.4);\n}\n.bg-white\\/50{\n  background-color: rgb(255 255 255 / 0.5);\n}\n.bg-white\\/80{\n  background-color: rgb(255 255 255 / 0.8);\n}\n.bg-white\\/90{\n  background-color: rgb(255 255 255 / 0.9);\n}\n.bg-white\\/95{\n  background-color: rgb(255 255 255 / 0.95);\n}\n.bg-yellow-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-900\\/90{\n  background-color: rgb(17 24 39 / 0.9);\n}\n.bg-opacity-0{\n  --tw-bg-opacity: 0;\n}\n.bg-opacity-10{\n  --tw-bg-opacity: 0.1;\n}\n.bg-opacity-20{\n  --tw-bg-opacity: 0.2;\n}\n.bg-opacity-40{\n  --tw-bg-opacity: 0.4;\n}\n.bg-opacity-50{\n  --tw-bg-opacity: 0.5;\n}\n.bg-opacity-60{\n  --tw-bg-opacity: 0.6;\n}\n.bg-opacity-75{\n  --tw-bg-opacity: 0.75;\n}\n.bg-gradient-to-b{\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\n.bg-gradient-to-br{\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r{\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-t{\n  background-image: linear-gradient(to top, var(--tw-gradient-stops));\n}\n.from-amber-50{\n  --tw-gradient-from: #fffbeb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 251 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-black\\/50{\n  --tw-gradient-from: rgb(0 0 0 / 0.5) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-400{\n  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-50{\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-500{\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-600{\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-danger-500{\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-emerald-600{\n  --tw-gradient-from: #059669 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gold-500{\n  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-50{\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-500{\n  --tw-gradient-from: #6b7280 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(107 114 128 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-700{\n  --tw-gradient-from: #374151 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(55 65 81 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-100{\n  --tw-gradient-from: #dcfce7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(220 252 231 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-400{\n  --tw-gradient-from: #4ade80 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-50{\n  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-500{\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-600{\n  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-indigo-100{\n  --tw-gradient-from: #e0e7ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(224 231 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-indigo-400{\n  --tw-gradient-from: #818cf8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(129 140 248 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-indigo-50{\n  --tw-gradient-from: #eef2ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(238 242 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-indigo-500{\n  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-indigo-600{\n  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-info-500{\n  --tw-gradient-from: #0ea5e9 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(14 165 233 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-orange-50{\n  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-orange-500{\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-500{\n  --tw-gradient-from: #007bff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 123 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-400{\n  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-50{\n  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-500{\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-600{\n  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-red-400{\n  --tw-gradient-from: #f87171 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(248 113 113 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-red-50{\n  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(254 242 242 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-red-500{\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-red-600{\n  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-secondary-100{\n  --tw-gradient-from: #f1f5f9 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(241 245 249 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-secondary-50{\n  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-sky-50{\n  --tw-gradient-from: #f0f9ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(240 249 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-sky-500{\n  --tw-gradient-from: #0ea5e9 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(14 165 233 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-sky-600{\n  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-100{\n  --tw-gradient-from: #f1f5f9 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(241 245 249 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-50{\n  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-700{\n  --tw-gradient-from: #334155 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(51 65 85 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-800{\n  --tw-gradient-from: #1e293b var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-900{\n  --tw-gradient-from: #0f172a var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-success-500{\n  --tw-gradient-from: #28a745 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(40 167 69 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-transparent{\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-warning-500{\n  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-white{\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-white\\/20{\n  --tw-gradient-from: rgb(255 255 255 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-yellow-500{\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.via-blue-400\\/20{\n  --tw-gradient-to: rgb(96 165 250 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(96 165 250 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-blue-50{\n  --tw-gradient-to: rgb(239 246 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #eff6ff var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-blue-600\\/5{\n  --tw-gradient-to: rgb(37 99 235 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(37 99 235 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-green-600{\n  --tw-gradient-to: rgb(22 163 74 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #16a34a var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-indigo-50{\n  --tw-gradient-to: rgb(238 242 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #eef2ff var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-purple-700{\n  --tw-gradient-to: rgb(126 34 206 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #7e22ce var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-red-700{\n  --tw-gradient-to: rgb(185 28 28 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #b91c1c var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-transparent{\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-white{\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-white\\/10{\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.to-blue-100{\n  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);\n}\n.to-blue-50{\n  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);\n}\n.to-blue-500{\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\n}\n.to-blue-600{\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\n}\n.to-blue-700{\n  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);\n}\n.to-danger-600{\n  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);\n}\n.to-emerald-100{\n  --tw-gradient-to: #d1fae5 var(--tw-gradient-to-position);\n}\n.to-emerald-50{\n  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);\n}\n.to-emerald-500{\n  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);\n}\n.to-emerald-600{\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\n}\n.to-gold-600{\n  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);\n}\n.to-gray-100{\n  --tw-gradient-to: #f3f4f6 var(--tw-gradient-to-position);\n}\n.to-gray-50{\n  --tw-gradient-to: #f9fafb var(--tw-gradient-to-position);\n}\n.to-gray-600{\n  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);\n}\n.to-gray-800{\n  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);\n}\n.to-green-100{\n  --tw-gradient-to: #dcfce7 var(--tw-gradient-to-position);\n}\n.to-green-50{\n  --tw-gradient-to: #f0fdf4 var(--tw-gradient-to-position);\n}\n.to-green-600{\n  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);\n}\n.to-green-700{\n  --tw-gradient-to: #15803d var(--tw-gradient-to-position);\n}\n.to-indigo-100{\n  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);\n}\n.to-indigo-400\\/20{\n  --tw-gradient-to: rgb(129 140 248 / 0.2) var(--tw-gradient-to-position);\n}\n.to-indigo-50{\n  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);\n}\n.to-indigo-600{\n  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);\n}\n.to-indigo-600\\/5{\n  --tw-gradient-to: rgb(79 70 229 / 0.05) var(--tw-gradient-to-position);\n}\n.to-indigo-700{\n  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);\n}\n.to-info-600{\n  --tw-gradient-to: #0284c7 var(--tw-gradient-to-position);\n}\n.to-orange-100{\n  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);\n}\n.to-orange-50{\n  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);\n}\n.to-orange-500{\n  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);\n}\n.to-orange-600{\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\n}\n.to-pink-50{\n  --tw-gradient-to: #fdf2f8 var(--tw-gradient-to-position);\n}\n.to-pink-500{\n  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);\n}\n.to-pink-600{\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\n}\n.to-primary-600{\n  --tw-gradient-to: #0056cc var(--tw-gradient-to-position);\n}\n.to-purple-100{\n  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);\n}\n.to-purple-50{\n  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);\n}\n.to-purple-500{\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\n}\n.to-purple-600{\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\n}\n.to-red-100{\n  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);\n}\n.to-red-50{\n  --tw-gradient-to: #fef2f2 var(--tw-gradient-to-position);\n}\n.to-red-600{\n  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);\n}\n.to-secondary-100{\n  --tw-gradient-to: #f1f5f9 var(--tw-gradient-to-position);\n}\n.to-secondary-200{\n  --tw-gradient-to: #e2e8f0 var(--tw-gradient-to-position);\n}\n.to-sky-600{\n  --tw-gradient-to: #0284c7 var(--tw-gradient-to-position);\n}\n.to-sky-700{\n  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);\n}\n.to-slate-100{\n  --tw-gradient-to: #f1f5f9 var(--tw-gradient-to-position);\n}\n.to-slate-50{\n  --tw-gradient-to: #f8fafc var(--tw-gradient-to-position);\n}\n.to-slate-800{\n  --tw-gradient-to: #1e293b var(--tw-gradient-to-position);\n}\n.to-slate-900{\n  --tw-gradient-to: #0f172a var(--tw-gradient-to-position);\n}\n.to-success-600{\n  --tw-gradient-to: #228b3c var(--tw-gradient-to-position);\n}\n.to-teal-600{\n  --tw-gradient-to: #0d9488 var(--tw-gradient-to-position);\n}\n.to-transparent{\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\n.to-warning-600{\n  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);\n}\n.to-yellow-600{\n  --tw-gradient-to: #ca8a04 var(--tw-gradient-to-position);\n}\n.bg-clip-text{\n  -webkit-background-clip: text;\n          background-clip: text;\n}\n.fill-current{\n  fill: currentColor;\n}\n.object-contain{\n  -o-object-fit: contain;\n     object-fit: contain;\n}\n.object-cover{\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n.p-0{\n  padding: 0px;\n}\n.p-1{\n  padding: 0.25rem;\n}\n.p-10{\n  padding: 2.5rem;\n}\n.p-12{\n  padding: 3rem;\n}\n.p-2{\n  padding: 0.5rem;\n}\n.p-3{\n  padding: 0.75rem;\n}\n.p-4{\n  padding: 1rem;\n}\n.p-5{\n  padding: 1.25rem;\n}\n.p-6{\n  padding: 1.5rem;\n}\n.p-8{\n  padding: 2rem;\n}\n.px-10{\n  padding-left: 2.5rem;\n  padding-right: 2.5rem;\n}\n.px-2{\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-2\\.5{\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3{\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4{\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-5{\n  padding-left: 1.25rem;\n  padding-right: 1.25rem;\n}\n.px-6{\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n.px-8{\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0\\.5{\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1{\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-1\\.5{\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-12{\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-16{\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\n.py-2{\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-2\\.5{\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n}\n.py-3{\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.py-4{\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.py-5{\n  padding-top: 1.25rem;\n  padding-bottom: 1.25rem;\n}\n.py-6{\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\n.py-8{\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.pb-0{\n  padding-bottom: 0px;\n}\n.pb-1{\n  padding-bottom: 0.25rem;\n}\n.pb-2{\n  padding-bottom: 0.5rem;\n}\n.pb-3{\n  padding-bottom: 0.75rem;\n}\n.pb-4{\n  padding-bottom: 1rem;\n}\n.pb-6{\n  padding-bottom: 1.5rem;\n}\n.pl-10{\n  padding-left: 2.5rem;\n}\n.pl-12{\n  padding-left: 3rem;\n}\n.pl-3{\n  padding-left: 0.75rem;\n}\n.pl-4{\n  padding-left: 1rem;\n}\n.pl-8{\n  padding-left: 2rem;\n}\n.pr-10{\n  padding-right: 2.5rem;\n}\n.pr-12{\n  padding-right: 3rem;\n}\n.pr-2{\n  padding-right: 0.5rem;\n}\n.pr-3{\n  padding-right: 0.75rem;\n}\n.pr-4{\n  padding-right: 1rem;\n}\n.pt-0{\n  padding-top: 0px;\n}\n.pt-2{\n  padding-top: 0.5rem;\n}\n.pt-4{\n  padding-top: 1rem;\n}\n.pt-6{\n  padding-top: 1.5rem;\n}\n.pt-8{\n  padding-top: 2rem;\n}\n.text-left{\n  text-align: left;\n}\n.text-center{\n  text-align: center;\n}\n.text-right{\n  text-align: right;\n}\n.align-middle{\n  vertical-align: middle;\n}\n.font-mono{\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n.text-2xl{\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl{\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl{\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-5xl{\n  font-size: 3rem;\n  line-height: 1;\n}\n.text-base{\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-lg{\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl{\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs{\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-black{\n  font-weight: 900;\n}\n.font-bold{\n  font-weight: 700;\n}\n.font-extrabold{\n  font-weight: 800;\n}\n.font-medium{\n  font-weight: 500;\n}\n.font-normal{\n  font-weight: 400;\n}\n.font-semibold{\n  font-weight: 600;\n}\n.italic{\n  font-style: italic;\n}\n.leading-none{\n  line-height: 1;\n}\n.leading-relaxed{\n  line-height: 1.625;\n}\n.leading-tight{\n  line-height: 1.25;\n}\n.tracking-tight{\n  letter-spacing: -0.025em;\n}\n.tracking-widest{\n  letter-spacing: 0.1em;\n}\n.text-amber-500{\n  --tw-text-opacity: 1;\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\n}\n.text-amber-600{\n  --tw-text-opacity: 1;\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\n}\n.text-amber-700{\n  --tw-text-opacity: 1;\n  color: rgb(180 83 9 / var(--tw-text-opacity, 1));\n}\n.text-amber-800{\n  --tw-text-opacity: 1;\n  color: rgb(146 64 14 / var(--tw-text-opacity, 1));\n}\n.text-blue-100{\n  --tw-text-opacity: 1;\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\n}\n.text-blue-500{\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\n.text-blue-600{\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.text-blue-700{\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.text-blue-800{\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n.text-blue-900{\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\n.text-danger-600{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.text-danger-700{\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n.text-destructive{\n  color: hsl(var(--destructive));\n}\n.text-destructive-foreground{\n  color: hsl(var(--destructive-foreground));\n}\n.text-emerald-600{\n  --tw-text-opacity: 1;\n  color: rgb(5 150 105 / var(--tw-text-opacity, 1));\n}\n.text-emerald-700{\n  --tw-text-opacity: 1;\n  color: rgb(4 120 87 / var(--tw-text-opacity, 1));\n}\n.text-foreground{\n  color: hsl(var(--foreground));\n}\n.text-gray-300{\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n.text-gray-400{\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.text-gray-500{\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\n.text-gray-600{\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n.text-gray-700{\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\n.text-gray-800{\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\n.text-gray-900{\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\n.text-green-100{\n  --tw-text-opacity: 1;\n  color: rgb(220 252 231 / var(--tw-text-opacity, 1));\n}\n.text-green-500{\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\n.text-green-600{\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\n.text-green-700{\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\n.text-green-800{\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\n.text-green-900{\n  --tw-text-opacity: 1;\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\n}\n.text-indigo-100{\n  --tw-text-opacity: 1;\n  color: rgb(224 231 255 / var(--tw-text-opacity, 1));\n}\n.text-indigo-400{\n  --tw-text-opacity: 1;\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\n}\n.text-indigo-500{\n  --tw-text-opacity: 1;\n  color: rgb(99 102 241 / var(--tw-text-opacity, 1));\n}\n.text-indigo-600{\n  --tw-text-opacity: 1;\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\n}\n.text-indigo-800{\n  --tw-text-opacity: 1;\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\n}\n.text-indigo-900{\n  --tw-text-opacity: 1;\n  color: rgb(49 46 129 / var(--tw-text-opacity, 1));\n}\n.text-muted-foreground{\n  color: hsl(var(--muted-foreground));\n}\n.text-orange-500{\n  --tw-text-opacity: 1;\n  color: rgb(249 115 22 / var(--tw-text-opacity, 1));\n}\n.text-orange-600{\n  --tw-text-opacity: 1;\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\n}\n.text-orange-700{\n  --tw-text-opacity: 1;\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\n}\n.text-orange-800{\n  --tw-text-opacity: 1;\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\n}\n.text-orange-900{\n  --tw-text-opacity: 1;\n  color: rgb(124 45 18 / var(--tw-text-opacity, 1));\n}\n.text-popover-foreground{\n  color: hsl(var(--popover-foreground));\n}\n.text-primary-600{\n  --tw-text-opacity: 1;\n  color: rgb(0 86 204 / var(--tw-text-opacity, 1));\n}\n.text-primary-700{\n  --tw-text-opacity: 1;\n  color: rgb(0 64 153 / var(--tw-text-opacity, 1));\n}\n.text-primary-foreground{\n  color: hsl(var(--primary-foreground));\n}\n.text-purple-100{\n  --tw-text-opacity: 1;\n  color: rgb(243 232 255 / var(--tw-text-opacity, 1));\n}\n.text-purple-500{\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\n.text-purple-600{\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.text-purple-700{\n  --tw-text-opacity: 1;\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\n}\n.text-purple-800{\n  --tw-text-opacity: 1;\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\n}\n.text-purple-900{\n  --tw-text-opacity: 1;\n  color: rgb(88 28 135 / var(--tw-text-opacity, 1));\n}\n.text-red-100{\n  --tw-text-opacity: 1;\n  color: rgb(254 226 226 / var(--tw-text-opacity, 1));\n}\n.text-red-200{\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\n.text-red-400{\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n.text-red-500{\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.text-red-600{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.text-red-700{\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n.text-red-800{\n  --tw-text-opacity: 1;\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\n}\n.text-secondary-400{\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\n.text-secondary-500{\n  --tw-text-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\n}\n.text-secondary-600{\n  --tw-text-opacity: 1;\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\n}\n.text-secondary-700{\n  --tw-text-opacity: 1;\n  color: rgb(51 65 85 / var(--tw-text-opacity, 1));\n}\n.text-secondary-800{\n  --tw-text-opacity: 1;\n  color: rgb(30 41 59 / var(--tw-text-opacity, 1));\n}\n.text-sky-100{\n  --tw-text-opacity: 1;\n  color: rgb(224 242 254 / var(--tw-text-opacity, 1));\n}\n.text-sky-600{\n  --tw-text-opacity: 1;\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\n}\n.text-sky-700{\n  --tw-text-opacity: 1;\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\n}\n.text-sky-800{\n  --tw-text-opacity: 1;\n  color: rgb(7 89 133 / var(--tw-text-opacity, 1));\n}\n.text-slate-300{\n  --tw-text-opacity: 1;\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\n}\n.text-slate-400{\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\n.text-slate-500{\n  --tw-text-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\n}\n.text-slate-600{\n  --tw-text-opacity: 1;\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\n}\n.text-slate-700{\n  --tw-text-opacity: 1;\n  color: rgb(51 65 85 / var(--tw-text-opacity, 1));\n}\n.text-slate-800{\n  --tw-text-opacity: 1;\n  color: rgb(30 41 59 / var(--tw-text-opacity, 1));\n}\n.text-transparent{\n  color: transparent;\n}\n.text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-yellow-500{\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\n.text-yellow-600{\n  --tw-text-opacity: 1;\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\n}\n.text-yellow-700{\n  --tw-text-opacity: 1;\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\n}\n.text-yellow-800{\n  --tw-text-opacity: 1;\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\n}\n.text-blue-400{\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.text-red-900{\n  --tw-text-opacity: 1;\n  color: rgb(127 29 29 / var(--tw-text-opacity, 1));\n}\n.underline-offset-4{\n  text-underline-offset: 4px;\n}\n.antialiased{\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.opacity-0{\n  opacity: 0;\n}\n.opacity-20{\n  opacity: 0.2;\n}\n.opacity-30{\n  opacity: 0.3;\n}\n.opacity-50{\n  opacity: 0.5;\n}\n.opacity-60{\n  opacity: 0.6;\n}\n.opacity-80{\n  opacity: 0.8;\n}\n.opacity-90{\n  opacity: 0.9;\n}\n.opacity-70{\n  opacity: 0.7;\n}\n.shadow{\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-2xl{\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-md{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm{\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline-none{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.outline{\n  outline-style: solid;\n}\n.ring-0{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-4{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-offset-background{\n  --tw-ring-offset-color: hsl(var(--background));\n}\n.blur-xl{\n  --tw-blur: blur(24px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow{\n  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter{\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-md{\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-sm{\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-all{\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity{\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow{\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform{\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-150{\n  transition-duration: 150ms;\n}\n.duration-200{\n  transition-duration: 200ms;\n}\n.duration-300{\n  transition-duration: 300ms;\n}\n.duration-500{\n  transition-duration: 500ms;\n}\n.duration-700{\n  transition-duration: 700ms;\n}\n.duration-100{\n  transition-duration: 100ms;\n}\n.ease-out{\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n@keyframes enter{\n  from{\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit{\n  to{\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.animate-in{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.fade-in-0{\n  --tw-enter-opacity: 0;\n}\n.zoom-in-95{\n  --tw-enter-scale: .95;\n}\n.slide-in-from-top-2{\n  --tw-enter-translate-y: -0.5rem;\n}\n.duration-150{\n  animation-duration: 150ms;\n}\n.duration-200{\n  animation-duration: 200ms;\n}\n.duration-300{\n  animation-duration: 300ms;\n}\n.duration-500{\n  animation-duration: 500ms;\n}\n.duration-700{\n  animation-duration: 700ms;\n}\n.duration-100{\n  animation-duration: 100ms;\n}\n.ease-out{\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n\n:root {\n  /* نظام ألوان ديوان أبو علوش المحدث - وفقاً للمتطلبات الجديدة */\n  --background: 249 249 249; /* #f9f9f9 - خلفية رمادية فاتحة */\n  --foreground: 51 51 51; /* #333333 - نص رمادي داكن */\n  --card: 255 255 255; /* #ffffff - بطاقات بيضاء نظيفة */\n  --card-foreground: 51 51 51; /* #333333 - نص البطاقات */\n  --popover: 255 255 255; /* #ffffff - النوافذ المنبثقة */\n  --popover-foreground: 51 51 51; /* #333333 - نص النوافذ المنبثقة */\n  --primary: 0 123 255; /* #007bff - أزرق أساسي */\n  --primary-foreground: 255 255 255; /* أبيض للنص على الأزرق */\n  --secondary: 241 245 249; /* ثانوي ناعم */\n  --secondary-foreground: 71 85 105; /* نص ثانوي */\n  --muted: 241 245 249; /* خافت */\n  --muted-foreground: 100 116 139; /* نص خافت */\n  --accent: 40 167 69; /* #28a745 - أخضر للعناصر المميزة */\n  --accent-foreground: 255 255 255; /* أبيض للنص على الأخضر */\n  --destructive: 220 53 69; /* أحمر للحذف */\n  --destructive-foreground: 255 255 255; /* أبيض للنص على الأحمر */\n  --border: 226 232 240; /* حدود ناعمة */\n  --input: 226 232 240; /* حدود المدخلات */\n  --ring: 0 123 255; /* #007bff - حلقة التركيز */\n  --radius: 0.75rem; /* زوايا ناعمة */\n\n  /* ألوان الحالات المتناسقة */\n  --success: 40 167 69; /* #28a745 - أخضر النجاح */\n  --success-foreground: 255 255 255;\n  --warning: 25 25 112; /* كحلي التحذير */\n  --warning-foreground: 255 255 255; /* نص أبيض على الكحلي */\n  --info: 0 123 255; /* #007bff - أزرق المعلومات */\n  --info-foreground: 255 255 255;\n  --danger: 220 53 69; /* أحمر الخطر */\n  --danger-foreground: 255 255 255;\n\n  /* ألوان الترويسات - كحلي غامق وعنابي */\n  --header-primary: 25 25 112; /* كحلي غامق #191970 */\n  --header-secondary: 128 0 32; /* عنابي #800020 */\n  --header-foreground: 255 255 255; /* أبيض للنص على الترويسات */\n\n  /* ألوان إضافية للتدرجات */\n  --gradient-start: 249 249 249; /* #f9f9f9 */\n  --gradient-end: 255 255 255; /* #ffffff */\n  --gradient-primary: 0 123 255; /* #007bff */\n  --gradient-accent: 40 167 69; /* #28a745 */\n}\n\n.dark {\n  /* نظام ألوان الوضع الداكن الجديد - متناسق وجميل */\n  --background: 15 23 42; /* secondary-900 - خلفية داكنة أنيقة */\n  --foreground: 241 245 249; /* secondary-100 - نص فاتح */\n  --card: 30 41 59; /* secondary-800 - بطاقات داكنة */\n  --card-foreground: 241 245 249; /* secondary-100 */\n  --popover: 30 41 59; /* secondary-800 */\n  --popover-foreground: 241 245 249; /* secondary-100 */\n  --primary: 139 147 255; /* primary-400 - أزرق فاتح جميل */\n  --primary-foreground: 30 27 75; /* primary-950 */\n  --secondary: 51 65 85; /* secondary-700 - ثانوي داكن */\n  --secondary-foreground: 241 245 249; /* secondary-100 */\n  --muted: 51 65 85; /* secondary-700 */\n  --muted-foreground: 148 163 184; /* secondary-400 */\n  --accent: 251 191 36; /* gold-400 - ذهبي فاتح */\n  --accent-foreground: 30 27 75; /* primary-950 */\n  --destructive: 248 113 113; /* danger-400 - أحمر فاتح */\n  --destructive-foreground: 30 27 75; /* primary-950 */\n  --border: 51 65 85; /* secondary-700 - حدود داكنة */\n  --input: 51 65 85; /* secondary-700 */\n  --ring: 139 147 255; /* primary-400 */\n\n  /* ألوان الحالات للوضع الداكن */\n  --success: 134 239 172; /* success-300 - أخضر فاتح */\n  --success-foreground: 5 46 22; /* success-950 */\n  --warning: 71 85 105; /* slate-600 - كحلي فاتح */\n  --warning-foreground: 241 245 249; /* slate-100 */\n  --info: 125 211 252; /* info-300 - أزرق سماوي فاتح */\n  --info-foreground: 8 47 73; /* info-950 */\n  --danger: 248 113 113; /* danger-400 - أحمر فاتح */\n  --danger-foreground: 69 10 10; /* danger-950 */\n\n  /* تدرجات للوضع الداكن */\n  --gradient-start: 15 23 42; /* secondary-900 */\n  --gradient-end: 30 41 59; /* secondary-800 */\n  --gradient-primary: 139 147 255; /* primary-400 */\n  --gradient-accent: 251 191 36; /* gold-400 */\n}\n\n* {\n  border-color: hsl(var(--border));\n}\n\nbody {\n  background: hsl(var(--background));\n  color: hsl(var(--foreground));\n  font-family: 'Cairo', 'Almarai', system-ui, sans-serif;\n  direction: rtl;\n}\n\n/* تحسينات للنصوص العربية */\n.arabic-text {\n  font-family: 'Cairo', 'Almarai', system-ui, sans-serif;\n  direction: rtl;\n  text-align: right;\n}\n\n/* تحسينات للأرقام */\n.numbers {\n  font-variant-numeric: tabular-nums;\n}\n\n/* تحسينات للجداول */\n.table-rtl {\n  direction: rtl;\n}\n\n.table-rtl th,\n.table-rtl td {\n  text-align: right;\n}\n\n/* أنماط مخصصة للديوان */\n.diwan-card {\n  background: hsl(var(--card));\n  border-radius: var(--radius);\n  box-shadow: 0 4px 6px -1px hsl(var(--foreground) / 0.1), 0 2px 4px -2px hsl(var(--foreground) / 0.1);\n  border: 1px solid hsl(var(--border));\n  padding: 1.5rem;\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  color: hsl(var(--card-foreground));\n}\n\n.diwan-card:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 8px 25px -5px hsl(var(--foreground) / 0.15);\n}\n\n.diwan-card-elevated {\n  box-shadow: 0 10px 15px -3px hsl(var(--foreground) / 0.1), 0 4px 6px -4px hsl(var(--foreground) / 0.1);\n}\n\n.diwan-card-interactive {\n  cursor: pointer;\n}\n\n.diwan-card-interactive:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 30px -8px hsl(var(--foreground) / 0.2);\n}\n\n/* أزرار ديوان الجديدة - متناسقة وجميلة */\n.diwan-button {\n  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);\n  color: hsl(var(--primary-foreground));\n  font-weight: 600;\n  padding: 0.75rem 1.5rem;\n  border-radius: var(--radius);\n  border: none;\n  cursor: pointer;\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 2px 8px -2px hsl(var(--primary) / 0.25);\n  position: relative;\n  overflow: hidden;\n  font-size: 0.875rem;\n}\n\n.diwan-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px -2px hsl(var(--primary) / 0.35);\n}\n\n.diwan-button:active {\n  transform: translateY(0);\n}\n\n.diwan-button-primary {\n  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);\n  color: hsl(var(--primary-foreground));\n}\n\n.diwan-button-accent {\n  background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--accent)) 100%);\n  color: hsl(var(--accent-foreground));\n}\n\n.diwan-button-success {\n  background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(var(--success)) 100%);\n  color: hsl(var(--success-foreground));\n}\n\n.diwan-button-outline {\n  background: transparent;\n  color: hsl(var(--primary));\n  border: 2px solid hsl(var(--primary));\n  box-shadow: none;\n}\n\n.diwan-button-outline:hover {\n  background: hsl(var(--primary) / 0.05);\n  border-color: hsl(var(--primary));\n}\n\n/* حقول الإدخال الاحترافية والهادئة */\n.diwan-input {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border: 2px solid hsl(var(--border));\n  border-radius: var(--radius);\n  background: hsl(var(--background));\n  color: hsl(var(--foreground));\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  outline: none;\n}\n\n.diwan-input:focus {\n  border-color: hsl(var(--primary));\n  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.1);\n}\n\n.diwan-input:hover {\n  border-color: hsl(var(--border));\n}\n\n.diwan-input::-moz-placeholder {\n  color: hsl(var(--muted-foreground));\n  font-weight: 400;\n}\n\n.diwan-input::placeholder {\n  color: hsl(var(--muted-foreground));\n  font-weight: 400;\n}\n\n/* بطاقات الإحصائيات المحسنة */\n.stats-card {\n  background: hsl(var(--card));\n  border: 1px solid hsl(var(--border));\n  color: hsl(var(--card-foreground));\n  border-radius: var(--radius);\n  padding: 1.5rem;\n  box-shadow: 0 4px 6px -1px hsl(var(--foreground) / 0.1);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.stats-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px -5px hsl(var(--foreground) / 0.15);\n}\n\n.stats-card-primary {\n  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);\n  color: hsl(var(--primary-foreground));\n}\n\n.stats-card-success {\n  background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(var(--success) / 0.8) 100%);\n  color: hsl(var(--success-foreground));\n}\n\n.stats-card-warning {\n  background: linear-gradient(135deg, hsl(var(--warning)) 0%, hsl(var(--warning) / 0.8) 100%);\n  color: hsl(var(--warning-foreground));\n}\n\n.stats-card-error {\n  background: linear-gradient(135deg, hsl(var(--destructive)) 0%, hsl(var(--destructive) / 0.8) 100%);\n  color: hsl(var(--destructive-foreground));\n}\n\n/* لون ذهبي محسن */\n.gold-accent {\n  color: hsl(var(--accent));\n  font-weight: 700;\n}\n\n/* أنماط النوافذ المحسنة */\n.diwan-dialog {\n  background: hsl(var(--background));\n  border-radius: calc(var(--radius) * 1.5);\n  border: 1px solid hsl(var(--border));\n  box-shadow: 0 25px 50px -12px hsl(var(--foreground) / 0.25);\n  -webkit-backdrop-filter: blur(8px);\n          backdrop-filter: blur(8px);\n}\n\n.diwan-dialog-header {\n  padding: 1.5rem 1.5rem 1rem 1.5rem;\n  border-bottom: 1px solid hsl(var(--border));\n  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--background)) 100%);\n  border-radius: calc(var(--radius) * 1.5) calc(var(--radius) * 1.5) 0 0;\n}\n\n.diwan-dialog-content {\n  padding: 1.5rem;\n}\n\n.diwan-dialog-footer {\n  padding: 1rem 1.5rem 1.5rem 1.5rem;\n  border-top: 1px solid hsl(var(--border));\n  background: hsl(var(--muted) / 0.3);\n  border-radius: 0 0 calc(var(--radius) * 1.5) calc(var(--radius) * 1.5);\n}\n\n/* أنماط الجداول المحسنة */\n.diwan-table {\n  width: 100%;\n  border-collapse: collapse;\n  background: hsl(var(--card));\n  border-radius: var(--radius);\n  overflow: hidden;\n  box-shadow: 0 4px 6px -1px hsl(var(--foreground) / 0.1);\n}\n\n.diwan-table th {\n  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--muted) / 0.8) 100%);\n  color: hsl(var(--foreground));\n  font-weight: 600;\n  padding: 1rem;\n  text-align: right;\n  border-bottom: 2px solid hsl(var(--border));\n}\n\n.diwan-table td {\n  padding: 0.875rem 1rem;\n  border-bottom: 1px solid hsl(var(--border));\n  color: hsl(var(--foreground));\n}\n\n.diwan-table tr:hover {\n  background: hsl(var(--muted) / 0.3);\n}\n\n.diwan-table tr:last-child td {\n  border-bottom: none;\n}\n\n/* أنماط الشارات المحسنة */\n.diwan-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.25rem 0.75rem;\n  border-radius: calc(var(--radius) * 0.75);\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n\n.diwan-badge-success {\n  background: hsl(var(--success) / 0.1);\n  color: hsl(var(--success));\n  border: 1px solid hsl(var(--success) / 0.2);\n}\n\n.diwan-badge-warning {\n  background: hsl(var(--warning) / 0.1);\n  color: hsl(var(--warning));\n  border: 1px solid hsl(var(--warning) / 0.2);\n}\n\n.diwan-badge-error {\n  background: hsl(var(--destructive) / 0.1);\n  color: hsl(var(--destructive));\n  border: 1px solid hsl(var(--destructive) / 0.2);\n}\n\n.diwan-badge-info {\n  background: hsl(var(--info) / 0.1);\n  color: hsl(var(--info));\n  border: 1px solid hsl(var(--info) / 0.2);\n}\n\n/* تحسينات التجاوب والتفاعل */\n@media (max-width: 768px) {\n  .diwan-card {\n    padding: 1rem;\n  }\n\n  .diwan-button {\n    padding: 0.625rem 1.25rem;\n    font-size: 0.8rem;\n  }\n\n  .diwan-input {\n    padding: 0.625rem 0.875rem;\n    font-size: 0.875rem;\n  }\n}\n\n/* تأثيرات التحميل */\n@keyframes shimmer {\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n}\n\n.loading-shimmer {\n  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--background)) 50%, hsl(var(--muted)) 75%);\n  background-size: 200px 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n/* تأثيرات الانتقال المحسنة */\n.smooth-transition {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.hover-lift:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 20px 40px -10px hsl(var(--foreground) / 0.2);\n}\n\n.hover-scale:hover {\n  transform: scale(1.02);\n}\n\n.focus-ring:focus {\n  outline: none;\n  ring: 2px;\n  ring-color: hsl(var(--primary));\n  ring-offset: 2px;\n}\n\n/* تحسينات الإمكانية الوصول */\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n/* تحسينات الطباعة */\n@media print {\n  .no-print {\n    display: none !important;\n  }\n\n  .diwan-card {\n    box-shadow: none;\n    border: 1px solid #000;\n  }\n\n  .diwan-button {\n    background: transparent !important;\n    color: #000 !important;\n    border: 1px solid #000 !important;\n  }\n}\n\n/* تحسينات خاصة بلوحة التحكم الجديدة */\n.dashboard-enhanced {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: 100vh;\n}\n\n.dashboard-card-enhanced {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.dashboard-card-enhanced:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.dashboard-stats-card {\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);\n  -webkit-backdrop-filter: blur(15px);\n          backdrop-filter: blur(15px);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 24px;\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.dashboard-stats-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #007bff 0%, #28a745 50%, #800020 100%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.dashboard-stats-card:hover::before {\n  opacity: 1;\n}\n\n.dashboard-stats-card:hover {\n  transform: translateY(-6px) scale(1.02);\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);\n  border-color: rgba(255, 255, 255, 0.5);\n}\n\n/* تأثيرات متحركة للأرقام */\n.dashboard-number {\n  background: linear-gradient(135deg, #191970 0%, #800020 100%);\n  background-clip: text;\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  font-weight: 900;\n  font-size: 2.5rem;\n  line-height: 1.2;\n  transition: all 0.3s ease;\n}\n\n.dashboard-number:hover {\n  transform: scale(1.05);\n}\n\n/* تحسينات الشريط الجانبي */\n.sidebar-enhanced {\n  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);\n  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);\n}\n\n.sidebar-nav-item {\n  position: relative;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.sidebar-nav-item::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 0;\n  height: 60%;\n  background: linear-gradient(135deg, #007bff 0%, #28a745 100%);\n  border-radius: 0 8px 8px 0;\n  transition: width 0.3s ease;\n}\n\n.sidebar-nav-item.active::before {\n  width: 4px;\n}\n\n/* تحسينات الرأس */\n.header-enhanced {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  border-bottom: 2px solid rgba(0, 123, 255, 0.1);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n}\n\n/* تأثيرات التحميل المحسنة */\n@keyframes shimmer-enhanced {\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n}\n\n.loading-shimmer-enhanced {\n  background: linear-gradient(90deg,\n    rgba(255, 255, 255, 0.1) 25%,\n    rgba(255, 255, 255, 0.3) 50%,\n    rgba(255, 255, 255, 0.1) 75%);\n  background-size: 200px 100%;\n  animation: shimmer-enhanced 1.5s infinite;\n}\n\n/* تحسينات الاستجابة */\n@media (max-width: 768px) {\n  .dashboard-card-enhanced {\n    border-radius: 16px;\n    padding: 1rem;\n  }\n\n  .dashboard-stats-card {\n    border-radius: 20px;\n  }\n\n  .dashboard-number {\n    font-size: 2rem;\n  }\n}\n\n/* تحسينات خاصة بصفحة الإعدادات */\n.settings-page {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: 100vh;\n}\n\n.settings-card {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  border: 1px solid rgb(226 232 240);\n  transition: all 0.2s ease-in-out;\n}\n\n.settings-card:hover {\n  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  transform: translateY(-1px);\n}\n\n.settings-header-icon {\n  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);\n  border-radius: 8px;\n  padding: 8px;\n  box-shadow: 0 4px 6px -1px rgb(14 165 233 / 0.3);\n}\n\n.settings-section-icon {\n  border-radius: 6px;\n  padding: 6px;\n  box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.2);\n}\n\n/* تحسينات للمفاتيح والأزرار */\n.settings-switch {\n  transition: all 0.2s ease-in-out;\n}\n\n.settings-button {\n  transition: all 0.2s ease-in-out;\n  font-weight: 500;\n}\n\n.settings-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);\n}\n\n/* تحسينات للتبويبات */\n.settings-tabs {\n  background: rgb(248 250 252);\n  border-radius: 8px;\n  padding: 4px;\n}\n\n.settings-tab-trigger {\n  border-radius: 6px;\n  transition: all 0.2s ease-in-out;\n  font-weight: 500;\n}\n\n.settings-tab-trigger[data-state=\"active\"] {\n  background: white;\n  box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1);\n  color: rgb(14 165 233);\n}\n\n/* تحسينات للحقول */\n.settings-input {\n  transition: all 0.2s ease-in-out;\n  border-radius: 6px;\n}\n\n.settings-input:focus {\n  ring: 2px;\n  ring-color: rgb(14 165 233 / 0.3);\n  border-color: rgb(14 165 233);\n  box-shadow: 0 0 0 3px rgb(14 165 233 / 0.1);\n}\n\n/* متغيرات CSS للثيم المخصص - محدثة وفقاً للألوان الجديدة */\n:root {\n  --theme-primary-color: #007bff; /* أزرق أساسي */\n  --theme-secondary-color: #333333; /* رمادي داكن للنصوص */\n  --theme-accent-color: #28a745; /* أخضر للعناصر المميزة */\n  --theme-background-color: #f9f9f9; /* خلفية رمادية فاتحة */\n  --theme-text-color: #333333; /* نص رمادي داكن */\n  --theme-header-color: #191970; /* كحلي غامق للترويسات */\n  --theme-header-alt-color: #800020; /* عنابي للترويسات البديلة */\n  --theme-font-family: 'Cairo', 'Almarai', system-ui, sans-serif;\n  --theme-font-size: 14px;\n  --theme-font-weight: normal;\n  --theme-line-height: 1.5;\n}\n\n/* تطبيق متغيرات الثيم */\n.theme-applied {\n  font-family: var(--theme-font-family);\n  font-size: var(--theme-font-size);\n  font-weight: var(--theme-font-weight);\n  line-height: var(--theme-line-height);\n  background-color: var(--theme-background-color);\n  color: var(--theme-text-color);\n  transition: all 0.3s ease;\n}\n\n/* أنماط الثيم الداكن المخصص */\n.dark-theme {\n  --theme-background-color: #111827;\n  --theme-text-color: #f9fafb;\n}\n\n.dark-theme .diwan-card {\n  background-color: #1f2937;\n  border-color: #374151;\n  color: #f9fafb;\n}\n\n.dark-theme .settings-card {\n  background-color: #1f2937;\n  border-color: #374151;\n  color: #f9fafb;\n}\n\n/* أنماط الألوان المخصصة */\n.custom-primary {\n  background-color: var(--theme-primary-color) !important;\n  border-color: var(--theme-primary-color) !important;\n  color: white !important;\n}\n\n.custom-secondary {\n  background-color: var(--theme-secondary-color) !important;\n  border-color: var(--theme-secondary-color) !important;\n  color: white !important;\n}\n\n.custom-accent {\n  background-color: var(--theme-accent-color) !important;\n  border-color: var(--theme-accent-color) !important;\n  color: white !important;\n}\n\n/* أنماط الترويسات الجديدة */\n.header-primary {\n  background-color: var(--theme-header-color) !important;\n  color: white !important;\n  border-color: var(--theme-header-color) !important;\n}\n\n.header-secondary {\n  background-color: var(--theme-header-alt-color) !important;\n  color: white !important;\n  border-color: var(--theme-header-alt-color) !important;\n}\n\n/* أنماط الأزرار المحدثة */\n.btn-primary {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\n.btn-primary:hover {\n  background-color: #0056cc !important;\n  border-color: #0056cc !important;\n}\n\n.btn-success {\n  background-color: #28a745 !important;\n  border-color: #28a745 !important;\n  color: white !important;\n}\n\n.btn-success:hover {\n  background-color: #228b3c !important;\n  border-color: #228b3c !important;\n}\n\n/* أنماط النصوص المحدثة */\n.text-primary-custom {\n  color: #007bff !important;\n}\n\n.text-success-custom {\n  color: #28a745 !important;\n}\n\n.text-header-primary {\n  color: #191970 !important;\n}\n\n.text-header-secondary {\n  color: #800020 !important;\n}\n\n/* أنماط الخلفيات المحدثة */\n.bg-light-custom {\n  background-color: #f9f9f9 !important;\n}\n\n.bg-white-custom {\n  background-color: #ffffff !important;\n}\n\n.bg-text-custom {\n  background-color: #333333 !important;\n  color: white !important;\n}\n\n.file\\:border-0::file-selector-button{\n  border-width: 0px;\n}\n\n.file\\:bg-transparent::file-selector-button{\n  background-color: transparent;\n}\n\n.file\\:text-sm::file-selector-button{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n\n.file\\:font-medium::file-selector-button{\n  font-weight: 500;\n}\n\n.placeholder\\:text-muted-foreground::-moz-placeholder{\n  color: hsl(var(--muted-foreground));\n}\n\n.placeholder\\:text-muted-foreground::placeholder{\n  color: hsl(var(--muted-foreground));\n}\n\n.placeholder\\:text-secondary-400::-moz-placeholder{\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\n\n.placeholder\\:text-secondary-400::placeholder{\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\n\n.last\\:border-b-0:last-child{\n  border-bottom-width: 0px;\n}\n\n.hover\\:-translate-y-0\\.5:hover{\n  --tw-translate-y: -0.125rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:-translate-y-1:hover{\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:-translate-y-2:hover{\n  --tw-translate-y: -0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:scale-105:hover{\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:scale-110:hover{\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:scale-\\[1\\.02\\]:hover{\n  --tw-scale-x: 1.02;\n  --tw-scale-y: 1.02;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:transform:hover{\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:border-blue-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-gray-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-gray-400:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-green-200:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-green-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-indigo-200:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-primary-400:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(51 159 255 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-purple-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-red-200:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-red-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-red-400:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-red-500:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-secondary-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-sky-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(125 211 252 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:bg-black\\/10:hover{\n  background-color: rgb(0 0 0 / 0.1);\n}\n\n.hover\\:bg-blue-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-blue-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-blue-50\\/50:hover{\n  background-color: rgb(239 246 255 / 0.5);\n}\n\n.hover\\:bg-blue-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-destructive\\/80:hover{\n  background-color: hsl(var(--destructive) / 0.8);\n}\n\n.hover\\:bg-emerald-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-gray-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-gray-200:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-gray-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-green-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-green-200:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-green-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-green-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-indigo-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-muted\\/50:hover{\n  background-color: hsl(var(--muted) / 0.5);\n}\n\n.hover\\:bg-primary-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(204 231 255 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-primary-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(230 243 255 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-primary\\/80:hover{\n  background-color: hsl(var(--primary) / 0.8);\n}\n\n.hover\\:bg-purple-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-purple-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-red-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-red-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-red-50\\/50:hover{\n  background-color: rgb(254 242 242 / 0.5);\n}\n\n.hover\\:bg-red-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-sky-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-slate-200:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-slate-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-slate-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-white:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-white\\/20:hover{\n  background-color: rgb(255 255 255 / 0.2);\n}\n\n.hover\\:bg-white\\/50:hover{\n  background-color: rgb(255 255 255 / 0.5);\n}\n\n.hover\\:bg-yellow-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-opacity-30:hover{\n  --tw-bg-opacity: 0.3;\n}\n\n.hover\\:bg-opacity-75:hover{\n  --tw-bg-opacity: 0.75;\n}\n\n.hover\\:bg-gradient-to-br:hover{\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n\n.hover\\:bg-gradient-to-r:hover{\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n\n.hover\\:from-blue-600:hover{\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-blue-700:hover{\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-danger-600:hover{\n  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-emerald-700:hover{\n  --tw-gradient-from: #047857 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(4 120 87 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-gold-600:hover{\n  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-green-600:hover{\n  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-green-700:hover{\n  --tw-gradient-from: #15803d var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(21 128 61 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-indigo-50:hover{\n  --tw-gradient-from: #eef2ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(238 242 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-indigo-600:hover{\n  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-info-600:hover{\n  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-primary-600:hover{\n  --tw-gradient-from: #0056cc var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 86 204 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-purple-600:hover{\n  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-purple-700:hover{\n  --tw-gradient-from: #7e22ce var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(126 34 206 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-red-500:hover{\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-red-600:hover{\n  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-red-700:hover{\n  --tw-gradient-from: #b91c1c var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(185 28 28 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-secondary-200:hover{\n  --tw-gradient-from: #e2e8f0 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(226 232 240 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-sky-600:hover{\n  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-sky-700:hover{\n  --tw-gradient-from: #0369a1 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(3 105 161 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-success-600:hover{\n  --tw-gradient-from: #228b3c var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(34 139 60 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-warning-600:hover{\n  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:via-green-700:hover{\n  --tw-gradient-to: rgb(21 128 61 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #15803d var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n\n.hover\\:via-purple-800:hover{\n  --tw-gradient-to: rgb(107 33 168 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #6b21a8 var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n\n.hover\\:via-red-800:hover{\n  --tw-gradient-to: rgb(153 27 27 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #991b1b var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n\n.hover\\:to-blue-50:hover{\n  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);\n}\n\n.hover\\:to-blue-700:hover{\n  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-blue-800:hover{\n  --tw-gradient-to: #1e40af var(--tw-gradient-to-position);\n}\n\n.hover\\:to-danger-700:hover{\n  --tw-gradient-to: #b91c1c var(--tw-gradient-to-position);\n}\n\n.hover\\:to-emerald-700:hover{\n  --tw-gradient-to: #047857 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-gold-700:hover{\n  --tw-gradient-to: #b45309 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-green-700:hover{\n  --tw-gradient-to: #15803d var(--tw-gradient-to-position);\n}\n\n.hover\\:to-green-800:hover{\n  --tw-gradient-to: #166534 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-indigo-700:hover{\n  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);\n}\n\n.hover\\:to-indigo-800:hover{\n  --tw-gradient-to: #3730a3 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-info-700:hover{\n  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-orange-700:hover{\n  --tw-gradient-to: #c2410c var(--tw-gradient-to-position);\n}\n\n.hover\\:to-primary-700:hover{\n  --tw-gradient-to: #004099 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-purple-700:hover{\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\n}\n\n.hover\\:to-red-600:hover{\n  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-red-700:hover{\n  --tw-gradient-to: #b91c1c var(--tw-gradient-to-position);\n}\n\n.hover\\:to-secondary-300:hover{\n  --tw-gradient-to: #cbd5e1 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-sky-700:hover{\n  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-sky-800:hover{\n  --tw-gradient-to: #075985 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-success-700:hover{\n  --tw-gradient-to: #1c6e32 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-teal-700:hover{\n  --tw-gradient-to: #0f766e var(--tw-gradient-to-position);\n}\n\n.hover\\:to-warning-700:hover{\n  --tw-gradient-to: #b45309 var(--tw-gradient-to-position);\n}\n\n.hover\\:text-blue-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-blue-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-emerald-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(4 120 87 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-gray-600:hover{\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-gray-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-gray-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-gray-900:hover{\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-green-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-indigo-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-primary-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(0 43 102 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-red-500:hover{\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-red-600:hover{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-red-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-sky-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-white:hover{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:underline:hover{\n  text-decoration-line: underline;\n}\n\n.hover\\:opacity-100:hover{\n  opacity: 1;\n}\n\n.hover\\:shadow-2xl:hover{\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-lg:hover{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-md:hover{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-xl:hover{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.focus\\:border-danger-500:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n\n.focus\\:border-indigo-500:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));\n}\n\n.focus\\:border-primary-500:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(0 123 255 / var(--tw-border-opacity, 1));\n}\n\n.focus\\:border-red-500:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n\n.focus\\:border-sky-500:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\n}\n\n.focus\\:bg-accent:focus{\n  background-color: hsl(var(--accent));\n}\n\n.focus\\:bg-gray-50:focus{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n\n.focus\\:bg-green-50:focus{\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\n\n.focus\\:bg-primary-100:focus{\n  --tw-bg-opacity: 1;\n  background-color: rgb(204 231 255 / var(--tw-bg-opacity, 1));\n}\n\n.focus\\:bg-purple-50:focus{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\n}\n\n.focus\\:text-accent-foreground:focus{\n  color: hsl(var(--accent-foreground));\n}\n\n.focus\\:text-primary-800:focus{\n  --tw-text-opacity: 1;\n  color: rgb(0 43 102 / var(--tw-text-opacity, 1));\n}\n\n.focus\\:outline-none:focus{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus\\:ring-2:focus{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus\\:ring-4:focus{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus\\:ring-danger-500\\/20:focus{\n  --tw-ring-color: rgb(239 68 68 / 0.2);\n}\n\n.focus\\:ring-indigo-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-primary-500\\/20:focus{\n  --tw-ring-color: rgb(0 123 255 / 0.2);\n}\n\n.focus\\:ring-red-100:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(254 226 226 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-red-200:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(254 202 202 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-red-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-ring:focus{\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus\\:ring-sky-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-slate-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-offset-2:focus{\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:border-primary-500:focus-visible{\n  --tw-border-opacity: 1;\n  border-color: rgb(0 123 255 / var(--tw-border-opacity, 1));\n}\n\n.focus-visible\\:outline-none:focus-visible{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus-visible\\:ring-2:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-4:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-primary-500\\/20:focus-visible{\n  --tw-ring-color: rgb(0 123 255 / 0.2);\n}\n\n.focus-visible\\:ring-ring:focus-visible{\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus-visible\\:ring-offset-2:focus-visible{\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:ring-offset-background:focus-visible{\n  --tw-ring-offset-color: hsl(var(--background));\n}\n\n.active\\:scale-95:active{\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.active\\:scale-\\[0\\.98\\]:active{\n  --tw-scale-x: 0.98;\n  --tw-scale-y: 0.98;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.disabled\\:pointer-events-none:disabled{\n  pointer-events: none;\n}\n\n.disabled\\:transform-none:disabled{\n  transform: none;\n}\n\n.disabled\\:cursor-not-allowed:disabled{\n  cursor: not-allowed;\n}\n\n.disabled\\:opacity-50:disabled{\n  opacity: 0.5;\n}\n\n.disabled\\:opacity-70:disabled{\n  opacity: 0.7;\n}\n\n.group:focus-within .group-focus-within\\:text-red-500{\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n\n.group:hover .group-hover\\:translate-x-\\[100\\%\\]{\n  --tw-translate-x: 100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:scale-105{\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:scale-110{\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:bg-black\\/20{\n  background-color: rgb(0 0 0 / 0.2);\n}\n\n.group:hover .group-hover\\:bg-red-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));\n}\n\n.group:hover .group-hover\\:bg-white\\/30{\n  background-color: rgb(255 255 255 / 0.3);\n}\n\n.group:hover .group-hover\\:bg-white\\/60{\n  background-color: rgb(255 255 255 / 0.6);\n}\n\n.group:hover .group-hover\\:bg-opacity-30{\n  --tw-bg-opacity: 0.3;\n}\n\n.group:hover .group-hover\\:text-gray-500{\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\n\n.group:hover .group-hover\\:text-indigo-700{\n  --tw-text-opacity: 1;\n  color: rgb(67 56 202 / var(--tw-text-opacity, 1));\n}\n\n.group:hover .group-hover\\:text-red-600{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n\n.group:hover .group-hover\\:text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.group:hover .group-hover\\:opacity-10{\n  opacity: 0.1;\n}\n\n.group:hover .group-hover\\:opacity-100{\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:shadow-xl{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.group.toaster .group-\\[\\.toaster\\]\\:border-border{\n  border-color: hsl(var(--border));\n}\n\n.group.toast .group-\\[\\.toast\\]\\:bg-muted{\n  background-color: hsl(var(--muted));\n}\n\n.group.toast .group-\\[\\.toast\\]\\:bg-primary{\n  background-color: hsl(var(--primary));\n}\n\n.group.toaster .group-\\[\\.toaster\\]\\:bg-background{\n  background-color: hsl(var(--background));\n}\n\n.group.toast .group-\\[\\.toast\\]\\:text-muted-foreground{\n  color: hsl(var(--muted-foreground));\n}\n\n.group.toast .group-\\[\\.toast\\]\\:text-primary-foreground{\n  color: hsl(var(--primary-foreground));\n}\n\n.group.toaster .group-\\[\\.toaster\\]\\:text-foreground{\n  color: hsl(var(--foreground));\n}\n\n.group.toaster .group-\\[\\.toaster\\]\\:shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed{\n  cursor: not-allowed;\n}\n\n.peer:disabled ~ .peer-disabled\\:opacity-70{\n  opacity: 0.7;\n}\n\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled]{\n  pointer-events: none;\n}\n\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"]{\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"]{\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"]{\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"]{\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[state\\=open\\]\\:rotate-180[data-state=\"open\"]{\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[state\\=active\\]\\:bg-background[data-state=\"active\"]{\n  background-color: hsl(var(--background));\n}\n\n.data-\\[state\\=checked\\]\\:bg-primary-100[data-state=\"checked\"]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(204 231 255 / var(--tw-bg-opacity, 1));\n}\n\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"]{\n  background-color: hsl(var(--accent));\n}\n\n.data-\\[state\\=selected\\]\\:bg-muted[data-state=\"selected\"]{\n  background-color: hsl(var(--muted));\n}\n\n.data-\\[state\\=checked\\]\\:font-semibold[data-state=\"checked\"]{\n  font-weight: 600;\n}\n\n.data-\\[state\\=active\\]\\:text-foreground[data-state=\"active\"]{\n  color: hsl(var(--foreground));\n}\n\n.data-\\[state\\=checked\\]\\:text-primary-800[data-state=\"checked\"]{\n  --tw-text-opacity: 1;\n  color: rgb(0 43 102 / var(--tw-text-opacity, 1));\n}\n\n.data-\\[disabled\\]\\:opacity-50[data-disabled]{\n  opacity: 0.5;\n}\n\n.data-\\[state\\=active\\]\\:shadow-sm[data-state=\"active\"]{\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"]{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"]{\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"]{\n  --tw-exit-opacity: 0;\n}\n\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"]{\n  --tw-enter-opacity: 0;\n}\n\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"]{\n  --tw-exit-scale: .95;\n}\n\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"]{\n  --tw-enter-scale: .95;\n}\n\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"]{\n  --tw-enter-translate-y: -0.5rem;\n}\n\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"]{\n  --tw-enter-translate-x: 0.5rem;\n}\n\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"]{\n  --tw-enter-translate-x: -0.5rem;\n}\n\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"]{\n  --tw-enter-translate-y: 0.5rem;\n}\n\n.dark\\:border-destructive:is(.dark *){\n  border-color: hsl(var(--destructive));\n}\n\n@media (min-width: 640px){\n  .sm\\:col-span-2{\n    grid-column: span 2 / span 2;\n  }\n  .sm\\:ml-4{\n    margin-left: 1rem;\n  }\n  .sm\\:inline{\n    display: inline;\n  }\n  .sm\\:w-\\[200px\\]{\n    width: 200px;\n  }\n  .sm\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .sm\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .sm\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .sm\\:flex-row{\n    flex-direction: row;\n  }\n  .sm\\:items-start{\n    align-items: flex-start;\n  }\n  .sm\\:items-center{\n    align-items: center;\n  }\n  .sm\\:justify-end{\n    justify-content: flex-end;\n  }\n  .sm\\:justify-between{\n    justify-content: space-between;\n  }\n  .sm\\:gap-3{\n    gap: 0.75rem;\n  }\n  .sm\\:gap-6{\n    gap: 1.5rem;\n  }\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n  .sm\\:space-x-3 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n  .sm\\:space-x-4 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 0;\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\n  .sm\\:space-x-reverse > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 1;\n  }\n  .sm\\:p-6{\n    padding: 1.5rem;\n  }\n  .sm\\:px-3{\n    padding-left: 0.75rem;\n    padding-right: 0.75rem;\n  }\n  .sm\\:px-6{\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n  .sm\\:px-8{\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n  .sm\\:text-right{\n    text-align: right;\n  }\n  .sm\\:text-3xl{\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n  .sm\\:text-base{\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n}\n\n@media (min-width: 768px){\n  .md\\:w-auto{\n    width: auto;\n  }\n  .md\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .md\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .md\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .md\\:grid-cols-5{\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n}\n\n@media (min-width: 1024px){\n  .lg\\:col-span-1{\n    grid-column: span 1 / span 1;\n  }\n  .lg\\:col-span-2{\n    grid-column: span 2 / span 2;\n  }\n  .lg\\:block{\n    display: block;\n  }\n  .lg\\:hidden{\n    display: none;\n  }\n  .lg\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .lg\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .lg\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .lg\\:grid-cols-5{\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n  .lg\\:grid-cols-6{\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n  .lg\\:flex-row{\n    flex-direction: row;\n  }\n  .lg\\:items-center{\n    align-items: center;\n  }\n  .lg\\:px-10{\n    padding-left: 2.5rem;\n    padding-right: 2.5rem;\n  }\n  .lg\\:px-8{\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n  .lg\\:pr-64{\n    padding-right: 16rem;\n  }\n  .lg\\:text-right{\n    text-align: right;\n  }\n}\n\n@media print{\n  .print\\:block{\n    display: block;\n  }\n  .print\\:hidden{\n    display: none;\n  }\n}\n\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]){\n  padding-right: 0px;\n}\n\n.\\[\\&\\>span\\]\\:line-clamp-1>span{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\n\n.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div{\n  --tw-translate-y: -3px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.\\[\\&\\>svg\\]\\:absolute>svg{\n  position: absolute;\n}\n\n.\\[\\&\\>svg\\]\\:left-4>svg{\n  left: 1rem;\n}\n\n.\\[\\&\\>svg\\]\\:top-4>svg{\n  top: 1rem;\n}\n\n.\\[\\&\\>svg\\]\\:text-destructive>svg{\n  color: hsl(var(--destructive));\n}\n\n.\\[\\&\\>svg\\]\\:text-foreground>svg{\n  color: hsl(var(--foreground));\n}\n\n.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~*{\n  padding-left: 1.75rem;\n}\n\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr{\n  border-bottom-width: 0px;\n}\n\n.\\[\\&_p\\]\\:leading-relaxed p{\n  line-height: 1.625;\n}\n\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child{\n  border-width: 0px;\n}\n\n.\\[\\&_tr\\]\\:border-b tr{\n  border-bottom-width: 1px;\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;;;;;AAQA;;;;AAaA;;;;;;;;;;;;AAgBA;;;;;AASA;;;;;;AAQA;;;;AAOA;;;;;AAYA;;;;;AAOA;;;;AAUA;;;;;;;AAYA;;;;AAMA;;;;;;;AAOA;;;;AAGA;;;;AAQA;;;;;;AAUA;;;;;;;;;;;;;AAmBA;;;;AAQA;;;;;;AAWA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAQA;;;;;AAOA;;;;AAOA;;;;;AAOA;;;;AAMA;;;;AAeA;;;;;AAIA;;;;AAGA;;;;;;AAUA;;;;AAMA;;;;AAOA;;;;;AAAA;;;;;AAYA;;;;AAOA;;;;AAQA;;;;;AAcA;;;;;AAMA;;;;AAGA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;;;;;;;;AAUA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;;;;AAIA;;;;;;;AAQA;;;;;;AAOA;;;;AAKA;;;;AAIA;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAMA;;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;AAIA;;;;;AAAA;;;;;AAWA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;;;;;AASA;;;;;;;AAOA;;;;AAIA;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;AAMA;;;;AAIA;;;;AAKA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;EACE;;;;EAIA;;;;;EAKA;;;;;;AAOF;;;;;;;;;;AASA;;;;;;AAOA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;AAQA;;;;;;;;;;;;AAaA;EACE;;;;EAIA;;;;;EAKA;;;;;;;AAQF;;;;;AAKA;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;AAYA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;AAOA;;;;;;;;;;;AAWA;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;AAKA;;;;;;;AASA;;;;;;;;;;AASA;;;;;AAUA;EACE;;;;;EAKA;;;;EAIA;;;;;AAMF;;;;;AAKA;;;;;;;;AAQA;;;;;AAKA;;;;;;;AAOA;;;;;;AAOA;;;;AAIA;;;;;AAKA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;;;AAQA;;;;;;;;;;;;;;AAeA;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAaA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;;AAMA;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAAA;;;;AAQA;;;;;AAAA;;;;;AAUA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;EACE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;;AAMF;EACE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAKF;EACE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;AAKF;EACE;;;;EAGA;;;;;AAKF;;;;AAIA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA", "debugId": null}}, {"offset": {"line": 5715, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/updated-colors.css"], "sourcesContent": ["/* ملف الألوان المحدثة لديوان أبو علوش */\n\n/* الألوان الأساسية الجديدة */\n:root {\n  /* الألوان المطلوبة */\n  --new-background: #f9f9f9;\n  --new-background-alt: #ffffff;\n  --new-text: #333333;\n  --new-primary: #007bff;\n  --new-success: #28a745;\n  --new-header-primary: #191970; /* كحلي غامق */\n  --new-header-secondary: #800020; /* عنابي */\n}\n\n/* تطبيق الألوان على العناصر الأساسية */\nbody {\n  background-color: var(--new-background) !important;\n  color: var(--new-text) !important;\n}\n\n/* البطاقات والحاويات */\n.card,\n.diwan-card,\n.bg-white {\n  background-color: var(--new-background-alt) !important;\n  color: var(--new-text) !important;\n}\n\n/* الأزرار الأساسية */\n.btn-primary,\n.bg-primary,\n.text-primary {\n  background-color: var(--new-primary) !important;\n  border-color: var(--new-primary) !important;\n  color: white !important;\n}\n\n.btn-primary:hover,\n.btn-primary:focus {\n  background-color: #0056cc !important;\n  border-color: #0056cc !important;\n}\n\n/* الأزرار الخضراء */\n.btn-success,\n.bg-success,\n.text-success {\n  background-color: var(--new-success) !important;\n  border-color: var(--new-success) !important;\n  color: white !important;\n}\n\n.btn-success:hover,\n.btn-success:focus {\n  background-color: #228b3c !important;\n  border-color: #228b3c !important;\n}\n\n/* الترويسات */\n.header,\n.navbar,\n.page-header,\nh1, h2, h3, h4, h5, h6 {\n  color: var(--new-header-primary) !important;\n}\n\n/* ترويسات بديلة */\n.header-alt,\n.navbar-alt,\n.secondary-header {\n  background-color: var(--new-header-secondary) !important;\n  color: white !important;\n}\n\n/* النصوص */\np, span, div, label, td, th {\n  color: var(--new-text) !important;\n}\n\n/* الروابط */\na {\n  color: var(--new-primary) !important;\n}\n\na:hover {\n  color: #0056cc !important;\n}\n\n/* المدخلات */\n.form-control,\n.form-select,\ninput,\ntextarea,\nselect {\n  background-color: var(--new-background-alt) !important;\n  color: var(--new-text) !important;\n  border-color: #dee2e6 !important;\n}\n\n.form-control:focus,\n.form-select:focus,\ninput:focus,\ntextarea:focus,\nselect:focus {\n  border-color: var(--new-primary) !important;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;\n}\n\n/* الجداول */\n.table {\n  background-color: var(--new-background-alt) !important;\n  color: var(--new-text) !important;\n}\n\n.table th {\n  background-color: var(--new-header-primary) !important;\n  color: white !important;\n}\n\n.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(249, 249, 249, 0.5) !important;\n}\n\n/* النوافذ المنبثقة */\n.modal-content,\n.dropdown-menu,\n.popover {\n  background-color: var(--new-background-alt) !important;\n  color: var(--new-text) !important;\n}\n\n/* الشريط الجانبي */\n.sidebar {\n  background-color: var(--new-header-primary) !important;\n  color: white !important;\n}\n\n.sidebar a {\n  color: white !important;\n}\n\n.sidebar a:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n/* التنبيهات */\n.alert-primary {\n  background-color: rgba(0, 123, 255, 0.1) !important;\n  border-color: var(--new-primary) !important;\n  color: var(--new-primary) !important;\n}\n\n.alert-success {\n  background-color: rgba(40, 167, 69, 0.1) !important;\n  border-color: var(--new-success) !important;\n  color: var(--new-success) !important;\n}\n\n/* الشارات */\n.badge-primary {\n  background-color: var(--new-primary) !important;\n}\n\n.badge-success {\n  background-color: var(--new-success) !important;\n}\n\n/* أشرطة التقدم */\n.progress-bar {\n  background-color: var(--new-primary) !important;\n}\n\n.progress-bar-success {\n  background-color: var(--new-success) !important;\n}\n\n/* التبويبات */\n.nav-tabs .nav-link.active {\n  background-color: var(--new-background-alt) !important;\n  border-color: var(--new-primary) !important;\n  color: var(--new-primary) !important;\n}\n\n.nav-tabs .nav-link {\n  color: var(--new-text) !important;\n}\n\n.nav-tabs .nav-link:hover {\n  color: var(--new-primary) !important;\n}\n\n/* الحدود */\n.border-primary {\n  border-color: var(--new-primary) !important;\n}\n\n.border-success {\n  border-color: var(--new-success) !important;\n}\n\n/* الظلال */\n.shadow-primary {\n  box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.15) !important;\n}\n\n.shadow-success {\n  box-shadow: 0 0.5rem 1rem rgba(40, 167, 69, 0.15) !important;\n}\n\n/* أنماط خاصة للديوان */\n.diwan-primary {\n  background-color: var(--new-primary) !important;\n  color: white !important;\n}\n\n.diwan-success {\n  background-color: var(--new-success) !important;\n  color: white !important;\n}\n\n.diwan-header {\n  background-color: var(--new-header-primary) !important;\n  color: white !important;\n}\n\n.diwan-header-alt {\n  background-color: var(--new-header-secondary) !important;\n  color: white !important;\n}\n\n/* تحسينات للتجاوب */\n@media (max-width: 768px) {\n  .header,\n  .navbar {\n    background-color: var(--new-header-primary) !important;\n    color: white !important;\n  }\n}\n"], "names": [], "mappings": "AAGA;;;;;;;;;;AAYA;;;;;AAMA;;;;;AAQA;;;;;;AAQA;;;;;AAOA;;;;;;AAQA;;;;;AAOA;;;;AAQA;;;;;AAQA;;;;AAKA;;;;AAIA;;;;AAKA;;;;;;AAUA;;;;;AAUA;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;;AAQA;;;;;AAKA;;;;AAIA;;;;AAKA;;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA;EACE", "debugId": null}}, {"offset": {"line": 5908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/sidebar-colors.css"], "sourcesContent": ["/* أنماط الشريط الجانبي بالألوان الجديدة */\n\n/* الشريط الجانبي الرئيسي */\n.sidebar,\n.fixed.inset-y-0.right-0.z-50.w-64 {\n  background-color: #191970 !important; /* كحلي غامق */\n  color: white !important;\n}\n\n/* رأس الشريط الجانبي */\n.sidebar .border-b,\n.sidebar .border-gray-200 {\n  border-color: rgba(255, 255, 255, 0.2) !important;\n}\n\n/* النصوص في الشريط الجانبي */\n.sidebar h1,\n.sidebar .text-gray-900 {\n  color: white !important;\n}\n\n.sidebar p,\n.sidebar .text-gray-500 {\n  color: #d1d5db !important; /* رمادي فاتح */\n}\n\n/* الأيقونات في الشريط الجانبي */\n.sidebar .text-diwan-600,\n.sidebar .text-gray-400 {\n  color: white !important;\n}\n\n/* روابط التنقل */\n.sidebar nav a,\n.sidebar .text-gray-700 {\n  color: #e5e7eb !important; /* رمادي فاتح */\n}\n\n.sidebar nav a:hover,\n.sidebar .hover\\:text-gray-900:hover {\n  color: white !important;\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n/* العنصر النشط */\n.sidebar .bg-diwan-100 {\n  background-color: rgba(255, 255, 255, 0.2) !important;\n}\n\n.sidebar .text-diwan-700 {\n  color: white !important;\n}\n\n.sidebar .text-diwan-500 {\n  color: #60a5fa !important; /* أزرق فاتح */\n}\n\n/* خلفية التمرير */\n.sidebar .hover\\:bg-gray-100:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n/* الجزء السفلي من الشريط الجانبي */\n.sidebar .border-t,\n.sidebar .border-gray-200 {\n  border-color: rgba(255, 255, 255, 0.2) !important;\n}\n\n/* الشريط الجانبي للأعضاء */\n.member-sidebar {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n.member-sidebar a {\n  color: #e5e7eb !important;\n}\n\n.member-sidebar a:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n  color: white !important;\n}\n\n/* تحديد أكثر دقة للشريط الجانبي */\ndiv[class*=\"fixed\"][class*=\"inset-y-0\"][class*=\"right-0\"][class*=\"w-64\"] {\n  background-color: #191970 !important;\n}\n\ndiv[class*=\"fixed\"][class*=\"inset-y-0\"][class*=\"right-0\"][class*=\"w-64\"] h1 {\n  color: white !important;\n}\n\ndiv[class*=\"fixed\"][class*=\"inset-y-0\"][class*=\"right-0\"][class*=\"w-64\"] p {\n  color: #d1d5db !important;\n}\n\ndiv[class*=\"fixed\"][class*=\"inset-y-0\"][class*=\"right-0\"][class*=\"w-64\"] a {\n  color: #e5e7eb !important;\n}\n\ndiv[class*=\"fixed\"][class*=\"inset-y-0\"][class*=\"right-0\"][class*=\"w-64\"] a:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n  color: white !important;\n}\n\n/* أنماط إضافية للتأكد من التطبيق */\n.lg\\:block.hidden.fixed.inset-y-0.right-0.z-50.w-64 {\n  background-color: #191970 !important;\n}\n\n/* تحسينات للاستجابة */\n@media (max-width: 1024px) {\n  .sidebar,\n  .fixed.inset-y-0.right-0.w-64 {\n    background-color: #191970 !important;\n  }\n}\n\n/* تطبيق الألوان على جميع عناصر الشريط الجانبي */\n[class*=\"sidebar\"] {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n[class*=\"sidebar\"] * {\n  color: inherit !important;\n}\n\n[class*=\"sidebar\"] h1,\n[class*=\"sidebar\"] h2,\n[class*=\"sidebar\"] h3,\n[class*=\"sidebar\"] h4,\n[class*=\"sidebar\"] h5,\n[class*=\"sidebar\"] h6 {\n  color: white !important;\n}\n\n[class*=\"sidebar\"] p,\n[class*=\"sidebar\"] span {\n  color: #d1d5db !important;\n}\n\n[class*=\"sidebar\"] a {\n  color: #e5e7eb !important;\n  transition: all 0.2s ease;\n}\n\n[class*=\"sidebar\"] a:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n  color: white !important;\n}\n\n/* إعادة تعيين الألوان الافتراضية */\n.sidebar .bg-white {\n  background-color: transparent !important;\n}\n\n.sidebar .text-gray-900,\n.sidebar .text-gray-700,\n.sidebar .text-gray-500,\n.sidebar .text-gray-400 {\n  color: inherit !important;\n}\n\n/* تطبيق الألوان على الحدود */\n.sidebar .border-gray-200,\n.sidebar .border-b,\n.sidebar .border-t {\n  border-color: rgba(255, 255, 255, 0.2) !important;\n}\n\n/* أنماط خاصة للأيقونات */\n.sidebar svg {\n  color: currentColor !important;\n}\n\n.sidebar .group:hover svg {\n  color: white !important;\n}\n\n/* تأكيد تطبيق الألوان */\n.sidebar,\n.sidebar * {\n  --tw-text-opacity: 1 !important;\n}\n\n.sidebar {\n  --tw-bg-opacity: 1 !important;\n  background-color: rgb(25 25 112 / var(--tw-bg-opacity)) !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;AAOA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAMA;;;;AAKA;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAKA;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAMA;;;;AAKA;EACE;;;;;AAOF;;;;;AAKA;;;;AAIA;;;;AASA;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;AAIA;;;;AAQA;;;;AAOA;;;;AAIA;;;;AAKA;;;;AAKA", "debugId": null}}, {"offset": {"line": 6061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/button-colors.css"], "sourcesContent": ["/* أنماط الأزرار بالألوان الجديدة */\n\n/* الأزرار الأساسية - أزرق #007bff */\n.btn-primary,\n.bg-primary,\nbutton[class*=\"bg-primary\"],\n.button-primary {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\n.btn-primary:hover,\n.bg-primary:hover,\nbutton[class*=\"bg-primary\"]:hover,\n.button-primary:hover {\n  background-color: #0056cc !important;\n  border-color: #0056cc !important;\n  color: white !important;\n}\n\n.btn-primary:focus,\n.bg-primary:focus,\nbutton[class*=\"bg-primary\"]:focus,\n.button-primary:focus {\n  background-color: #0056cc !important;\n  border-color: #0056cc !important;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;\n}\n\n/* الأزرار الخضراء - أخضر #28a745 */\n.btn-success,\n.bg-success,\nbutton[class*=\"bg-success\"],\n.button-success {\n  background-color: #28a745 !important;\n  border-color: #28a745 !important;\n  color: white !important;\n}\n\n.btn-success:hover,\n.bg-success:hover,\nbutton[class*=\"bg-success\"]:hover,\n.button-success:hover {\n  background-color: #228b3c !important;\n  border-color: #228b3c !important;\n  color: white !important;\n}\n\n.btn-success:focus,\n.bg-success:focus,\nbutton[class*=\"bg-success\"]:focus,\n.button-success:focus {\n  background-color: #228b3c !important;\n  border-color: #228b3c !important;\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;\n}\n\n/* أزرار الحذف والخطر */\n.btn-danger,\n.bg-destructive,\nbutton[class*=\"bg-destructive\"],\n.button-danger {\n  background-color: #dc3545 !important;\n  border-color: #dc3545 !important;\n  color: white !important;\n}\n\n.btn-danger:hover,\n.bg-destructive:hover,\nbutton[class*=\"bg-destructive\"]:hover,\n.button-danger:hover {\n  background-color: #c82333 !important;\n  border-color: #c82333 !important;\n  color: white !important;\n}\n\n/* أزرار التحذير */\n.btn-warning,\n.bg-warning,\nbutton[class*=\"bg-warning\"],\n.button-warning {\n  background-color: #ffc107 !important;\n  border-color: #ffc107 !important;\n  color: #333333 !important;\n}\n\n.btn-warning:hover,\n.bg-warning:hover,\nbutton[class*=\"bg-warning\"]:hover,\n.button-warning:hover {\n  background-color: #e0a800 !important;\n  border-color: #e0a800 !important;\n  color: #333333 !important;\n}\n\n/* أزرار المعلومات */\n.btn-info,\n.bg-info,\nbutton[class*=\"bg-info\"],\n.button-info {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\n/* الأزرار الثانوية */\n.btn-secondary,\n.bg-secondary,\nbutton[class*=\"bg-secondary\"],\n.button-secondary {\n  background-color: #6c757d !important;\n  border-color: #6c757d !important;\n  color: white !important;\n}\n\n.btn-secondary:hover,\n.bg-secondary:hover,\nbutton[class*=\"bg-secondary\"]:hover,\n.button-secondary:hover {\n  background-color: #5a6268 !important;\n  border-color: #5a6268 !important;\n  color: white !important;\n}\n\n/* الأزرار المحددة */\n.btn-outline-primary {\n  background-color: transparent !important;\n  border-color: #007bff !important;\n  color: #007bff !important;\n}\n\n.btn-outline-primary:hover {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\n.btn-outline-success {\n  background-color: transparent !important;\n  border-color: #28a745 !important;\n  color: #28a745 !important;\n}\n\n.btn-outline-success:hover {\n  background-color: #28a745 !important;\n  border-color: #28a745 !important;\n  color: white !important;\n}\n\n/* أزرار Tailwind CSS */\n.bg-blue-500,\n.bg-blue-600 {\n  background-color: #007bff !important;\n}\n\n.bg-blue-500:hover,\n.bg-blue-600:hover,\n.hover\\:bg-blue-600:hover,\n.hover\\:bg-blue-700:hover {\n  background-color: #0056cc !important;\n}\n\n.bg-green-500,\n.bg-green-600 {\n  background-color: #28a745 !important;\n}\n\n.bg-green-500:hover,\n.bg-green-600:hover,\n.hover\\:bg-green-600:hover,\n.hover\\:bg-green-700:hover {\n  background-color: #228b3c !important;\n}\n\n/* أزرار مخصصة للديوان */\n.diwan-btn-primary {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n  padding: 0.5rem 1rem;\n  border-radius: 0.375rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.diwan-btn-primary:hover {\n  background-color: #0056cc !important;\n  border-color: #0056cc !important;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\n}\n\n.diwan-btn-success {\n  background-color: #28a745 !important;\n  border-color: #28a745 !important;\n  color: white !important;\n  padding: 0.5rem 1rem;\n  border-radius: 0.375rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.diwan-btn-success:hover {\n  background-color: #228b3c !important;\n  border-color: #228b3c !important;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\n}\n\n/* أزرار الحفظ والإلغاء */\n.save-button,\nbutton[type=\"submit\"] {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\n.save-button:hover,\nbutton[type=\"submit\"]:hover {\n  background-color: #0056cc !important;\n  border-color: #0056cc !important;\n}\n\n.cancel-button,\nbutton[type=\"button\"][class*=\"cancel\"] {\n  background-color: #6c757d !important;\n  border-color: #6c757d !important;\n  color: white !important;\n}\n\n.cancel-button:hover,\nbutton[type=\"button\"][class*=\"cancel\"]:hover {\n  background-color: #5a6268 !important;\n  border-color: #5a6268 !important;\n}\n\n/* أزرار الإجراءات */\n.action-button-edit {\n  background-color: #007bff !important;\n  color: white !important;\n}\n\n.action-button-delete {\n  background-color: #dc3545 !important;\n  color: white !important;\n}\n\n.action-button-view {\n  background-color: #28a745 !important;\n  color: white !important;\n}\n\n/* تحسينات للتفاعل */\nbutton:focus,\n.btn:focus {\n  outline: none !important;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;\n}\n\n/* أزرار صغيرة */\n.btn-sm {\n  padding: 0.25rem 0.5rem !important;\n  font-size: 0.875rem !important;\n}\n\n/* أزرار كبيرة */\n.btn-lg {\n  padding: 0.75rem 1.5rem !important;\n  font-size: 1.125rem !important;\n}\n\n/* تأثيرات الانتقال */\nbutton,\n.btn {\n  transition: all 0.2s ease !important;\n}\n\nbutton:hover,\n.btn:hover {\n  transform: translateY(-1px) !important;\n}\n\nbutton:active,\n.btn:active {\n  transform: translateY(0) !important;\n}\n\n/* أزرار معطلة */\nbutton:disabled,\n.btn:disabled {\n  opacity: 0.6 !important;\n  cursor: not-allowed !important;\n  transform: none !important;\n}\n\nbutton:disabled:hover,\n.btn:disabled:hover {\n  transform: none !important;\n  background-color: inherit !important;\n}\n\n/* إصلاح ألوان أزرار صفحة الأعضاء */\n.members-page .btn-primary,\n.members-page button[style*=\"background-color: rgb(0, 123, 255)\"] {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\n.members-page .btn-success,\n.members-page button[style*=\"background-color: rgb(40, 167, 69)\"] {\n  background-color: #28a745 !important;\n  border-color: #28a745 !important;\n  color: white !important;\n}\n\n.members-page .btn-danger,\n.members-page button[style*=\"background-color: rgb(220, 53, 69)\"] {\n  background-color: #dc3545 !important;\n  border-color: #dc3545 !important;\n  color: white !important;\n}\n\n/* تطبيق قوي للأزرار في جميع الصفحات */\nbutton[class*=\"bg-blue\"],\n.bg-blue-500,\n.bg-blue-600 {\n  background-color: #007bff !important;\n  color: white !important;\n}\n\nbutton[class*=\"bg-green\"],\n.bg-green-500,\n.bg-green-600 {\n  background-color: #28a745 !important;\n  color: white !important;\n}\n\nbutton[class*=\"bg-red\"],\n.bg-red-500,\n.bg-red-600 {\n  background-color: #dc3545 !important;\n  color: white !important;\n}\n\n/* أزرار التصدير - استخدام attribute selectors بدلاً من contains */\nbutton[title*=\"تصدير\"],\nbutton[aria-label*=\"تصدير\"],\n.export-button {\n  background-color: #28a745 !important;\n  color: white !important;\n}\n\nbutton[title*=\"PDF\"],\nbutton[aria-label*=\"PDF\"],\n.pdf-button {\n  background-color: #dc3545 !important;\n  color: white !important;\n}\n\n/* أزرار الإضافة */\nbutton[title*=\"إضافة\"],\nbutton[aria-label*=\"إضافة\"],\nbutton[title*=\"جديد\"],\nbutton[aria-label*=\"جديد\"],\n.add-button {\n  background-color: #007bff !important;\n  color: white !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;;AASA;;;;;;AASA;;;;;;AAUA;;;;;;AASA;;;;;;AASA;;;;;;AAUA;;;;;;AASA;;;;;;AAUA;;;;;;AASA;;;;;;AAUA;;;;;;AAUA;;;;;;AASA;;;;;;AAUA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;AAKA;;;;AAOA;;;;AAKA;;;;AAQA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAQA;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;AAOA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAMA;;;;;;AAOA;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAQA;;;;;AAOA;;;;;AAOA;;;;;AAQA;;;;;AAOA;;;;;AAQA", "debugId": null}}, {"offset": {"line": 6338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/header-colors.css"], "sourcesContent": ["/* أنماط الترويسات بالألوان الجديدة */\n\n/* الترويسات الأساسية - كحلي غامق #191970 */\nh1, h2, h3, h4, h5, h6 {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الصفحات */\n.page-title,\n.page-header h1,\n.page-header h2 {\n  color: #191970 !important;\n  font-weight: 700 !important;\n}\n\n/* ترويسات البطاقات */\n.card-header,\n.card-title,\n.diwan-card h1,\n.diwan-card h2,\n.diwan-card h3 {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الجداول */\n.table th,\n.table thead th,\ntable th {\n  background-color: #191970 !important;\n  color: white !important;\n  font-weight: 600 !important;\n  border-color: #191970 !important;\n}\n\n/* ترويسات النوافذ المنبثقة */\n.modal-header,\n.modal-title,\n.dialog-header,\n.dialog-title {\n  background-color: #191970 !important;\n  color: white !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الشريط العلوي */\n.header,\n.navbar,\n.top-header {\n  background-color: #191970 !important;\n  color: white !important;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;\n}\n\n.header h1,\n.header h2,\n.navbar h1,\n.navbar h2 {\n  color: white !important;\n}\n\n/* ترويسات بديلة - عنابي #800020 */\n.header-alt,\n.navbar-alt,\n.secondary-header {\n  background-color: #800020 !important;\n  color: white !important;\n}\n\n.header-alt h1,\n.header-alt h2,\n.navbar-alt h1,\n.navbar-alt h2,\n.secondary-header h1,\n.secondary-header h2 {\n  color: white !important;\n}\n\n/* ترويسات الأقسام */\n.section-header,\n.section-title {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  border-bottom: 2px solid #191970 !important;\n  padding-bottom: 0.5rem !important;\n  margin-bottom: 1rem !important;\n}\n\n/* ترويسات فرعية */\n.sub-header,\n.sub-title {\n  color: #800020 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الإحصائيات */\n.stats-header,\n.stats-title {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  text-align: center !important;\n}\n\n/* ترويسات النماذج */\n.form-header,\n.form-title {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  margin-bottom: 1rem !important;\n}\n\n/* ترويسات التقارير */\n.report-header,\n.report-title {\n  background-color: #191970 !important;\n  color: white !important;\n  padding: 1rem !important;\n  text-align: center !important;\n  font-weight: 700 !important;\n  border-radius: 0.5rem 0.5rem 0 0 !important;\n}\n\n/* ترويسات الإعدادات */\n.settings-header,\n.settings-title {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  border-bottom: 1px solid #e5e7eb !important;\n  padding-bottom: 0.5rem !important;\n}\n\n/* ترويسات المعرض */\n.gallery-header,\n.gallery-title {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  text-align: center !important;\n  margin-bottom: 2rem !important;\n}\n\n/* ترويسات الإشعارات */\n.notification-header,\n.notification-title {\n  color: #800020 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الأعضاء */\n.member-header,\n.member-title {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات الإيرادات والمصروفات */\n.income-header,\n.expense-header {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* ترويسات مخصصة للديوان */\n.diwan-header-primary {\n  background-color: #191970 !important;\n  color: white !important;\n  padding: 1rem !important;\n  border-radius: 0.5rem !important;\n  text-align: center !important;\n  font-weight: 700 !important;\n}\n\n.diwan-header-secondary {\n  background-color: #800020 !important;\n  color: white !important;\n  padding: 1rem !important;\n  border-radius: 0.5rem !important;\n  text-align: center !important;\n  font-weight: 700 !important;\n}\n\n.diwan-header-text {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  margin-bottom: 1rem !important;\n}\n\n/* تدرجات للترويسات */\n.gradient-header {\n  background: linear-gradient(135deg, #191970 0%, #800020 100%) !important;\n  color: white !important;\n  padding: 1rem !important;\n  border-radius: 0.5rem !important;\n  text-align: center !important;\n  font-weight: 700 !important;\n}\n\n/* ترويسات مع ظلال */\n.shadow-header {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  text-shadow: 2px 2px 4px rgba(25, 25, 112, 0.3) !important;\n}\n\n/* ترويسات للطباعة */\n@media print {\n  h1, h2, h3, h4, h5, h6 {\n    color: #000000 !important;\n  }\n  \n  .header,\n  .navbar,\n  .modal-header {\n    background-color: #ffffff !important;\n    color: #000000 !important;\n    border: 1px solid #000000 !important;\n  }\n}\n\n/* تحسينات للاستجابة */\n@media (max-width: 768px) {\n  h1 {\n    font-size: 1.5rem !important;\n  }\n  \n  h2 {\n    font-size: 1.25rem !important;\n  }\n  \n  h3 {\n    font-size: 1.125rem !important;\n  }\n  \n  .page-title {\n    font-size: 1.5rem !important;\n    text-align: center !important;\n  }\n  \n  .section-header {\n    font-size: 1.25rem !important;\n  }\n}\n\n/* تأثيرات خاصة */\n.animated-header {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  transition: all 0.3s ease !important;\n}\n\n.animated-header:hover {\n  color: #800020 !important;\n  transform: translateY(-2px) !important;\n}\n\n/* ترويسات مع خطوط تحت */\n.underlined-header {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  position: relative !important;\n}\n\n.underlined-header::after {\n  content: '' !important;\n  position: absolute !important;\n  bottom: -4px !important;\n  left: 0 !important;\n  width: 100% !important;\n  height: 2px !important;\n  background-color: #800020 !important;\n}\n\n/* ترويسات مع أيقونات */\n.icon-header {\n  color: #191970 !important;\n  font-weight: 600 !important;\n  display: flex !important;\n  align-items: center !important;\n  gap: 0.5rem !important;\n}\n\n.icon-header svg {\n  color: #800020 !important;\n}\n\n/* تطبيق الألوان على عناصر محددة */\n.text-primary {\n  color: #007bff !important;\n}\n\n.text-success {\n  color: #28a745 !important;\n}\n\n.text-header {\n  color: #191970 !important;\n}\n\n.text-header-alt {\n  color: #800020 !important;\n}\n\n/* خلفيات الترويسات */\n.bg-header {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n.bg-header-alt {\n  background-color: #800020 !important;\n  color: white !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;AAMA;;;;;AAQA;;;;;AAUA;;;;;;;AAUA;;;;;;AAUA;;;;;;AAQA;;;;AAQA;;;;;AAOA;;;;AAUA;;;;;;;;AAUA;;;;;AAOA;;;;;;AAQA;;;;;;AAQA;;;;;;;;;AAWA;;;;;;;AASA;;;;;;;AASA;;;;;AAOA;;;;;AAcA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;AAOA;;;;;;;;;AAUA;;;;;;AAOA;EACE;;;;EAIA;;;;;;;AAUF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAMF;;;;;;AAMA;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;AAKA", "debugId": null}}, {"offset": {"line": 6584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/page-layout.css"], "sourcesContent": ["/* تنسيق الصفحات المحسن لديوان أبو علوش */\n\n/* تنسيق عام للصفحات */\n.page-container {\n  background-color: #f9f9f9 !important;\n  min-height: 100vh;\n  padding: 1.5rem;\n}\n\n/* رؤوس الصفحات */\n.page-header {\n  background-color: #ffffff !important;\n  border-radius: 1rem !important;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;\n  padding: 2rem !important;\n  margin-bottom: 2rem !important;\n  border: 1px solid #e5e7eb !important;\n}\n\n.page-title {\n  color: #191970 !important;\n  font-size: 2.5rem !important;\n  font-weight: 700 !important;\n  margin-bottom: 0.5rem !important;\n  text-align: center !important;\n}\n\n.page-subtitle {\n  color: #333333 !important;\n  font-size: 1.125rem !important;\n  font-weight: 500 !important;\n  text-align: center !important;\n  margin-bottom: 1rem !important;\n}\n\n.page-divider {\n  height: 4px !important;\n  width: 6rem !important;\n  background-color: #800020 !important;\n  border-radius: 2px !important;\n  margin: 0 auto !important;\n}\n\n/* البطاقات المحسنة */\n.enhanced-card {\n  background-color: #ffffff !important;\n  border-radius: 1rem !important;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;\n  border: 1px solid #e5e7eb !important;\n  overflow: hidden !important;\n  transition: all 0.3s ease !important;\n}\n\n.enhanced-card:hover {\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;\n  transform: translateY(-2px) !important;\n}\n\n.card-header-enhanced {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;\n  padding: 1.5rem !important;\n  border-bottom: 1px solid #e5e7eb !important;\n}\n\n.card-title-enhanced {\n  color: #191970 !important;\n  font-size: 1.25rem !important;\n  font-weight: 600 !important;\n  display: flex !important;\n  align-items: center !important;\n  gap: 0.75rem !important;\n}\n\n.card-content-enhanced {\n  padding: 1.5rem !important;\n}\n\n/* بطاقات الإحصائيات */\n.stats-card {\n  background-color: #ffffff !important;\n  border-radius: 1rem !important;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;\n  border: 1px solid #e5e7eb !important;\n  overflow: hidden !important;\n  transition: all 0.3s ease !important;\n}\n\n.stats-card:hover {\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;\n  transform: translateY(-2px) !important;\n}\n\n.stats-card-header {\n  padding: 1rem !important;\n  display: flex !important;\n  justify-content: space-between !important;\n  align-items: center !important;\n}\n\n.stats-card-title {\n  color: #333333 !important;\n  font-size: 0.875rem !important;\n  font-weight: 600 !important;\n}\n\n.stats-card-icon {\n  padding: 0.75rem !important;\n  border-radius: 0.75rem !important;\n  color: white !important;\n}\n\n.stats-card-content {\n  padding: 0 1rem 1rem 1rem !important;\n}\n\n.stats-card-value {\n  color: #191970 !important;\n  font-size: 2rem !important;\n  font-weight: 700 !important;\n  margin-bottom: 0.25rem !important;\n}\n\n.stats-card-description {\n  color: #333333 !important;\n  font-size: 0.75rem !important;\n  font-weight: 500 !important;\n}\n\n/* شريط علوي ملون للبطاقات */\n.card-top-bar {\n  height: 4px !important;\n  width: 100% !important;\n  border-radius: 1rem 1rem 0 0 !important;\n}\n\n.card-top-bar-blue {\n  background: linear-gradient(90deg, #007bff 0%, #0056cc 100%) !important;\n}\n\n.card-top-bar-green {\n  background: linear-gradient(90deg, #28a745 0%, #228b3c 100%) !important;\n}\n\n.card-top-bar-red {\n  background: linear-gradient(90deg, #dc3545 0%, #c82333 100%) !important;\n}\n\n.card-top-bar-yellow {\n  background: linear-gradient(90deg, #191970 0%, #14145a 100%) !important;\n}\n\n.card-top-bar-purple {\n  background: linear-gradient(90deg, #800020 0%, #660019 100%) !important;\n}\n\n.card-top-bar-indigo {\n  background: linear-gradient(90deg, #191970 0%, #14145a 100%) !important;\n}\n\n/* الجداول المحسنة */\n.enhanced-table {\n  background-color: #ffffff !important;\n  border-radius: 1rem !important;\n  overflow: hidden !important;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;\n  border: 1px solid #e5e7eb !important;\n}\n\n.enhanced-table thead th {\n  background-color: #191970 !important;\n  color: white !important;\n  font-weight: 600 !important;\n  padding: 1rem !important;\n  text-align: right !important;\n  border: none !important;\n}\n\n.enhanced-table tbody td {\n  padding: 1rem !important;\n  border-bottom: 1px solid #f3f4f6 !important;\n  color: #333333 !important;\n}\n\n.enhanced-table tbody tr:hover {\n  background-color: #f8f9fa !important;\n}\n\n.enhanced-table tbody tr:last-child td {\n  border-bottom: none !important;\n}\n\n/* النماذج المحسنة */\n.enhanced-form {\n  background-color: #ffffff !important;\n  border-radius: 1rem !important;\n  padding: 2rem !important;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;\n  border: 1px solid #e5e7eb !important;\n}\n\n.form-group-enhanced {\n  margin-bottom: 1.5rem !important;\n}\n\n.form-label-enhanced {\n  color: #333333 !important;\n  font-weight: 600 !important;\n  margin-bottom: 0.5rem !important;\n  display: block !important;\n}\n\n.form-input-enhanced {\n  background-color: #ffffff !important;\n  border: 1px solid #d1d5db !important;\n  border-radius: 0.5rem !important;\n  padding: 0.75rem 1rem !important;\n  width: 100% !important;\n  color: #333333 !important;\n  transition: all 0.2s ease !important;\n}\n\n.form-input-enhanced:focus {\n  border-color: #007bff !important;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;\n  outline: none !important;\n}\n\n/* الأزرار المحسنة */\n.btn-enhanced {\n  padding: 0.75rem 1.5rem !important;\n  border-radius: 0.5rem !important;\n  font-weight: 600 !important;\n  transition: all 0.2s ease !important;\n  border: none !important;\n  cursor: pointer !important;\n  display: inline-flex !important;\n  align-items: center !important;\n  gap: 0.5rem !important;\n}\n\n.btn-enhanced:hover {\n  transform: translateY(-1px) !important;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;\n}\n\n.btn-primary-enhanced {\n  background-color: #007bff !important;\n  color: white !important;\n}\n\n.btn-primary-enhanced:hover {\n  background-color: #0056cc !important;\n}\n\n.btn-success-enhanced {\n  background-color: #28a745 !important;\n  color: white !important;\n}\n\n.btn-success-enhanced:hover {\n  background-color: #228b3c !important;\n}\n\n.btn-danger-enhanced {\n  background-color: #dc3545 !important;\n  color: white !important;\n}\n\n.btn-danger-enhanced:hover {\n  background-color: #c82333 !important;\n}\n\n/* أدوات البحث والتصفية */\n.search-container {\n  background-color: #ffffff !important;\n  border-radius: 1rem !important;\n  padding: 1.5rem !important;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;\n  border: 1px solid #e5e7eb !important;\n  margin-bottom: 2rem !important;\n}\n\n.search-input {\n  background-color: #f8f9fa !important;\n  border: 1px solid #e9ecef !important;\n  border-radius: 0.5rem !important;\n  padding: 0.75rem 1rem !important;\n  color: #333333 !important;\n  transition: all 0.2s ease !important;\n}\n\n.search-input:focus {\n  background-color: #ffffff !important;\n  border-color: #007bff !important;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;\n}\n\n/* التنبيهات والرسائل */\n.alert-enhanced {\n  border-radius: 0.75rem !important;\n  padding: 1rem 1.5rem !important;\n  margin-bottom: 1rem !important;\n  border: 1px solid !important;\n}\n\n.alert-success {\n  background-color: rgba(40, 167, 69, 0.1) !important;\n  border-color: #28a745 !important;\n  color: #155724 !important;\n}\n\n.alert-danger {\n  background-color: rgba(220, 53, 69, 0.1) !important;\n  border-color: #dc3545 !important;\n  color: #721c24 !important;\n}\n\n.alert-warning {\n  background-color: rgba(25, 25, 112, 0.1) !important;\n  border-color: #191970 !important;\n  color: #191970 !important;\n}\n\n.alert-info {\n  background-color: rgba(0, 123, 255, 0.1) !important;\n  border-color: #007bff !important;\n  color: #004085 !important;\n}\n\n/* التحميل والحالات الفارغة */\n.loading-container {\n  display: flex !important;\n  flex-direction: column !important;\n  align-items: center !important;\n  justify-content: center !important;\n  padding: 4rem 2rem !important;\n  background-color: #f8f9fa !important;\n  border-radius: 1rem !important;\n}\n\n.loading-spinner {\n  width: 3rem !important;\n  height: 3rem !important;\n  border: 4px solid #e9ecef !important;\n  border-top: 4px solid #007bff !important;\n  border-radius: 50% !important;\n  animation: spin 1s linear infinite !important;\n  margin-bottom: 1rem !important;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.empty-state {\n  text-align: center !important;\n  padding: 4rem 2rem !important;\n  color: #6c757d !important;\n}\n\n.empty-state-icon {\n  width: 4rem !important;\n  height: 4rem !important;\n  color: #dee2e6 !important;\n  margin: 0 auto 1rem auto !important;\n}\n\n/* تحسينات للاستجابة */\n@media (max-width: 768px) {\n  .page-container {\n    padding: 1rem !important;\n  }\n  \n  .page-header {\n    padding: 1.5rem !important;\n  }\n  \n  .page-title {\n    font-size: 2rem !important;\n  }\n  \n  .enhanced-card {\n    margin-bottom: 1rem !important;\n  }\n  \n  .card-header-enhanced,\n  .card-content-enhanced {\n    padding: 1rem !important;\n  }\n}\n"], "names": [], "mappings": "AAGA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;AASA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;AAKA;;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;AAOA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;AAMA;;;;AAIA;;;;AAKA;;;;;;;;AAQA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAKA;;;;;;AAMA;;;;;;;AAQA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 6968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/table-styles.css"], "sourcesContent": ["/* أنماط الجداول المحسنة لديوان أبو علوش */\n\n/* الجداول الأساسية */\n.table,\ntable {\n  background-color: #ffffff !important;\n  border-radius: 1rem !important;\n  overflow: hidden !important;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;\n  border: 1px solid #e5e7eb !important;\n  width: 100% !important;\n}\n\n/* رؤوس الجداول */\n.table thead th,\ntable thead th,\n.table th,\ntable th,\n[data-testid=\"table-header\"] th,\n[role=\"columnheader\"] {\n  background-color: #191970 !important;\n  color: white !important;\n  font-weight: 600 !important;\n  padding: 1rem !important;\n  text-align: right !important;\n  border: none !important;\n  font-size: 0.875rem !important;\n}\n\n/* تطبيق قوي لرؤوس الجداول */\nthead th,\nth[role=\"columnheader\"],\n.table-header th,\n[class*=\"TableHead\"] {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n/* خلايا الجداول */\n.table tbody td,\ntable tbody td,\n.table td,\ntable td {\n  padding: 1rem !important;\n  border-bottom: 1px solid #f3f4f6 !important;\n  color: #333333 !important;\n  vertical-align: middle !important;\n  font-size: 0.875rem !important;\n}\n\n/* صفوف الجداول */\n.table tbody tr,\ntable tbody tr {\n  transition: background-color 0.2s ease !important;\n}\n\n.table tbody tr:hover,\ntable tbody tr:hover {\n  background-color: #f8f9fa !important;\n}\n\n.table tbody tr:last-child td,\ntable tbody tr:last-child td {\n  border-bottom: none !important;\n}\n\n/* الصفوف المخططة */\n.table-striped tbody tr:nth-of-type(odd),\ntable.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(249, 249, 249, 0.5) !important;\n}\n\n.table-striped tbody tr:nth-of-type(odd):hover,\ntable.table-striped tbody tr:nth-of-type(odd):hover {\n  background-color: #f8f9fa !important;\n}\n\n/* جداول مضغوطة */\n.table-sm th,\n.table-sm td {\n  padding: 0.5rem !important;\n}\n\n/* جداول بحدود */\n.table-bordered,\n.table-bordered th,\n.table-bordered td {\n  border: 1px solid #e5e7eb !important;\n}\n\n/* جداول بدون حدود */\n.table-borderless th,\n.table-borderless td,\n.table-borderless thead th,\n.table-borderless tbody + tbody {\n  border: 0 !important;\n}\n\n/* ألوان الجداول المتغيرة */\n.table-primary {\n  background-color: rgba(0, 123, 255, 0.1) !important;\n}\n\n.table-primary th,\n.table-primary td,\n.table-primary thead th,\n.table-primary tbody + tbody {\n  border-color: rgba(0, 123, 255, 0.2) !important;\n}\n\n.table-success {\n  background-color: rgba(40, 167, 69, 0.1) !important;\n}\n\n.table-success th,\n.table-success td,\n.table-success thead th,\n.table-success tbody + tbody {\n  border-color: rgba(40, 167, 69, 0.2) !important;\n}\n\n/* جداول تفاعلية */\n.table-hover tbody tr:hover {\n  background-color: rgba(0, 123, 255, 0.05) !important;\n}\n\n/* جداول مع ظلال */\n.table-shadow {\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;\n}\n\n/* رؤوس جداول ملونة */\n.table-header-primary thead th {\n  background-color: #007bff !important;\n  color: white !important;\n}\n\n.table-header-success thead th {\n  background-color: #28a745 !important;\n  color: white !important;\n}\n\n.table-header-danger thead th {\n  background-color: #dc3545 !important;\n  color: white !important;\n}\n\n.table-header-warning thead th {\n  background-color: #ffc107 !important;\n  color: #333333 !important;\n}\n\n.table-header-info thead th {\n  background-color: #17a2b8 !important;\n  color: white !important;\n}\n\n.table-header-dark thead th {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n.table-header-burgundy thead th {\n  background-color: #800020 !important;\n  color: white !important;\n}\n\n/* جداول مستجيبة */\n.table-responsive {\n  display: block !important;\n  width: 100% !important;\n  overflow-x: auto !important;\n  -webkit-overflow-scrolling: touch !important;\n}\n\n.table-responsive > .table {\n  margin-bottom: 0 !important;\n}\n\n/* تحسينات للشاشات الصغيرة */\n@media (max-width: 768px) {\n  .table th,\n  .table td {\n    padding: 0.75rem 0.5rem !important;\n    font-size: 0.8rem !important;\n  }\n  \n  .table-responsive {\n    border: 1px solid #e5e7eb !important;\n    border-radius: 0.5rem !important;\n  }\n}\n\n/* أنماط خاصة للديوان */\n.diwan-table {\n  background-color: #ffffff !important;\n  border-radius: 1rem !important;\n  overflow: hidden !important;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;\n  border: 1px solid #e5e7eb !important;\n}\n\n.diwan-table thead th {\n  background: linear-gradient(135deg, #191970 0%, #800020 100%) !important;\n  color: white !important;\n  font-weight: 600 !important;\n  padding: 1rem !important;\n  text-align: right !important;\n  border: none !important;\n}\n\n.diwan-table tbody td {\n  padding: 1rem !important;\n  border-bottom: 1px solid #f3f4f6 !important;\n  color: #333333 !important;\n}\n\n.diwan-table tbody tr:hover {\n  background-color: #f8f9fa !important;\n}\n\n/* أزرار الإجراءات في الجداول */\n.table-actions {\n  display: flex !important;\n  gap: 0.5rem !important;\n  justify-content: center !important;\n  align-items: center !important;\n}\n\n.table-action-btn {\n  padding: 0.5rem !important;\n  border-radius: 0.5rem !important;\n  border: none !important;\n  cursor: pointer !important;\n  transition: all 0.2s ease !important;\n  display: inline-flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n}\n\n.table-action-btn:hover {\n  transform: translateY(-1px) !important;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;\n}\n\n.table-action-edit {\n  background-color: #007bff !important;\n  color: white !important;\n}\n\n.table-action-edit:hover {\n  background-color: #0056cc !important;\n}\n\n.table-action-delete {\n  background-color: #dc3545 !important;\n  color: white !important;\n}\n\n.table-action-delete:hover {\n  background-color: #c82333 !important;\n}\n\n.table-action-view {\n  background-color: #28a745 !important;\n  color: white !important;\n}\n\n.table-action-view:hover {\n  background-color: #228b3c !important;\n}\n\n/* شارات الحالة في الجداول */\n.table-badge {\n  padding: 0.25rem 0.75rem !important;\n  border-radius: 1rem !important;\n  font-size: 0.75rem !important;\n  font-weight: 600 !important;\n  text-align: center !important;\n  display: inline-block !important;\n}\n\n.table-badge-primary {\n  background-color: rgba(0, 123, 255, 0.1) !important;\n  color: #007bff !important;\n  border: 1px solid rgba(0, 123, 255, 0.2) !important;\n}\n\n.table-badge-success {\n  background-color: rgba(40, 167, 69, 0.1) !important;\n  color: #28a745 !important;\n  border: 1px solid rgba(40, 167, 69, 0.2) !important;\n}\n\n.table-badge-danger {\n  background-color: rgba(220, 53, 69, 0.1) !important;\n  color: #dc3545 !important;\n  border: 1px solid rgba(220, 53, 69, 0.2) !important;\n}\n\n.table-badge-warning {\n  background-color: rgba(255, 193, 7, 0.1) !important;\n  color: #856404 !important;\n  border: 1px solid rgba(255, 193, 7, 0.2) !important;\n}\n\n.table-badge-info {\n  background-color: rgba(23, 162, 184, 0.1) !important;\n  color: #17a2b8 !important;\n  border: 1px solid rgba(23, 162, 184, 0.2) !important;\n}\n\n/* تحسينات للطباعة */\n@media print {\n  .table {\n    border-collapse: collapse !important;\n  }\n  \n  .table th,\n  .table td {\n    border: 1px solid #000000 !important;\n    padding: 0.5rem !important;\n  }\n  \n  .table thead th {\n    background-color: #f8f9fa !important;\n    color: #000000 !important;\n  }\n  \n  .table-actions {\n    display: none !important;\n  }\n}\n\n/* تحسينات للوصولية */\n.table th[scope=\"col\"] {\n  text-align: right !important;\n}\n\n.table th[scope=\"row\"] {\n  text-align: right !important;\n  font-weight: 600 !important;\n}\n\n/* تأثيرات التحميل */\n.table-loading {\n  position: relative !important;\n}\n\n.table-loading::after {\n  content: '' !important;\n  position: absolute !important;\n  top: 0 !important;\n  left: 0 !important;\n  right: 0 !important;\n  bottom: 0 !important;\n  background-color: rgba(255, 255, 255, 0.8) !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n}\n\n/* تحسينات للتمرير */\n.table-container {\n  max-height: 600px !important;\n  overflow-y: auto !important;\n  border-radius: 1rem !important;\n  border: 1px solid #e5e7eb !important;\n}\n\n.table-container::-webkit-scrollbar {\n  width: 8px !important;\n}\n\n.table-container::-webkit-scrollbar-track {\n  background: #f1f1f1 !important;\n  border-radius: 4px !important;\n}\n\n.table-container::-webkit-scrollbar-thumb {\n  background: #c1c1c1 !important;\n  border-radius: 4px !important;\n}\n\n.table-container::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8 !important;\n}\n\n/* تطبيق خاص لمكونات React Table */\n[data-radix-collection-item] {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n/* تطبيق على مكونات Shadcn UI */\n.table-header-row th,\n.table-header-row [role=\"columnheader\"] {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n/* تطبيق شامل لجميع عناصر رأس الجدول */\ntable > thead > tr > th,\ntable thead tr th,\n.table > thead > tr > th,\n[role=\"table\"] [role=\"columnheader\"],\n[data-testid*=\"table\"] [role=\"columnheader\"] {\n  background-color: #191970 !important;\n  color: white !important;\n  font-weight: 600 !important;\n}\n\n/* تطبيق على مكونات Next.js */\n[class*=\"TableHead\"],\n[class*=\"table-head\"],\n[class*=\"tableHead\"] {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n/* تطبيق قوي جداً */\nth,\n[role=\"columnheader\"] {\n  background-color: #191970 !important;\n  color: white !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;;;;;AAWA;;;;;;;;;;AAgBA;;;;;AASA;;;;;;;;AAYA;;;;AAKA;;;;AAKA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAMA;;;;AAOA;;;;AAQA;;;;AAIA;;;;AAOA;;;;AAIA;;;;AAQA;;;;AAKA;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;;;AAOA;;;;AAKA;EACE;;;;;EAMA;;;;;;AAOF;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;AAMA;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;EACE;;;;EAIA;;;;;EAMA;;;;;EAKA;;;;;AAMF;;;;AAIA;;;;;AAMA;;;;AAIA;;;;;;;;;;AAcA;;;;;;;AAOA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;;AAaA;;;;;;AAWA", "debugId": null}}, {"offset": {"line": 7316, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/color-fixes.css"], "sourcesContent": ["/* إصلاحات الألوان الطارئة لديوان أبو علوش */\n\n/* إصلاح رؤوس الجداول - تطبيق قوي جداً */\nth,\nthead th,\ntable th,\n.table th,\n[role=\"columnheader\"],\n[data-testid*=\"table\"] th,\n[class*=\"TableHead\"],\n[class*=\"table-head\"] {\n  background-color: #191970 !important;\n  color: white !important;\n  font-weight: 600 !important;\n  border: none !important;\n}\n\n/* إصلاح خاص لمكونات Shadcn UI */\n[data-radix-collection-item],\n[data-state=\"open\"] th,\n[data-state=\"closed\"] th {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n/* إصلاح الأزرار الأساسية */\nbutton[style*=\"007bff\"],\n.btn-primary,\n.bg-primary {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\nbutton[style*=\"28a745\"],\n.btn-success,\n.bg-success {\n  background-color: #28a745 !important;\n  border-color: #28a745 !important;\n  color: white !important;\n}\n\nbutton[style*=\"dc3545\"],\n.btn-danger,\n.bg-danger {\n  background-color: #dc3545 !important;\n  border-color: #dc3545 !important;\n  color: white !important;\n}\n\n/* إصلاح ألوان النصوص - تحسين التباين */\n.text-blue-600,\n.text-blue-500 {\n  color: #0056cc !important; /* أزرق أغمق للوضوح */\n}\n\n.text-green-600,\n.text-green-500 {\n  color: #1e7e34 !important; /* أخضر أغمق للوضوح */\n}\n\n.text-red-600,\n.text-red-500 {\n  color: #c82333 !important; /* أحمر أغمق للوضوح */\n}\n\n/* تحسين وضوح النصوص العامة */\np, span, div, label, td, li {\n  color: #333333 !important;\n  font-weight: 500 !important; /* وزن خط أثقل للوضوح */\n}\n\n/* تحسين النصوص على الخلفيات الفاتحة */\n.bg-white p,\n.bg-white span,\n.bg-white div,\n.bg-white label {\n  color: #212529 !important; /* لون أغمق للوضوح */\n}\n\n/* إصلاح خلفيات البطاقات */\n.bg-gradient-to-br {\n  background: white !important;\n}\n\n/* تحسين النصوص في البطاقات */\n.card h1,\n.card h2,\n.card h3,\n.card h4,\n.card h5,\n.card h6 {\n  color: #191970 !important;\n  font-weight: 700 !important;\n}\n\n.card p,\n.card span,\n.card div {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تحسين عناوين البطاقات */\n.card-title,\n[data-testid=\"card-title\"],\nh3[class*=\"text-sm\"] {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n/* تحسين النصوص في بطاقات الإحصائيات */\n.stats-card-value,\ndiv[class*=\"text-2xl\"],\n.text-2xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n.stats-card-title,\n.stats-card-description {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n/* تطبيق قوي للنصوص في البطاقات */\n[role=\"region\"] h3,\n[role=\"region\"] div[class*=\"text-2xl\"],\n.card-content div,\n.card-header h3 {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n/* تحسين خاص لبطاقات الأعضاء */\n.members-stats .card-title,\n.members-stats h3 {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n.members-stats .text-2xl,\n.members-stats div[class*=\"font-bold\"] {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* إصلاح الأيقونات الملونة */\n.text-blue-600 svg,\n.text-green-600 svg,\n.text-red-600 svg {\n  color: inherit !important;\n}\n\n/* إصلاح أزرار الإجراءات في الجداول - تحسين الوضوح */\n.table-actions button {\n  padding: 0.5rem !important;\n  border-radius: 0.5rem !important;\n  transition: all 0.2s ease !important;\n  border: 1px solid transparent !important;\n}\n\n/* أزرار الإجراءات بألوان محسنة للوضوح */\nbutton[style*=\"color: rgb(0, 123, 255)\"],\n.table-actions .text-blue-600 {\n  color: #0056cc !important; /* أزرق أغمق */\n  background-color: rgba(0, 86, 204, 0.1) !important;\n  border-color: rgba(0, 86, 204, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(0, 123, 255)\"]:hover,\n.table-actions .text-blue-600:hover {\n  background-color: rgba(0, 86, 204, 0.2) !important;\n  color: #003d82 !important;\n}\n\nbutton[style*=\"color: rgb(40, 167, 69)\"],\n.table-actions .text-green-600 {\n  color: #1e7e34 !important; /* أخضر أغمق */\n  background-color: rgba(30, 126, 52, 0.1) !important;\n  border-color: rgba(30, 126, 52, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(40, 167, 69)\"]:hover,\n.table-actions .text-green-600:hover {\n  background-color: rgba(30, 126, 52, 0.2) !important;\n  color: #155724 !important;\n}\n\nbutton[style*=\"color: rgb(220, 53, 69)\"],\n.table-actions .text-red-600 {\n  color: #c82333 !important; /* أحمر أغمق */\n  background-color: rgba(200, 35, 51, 0.1) !important;\n  border-color: rgba(200, 35, 51, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(220, 53, 69)\"]:hover,\n.table-actions .text-red-600:hover {\n  background-color: rgba(200, 35, 51, 0.2) !important;\n  color: #a71e2a !important;\n}\n\nbutton[style*=\"color: rgb(128, 0, 32)\"],\n.table-actions .text-purple-600 {\n  color: #800020 !important;\n  background-color: rgba(128, 0, 32, 0.1) !important;\n  border-color: rgba(128, 0, 32, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(128, 0, 32)\"]:hover,\n.table-actions .text-purple-600:hover {\n  background-color: rgba(128, 0, 32, 0.2) !important;\n  color: #660019 !important;\n}\n\nbutton[style*=\"color: rgb(255, 193, 7)\"],\n.table-actions .text-orange-600 {\n  color: #b8860b !important; /* أصفر أغمق للوضوح */\n  background-color: rgba(184, 134, 11, 0.1) !important;\n  border-color: rgba(184, 134, 11, 0.2) !important;\n}\n\nbutton[style*=\"color: rgb(255, 193, 7)\"]:hover,\n.table-actions .text-orange-600:hover {\n  background-color: rgba(184, 134, 11, 0.2) !important;\n  color: #9a7209 !important;\n}\n\n/* إصلاح شامل لجميع عناصر الجدول */\ntable,\n.table {\n  background-color: white !important;\n}\n\ntable tbody tr:hover,\n.table tbody tr:hover {\n  background-color: #f8f9fa !important;\n}\n\ntable td,\n.table td {\n  color: #212529 !important; /* لون أغمق للوضوح */\n  border-bottom: 1px solid #f3f4f6 !important;\n  font-weight: 500 !important; /* وزن خط أثقل */\n}\n\n/* تحسين النصوص في خلايا الجدول */\ntable td span,\ntable td div,\n.table td span,\n.table td div {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تحسين الشارات في الجداول */\n.badge {\n  font-weight: 600 !important;\n  font-size: 0.75rem !important;\n  padding: 0.375rem 0.75rem !important;\n}\n\n/* إصلاح أزرار التصفح */\n.pagination button {\n  background-color: white !important;\n  border: 1px solid #dee2e6 !important;\n  color: #333333 !important;\n}\n\n.pagination button:hover {\n  background-color: #007bff !important;\n  border-color: #007bff !important;\n  color: white !important;\n}\n\n.pagination button:disabled {\n  background-color: #f8f9fa !important;\n  border-color: #dee2e6 !important;\n  color: #6c757d !important;\n}\n\n/* إصلاح أزرار البحث والتصفية */\n.search-container button {\n  background-color: #007bff !important;\n  color: white !important;\n  border: none !important;\n}\n\n.search-container button:hover {\n  background-color: #0056cc !important;\n}\n\n/* إصلاح النماذج */\ninput,\nselect,\ntextarea {\n  background-color: white !important;\n  color: #333333 !important;\n  border: 1px solid #dee2e6 !important;\n}\n\ninput:focus,\nselect:focus,\ntextarea:focus {\n  border-color: #007bff !important;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;\n}\n\n/* إصلاح الشارات */\n.badge,\n.badge-primary {\n  background-color: rgba(0, 123, 255, 0.1) !important;\n  color: #007bff !important;\n  border: 1px solid rgba(0, 123, 255, 0.2) !important;\n}\n\n.badge-success {\n  background-color: rgba(40, 167, 69, 0.1) !important;\n  color: #28a745 !important;\n  border: 1px solid rgba(40, 167, 69, 0.2) !important;\n}\n\n.badge-danger {\n  background-color: rgba(220, 53, 69, 0.1) !important;\n  color: #dc3545 !important;\n  border: 1px solid rgba(220, 53, 69, 0.2) !important;\n}\n\n.badge-warning {\n  background-color: rgba(25, 25, 112, 0.1) !important;\n  color: #191970 !important;\n  border: 1px solid rgba(25, 25, 112, 0.2) !important;\n}\n\n/* إصلاح التنبيهات */\n.alert-primary {\n  background-color: rgba(0, 123, 255, 0.1) !important;\n  border-color: #007bff !important;\n  color: #004085 !important;\n}\n\n.alert-success {\n  background-color: rgba(40, 167, 69, 0.1) !important;\n  border-color: #28a745 !important;\n  color: #155724 !important;\n}\n\n.alert-danger {\n  background-color: rgba(220, 53, 69, 0.1) !important;\n  border-color: #dc3545 !important;\n  color: #721c24 !important;\n}\n\n/* إصلاح الروابط */\na {\n  color: #007bff !important;\n}\n\na:hover {\n  color: #0056cc !important;\n}\n\n/* إصلاح عام للألوان */\n.text-primary {\n  color: #007bff !important;\n}\n\n.text-success {\n  color: #28a745 !important;\n}\n\n.text-danger {\n  color: #dc3545 !important;\n}\n\n.text-warning {\n  color: #191970 !important;\n}\n\n.text-info {\n  color: #17a2b8 !important;\n}\n\n/* إصلاح الخلفيات */\n.bg-primary {\n  background-color: #007bff !important;\n  color: white !important;\n}\n\n.bg-success {\n  background-color: #28a745 !important;\n  color: white !important;\n}\n\n.bg-danger {\n  background-color: #dc3545 !important;\n  color: white !important;\n}\n\n.bg-warning {\n  background-color: #191970 !important;\n  color: white !important;\n}\n\n.bg-info {\n  background-color: #17a2b8 !important;\n  color: white !important;\n}\n\n/* إصلاح الحدود */\n.border-primary {\n  border-color: #007bff !important;\n}\n\n.border-success {\n  border-color: #28a745 !important;\n}\n\n.border-danger {\n  border-color: #dc3545 !important;\n}\n\n.border-warning {\n  border-color: #191970 !important;\n}\n\n.border-info {\n  border-color: #17a2b8 !important;\n}\n\n/* تحسينات شاملة لوضوح النصوص */\nbody {\n  font-family: 'Cairo', 'Almarai', system-ui, sans-serif !important;\n  -webkit-font-smoothing: antialiased !important;\n  -moz-osx-font-smoothing: grayscale !important;\n}\n\n/* تحسين التباين للنصوص الصغيرة */\n.text-xs,\n.text-sm {\n  font-weight: 600 !important;\n  color: #495057 !important;\n}\n\n/* تحسين النصوص في النماذج */\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  color: #6c757d !important;\n  font-weight: 500 !important;\n}\ninput::placeholder,\ntextarea::placeholder,\nselect option {\n  color: #6c757d !important;\n  font-weight: 500 !important;\n}\n\n/* تحسين النصوص في القوائم المنسدلة */\n.dropdown-menu {\n  color: #212529 !important;\n}\n\n.dropdown-item {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n.dropdown-item:hover {\n  background-color: #f8f9fa !important;\n  color: #191970 !important;\n}\n\n/* تحسين النصوص في التبويبات */\n.nav-link {\n  color: #495057 !important;\n  font-weight: 500 !important;\n}\n\n.nav-link.active {\n  color: #191970 !important;\n  font-weight: 600 !important;\n}\n\n/* تحسين النصوص في الشريط الجانبي */\n.sidebar a {\n  color: #e9ecef !important;\n  font-weight: 500 !important;\n}\n\n.sidebar a:hover {\n  color: white !important;\n  font-weight: 600 !important;\n}\n\n/* تحسين عام للقراءة */\n* {\n  text-rendering: optimizeLegibility !important;\n}\n\n/* تحسين النصوص على الخلفيات الملونة */\n.bg-primary *,\n.bg-success *,\n.bg-danger *,\n.bg-warning *,\n.bg-info * {\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;;;AAeA;;;;;AAQA;;;;;;AAQA;;;;;;AAQA;;;;;;AASA;;;;AAKA;;;;AAKA;;;;AAMA;;;;;AAMA;;;;AAQA;;;;AAKA;;;;;AAUA;;;;;AAQA;;;;;;AASA;;;;;;AAQA;;;;;AAgBA;;;;;;AAOA;;;;;;AAQA;;;;AAOA;;;;;;;AAQA;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAOA;;;;AAKA;;;;AAKA;;;;;;AAQA;;;;;AASA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;;AAMA;;;;AAKA;;;;;;AAQA;;;;;AAQA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;AAOA;;;;;AAOA;;;;;AAIA;;;;;AAQA;;;;AAIA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;AAKA", "debugId": null}}, {"offset": {"line": 7720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/members-text-fix.css"], "sourcesContent": ["/* إصلاح خاص لنصوص صفحة الأعضاء */\n\n/* تطبيق قوي جداً لنصوص بطاقات الإحصائيات */\n.members-stats * {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n.members-stats .text-2xl,\n.members-stats div[class*=\"text-2xl\"],\n.members-stats div[class*=\"font-bold\"],\n.members-stats div[class*=\"font-black\"] {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n  line-height: 1.2 !important;\n}\n\n.members-stats h3,\n.members-stats .card-title,\n.members-stats [role=\"heading\"] {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n  line-height: 1.4 !important;\n}\n\n/* تطبيق مباشر على عناصر محددة */\n.members-stats .bg-white h3 {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n.members-stats .bg-white div[class*=\"text-2xl\"] {\n  color: #191970 !important;\n  font-weight: 900 !important;\n}\n\n/* تطبيق على جميع النصوص في البطاقات */\n[class*=\"Card\"] h3,\n[class*=\"Card\"] div[class*=\"text-2xl\"],\n[class*=\"CardTitle\"],\n[class*=\"CardContent\"] div {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n[class*=\"CardContent\"] div[class*=\"text-2xl\"] {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* تطبيق على عناصر React */\n[data-testid*=\"card\"] h3,\n[data-testid*=\"card\"] div {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n/* تطبيق شامل لجميع النصوص في صفحة الأعضاء */\n.members-page h1,\n.members-page h2,\n.members-page h3,\n.members-page h4,\n.members-page h5,\n.members-page h6 {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n.members-page p,\n.members-page span,\n.members-page div {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تطبيق خاص للأرقام الكبيرة */\n.members-page .text-2xl,\n.members-page .text-3xl,\n.members-page .text-4xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n}\n\n/* تطبيق على النصوص في الجداول */\n.members-page table td,\n.members-page table th {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n.members-page table th {\n  color: white !important;\n  font-weight: 600 !important;\n}\n\n/* تطبيق على الأزرار */\n.members-page button {\n  font-weight: 600 !important;\n}\n\n/* تطبيق على النماذج */\n.members-page input,\n.members-page select,\n.members-page textarea {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تطبيق على الشارات */\n.members-page .badge,\n.members-page span[class*=\"rounded-full\"] {\n  font-weight: 600 !important;\n}\n\n/* تطبيق قوي للغاية */\n* {\n  -webkit-font-smoothing: antialiased !important;\n  -moz-osx-font-smoothing: grayscale !important;\n  text-rendering: optimizeLegibility !important;\n}\n\n/* تطبيق خاص لعناصر Shadcn UI */\n[data-radix-collection-item] *,\n[data-state] *,\n[role=\"region\"] * {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تطبيق على النصوص الصغيرة */\n.text-xs,\n.text-sm {\n  color: #495057 !important;\n  font-weight: 600 !important;\n}\n\n/* تطبيق على النصوص المتوسطة */\n.text-base,\n.text-lg {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تطبيق على النصوص الكبيرة */\n.text-xl,\n.text-2xl,\n.text-3xl {\n  color: #191970 !important;\n  font-weight: 700 !important;\n}\n\n/* تطبيق خاص للعناوين */\nh1, h2, h3, h4, h5, h6 {\n  color: #191970 !important;\n  font-weight: 700 !important;\n}\n\n/* تطبيق على النصوص العادية */\np, span, div, label {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تطبيق خاص لبطاقات الإحصائيات - تطبيق نهائي */\n.members-stats .bg-white .text-sm {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n.members-stats .bg-white .text-2xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* تطبيق على جميع عناصر البطاقات */\n.card *,\n.Card *,\n[class*=\"card\"] *,\n[class*=\"Card\"] * {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n.card .text-2xl,\n.Card .text-2xl,\n[class*=\"card\"] .text-2xl,\n[class*=\"Card\"] .text-2xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n}\n\n/* تطبيق نهائي وقوي */\nbody * {\n  color: #212529 !important;\n}\n\n.text-2xl,\n.font-bold,\n.font-black {\n  color: #191970 !important;\n  font-weight: 900 !important;\n}\n\n.text-sm {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;AAKA;;;;;;;AAUA;;;;;;;AAUA;;;;;AAKA;;;;;AAMA;;;;;AAQA;;;;;;AAOA;;;;;AAiBA;;;;;AAQA;;;;;AAQA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;;AAQA;;;;AAMA;;;;;;AAOA;;;;;AAQA;;;;;AAOA;;;;;AAOA;;;;;AAcA;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;AAQA;;;;;AASA;;;;AAIA;;;;;AAOA", "debugId": null}}, {"offset": {"line": 7867, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/global-stats-fix.css"], "sourcesContent": ["/* إصلاح شامل لوضوح النصوص في بطاقات الإحصائيات عبر كامل البرنامج */\n\n/* تطبيق قوي جداً على جميع بطاقات الإحصائيات */\n[class*=\"Card\"],\n.card,\n.stats-card,\n[role=\"region\"],\n[data-testid*=\"card\"] {\n  background-color: white !important;\n}\n\n/* إصلاح عناوين البطاقات */\n[class*=\"Card\"] h3,\n[class*=\"Card\"] [class*=\"CardTitle\"],\n[class*=\"CardTitle\"],\n.card h3,\n.card .card-title,\n.stats-card h3,\n[role=\"region\"] h3,\n[data-testid*=\"card\"] h3 {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n  line-height: 1.4 !important;\n  text-shadow: none !important;\n}\n\n/* إصلاح الأرقام الكبيرة في البطاقات */\n[class*=\"Card\"] .text-2xl,\n[class*=\"Card\"] .text-3xl,\n[class*=\"Card\"] [class*=\"font-bold\"],\n[class*=\"Card\"] [class*=\"font-black\"],\n.card .text-2xl,\n.card .text-3xl,\n.stats-card .value,\n[role=\"region\"] .text-2xl,\n[role=\"region\"] .text-3xl,\n[data-testid*=\"card\"] .text-2xl,\n[data-testid*=\"card\"] .text-3xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n  line-height: 1.2 !important;\n  text-shadow: none !important;\n}\n\n/* إصلاح النصوص العادية في البطاقات */\n[class*=\"Card\"] p,\n[class*=\"Card\"] span,\n[class*=\"Card\"] div:not(.text-2xl):not(.text-3xl),\n.card p,\n.card span,\n.card div:not(.text-2xl):not(.text-3xl),\n.stats-card p,\n.stats-card span,\n[role=\"region\"] p,\n[role=\"region\"] span,\n[data-testid*=\"card\"] p,\n[data-testid*=\"card\"] span {\n  color: #495057 !important;\n  font-weight: 500 !important;\n  text-shadow: none !important;\n}\n\n/* تطبيق خاص لصفحة الرئيسية */\n.dashboard-page [class*=\"Card\"] h3,\n.dashboard-page [class*=\"CardTitle\"] {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n.dashboard-page [class*=\"Card\"] .text-3xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* تطبيق خاص لصفحة الأعضاء */\n.members-page [class*=\"Card\"] h3,\n.members-page [class*=\"CardTitle\"],\n.members-stats [class*=\"CardTitle\"] {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n.members-page [class*=\"Card\"] .text-2xl,\n.members-stats .text-2xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* تطبيق خاص لصفحة الإيرادات */\n.incomes-page [class*=\"Card\"] h3,\n.incomes-page [class*=\"CardTitle\"] {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n.incomes-page [class*=\"Card\"] .text-3xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* تطبيق خاص لصفحة المصروفات */\n.expenses-page [class*=\"Card\"] h3,\n.expenses-page [class*=\"CardTitle\"] {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n.expenses-page [class*=\"Card\"] .text-3xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* تطبيق خاص لصفحة التقارير */\n.reports-page [class*=\"Card\"] h3,\n.reports-page [class*=\"CardTitle\"] {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n.reports-page [class*=\"Card\"] .text-2xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* تطبيق على مكونات Shadcn UI */\n[data-radix-collection-item] h3,\n[data-radix-collection-item] [class*=\"text-\"],\n[data-state] h3,\n[data-state] [class*=\"text-\"] {\n  color: #212529 !important;\n  font-weight: 700 !important;\n}\n\n[data-radix-collection-item] .text-2xl,\n[data-radix-collection-item] .text-3xl,\n[data-state] .text-2xl,\n[data-state] .text-3xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n}\n\n/* تطبيق على عناصر محددة بالألوان */\n.text-gray-800,\n.text-gray-700,\n.text-gray-600 {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n.text-orange-700,\n.text-green-700,\n.text-blue-700,\n.text-red-700 {\n  color: #191970 !important;\n  font-weight: 900 !important;\n}\n\n/* تطبيق على النصوص الصغيرة */\n.text-xs,\n.text-sm {\n  color: #495057 !important;\n  font-weight: 600 !important;\n}\n\n/* تطبيق على النصوص المتوسطة */\n.text-base {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تطبيق على النصوص الكبيرة */\n.text-lg,\n.text-xl,\n.text-2xl,\n.text-3xl,\n.text-4xl {\n  color: #191970 !important;\n  font-weight: 700 !important;\n}\n\n/* تطبيق على أوزان الخطوط */\n.font-medium {\n  font-weight: 600 !important;\n}\n\n.font-semibold {\n  font-weight: 700 !important;\n}\n\n.font-bold,\n.font-black {\n  font-weight: 900 !important;\n}\n\n/* تطبيق شامل لتحسين الوضوح */\n* {\n  -webkit-font-smoothing: antialiased !important;\n  -moz-osx-font-smoothing: grayscale !important;\n  text-rendering: optimizeLegibility !important;\n}\n\n/* تطبيق خاص للعناوين */\nh1, h2, h3, h4, h5, h6 {\n  color: #191970 !important;\n  font-weight: 700 !important;\n  text-shadow: none !important;\n}\n\n/* تطبيق على النصوص العادية */\np, span, div, label {\n  color: #212529 !important;\n  font-weight: 500 !important;\n  text-shadow: none !important;\n}\n\n/* تطبيق خاص للأرقام */\n.number,\n.amount,\n.count,\n.value {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n/* تطبيق على الشارات */\n.badge,\n.tag,\n.chip {\n  color: #212529 !important;\n  font-weight: 600 !important;\n}\n\n/* تطبيق على الروابط */\na {\n  color: #007bff !important;\n  font-weight: 500 !important;\n}\n\na:hover {\n  color: #0056cc !important;\n  font-weight: 600 !important;\n}\n\n/* تطبيق على النماذج */\ninput,\nselect,\ntextarea {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  color: #6c757d !important;\n  font-weight: 400 !important;\n}\n\ninput::placeholder,\ntextarea::placeholder {\n  color: #6c757d !important;\n  font-weight: 400 !important;\n}\n\n/* تطبيق على الأزرار */\nbutton {\n  font-weight: 600 !important;\n}\n\n/* تطبيق على القوائم */\nul, ol, li {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n/* تطبيق على الجداول */\ntable, th, td {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\nth {\n  font-weight: 600 !important;\n}\n\n/* تطبيق نهائي وقوي للغاية */\nbody * {\n  text-shadow: none !important;\n}\n\n/* تطبيق خاص لبطاقات الإحصائيات المحددة */\n.stats-card-primary .value,\n.stats-card-success .value,\n.stats-card-warning .value,\n.stats-card-danger .value {\n  color: #191970 !important;\n  font-weight: 900 !important;\n  font-size: 2rem !important;\n}\n\n.stats-card-primary h3,\n.stats-card-success h3,\n.stats-card-warning h3,\n.stats-card-danger h3 {\n  color: #212529 !important;\n  font-weight: 700 !important;\n  font-size: 0.875rem !important;\n}\n\n/* تطبيق على بطاقات التدرج */\n.bg-gradient-to-br *,\n.bg-gradient-to-r *,\n.bg-gradient-to-l * {\n  color: #212529 !important;\n  font-weight: 500 !important;\n}\n\n.bg-gradient-to-br .text-2xl,\n.bg-gradient-to-r .text-2xl,\n.bg-gradient-to-l .text-2xl,\n.bg-gradient-to-br .text-3xl,\n.bg-gradient-to-r .text-3xl,\n.bg-gradient-to-l .text-3xl {\n  color: #191970 !important;\n  font-weight: 900 !important;\n}\n"], "names": [], "mappings": "AAGA;;;;AASA;;;;;;;;AAgBA;;;;;;;;AAmBA;;;;;;AAkBA;;;;;;AAOA;;;;;;AAOA;;;;;;AAQA;;;;;;AAQA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;AAQA;;;;;AASA;;;;;AAOA;;;;;AASA;;;;;AAOA;;;;;AAMA;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAMA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAUA;;;;;AAQA;;;;;AAKA;;;;;AAMA;;;;;AAOA;;;;;AAAA;;;;;AAYA;;;;AAKA;;;;;AAWA;;;;AAKA;;;;AAKA;;;;;;AASA;;;;;;AAUA;;;;;AAOA", "debugId": null}}, {"offset": {"line": 8095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/final-colors.css"], "sourcesContent": ["/* الألوان النهائية المحدثة لديوان أبو علوش */\n\n/* تطبيق الألوان الأساسية على الجذر */\n:root {\n  /* الألوان المطلوبة */\n  --final-background: #f9f9f9;\n  --final-background-alt: #ffffff;\n  --final-text: #333333;\n  --final-primary: #007bff;\n  --final-success: #28a745;\n  --final-header-primary: #191970;\n  --final-header-secondary: #800020;\n}\n\n/* تطبيق شامل للألوان */\n* {\n  color: var(--final-text) !important;\n}\n\n/* الخلفيات */\nbody,\n.bg-gray-50,\n.bg-gray-100 {\n  background-color: var(--final-background) !important;\n}\n\n.bg-white,\n.card,\n.modal-content,\n.dropdown-menu {\n  background-color: var(--final-background-alt) !important;\n}\n\n/* النصوص */\np, span, div, label, td, li, a {\n  color: var(--final-text) !important;\n}\n\n/* الترويسات */\nh1, h2, h3, h4, h5, h6 {\n  color: var(--final-header-primary) !important;\n}\n\n/* الأزرار الأساسية */\n.btn-primary,\n.bg-blue-500,\n.bg-blue-600,\nbutton[class*=\"bg-blue\"] {\n  background-color: var(--final-primary) !important;\n  border-color: var(--final-primary) !important;\n  color: white !important;\n}\n\n.btn-primary:hover,\n.bg-blue-500:hover,\n.bg-blue-600:hover,\nbutton[class*=\"bg-blue\"]:hover {\n  background-color: #0056cc !important;\n  border-color: #0056cc !important;\n}\n\n/* الأزرار الخضراء */\n.btn-success,\n.bg-green-500,\n.bg-green-600,\nbutton[class*=\"bg-green\"] {\n  background-color: var(--final-success) !important;\n  border-color: var(--final-success) !important;\n  color: white !important;\n}\n\n.btn-success:hover,\n.bg-green-500:hover,\n.bg-green-600:hover,\nbutton[class*=\"bg-green\"]:hover {\n  background-color: #228b3c !important;\n  border-color: #228b3c !important;\n}\n\n/* الشريط الجانبي */\n.sidebar,\ndiv[class*=\"fixed\"][class*=\"inset-y-0\"][class*=\"right-0\"][class*=\"w-64\"] {\n  background-color: var(--final-header-primary) !important;\n  color: white !important;\n}\n\n.sidebar *,\ndiv[class*=\"fixed\"][class*=\"inset-y-0\"][class*=\"right-0\"][class*=\"w-64\"] * {\n  color: white !important;\n}\n\n.sidebar h1,\n.sidebar h2,\n.sidebar h3 {\n  color: white !important;\n}\n\n.sidebar p,\n.sidebar .text-gray-500 {\n  color: #d1d5db !important;\n}\n\n.sidebar a {\n  color: #e5e7eb !important;\n}\n\n.sidebar a:hover {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n  color: white !important;\n}\n\n/* الجداول */\n.table th,\ntable th,\nthead th {\n  background-color: var(--final-header-primary) !important;\n  color: white !important;\n}\n\n.table,\ntable {\n  background-color: var(--final-background-alt) !important;\n}\n\n.table td,\ntable td {\n  color: var(--final-text) !important;\n}\n\n/* النماذج */\n.form-control,\n.form-select,\ninput,\ntextarea,\nselect {\n  background-color: var(--final-background-alt) !important;\n  color: var(--final-text) !important;\n  border-color: #dee2e6 !important;\n}\n\n.form-control:focus,\ninput:focus,\ntextarea:focus,\nselect:focus {\n  border-color: var(--final-primary) !important;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;\n}\n\n/* الروابط */\na {\n  color: var(--final-primary) !important;\n}\n\na:hover {\n  color: #0056cc !important;\n}\n\n/* التنبيهات */\n.alert-primary {\n  background-color: rgba(0, 123, 255, 0.1) !important;\n  border-color: var(--final-primary) !important;\n  color: var(--final-primary) !important;\n}\n\n.alert-success {\n  background-color: rgba(40, 167, 69, 0.1) !important;\n  border-color: var(--final-success) !important;\n  color: var(--final-success) !important;\n}\n\n/* الشارات */\n.badge-primary {\n  background-color: var(--final-primary) !important;\n  color: white !important;\n}\n\n.badge-success {\n  background-color: var(--final-success) !important;\n  color: white !important;\n}\n\n/* النوافذ المنبثقة */\n.modal-header {\n  background-color: var(--final-header-primary) !important;\n  color: white !important;\n}\n\n.modal-title {\n  color: white !important;\n}\n\n/* التبويبات */\n.nav-tabs .nav-link.active {\n  background-color: var(--final-background-alt) !important;\n  border-color: var(--final-primary) !important;\n  color: var(--final-primary) !important;\n}\n\n.nav-tabs .nav-link {\n  color: var(--final-text) !important;\n}\n\n.nav-tabs .nav-link:hover {\n  color: var(--final-primary) !important;\n}\n\n/* أشرطة التقدم */\n.progress-bar {\n  background-color: var(--final-primary) !important;\n}\n\n/* الحدود */\n.border-primary {\n  border-color: var(--final-primary) !important;\n}\n\n.border-success {\n  border-color: var(--final-success) !important;\n}\n\n/* أنماط خاصة بالديوان */\n.diwan-primary {\n  background-color: var(--final-primary) !important;\n  color: white !important;\n}\n\n.diwan-success {\n  background-color: var(--final-success) !important;\n  color: white !important;\n}\n\n.diwan-header {\n  background-color: var(--final-header-primary) !important;\n  color: white !important;\n}\n\n.diwan-header-alt {\n  background-color: var(--final-header-secondary) !important;\n  color: white !important;\n}\n\n.diwan-text {\n  color: var(--final-text) !important;\n}\n\n.diwan-text-header {\n  color: var(--final-header-primary) !important;\n}\n\n/* تطبيق قوي للألوان */\n.text-gray-900,\n.text-gray-800,\n.text-gray-700,\n.text-gray-600 {\n  color: var(--final-text) !important;\n}\n\n.text-gray-500,\n.text-gray-400 {\n  color: #6b7280 !important;\n}\n\n.bg-gray-50,\n.bg-gray-100 {\n  background-color: var(--final-background) !important;\n}\n\n.bg-white {\n  background-color: var(--final-background-alt) !important;\n}\n\n/* تحسينات للاستجابة */\n@media (max-width: 768px) {\n  .sidebar {\n    background-color: var(--final-header-primary) !important;\n  }\n  \n  h1, h2, h3 {\n    color: var(--final-header-primary) !important;\n  }\n}\n\n/* تطبيق نهائي شامل */\nbody * {\n  color: var(--final-text) !important;\n}\n\nbody h1,\nbody h2,\nbody h3,\nbody h4,\nbody h5,\nbody h6 {\n  color: var(--final-header-primary) !important;\n}\n\nbody .sidebar,\nbody .sidebar * {\n  color: white !important;\n}\n\nbody .sidebar h1,\nbody .sidebar h2,\nbody .sidebar h3 {\n  color: white !important;\n}\n\n/* إعادة تعيين قوية */\n.text-blue-600,\n.text-blue-500 {\n  color: var(--final-primary) !important;\n}\n\n.text-green-600,\n.text-green-500 {\n  color: var(--final-success) !important;\n}\n\n/* تأكيد الألوان */\n.important-text {\n  color: var(--final-text) !important;\n}\n\n.important-header {\n  color: var(--final-header-primary) !important;\n}\n\n.important-primary {\n  color: var(--final-primary) !important;\n}\n\n.important-success {\n  color: var(--final-success) !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;;;;;;AAYA;;;;AAKA;;;;AAMA;;;;AAQA;;;;AAKA;;;;AAKA;;;;;;AASA;;;;;AASA;;;;;;AASA;;;;;AASA;;;;;AAMA;;;;AAWA;;;;AAKA;;;;AAIA;;;;;AAMA;;;;;AAOA;;;;AAKA;;;;AAMA;;;;;;AAUA;;;;;AASA;;;;AAIA;;;;AAKA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAKA;;;;AAKA;;;;AAIA;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAKA;;;;AAOA;;;;AAKA;;;;AAKA;;;;AAKA;EACE;;;;EAIA;;;;;AAMF;;;;AAIA;;;;AASA;;;;AAYA;;;;AAKA;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA", "debugId": null}}, {"offset": {"line": 8350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/%D8%A7%D8%A8%D9%88%D8%B9%D9%84%D9%88%D8%B4/diwan-abu-alosh/src/styles/enhanced-dialog.css"], "sourcesContent": ["/* تحسينات CSS للشاشة المنبثقة التفاعلية */\n\n/* تأثيرات الانتقال المحسنة */\n.dialog-container {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.dialog-content {\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  will-change: transform, filter;\n}\n\n/* تأثيرات الظل المتقدمة */\n.dialog-shadow-enhanced {\n  filter: drop-shadow(0 20px 25px rgb(0 0 0 / 0.1)) drop-shadow(0 8px 10px rgb(0 0 0 / 0.04));\n}\n\n.dialog-shadow-hover {\n  filter: drop-shadow(0 25px 25px rgb(0 0 0 / 0.15)) drop-shadow(0 10px 10px rgb(0 0 0 / 0.04));\n}\n\n/* تأثيرات التمرير السلس المحسنة */\n.smooth-scroll {\n  scroll-behavior: smooth;\n  scrollbar-width: thin;\n  scrollbar-color: rgba(59, 130, 246, 0.3) rgba(243, 244, 246, 0.5);\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n.smooth-scroll::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n.smooth-scroll::-webkit-scrollbar-track {\n  background: rgba(243, 244, 246, 0.5);\n  border-radius: 4px;\n  margin: 4px;\n}\n\n.smooth-scroll::-webkit-scrollbar-thumb {\n  background: linear-gradient(180deg, rgba(59, 130, 246, 0.6), rgba(37, 99, 235, 0.8));\n  border-radius: 4px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  -webkit-transition: all 0.2s ease;\n  transition: all 0.2s ease;\n}\n\n.smooth-scroll::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(180deg, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 1));\n  transform: scale(1.1);\n}\n\n.smooth-scroll::-webkit-scrollbar-thumb:active {\n  background: linear-gradient(180deg, rgba(37, 99, 235, 0.9), rgba(29, 78, 216, 1));\n}\n\n/* تحسين التمرير على الأجهزة اللمسية */\n.smooth-scroll {\n  -webkit-overflow-scrolling: touch;\n  overscroll-behavior: contain;\n}\n\n/* تأثيرات التمرير التفاعلية */\n.dialog-scroll-indicator {\n  position: absolute;\n  right: 8px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 60px;\n  background: rgba(59, 130, 246, 0.1);\n  border-radius: 2px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  pointer-events: none;\n}\n\n.smooth-scroll:hover .dialog-scroll-indicator {\n  opacity: 1;\n}\n\n.dialog-scroll-thumb {\n  position: absolute;\n  right: 0;\n  width: 4px;\n  background: rgba(59, 130, 246, 0.6);\n  border-radius: 2px;\n  transition: all 0.2s ease;\n}\n\n/* تأثيرات الخلفية المحسنة */\n.dialog-backdrop {\n  -webkit-backdrop-filter: blur(8px) saturate(180%);\n          backdrop-filter: blur(8px) saturate(180%);\n  background: rgba(0, 0, 0, 0.6);\n  transition: all 0.3s ease-out;\n}\n\n.dialog-backdrop.enhanced {\n  -webkit-backdrop-filter: blur(12px) saturate(200%);\n          backdrop-filter: blur(12px) saturate(200%);\n  background: rgba(0, 0, 0, 0.7);\n}\n\n/* تأثيرات الحركة المتقدمة */\n@keyframes dialogSlideIn {\n  from {\n    opacity: 0;\n    transform: translate3d(0, -20px, 0) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translate3d(0, 0, 0) scale(1);\n  }\n}\n\n@keyframes dialogSlideOut {\n  from {\n    opacity: 1;\n    transform: translate3d(0, 0, 0) scale(1);\n  }\n  to {\n    opacity: 0;\n    transform: translate3d(0, -20px, 0) scale(0.95);\n  }\n}\n\n.dialog-animate-in {\n  animation: dialogSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;\n}\n\n.dialog-animate-out {\n  animation: dialogSlideOut 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;\n}\n\n/* تأثيرات التفاعل مع الماوس */\n.dialog-mouse-follow {\n  cursor: move;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n\n.dialog-mouse-follow:hover {\n  transform: scale(1.02);\n  transition: transform 0.2s ease-out;\n}\n\n/* تأثيرات الحدود المحسنة */\n.dialog-border-glow {\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  box-shadow: \n    0 0 0 1px rgba(59, 130, 246, 0.1),\n    0 4px 6px -1px rgba(0, 0, 0, 0.1),\n    0 2px 4px -1px rgba(0, 0, 0, 0.06);\n}\n\n.dialog-border-glow:hover {\n  border-color: rgba(59, 130, 246, 0.5);\n  box-shadow: \n    0 0 0 1px rgba(59, 130, 246, 0.2),\n    0 10px 15px -3px rgba(0, 0, 0, 0.1),\n    0 4px 6px -2px rgba(0, 0, 0, 0.05);\n}\n\n/* تأثيرات الشفافية المتقدمة */\n.dialog-glass-effect {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px) saturate(180%);\n          backdrop-filter: blur(10px) saturate(180%);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n/* تأثيرات الإضاءة */\n.dialog-highlight {\n  position: relative;\n  overflow: hidden;\n}\n\n.dialog-highlight::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    transparent,\n    rgba(255, 255, 255, 0.2),\n    transparent\n  );\n  transition: left 0.5s ease-in-out;\n}\n\n.dialog-highlight:hover::before {\n  left: 100%;\n}\n\n/* تأثيرات الاستجابة للشاشات المختلفة */\n@media (max-width: 768px) {\n  .dialog-content {\n    max-width: 95vw !important;\n    margin: 10px;\n  }\n  \n  .dialog-mouse-follow {\n    cursor: default;\n  }\n  \n  .dialog-mouse-follow:hover {\n    transform: none;\n  }\n}\n\n@media (max-width: 480px) {\n  .dialog-content {\n    max-width: 100vw !important;\n    margin: 0;\n    border-radius: 0;\n    height: 100vh;\n    max-height: 100vh;\n  }\n}\n\n/* تأثيرات الأداء المحسن */\n.dialog-performance-optimized {\n  transform: translate3d(0, 0, 0);\n  backface-visibility: hidden;\n  perspective: 1000px;\n}\n\n/* تأثيرات التركيز المحسنة */\n.dialog-focus-trap {\n  outline: none;\n}\n\n.dialog-focus-trap:focus-visible {\n  outline: 2px solid rgba(59, 130, 246, 0.6);\n  outline-offset: 2px;\n}\n\n/* تأثيرات الحالة المختلفة */\n.dialog-loading {\n  pointer-events: none;\n  opacity: 0.7;\n}\n\n.dialog-loading::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 20px;\n  height: 20px;\n  margin: -10px 0 0 -10px;\n  border: 2px solid rgba(59, 130, 246, 0.3);\n  border-top-color: rgba(59, 130, 246, 1);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* تأثيرات الألوان المتدرجة */\n.dialog-gradient-border {\n  background: linear-gradient(white, white) padding-box,\n              linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4) border-box;\n  border: 2px solid transparent;\n}\n\n/* تأثيرات النبضة */\n.dialog-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.8;\n  }\n}\n\n/* تأثيرات الاهتزاز الخفيف */\n.dialog-shake {\n  animation: shake 0.5s ease-in-out;\n}\n\n@keyframes shake {\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-5px); }\n  75% { transform: translateX(5px); }\n}\n\n/* تأثيرات التكبير التدريجي */\n.dialog-zoom-in {\n  animation: zoomIn 0.3s ease-out;\n}\n\n@keyframes zoomIn {\n  from {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n/* تأثيرات الانزلاق من الأعلى */\n.dialog-slide-down {\n  animation: slideDown 0.3s ease-out;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* تحسينات الطباعة */\n@media print {\n  .dialog-container {\n    position: static !important;\n    background: white !important;\n    box-shadow: none !important;\n    border: 1px solid #ccc !important;\n  }\n  \n  .dialog-backdrop {\n    display: none !important;\n  }\n}\n"], "names": [], "mappings": "AAGA;;;;AAIA;;;;;AAMA;;;;AAIA;;;;AAKA;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;AAKA;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;;;;AAUA;;;;;;AAOA;;;;;AAOA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;AAIA;;;;AAKA;;;;;;;AAOA;;;;;AAMA;;;;;AAQA;;;;;AASA;;;;;;AAQA;;;;;AAKA;;;;;;;;;;;AAgBA;;;;AAKA;EACE;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;;;;;;AAUF;;;;;;AAOA;;;;AAIA;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;;AAOA;;;;;AAOA;;;;AAIA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;;;;;;AAYA;EACE;;;;;;;EAOA", "debugId": null}}]}