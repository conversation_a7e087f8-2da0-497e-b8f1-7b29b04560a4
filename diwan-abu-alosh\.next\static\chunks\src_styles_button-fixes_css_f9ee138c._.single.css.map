{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/button-fixes.css"], "sourcesContent": ["/* إصلاحات ألوان الأزرار من نوع outline */\n\n/* إصلاح الأزرار من نوع outline */\n.btn-outline,\nbutton[data-variant=\"outline\"],\n[class*=\"outline\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n.btn-outline:hover,\nbutton[data-variant=\"outline\"]:hover,\n[class*=\"outline\"]:hover {\n  background-color: #f9fafb !important; /* gray-50 */\n  color: black !important;\n  border-color: #9ca3af !important; /* gray-400 */\n}\n\n.btn-outline:focus,\nbutton[data-variant=\"outline\"]:focus,\n[class*=\"outline\"]:focus {\n  background-color: white !important;\n  color: black !important;\n  border-color: #3b82f6 !important; /* blue-500 */\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;\n}\n\n.btn-outline:active,\nbutton[data-variant=\"outline\"]:active,\n[class*=\"outline\"]:active {\n  background-color: #f3f4f6 !important; /* gray-100 */\n  color: black !important;\n  border-color: #6b7280 !important; /* gray-500 */\n}\n\n/* إصلاح أزرار outline محددة بالفئات */\n.border-primary-300,\n.border-diwan-300,\n.border-secondary-300 {\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n.text-primary-700,\n.text-diwan-700,\n.text-secondary-700 {\n  color: black !important;\n}\n\n.hover\\:bg-primary-50:hover,\n.hover\\:bg-diwan-50:hover,\n.hover\\:bg-secondary-50:hover {\n  background-color: #f9fafb !important; /* gray-50 */\n}\n\n.hover\\:border-primary-400:hover,\n.hover\\:border-diwan-400:hover,\n.hover\\:border-secondary-400:hover {\n  border-color: #9ca3af !important; /* gray-400 */\n}\n\n/* إصلاح أزرار outline في النماذج */\nform .btn-outline,\nform button[data-variant=\"outline\"],\n.form-group .btn-outline,\n.form-group button[data-variant=\"outline\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline في الحوارات */\n.dialog .btn-outline,\n.dialog button[data-variant=\"outline\"],\n[role=\"dialog\"] .btn-outline,\n[role=\"dialog\"] button[data-variant=\"outline\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline في الجداول */\n.table .btn-outline,\n.table button[data-variant=\"outline\"],\ntable .btn-outline,\ntable button[data-variant=\"outline\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline في البطاقات */\n.card .btn-outline,\n.card button[data-variant=\"outline\"],\n[class*=\"card\"] .btn-outline,\n[class*=\"card\"] button[data-variant=\"outline\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline الصغيرة */\n.btn-outline.btn-sm,\n.btn-outline[data-size=\"sm\"],\nbutton[data-variant=\"outline\"][data-size=\"sm\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline الكبيرة */\n.btn-outline.btn-lg,\n.btn-outline[data-size=\"lg\"],\nbutton[data-variant=\"outline\"][data-size=\"lg\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline المعطلة */\n.btn-outline:disabled,\n.btn-outline[disabled],\nbutton[data-variant=\"outline\"]:disabled,\nbutton[data-variant=\"outline\"][disabled] {\n  background-color: #f9fafb !important; /* gray-50 */\n  color: #9ca3af !important; /* gray-400 */\n  border-color: #e5e7eb !important; /* gray-200 */\n  opacity: 0.6 !important;\n}\n\n/* إصلاح أزرار outline مع أيقونات */\n.btn-outline .icon,\n.btn-outline svg,\nbutton[data-variant=\"outline\"] .icon,\nbutton[data-variant=\"outline\"] svg {\n  color: black !important;\n}\n\n.btn-outline:hover .icon,\n.btn-outline:hover svg,\nbutton[data-variant=\"outline\"]:hover .icon,\nbutton[data-variant=\"outline\"]:hover svg {\n  color: black !important;\n}\n\n/* إصلاح أزرار outline في الشريط الجانبي */\n.sidebar .btn-outline,\n.sidebar button[data-variant=\"outline\"],\n[class*=\"sidebar\"] .btn-outline,\n[class*=\"sidebar\"] button[data-variant=\"outline\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline في الهيدر */\n.header .btn-outline,\n.header button[data-variant=\"outline\"],\n[class*=\"header\"] .btn-outline,\n[class*=\"header\"] button[data-variant=\"outline\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline في التنقل */\n.nav .btn-outline,\n.nav button[data-variant=\"outline\"],\nnav .btn-outline,\nnav button[data-variant=\"outline\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline في الفوتر */\n.footer .btn-outline,\n.footer button[data-variant=\"outline\"],\nfooter .btn-outline,\nfooter button[data-variant=\"outline\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline مع فئات Tailwind محددة */\n.bg-transparent {\n  background-color: white !important;\n}\n\n.border-2.border-primary-300,\n.border-2.border-diwan-300,\n.border-2.border-secondary-300 {\n  border-color: #d1d5db !important; /* gray-300 */\n  border-width: 2px !important;\n}\n\n/* إصلاح أزرار outline في الوضع الداكن */\n@media (prefers-color-scheme: dark) {\n  .btn-outline,\n  button[data-variant=\"outline\"],\n  [class*=\"outline\"] {\n    background-color: white !important;\n    color: black !important;\n    border-color: #d1d5db !important; /* gray-300 */\n  }\n}\n\n/* إصلاح أزرار outline مع فئات مخصصة */\n.button-outline,\n.btn-white,\n.button-white,\n.outline-button {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n.button-outline:hover,\n.btn-white:hover,\n.button-white:hover,\n.outline-button:hover {\n  background-color: #f9fafb !important; /* gray-50 */\n  color: black !important;\n  border-color: #9ca3af !important; /* gray-400 */\n}\n\n/* إصلاح أزرار outline في مكونات محددة */\n.member-dialog .btn-outline,\n.upload-dialog .btn-outline,\n.settings-dialog .btn-outline {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline في صفحات محددة */\n.members-page .btn-outline,\n.incomes-page .btn-outline,\n.expenses-page .btn-outline,\n.gallery-page .btn-outline,\n.reports-page .btn-outline {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n}\n\n/* إصلاح أزرار outline مع حالات خاصة */\n.btn-outline.loading,\n.btn-outline[data-loading=\"true\"],\nbutton[data-variant=\"outline\"].loading,\nbutton[data-variant=\"outline\"][data-loading=\"true\"] {\n  background-color: white !important;\n  color: black !important;\n  border-color: #d1d5db !important; /* gray-300 */\n  opacity: 0.7 !important;\n}\n\n/* إصلاح أزرار outline مع تأثيرات الانتقال */\n.btn-outline,\nbutton[data-variant=\"outline\"] {\n  transition: all 0.2s ease-in-out !important;\n}\n\n/* إصلاح أزرار outline مع تأثيرات الظل */\n.btn-outline:hover,\nbutton[data-variant=\"outline\"]:hover {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;\n}\n\n.btn-outline:focus,\nbutton[data-variant=\"outline\"]:focus {\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;\n}\n"], "names": [], "mappings": "AAGA;;;;;;AAQA;;;;;;AAQA;;;;;;;AASA;;;;;;AASA;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAOA;;;;;;AA0DA;;;;;;;AAWA;;;;AAeA;;;;;;AAwCA;;;;AAIA;;;;;AAQA;EACE;;;;;;;AAUF;;;;;;AASA;;;;;;AAUA;;;;;;AAoBA;;;;;;;AAWA;;;;AAMA;;;;AAKA"}}]}